Project Summary
Build a comprehensive ISP (Internet Service Provider) management platform similar to Splynx.com using Laravel framework. This system is specifically designed for ISPs using Mikrotik equipment with PPPoE and Static IP internet service delivery models.
Core System Requirements
1. Customer Management System (CRM)

Customer Registration & Profiles

Personal/business customer information
Service history and status tracking
Document management (contracts, IDs, etc.)
Customer portal for self-service
Multi-location support for customers


Service Management

PPPoE service plans and packages
Static IP service plans and packages
Service activation/deactivation workflows
Service upgrades/downgrades
Bandwidth allocation per service type
PPPoE username/password management
Static IP assignment and management



2. Billing & Financial Management

Billing Engine

Recurring billing automation (monthly/quarterly/annual)
Prorated billing for mid-cycle changes
Multiple payment methods (credit card, bank transfer, cash)
Invoice generation and delivery (email/SMS/print)
Payment processing and reconciliation
Late fee and penalty management
Separate billing for PPPoE vs Static IP services


Financial Reporting

Revenue reports and analytics
Accounts receivable tracking
Payment history and trends
Tax calculation and reporting
Financial dashboards



3. Mikrotik Network Management & Integration

Mikrotik RouterOS Integration

Direct API connection to Mikrotik routers
PPPoE server management and configuration
Static IP route management
Real-time bandwidth monitoring via Mikrotik API
User profile creation and management on Mikrotik
Service activation/suspension via API commands


PPPoE Service Management

Automated PPPoE user creation on Mikrotik
Bandwidth profile assignment (download/upload limits)
PPPoE secret management (username/password)
Session monitoring and management
Automatic service suspension for non-payment
PPPoE user statistics and usage tracking


Static IP Service Management

IP address pool management
Static route creation on Mikrotik routers
Firewall rule management for static IP customers
NAT rule configuration
Static IP customer bandwidth control
IP conflict detection and resolution



4. Network Monitoring & Performance (Mikrotik-Specific)

Real-time Monitoring

Live bandwidth usage per customer (PPPoE and Static IP)
Connection status monitoring
Session tracking for PPPoE users
Router health monitoring via SNMP
Interface utilization on Mikrotik equipment


Performance Analytics

Historical bandwidth usage reports
Peak usage time analysis
Service quality metrics
Network congestion identification
Customer usage patterns



5. Service Provisioning & Automation

Automated PPPoE Provisioning

Automatic PPPoE user creation upon payment
Bandwidth profile assignment based on service plan
Service activation via Mikrotik API
Automatic suspension for non-payment
Service restoration upon payment


Automated Static IP Provisioning

Automatic IP assignment from available pools
Static route creation on Mikrotik
Firewall and NAT rule configuration
Service activation and bandwidth allocation
IP release upon service termination



6. Ticketing & Support System

Help Desk Management

Ticket creation and tracking
Priority levels and escalation
Technician assignment and scheduling
Customer communication history
SLA tracking and reporting
Integration with network monitoring for proactive support



7. Reporting & Analytics

Business Intelligence

Customer acquisition and churn reports
Revenue analysis by service type (PPPoE vs Static IP)
Network utilization reports
Service quality metrics
Bandwidth usage analytics
Customizable dashboards



Technical Architecture Requirements
Laravel Framework Structure

Version: Latest stable Laravel (10.x or 11.x)
Architecture: Modular structure with clear separation of concerns
Database: MySQL/PostgreSQL with proper indexing for performance
Caching: Redis for session management and caching
Queue System: Laravel Queues for Mikrotik API calls and background processing

Key Laravel Components to Implement

Authentication & Authorization

Multi-role system (Admin, Manager, Technician, Customer)
Permission-based access control
API authentication (Sanctum/Passport)


Database Design

Customers, Services, Plans, Invoices, Payments tables
PPPoE users and credentials
Static IP assignments and pools
Mikrotik equipment inventory
Network monitoring data storage
Audit trails for all critical operations


Background Jobs

Billing automation
Mikrotik API synchronization
Network monitoring tasks
Email/SMS notifications
Report generation



Mikrotik API Integration Requirements

RouterOS API Connection

Secure API connection to multiple Mikrotik routers
Connection pooling and failover handling
API command queuing for bulk operations
Error handling and retry mechanisms


PPPoE Management via API

/ppp/secret management for user credentials
/ppp/profile management for bandwidth limits
/ppp/active monitoring for active sessions
Real-time session control (disconnect/enable/disable)


Static IP Management via API

/ip/route management for customer routes
/ip/firewall/nat rule management
/queue/simple for bandwidth control
/ip/address pool management


Monitoring via API

Interface statistics collection
CPU and memory usage monitoring
Active connection tracking
Bandwidth utilization data



Payment Gateway Integration

Payment Processing

Stripe/PayPal integration
Bank API integrations for direct debits
Multiple currency support
Automated payment retry for failed transactions



Communication APIs

Notifications

Email service integration (SendGrid/Mailgun)
SMS gateway integration
Payment reminders and service notifications



User Interface Requirements
Admin Dashboard

Real-time network overview with Mikrotik statistics
Customer management interface
PPPoE and Static IP service management
Financial reporting tools
Mikrotik equipment monitoring
Service provisioning controls

Customer Portal

Account information and service details
Bandwidth usage statistics
PPPoE credential management
Static IP information display
Billing history and payment options
Speed test integration
Support ticket submission

Network Management Interface

Mikrotik router management dashboard
PPPoE user overview and management
Static IP assignment interface
Real-time monitoring displays
Service provisioning tools

Service-Specific Features
PPPoE Service Features

Plan Management: Different bandwidth profiles with upload/download limits
User Management: Automated username/password generation or custom credentials
Session Control: Force disconnect, session time limits, concurrent session limits
Usage Monitoring: Real-time and historical bandwidth usage
Automatic Actions: Service suspension, bandwidth throttling, service restoration

Static IP Service Features

IP Pool Management: Subnet allocation and IP assignment
Route Management: Automatic static route creation on Mikrotik
Bandwidth Control: Queue management for static IP customers
Firewall Integration: Automated firewall rules for customer networks
Service Tiers: Different static IP plans with varying features

Performance & Scalability Requirements

Support for 10,000+ active customers (PPPoE and Static IP combined)
Real-time Mikrotik API calls without performance impact
Efficient database queries for network monitoring data
Background job processing for Mikrotik operations
Caching strategy for frequently accessed network data

Security Requirements

Secure Mikrotik API connections (SSL/TLS)
Encrypted storage of PPPoE credentials
Network access logging and monitoring
Customer data privacy compliance
Secure payment processing

Deployment & Infrastructure

Docker containerization for easy deployment
Mikrotik API connection management
Database replication and backup strategies
Network monitoring and logging
CI/CD pipeline setup

Development Phases

Phase 1: Core CRM and Mikrotik API integration
Phase 2: PPPoE service management and billing
Phase 3: Static IP service management
Phase 4: Advanced monitoring and automation
Phase 5: Customer portal and reporting features

Success Metrics

Automated PPPoE and Static IP provisioning
Real-time network monitoring via Mikrotik integration
99% uptime for service provisioning
Reduced manual network configuration by 95%
Customer self-service capabilities

Mikrotik-Specific Technical Notes

Use RouterOS API protocol for all communications
Implement connection pooling for multiple router management
Handle API timeouts and connection failures gracefully
Store Mikrotik configuration backups
Implement bulk operations for efficient API usage
Consider Mikrotik version compatibility across different RouterOS versions