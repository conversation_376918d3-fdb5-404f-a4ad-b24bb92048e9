@extends('layouts.app')

@section('title', 'RADIUS User Details')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">User Details: {{ $user->username }}</h5>
                    <div>
                        <a href="{{ route('radius.users.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                        <a href="{{ route('radius.users.edit', $user) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit User
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2 mb-3">Basic Information</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th style="width: 30%">Username:</th>
                                    <td>{{ $user->username }}</td>
                                </tr>
                                <tr>
                                    <th>Group:</th>
                                    <td>
                                        @if($user->groupname)
                                            <a href="{{ route('radius.groups.show', $user->group) }}">
                                                {{ $user->groupname }}
                                            </a>
                                        @else
                                            <span class="text-muted">None</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <span class="badge bg-{{ $user->active ? 'success' : 'danger' }}">
                                            {{ $user->active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created:</th>
                                    <td>{{ $user->created_at->format('Y-m-d H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th>Last Updated:</th>
                                    <td>{{ $user->updated_at->format('Y-m-d H:i:s') }}</td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2 mb-3">Usage Statistics</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th style="width: 30%">Total Sessions:</th>
                                    <td>{{ $user->accountingRecords()->count() }}</td>
                                </tr>
                                <tr>
                                    <th>Total Download:</th>
                                    <td>{{ formatBytes($user->accountingRecords()->sum('acct_input_octets')) }}</td>
                                </tr>
                                <tr>
                                    <th>Total Upload:</th>
                                    <td>{{ formatBytes($user->accountingRecords()->sum('acct_output_octets')) }}</td>
                                </tr>
                                <tr>
                                    <th>Total Usage:</th>
                                    <td>{{ formatBytes($user->accountingRecords()->sum('acct_input_octets') + $user->accountingRecords()->sum('acct_output_octets')) }}</td>
                                </tr>
                                <tr>
                                    <th>Last Session:</th>
                                    <td>
                                        @php
                                            $lastSession = $user->accountingRecords()->latest('acct_start_time')->first();
                                        @endphp
                                        @if($lastSession)
                                            {{ $lastSession->acct_start_time->format('Y-m-d H:i:s') }}
                                        @else
                                            <span class="text-muted">Never</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="border-bottom pb-2 mb-0">User Attributes</h6>
                                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addAttributeModal">
                                    <i class="fas fa-plus"></i> Add Attribute
                                </button>
                            </div>

                            @if($user->attributes->isEmpty())
                                <div class="alert alert-info">
                                    No attributes defined for this user.
                                </div>
                            @else
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Attribute</th>
                                                <th>Operator</th>
                                                <th>Value</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->attributes as $attribute)
                                                <tr>
                                                    <td>{{ $attribute->attribute }}</td>
                                                    <td>{{ $attribute->op }}</td>
                                                    <td>{{ $attribute->value }}</td>
                                                    <td>
                                                        <form action="{{ route('radius.users.attributes.destroy', [$user, $attribute]) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this attribute?')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h6 class="border-bottom pb-2 mb-3">Recent Accounting Records</h6>

                            @if($accountingRecords->isEmpty())
                                <div class="alert alert-info">
                                    No accounting records found for this user.
                                </div>
                            @else
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Session ID</th>
                                                <th>NAS IP</th>
                                                <th>Start Time</th>
                                                <th>Stop Time</th>
                                                <th>Duration</th>
                                                <th>Download</th>
                                                <th>Upload</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($accountingRecords as $record)
                                                <tr>
                                                    <td>{{ Str::limit($record->acct_session_id, 10) }}</td>
                                                    <td>{{ $record->nas_ip_address }}</td>
                                                    <td>{{ $record->acct_start_time ? $record->acct_start_time->format('Y-m-d H:i:s') : '-' }}</td>
                                                    <td>{{ $record->acct_stop_time ? $record->acct_stop_time->format('Y-m-d H:i:s') : '-' }}</td>
                                                    <td>{{ $record->acct_session_time ? formatDuration($record->acct_session_time) : '-' }}</td>
                                                    <td>{{ formatBytes($record->acct_input_octets) }}</td>
                                                    <td>{{ formatBytes($record->acct_output_octets) }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <div class="d-flex justify-content-center mt-4">
                                    {{ $accountingRecords->links() }}
                                </div>

                                <div class="text-center mt-2">
                                    <a href="{{ route('radius.users.accounting', $user) }}" class="btn btn-info btn-sm">
                                        View All Accounting Records
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Attribute Modal -->
<div class="modal fade" id="addAttributeModal" tabindex="-1" aria-labelledby="addAttributeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAttributeModalLabel">Add Attribute</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('radius.users.attributes.store', $user) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="attribute" class="form-label">Attribute</label>
                        <input type="text" class="form-control" id="attribute" name="attribute" required>
                    </div>
                    <div class="mb-3">
                        <label for="op" class="form-label">Operator</label>
                        <select class="form-select" id="op" name="op" required>
                            <option value="=">=</option>
                            <option value=":=">:=</option>
                            <option value="==">==</option>
                            <option value="+=">&gt;=</option>
                            <option value="!=">&lt;=</option>
                            <option value="&gt;">&gt;</option>
                            <option value="&lt;">&lt;</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="value" class="form-label">Value</label>
                        <input type="text" class="form-control" id="value" name="value" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Attribute</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Any additional JavaScript can go here
</script>
@endsection

@php
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= (1 << (10 * $pow));

    return round($bytes, $precision) . ' ' . $units[$pow];
}

function formatDuration($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $secs = $seconds % 60;

    return sprintf('%02d:%02d:%02d', $hours, $minutes, $secs);
}
@endphp
