@extends('layouts.app')

@section('title', isset($user) ? 'Edit RADIUS User' : 'Create RADIUS User')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ isset($user) ? 'Edit User: ' . $user->username : 'Create New RADIUS User' }}</h5>
                    <a href="{{ isset($user) ? route('radius.users.show', $user) : route('radius.users.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> {{ isset($user) ? 'Back to User' : 'Back to List' }}
                    </a>
                </div>

                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ isset($user) ? route('radius.users.update', $user) : route('radius.users.store') }}">
                        @csrf
                        @if(isset($user))
                            @method('PUT')
                        @endif

                        <div class="mb-3">
                            <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('username') is-invalid @enderror" id="username" name="username"
                                value="{{ old('username', isset($user) ? $user->username : '') }}"
                                {{ isset($user) ? 'readonly' : 'required' }}>
                            @error('username')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            @if(isset($user))
                                <div class="form-text text-muted">Username cannot be changed after creation.</div>
                            @endif
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">{{ isset($user) ? 'New Password' : 'Password' }} {{ isset($user) ? '' : '<span class="text-danger">*</span>' }}</label>
                            <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password"
                                {{ isset($user) ? '' : 'required' }}>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            @if(isset($user))
                                <div class="form-text text-muted">Leave blank to keep the current password.</div>
                            @endif
                        </div>

                        <div class="mb-3">
                            <label for="password_confirmation" class="form-label">{{ isset($user) ? 'Confirm New Password' : 'Confirm Password' }}</label>
                            <input type="password" class="form-control" id="password_confirmation" name="password_confirmation">
                        </div>

                        <div class="mb-3">
                            <label for="groupname" class="form-label">Group</label>
                            <select class="form-select @error('groupname') is-invalid @enderror" id="groupname" name="groupname">
                                <option value="">No Group</option>
                                @foreach($groups as $group)
                                    <option value="{{ $group->groupname }}"
                                        {{ old('groupname', isset($user) ? $user->groupname : '') == $group->groupname ? 'selected' : '' }}>
                                        {{ $group->groupname }}
                                    </option>
                                @endforeach
                            </select>
                            @error('groupname')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input @error('active') is-invalid @enderror" type="checkbox" id="active" name="active"
                                    {{ old('active', isset($user) ? $user->active : true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="active">Active</label>
                                @error('active')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        @if(isset($user))
                            <h6 class="border-bottom pb-2 mb-3 mt-4">User Attributes</h6>

                            @if($user->attributes->isEmpty())
                                <div class="alert alert-info">
                                    No attributes defined for this user. You can add attributes after saving.
                                </div>
                            @else
                                <div class="table-responsive mb-3">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Attribute</th>
                                                <th>Operator</th>
                                                <th>Value</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->attributes as $attribute)
                                                <tr>
                                                    <td>{{ $attribute->attribute }}</td>
                                                    <td>{{ $attribute->op }}</td>
                                                    <td>{{ $attribute->value }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                <div class="form-text text-muted mb-3">
                                    You can manage attributes from the user details page after saving.
                                </div>
                            @endif
                        @endif

                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                {{ isset($user) ? 'Update User' : 'Create User' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Any additional JavaScript can go here
</script>
@endsection
