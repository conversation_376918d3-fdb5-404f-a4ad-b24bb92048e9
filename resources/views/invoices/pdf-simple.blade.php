<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice {{ $invoice->invoice_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            display: table;
            width: 100%;
            margin-bottom: 30px;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 20px;
        }

        .company-info {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .company-details {
            color: #6b7280;
            line-height: 1.5;
        }

        .invoice-info {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            text-align: right;
        }

        .invoice-title {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .invoice-number {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 10px;
        }

        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-paid { background-color: #d1fae5; color: #065f46; }
        .status-pending { background-color: #fef3c7; color: #92400e; }
        .status-overdue { background-color: #fee2e2; color: #991b1b; }
        .status-draft { background-color: #f3f4f6; color: #374151; }
        .status-cancelled { background-color: #f3f4f6; color: #6b7280; }

        .details-section {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }

        .bill-to {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding-right: 20px;
        }

        .invoice-details {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }

        .customer-name {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .customer-details {
            color: #6b7280;
            line-height: 1.5;
        }

        .detail-row {
            display: table;
            width: 100%;
            margin-bottom: 5px;
        }

        .detail-label {
            display: table-cell;
            width: 40%;
            font-weight: bold;
            color: #374151;
        }

        .detail-value {
            display: table-cell;
            color: #1f2937;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            border: 1px solid #e5e7eb;
        }

        .items-table th {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            color: #374151;
        }

        .items-table td {
            border: 1px solid #e5e7eb;
            padding: 12px;
            color: #1f2937;
        }

        .text-right {
            text-align: right;
        }

        .totals-section {
            margin-top: 20px;
            text-align: right;
        }

        .total-row {
            display: table;
            width: 300px;
            margin-left: auto;
            margin-bottom: 8px;
        }

        .total-label {
            display: table-cell;
            padding: 8px 12px;
            font-weight: bold;
            color: #374151;
        }

        .total-value {
            display: table-cell;
            padding: 8px 12px;
            text-align: right;
            color: #1f2937;
        }

        .final-total {
            border-top: 2px solid #1f2937;
            font-size: 16px;
            font-weight: bold;
        }

        .final-total .total-label,
        .final-total .total-value {
            color: #1f2937;
            font-size: 16px;
        }

        .payments-section {
            margin-top: 40px;
        }

        .payments-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            border: 1px solid #e5e7eb;
        }

        .payments-table th {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            padding: 10px;
            text-align: left;
            font-weight: bold;
            color: #374151;
            font-size: 11px;
        }

        .payments-table td {
            border: 1px solid #e5e7eb;
            padding: 10px;
            color: #1f2937;
            font-size: 11px;
        }

        .payment-method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            background-color: #f3f4f6;
            color: #374151;
        }

        .payment-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .payment-status-completed { background-color: #d1fae5; color: #065f46; }
        .payment-status-pending { background-color: #fef3c7; color: #92400e; }
        .payment-status-failed { background-color: #fee2e2; color: #991b1b; }

        .summary-section {
            margin-top: 30px;
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .summary-grid {
            display: table;
            width: 100%;
        }

        .summary-item {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            padding: 0 10px;
        }

        .summary-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 5px;
        }

        .summary-value {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
        }

        .summary-value.paid {
            color: #059669;
        }

        .summary-value.remaining {
            color: #dc2626;
        }

        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9fafb;
            border-left: 4px solid #3b82f6;
        }

        .notes-title {
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .notes-content {
            color: #374151;
            line-height: 1.5;
        }

        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-info">
                <div class="company-name">{{ $companyInfo['name'] ?? 'ISP Management System' }}</div>
                <div class="company-details">
                    @if(!empty($companyInfo['address']))
                        {{ $companyInfo['address'] }}<br>
                    @endif
                    @if(!empty($companyInfo['email']))
                        Email: {{ $companyInfo['email'] }}<br>
                    @endif
                    @if(!empty($companyInfo['phone']))
                        Phone: {{ $companyInfo['phone'] }}
                    @endif
                </div>
            </div>
            <div class="invoice-info">
                <div class="invoice-title">INVOICE</div>
                <div class="invoice-number"># {{ $invoice->invoice_number }}</div>
                <div class="status status-{{ $invoice->status }}">
                    {{ ucfirst($invoice->status) }}
                </div>
            </div>
        </div>

        <!-- Customer and Invoice Details -->
        <div class="details-section">
            <div class="bill-to">
                <div class="section-title">Bill To</div>
                <div class="customer-name">{{ $invoice->customer->name }}</div>
                <div class="customer-details">
                    {{ $invoice->customer->email }}<br>
                    @if($invoice->customer->phone)
                        {{ $invoice->customer->phone }}<br>
                    @endif
                    @if($invoice->customer->full_address)
                        {{ $invoice->customer->full_address }}
                    @endif
                </div>
            </div>
            <div class="invoice-details">
                <div class="section-title">Invoice Details</div>
                <div class="detail-row">
                    <div class="detail-label">Issue Date:</div>
                    <div class="detail-value">{{ $invoice->issue_date->format('M d, Y') }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Due Date:</div>
                    <div class="detail-value">{{ $invoice->due_date->format('M d, Y') }}</div>
                </div>
                @if($invoice->paid_date)
                <div class="detail-row">
                    <div class="detail-label">Paid Date:</div>
                    <div class="detail-value">{{ $invoice->paid_date->format('M d, Y') }}</div>
                </div>
                @endif
                @if($invoice->subscription)
                <div class="detail-row">
                    <div class="detail-label">Subscription:</div>
                    <div class="detail-value">{{ $invoice->subscription->name }}</div>
                </div>
                @endif
            </div>
        </div>

        <!-- Invoice Items -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th class="text-right">Quantity</th>
                    <th class="text-right">Unit Price</th>
                    <th class="text-right">Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach($invoice->items as $item)
                <tr>
                    <td>{{ $item->description }}</td>
                    <td class="text-right">{{ $item->quantity }}</td>
                    <td class="text-right">{{ $formatCurrency($item->unit_price) }}</td>
                    <td class="text-right">{{ $formatCurrency($item->total_price) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <div class="total-row">
                <div class="total-label">Subtotal:</div>
                <div class="total-value">{{ $formatCurrency($invoice->amount) }}</div>
            </div>
            @if($invoice->tax_amount > 0)
            <div class="total-row">
                <div class="total-label">Tax:</div>
                <div class="total-value">{{ $formatCurrency($invoice->tax_amount) }}</div>
            </div>
            @endif
            <div class="total-row final-total">
                <div class="total-label">Total:</div>
                <div class="total-value">{{ $formatCurrency($invoice->total_amount) }}</div>
            </div>
        </div>

        <!-- Payment Summary -->
        <div class="summary-section">
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-label">Total Amount</div>
                    <div class="summary-value">{{ $formatCurrency($invoice->total_amount) }}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Amount Paid</div>
                    <div class="summary-value paid">{{ $formatCurrency($invoice->total_paid) }}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Remaining Balance</div>
                    <div class="summary-value remaining">{{ $formatCurrency($invoice->remaining_balance) }}</div>
                </div>
            </div>
        </div>

        <!-- Payment History -->
        @if($invoice->payments && count($invoice->payments) > 0)
        <div class="payments-section">
            <div class="section-title">Payment History</div>
            <table class="payments-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Method</th>
                        <th>Reference</th>
                        <th class="text-right">Amount</th>
                        <th>Status</th>
                        <th>Confirmed By</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($invoice->payments as $payment)
                    <tr>
                        <td>{{ $payment->payment_date ? $payment->payment_date->format('M d, Y') : 'N/A' }}</td>
                        <td>
                            <span class="payment-method">{{ $payment->payment_method }}</span>
                        </td>
                        <td>
                            {{ $payment->payment_method === 'mpesa'
                                ? ($payment->mpesa_receipt_number ?? $payment->transaction_id)
                                : ($payment->reference_number ?? 'N/A') }}
                        </td>
                        <td class="text-right">{{ $formatCurrency($payment->amount) }}</td>
                        <td>
                            <span class="payment-status payment-status-{{ $payment->status }}">
                                {{ ucfirst($payment->status) }}
                            </span>
                        </td>
                        <td>{{ $payment->confirmedBy ? $payment->confirmedBy->name : 'System' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @endif

        <!-- Notes -->
        @if($invoice->notes)
        <div class="notes">
            <div class="notes-title">Notes</div>
            <div class="notes-content">{{ $invoice->notes }}</div>
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <p>Generated on {{ now()->format('M d, Y \a\t g:i A') }}</p>
            <p>{{ $companyInfo['name'] ?? 'ISP Management System' }} - Invoice {{ $invoice->invoice_number }}</p>
        </div>
    </div>
</body>
</html>
