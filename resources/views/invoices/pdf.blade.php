<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {{ $invoice->invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: table;
            width: 100%;
            margin-bottom: 30px;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 20px;
        }

        .company-info {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .company-details {
            color: #6b7280;
            line-height: 1.5;
        }

        .invoice-info {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            text-align: right;
        }

        .invoice-title {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }

        .invoice-number {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 5px;
        }

        .invoice-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-paid {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-overdue {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .status-cancelled {
            background-color: #f3f4f6;
            color: #374151;
        }

        .details-section {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }

        .bill-to, .invoice-details {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding-right: 20px;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .customer-info {
            background-color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #3b82f6;
        }

        .customer-name {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .customer-details {
            color: #6b7280;
            line-height: 1.5;
        }

        .detail-row {
            display: table;
            width: 100%;
            margin-bottom: 8px;
        }

        .detail-label {
            display: table-cell;
            width: 40%;
            font-weight: bold;
            color: #374151;
        }

        .detail-value {
            display: table-cell;
            color: #6b7280;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            background-color: #fff;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .items-table th {
            background-color: #f8fafc;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .items-table th:last-child,
        .items-table td:last-child {
            text-align: right;
        }

        .items-table td {
            padding: 12px;
            border-bottom: 1px solid #f3f4f6;
            color: #6b7280;
        }

        .items-table tr:last-child td {
            border-bottom: none;
        }

        .item-description {
            font-weight: 500;
            color: #374151;
        }

        .totals-section {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }

        .totals-spacer {
            display: table-cell;
            width: 60%;
        }

        .totals-table {
            display: table-cell;
            width: 40%;
        }

        .total-row {
            display: table;
            width: 100%;
            margin-bottom: 8px;
            padding: 8px 0;
        }

        .total-row.final {
            border-top: 2px solid #e5e7eb;
            padding-top: 12px;
            margin-top: 12px;
        }

        .total-label {
            display: table-cell;
            font-weight: bold;
            color: #374151;
        }

        .total-value {
            display: table-cell;
            text-align: right;
            color: #6b7280;
        }

        .total-row.final .total-label,
        .total-row.final .total-value {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
        }

        .payment-summary {
            background-color: #f0f9ff;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #0ea5e9;
            margin-bottom: 30px;
        }

        .payment-title {
            font-size: 14px;
            font-weight: bold;
            color: #0c4a6e;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .payment-grid {
            display: table;
            width: 100%;
        }

        .payment-item {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            padding: 0 10px;
        }

        .payment-amount {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .payment-amount.total {
            color: #1f2937;
        }

        .payment-amount.paid {
            color: #059669;
        }

        .payment-amount.remaining {
            color: #dc2626;
        }

        .payment-label {
            font-size: 11px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #9ca3af;
            font-size: 11px;
        }

        .notes {
            background-color: #fffbeb;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #f59e0b;
            margin-bottom: 20px;
        }

        .notes-title {
            font-weight: bold;
            color: #92400e;
            margin-bottom: 8px;
        }

        .notes-content {
            color: #78350f;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-info">
                <div class="company-name">{{ $companyInfo['name'] }}</div>
                <div class="company-details">
                    @if(!empty($companyInfo['address']))
                        {{ $companyInfo['address'] }}<br>
                    @endif
                    @if(!empty($companyInfo['email']))
                        Email: {{ $companyInfo['email'] }}<br>
                    @endif
                    @if(!empty($companyInfo['phone']))
                        Phone: {{ $companyInfo['phone'] }}
                    @endif
                </div>
            </div>
            <div class="invoice-info">
                <div class="invoice-title">INVOICE</div>
                <div class="invoice-number"># {{ $invoice->invoice_number }}</div>
                <div class="invoice-status status-{{ $invoice->status }}">
                    {{ ucfirst($invoice->status) }}
                </div>
            </div>
        </div>

        <!-- Customer and Invoice Details -->
        <div class="details-section">
            <div class="bill-to">
                <div class="section-title">Bill To</div>
                <div class="customer-info">
                    <div class="customer-name">{{ $invoice->customer->name }}</div>
                    <div class="customer-details">
                        {{ $invoice->customer->email }}<br>
                        @if($invoice->customer->phone)
                            {{ $invoice->customer->phone }}<br>
                        @endif
                        @if($invoice->customer->full_address)
                            {{ $invoice->customer->full_address }}
                        @endif
                    </div>
                </div>
            </div>
            <div class="invoice-details">
                <div class="section-title">Invoice Details</div>
                <div class="detail-row">
                    <div class="detail-label">Issue Date:</div>
                    <div class="detail-value">{{ $invoice->issue_date->format('M d, Y') }}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Due Date:</div>
                    <div class="detail-value">{{ $invoice->due_date->format('M d, Y') }}</div>
                </div>
                @if($invoice->paid_date)
                <div class="detail-row">
                    <div class="detail-label">Paid Date:</div>
                    <div class="detail-value">{{ $invoice->paid_date->format('M d, Y') }}</div>
                </div>
                @endif
                @if($invoice->subscription)
                <div class="detail-row">
                    <div class="detail-label">Subscription:</div>
                    <div class="detail-value">{{ $invoice->subscription->name }}</div>
                </div>
                @endif
            </div>
        </div>

        <!-- Invoice Items -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th style="width: 80px;">Qty</th>
                    <th style="width: 100px;">Unit Price</th>
                    <th style="width: 100px;">Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach($invoice->items as $item)
                <tr>
                    <td class="item-description">{{ $item->description }}</td>
                    <td>{{ number_format($item->quantity, 2) }}</td>
                    <td>{{ $formatCurrency($item->unit_price) }}</td>
                    <td>{{ $formatCurrency($item->total) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <div class="totals-spacer"></div>
            <div class="totals-table">
                <div class="total-row">
                    <div class="total-label">Subtotal:</div>
                    <div class="total-value">{{ $formatCurrency($invoice->amount) }}</div>
                </div>
                @if($invoice->tax_amount > 0)
                <div class="total-row">
                    <div class="total-label">Tax:</div>
                    <div class="total-value">{{ $formatCurrency($invoice->tax_amount) }}</div>
                </div>
                @endif
                <div class="total-row final">
                    <div class="total-label">Total:</div>
                    <div class="total-value">{{ $formatCurrency($invoice->total_amount) }}</div>
                </div>
            </div>
        </div>

        <!-- Payment Summary -->
        <div class="payment-summary">
            <div class="payment-title">Payment Summary</div>
            <div class="payment-grid">
                <div class="payment-item">
                    <div class="payment-amount total">{{ $formatCurrency($invoice->total_amount) }}</div>
                    <div class="payment-label">Total Amount</div>
                </div>
                <div class="payment-item">
                    <div class="payment-amount paid">{{ $formatCurrency($invoice->total_paid) }}</div>
                    <div class="payment-label">Amount Paid</div>
                </div>
                <div class="payment-item">
                    <div class="payment-amount remaining">{{ $formatCurrency($invoice->remaining_balance) }}</div>
                    <div class="payment-label">Balance Due</div>
                </div>
            </div>
        </div>

        <!-- Notes -->
        @if($invoice->notes)
        <div class="notes">
            <div class="notes-title">Notes</div>
            <div class="notes-content">{{ $invoice->notes }}</div>
        </div>
        @endif

        <!-- Footer -->
        <div class="footer">
            <p>Generated on {{ now()->format('M d, Y \a\t g:i A') }}</p>
            <p>{{ $companyInfo['name'] }} - Invoice {{ $invoice->invoice_number }}</p>
        </div>
    </div>
</body>
</html>
