import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import {
    BookOpen,
    LayoutGrid,
    Users,
    CreditCard,
    FileText,
    Server,
    Activity,
    Wifi,
    Folder,
    DollarSign,
    Settings
} from 'lucide-react';
import AppLogo from './app-logo';

const getMainNavItems = (permissions: string[]): NavItem[] => {
    const hasPermission = (permission: string) => permissions.includes(permission);

    const items: NavItem[] = [];

    // Dashboard - always visible for authenticated users
    items.push({
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    });

    // Network - requires network permissions
    if (hasPermission('view network')) {
        items.push({
            title: 'Network',
            href: '/network',
            icon: Server,
        });
    }

    // Bandwidth - requires bandwidth permissions
    if (hasPermission('view bandwidth')) {
        items.push({
            title: 'Bandwidth',
            href: '/bandwidth',
            icon: Activity,
        });
    }

    // Customers - requires customer permissions
    if (hasPermission('view customers')) {
        items.push({
            title: 'Customers',
            href: '/customers',
            icon: Users,
        });
    }

    // Subscriptions - requires subscription permissions
    if (hasPermission('view subscriptions')) {
        items.push({
            title: 'Subscriptions',
            href: '/subscriptions',
            icon: CreditCard,
        });
    }

    // Services - requires service permissions
    if (hasPermission('view services')) {
        items.push({
            title: 'Services',
            href: '/services',
            icon: Wifi,
        });
    }

    // Invoices - requires invoice permissions
    if (hasPermission('view invoices')) {
        items.push({
            title: 'Invoices',
            href: '/invoices',
            icon: FileText,
        });
    }

    // Payments - requires payment permissions
    if (hasPermission('view payments')) {
        items.push({
            title: 'Payments',
            href: '/payments',
            icon: DollarSign,
        });
    }

    // Admin Settings - requires admin settings permissions
    if (hasPermission('view admin settings')) {
        items.push({
            title: 'Admin Settings',
            href: '/admin/settings',
            icon: Settings,
        });
    }

    return items;
};

const footerNavItems: NavItem[] = [
    {
        title: 'Repository',
        href: 'https://github.com/laravel/react-starter-kit',
        icon: Folder,
    },
    {
        title: 'Documentation',
        href: 'https://laravel.com/docs/starter-kits#react',
        icon: BookOpen,
    },
];

export function AppSidebar() {
    const { auth } = usePage().props as any;
    const permissions = auth.permissions || [];
    const mainNavItems = getMainNavItems(permissions);

    return (
        <Sidebar collapsible="icon" variant="inset" className="border-r border-sidebar-border/50">
            <SidebarHeader className="border-b border-sidebar-border/50 p-4">
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild className="hover:bg-sidebar-accent/50 transition-colors">
                            <Link  href="/dashboard" prefetch="hover" className="flex items-center gap-3 p-2">
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent className="px-2 py-4">
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter className="border-t border-sidebar-border/50 p-4">
                {/* <NavFooter items={footerNavItems} className="mb-2" /> */}
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
