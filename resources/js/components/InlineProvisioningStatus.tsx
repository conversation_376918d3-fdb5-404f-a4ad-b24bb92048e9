import React, { useEffect, useState } from 'react';
import { Check<PERSON>ircle, AlertCircle, Clock, Loader2, Router, Network, Zap, Settings, X } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';

interface ProvisioningData {
  service_id: number;
  session_id: string;
  step: string;
  message: string;
  current_step?: number;
  total_steps?: number;
  progress: number;
  success?: boolean;
  error_message?: string;
  timestamp: string;
  service?: {
    id: number;
    ip_address: string;
    status: string;
  };
}

interface InlineProvisioningStatusProps {
  sessionId?: string;
  serviceId: number;
  initialStatus?: 'provisioning' | 'active' | 'failed';
  onError?: (error: string) => void;
  onDismiss?: () => void;
}

const STEP_LABELS = {
  started: 'Initializing provisioning...',
  creating_route: 'Creating network route',
  creating_nat: 'Configuring NAT rules',
  creating_queue: 'Setting up bandwidth limits',
  finalizing: 'Finalizing configuration',
  completed: 'Provisioning completed successfully!',
  failed: 'Provisioning failed'
};

const STEP_ICONS = {
  started: Settings,
  creating_route: Router,
  creating_nat: Network,
  creating_queue: Zap,
  finalizing: Settings,
  completed: CheckCircle,
  failed: AlertCircle
};

export default function InlineProvisioningStatus({
  sessionId,
  serviceId,
  initialStatus = 'provisioning',
  onError,
  onDismiss
}: InlineProvisioningStatusProps) {
  const [progress, setProgress] = useState<ProvisioningData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [isVisible, setIsVisible] = useState(true);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // Auto-refresh after successful completion
  useEffect(() => {
    if (progress?.step === 'completed' && progress?.success) {
      setShowSuccessMessage(true);
      const timer = setTimeout(() => {
        // Perform hard refresh to the service detail page to show updated status
        const currentUrl = window.location.href;
        const baseUrl = currentUrl.split('?')[0]; // Remove any query parameters
        window.location.href = baseUrl;
      }, 3000); // Refresh after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [progress, serviceId]);

  useEffect(() => {
    // Only connect if we have a session ID and the component should be visible
    if (!sessionId || !isVisible || !window.Echo) return;

    setIsConnected(true);
    setError(null);

    // Listen to the private channel for this session
    const channel = window.Echo.private(`static-ip-provisioning.${sessionId}`);

    // Listen for started event
    channel.listen('.provisioning.started', (data: ProvisioningData) => {
      console.log('Provisioning started:', data);
      setProgress(data);
      setCompletedSteps(new Set());
    });

    // Listen for progress events
    channel.listen('.provisioning.progress', (data: ProvisioningData) => {
      console.log('Provisioning progress:', data);
      setProgress(data);
      setCompletedSteps(prev => new Set([...prev, data.step]));
    });

    // Listen for completion events
    channel.listen('.provisioning.completed', (data: ProvisioningData) => {
      console.log('Provisioning completed:', data);
      setProgress(data);
      setCompletedSteps(prev => new Set([...prev, 'completed']));
    });

    // Listen for failure events
    channel.listen('.provisioning.failed', (data: ProvisioningData) => {
      console.log('Provisioning failed:', data);
      setProgress(data);
      setError(data.error_message || 'Provisioning failed');

      if (onError) {
        onError(data.error_message || 'Provisioning failed');
      }
    });

    // Handle connection events
    channel.subscribed(() => {
      console.log('Subscribed to provisioning channel');
      setIsConnected(true);
    });

    channel.error((error: any) => {
      console.error('Channel error:', error);
      setIsConnected(false);
      setError('Connection error occurred');
    });

    return () => {
      console.log('Leaving provisioning channel');
      window.Echo.leave(`static-ip-provisioning.${sessionId}`);
    };
  }, [sessionId, isVisible, onError]);

  const getStepStatus = (stepKey: string) => {
    if (error && stepKey !== 'failed') return 'pending';
    if (stepKey === 'failed' && error) return 'error';
    if (completedSteps.has(stepKey)) return 'completed';
    if (progress?.step === stepKey) return 'in_progress';
    return 'pending';
  };

  const getStepIcon = (stepKey: string) => {
    const status = getStepStatus(stepKey);
    const IconComponent = STEP_ICONS[stepKey as keyof typeof STEP_ICONS] || Settings;

    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <IconComponent className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStepTextColor = (stepKey: string) => {
    const status = getStepStatus(stepKey);
    switch (status) {
      case 'completed':
        return 'text-green-700';
      case 'in_progress':
        return 'text-blue-700 font-medium';
      case 'error':
        return 'text-red-700';
      default:
        return 'text-gray-500';
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss) {
      onDismiss();
    }
  };

  // Don't render if not visible or no session ID for active provisioning
  if (!isVisible || (initialStatus === 'provisioning' && !sessionId)) {
    return null;
  }

  // Show error state for failed services
  if (initialStatus === 'failed' && !sessionId) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50/50 p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-600" />
            <h3 className="text-sm font-medium text-red-800">Provisioning Failed</h3>
          </div>
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={handleDismiss}>
            <X className="w-3 h-3" />
          </Button>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-md p-2">
          <p className="text-xs text-red-800">
            Service provisioning failed. Please check the configuration and try again, or contact support.
          </p>
        </div>
      </div>
    );
  }

  const steps = ['started', 'creating_route', 'creating_nat', 'creating_queue', 'finalizing'];
  const currentProgress = progress?.progress || 0;
  const isCompleted = progress?.step === 'completed' && progress?.success;
  const isFailed = progress?.step === 'failed' || error;

  return (
    <div className={`rounded-lg border p-4 transition-all duration-300 ${
      isCompleted ? 'border-green-200 bg-green-50/50' :
      isFailed ? 'border-red-200 bg-red-50/50' :
      'border-blue-200 bg-blue-50/50 shadow-sm'
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          {isCompleted ? (
            <CheckCircle className="w-4 h-4 text-green-600" />
          ) : isFailed ? (
            <AlertCircle className="w-4 h-4 text-red-600" />
          ) : (
            <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
          )}
          <h3 className={`text-sm font-medium ${isCompleted ? 'text-green-800' : isFailed ? 'text-red-800' : 'text-blue-800'}`}>
            {isCompleted ? 'Provisioning Completed' : isFailed ? 'Provisioning Failed' : 'Provisioning Service'}
          </h3>
          <div className={`w-1.5 h-1.5 rounded-full ml-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
        </div>
        {(isCompleted || isFailed) && (
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={handleDismiss}>
            <X className="w-3 h-3" />
          </Button>
        )}
      </div>

      {/* Progress Bar */}
      <div className="mb-3">
        <div className="flex justify-between items-center mb-1">
          <span className="text-xs text-gray-600">
            {progress?.message || 'Initializing...'}
          </span>
          <span className="text-xs font-medium text-gray-700">{Math.round(currentProgress)}%</span>
        </div>
        <Progress value={currentProgress} className="h-1.5" />
      </div>

      {/* Steps - Compact horizontal layout */}
      <div className="flex items-center gap-2 mb-3">
        {steps.map((stepKey, index) => {
          const status = getStepStatus(stepKey);
          return (
            <div key={stepKey} className="flex items-center gap-1">
              <div
                className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium transition-all duration-300 transform cursor-help ${
                  status === 'completed' ? 'bg-green-100 text-green-700 border border-green-200 scale-110' :
                  status === 'in_progress' ? 'bg-blue-100 text-blue-700 border border-blue-200 scale-110 shadow-sm' :
                  status === 'error' ? 'bg-red-100 text-red-700 border border-red-200' :
                  'bg-gray-100 text-gray-500 border border-gray-200 scale-95'
                }`}
                title={STEP_LABELS[stepKey as keyof typeof STEP_LABELS]}
              >
                {status === 'completed' ? (
                  <CheckCircle className="w-3 h-3" />
                ) : status === 'in_progress' ? (
                  <Loader2 className="w-3 h-3 animate-spin" />
                ) : status === 'error' ? (
                  <AlertCircle className="w-3 h-3" />
                ) : (
                  <span>{index + 1}</span>
                )}
              </div>
              {index < steps.length - 1 && (
                <div className={`w-6 h-0.5 transition-all duration-500 ${
                  status === 'completed' ? 'bg-green-300' : 'bg-gray-200'
                }`} />
              )}
            </div>
          );
        })}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-2 mb-3">
          <p className="text-xs text-red-800">{error}</p>
        </div>
      )}

      {/* Success Message */}
      {showSuccessMessage && isCompleted && (
        <div className="bg-green-50 border border-green-200 rounded-md p-2 mb-3">
          <p className="text-xs text-green-800">
            ✅ Service provisioned successfully! Refreshing page...
          </p>
        </div>
      )}

      {/* Step Details - Only show current step */}
      {progress?.step && !isCompleted && !isFailed && (
        <div className="text-xs text-gray-600">
          <span className="font-medium">Current:</span> {STEP_LABELS[progress.step as keyof typeof STEP_LABELS]}
        </div>
      )}
    </div>
  );
}
