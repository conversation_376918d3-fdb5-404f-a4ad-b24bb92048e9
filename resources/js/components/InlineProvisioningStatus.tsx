import React, { useEffect, useState } from 'react';
import { Check<PERSON>ircle, AlertCircle, Clock, Loader2, Router, Network, Zap, Settings, X } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

interface ProvisioningData {
  service_id: number;
  session_id: string;
  step: string;
  message: string;
  current_step?: number;
  total_steps?: number;
  progress: number;
  success?: boolean;
  error_message?: string;
  timestamp: string;
  service?: {
    id: number;
    ip_address: string;
    status: string;
  };
}

interface InlineProvisioningStatusProps {
  sessionId?: string;
  serviceId: number;
  initialStatus?: 'provisioning' | 'active' | 'failed';
  onComplete?: (service: any) => void;
  onError?: (error: string) => void;
  onDismiss?: () => void;
}

const STEP_LABELS = {
  started: 'Initializing provisioning...',
  creating_route: 'Creating network route',
  creating_nat: 'Configuring NAT rules',
  creating_queue: 'Setting up bandwidth limits',
  finalizing: 'Finalizing configuration',
  completed: 'Provisioning completed successfully!',
  failed: 'Provisioning failed'
};

const STEP_ICONS = {
  started: Settings,
  creating_route: Router,
  creating_nat: Network,
  creating_queue: Zap,
  finalizing: Settings,
  completed: CheckCircle,
  failed: AlertCircle
};

export default function InlineProvisioningStatus({
  sessionId,
  serviceId,
  initialStatus = 'provisioning',
  onComplete,
  onError,
  onDismiss
}: InlineProvisioningStatusProps) {
  const [progress, setProgress] = useState<ProvisioningData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [isVisible, setIsVisible] = useState(true);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // Auto-hide after successful completion
  useEffect(() => {
    if (progress?.step === 'completed' && progress?.success) {
      setShowSuccessMessage(true);
      const timer = setTimeout(() => {
        setIsVisible(false);
        if (onComplete && progress.service) {
          onComplete(progress.service);
        }
      }, 3000); // Hide after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [progress, onComplete]);

  useEffect(() => {
    // Only connect if we have a session ID and the component should be visible
    if (!sessionId || !isVisible || !window.Echo) return;

    setIsConnected(true);
    setError(null);

    // Listen to the private channel for this session
    const channel = window.Echo.private(`static-ip-provisioning.${sessionId}`);

    // Listen for started event
    channel.listen('.provisioning.started', (data: ProvisioningData) => {
      console.log('Provisioning started:', data);
      setProgress(data);
      setCompletedSteps(new Set());
    });

    // Listen for progress events
    channel.listen('.provisioning.progress', (data: ProvisioningData) => {
      console.log('Provisioning progress:', data);
      setProgress(data);
      setCompletedSteps(prev => new Set([...prev, data.step]));
    });

    // Listen for completion events
    channel.listen('.provisioning.completed', (data: ProvisioningData) => {
      console.log('Provisioning completed:', data);
      setProgress(data);
      setCompletedSteps(prev => new Set([...prev, 'completed']));
    });

    // Listen for failure events
    channel.listen('.provisioning.failed', (data: ProvisioningData) => {
      console.log('Provisioning failed:', data);
      setProgress(data);
      setError(data.error_message || 'Provisioning failed');
      
      if (onError) {
        onError(data.error_message || 'Provisioning failed');
      }
    });

    // Handle connection events
    channel.subscribed(() => {
      console.log('Subscribed to provisioning channel');
      setIsConnected(true);
    });

    channel.error((error: any) => {
      console.error('Channel error:', error);
      setIsConnected(false);
      setError('Connection error occurred');
    });

    return () => {
      console.log('Leaving provisioning channel');
      window.Echo.leave(`static-ip-provisioning.${sessionId}`);
    };
  }, [sessionId, isVisible, onError]);

  const getStepStatus = (stepKey: string) => {
    if (error && stepKey !== 'failed') return 'pending';
    if (stepKey === 'failed' && error) return 'error';
    if (completedSteps.has(stepKey)) return 'completed';
    if (progress?.step === stepKey) return 'in_progress';
    return 'pending';
  };

  const getStepIcon = (stepKey: string) => {
    const status = getStepStatus(stepKey);
    const IconComponent = STEP_ICONS[stepKey as keyof typeof STEP_ICONS] || Settings;
    
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <IconComponent className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStepTextColor = (stepKey: string) => {
    const status = getStepStatus(stepKey);
    switch (status) {
      case 'completed':
        return 'text-green-700';
      case 'in_progress':
        return 'text-blue-700 font-medium';
      case 'error':
        return 'text-red-700';
      default:
        return 'text-gray-500';
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss) {
      onDismiss();
    }
  };

  // Don't render if not visible or no session ID for active provisioning
  if (!isVisible || (initialStatus === 'provisioning' && !sessionId)) {
    return null;
  }

  // Show error state for failed services
  if (initialStatus === 'failed' && !sessionId) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-red-800 flex items-center gap-2">
              <AlertCircle className="w-5 h-5" />
              Provisioning Failed
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={handleDismiss}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertDescription>
              The service provisioning failed. Please check the service configuration and try again, or contact support if the issue persists.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const steps = ['started', 'creating_route', 'creating_nat', 'creating_queue', 'finalizing'];
  const currentProgress = progress?.progress || 0;
  const isCompleted = progress?.step === 'completed' && progress?.success;
  const isFailed = progress?.step === 'failed' || error;

  return (
    <Card className={`${isCompleted ? 'border-green-200 bg-green-50' : isFailed ? 'border-red-200 bg-red-50' : 'border-blue-200 bg-blue-50'}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className={`flex items-center gap-2 ${isCompleted ? 'text-green-800' : isFailed ? 'text-red-800' : 'text-blue-800'}`}>
            {isCompleted ? (
              <CheckCircle className="w-5 h-5" />
            ) : isFailed ? (
              <AlertCircle className="w-5 h-5" />
            ) : (
              <Loader2 className="w-5 h-5 animate-spin" />
            )}
            {isCompleted ? 'Provisioning Completed' : isFailed ? 'Provisioning Failed' : 'Provisioning in Progress'}
          </CardTitle>
          {(isCompleted || isFailed) && (
            <Button variant="ghost" size="sm" onClick={handleDismiss}>
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Connection Status */}
        <div className="flex items-center text-sm">
          <div className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-gray-600">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Progress</span>
            <span className="text-gray-600">{Math.round(currentProgress)}%</span>
          </div>
          <Progress value={currentProgress} className="h-2" />
        </div>

        {/* Current Status Message */}
        {progress?.message && (
          <div className="p-3 bg-white rounded-md border">
            <p className="text-sm font-medium text-gray-700">{progress.message}</p>
            {progress.timestamp && (
              <p className="text-xs text-gray-500 mt-1">
                {new Date(progress.timestamp).toLocaleTimeString()}
              </p>
            )}
          </div>
        )}

        {/* Error Message */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Success Message */}
        {showSuccessMessage && isCompleted && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Static IP service has been successfully provisioned and is now active!
            </AlertDescription>
          </Alert>
        )}

        {/* Steps List */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Provisioning Steps:</h4>
          {steps.map((stepKey) => (
            <div key={stepKey} className="flex items-center space-x-3">
              {getStepIcon(stepKey)}
              <span className={`text-sm ${getStepTextColor(stepKey)}`}>
                {STEP_LABELS[stepKey as keyof typeof STEP_LABELS]}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
