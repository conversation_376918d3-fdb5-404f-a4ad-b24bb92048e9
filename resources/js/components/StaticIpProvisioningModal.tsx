import React, { useEffect, useState } from 'react';
import { X, CheckCircle, AlertCircle, Clock, Loader2, Router, Network, Zap, Settings } from 'lucide-react';

interface ProvisioningData {
  service_id: number;
  session_id: string;
  step: string;
  message: string;
  current_step?: number;
  total_steps?: number;
  progress: number;
  success?: boolean;
  error_message?: string;
  timestamp: string;
  service?: {
    id: number;
    ip_address: string;
    status: string;
  };
}

interface StaticIpProvisioningModalProps {
  isOpen: boolean;
  sessionId: string;
  serviceId: number;
  onComplete?: (service: any) => void;
  onError?: (error: string) => void;
  onClose?: () => void;
}

const STEP_ICONS = {
  started: Clock,
  creating_route: Network,
  creating_nat: Router,
  creating_queue: Zap,
  finalizing: Settings,
  completed: CheckCircle,
  failed: AlertCircle,
};

const STEP_LABELS = {
  started: 'Initializing provisioning...',
  creating_route: 'Creating network route...',
  creating_nat: 'Configuring NAT rules...',
  creating_queue: 'Setting up bandwidth limits...',
  finalizing: 'Finalizing service configuration...',
  completed: 'Provisioning completed successfully!',
  failed: 'Provisioning failed',
};

export default function StaticIpProvisioningModal({
  isOpen,
  sessionId,
  serviceId,
  onComplete,
  onError,
  onClose
}: StaticIpProvisioningModalProps) {
  const [progress, setProgress] = useState<ProvisioningData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (!isOpen || !sessionId || !window.Echo) return;

    setIsConnected(true);
    setError(null);
    setProgress(null);
    setCompletedSteps(new Set());

    // Listen to the private channel for this session
    const channel = window.Echo.private(`static-ip-provisioning.${sessionId}`);

    // Listen for started event
    channel.listen('.provisioning.started', (data: ProvisioningData) => {
      console.log('Provisioning started:', data);
      setProgress(data);
      setCompletedSteps(new Set());
    });

    // Listen for progress events
    channel.listen('.provisioning.progress', (data: ProvisioningData) => {
      console.log('Provisioning progress:', data);
      setProgress(data);
      setCompletedSteps(prev => new Set([...prev, data.step]));
    });

    // Listen for completion events
    channel.listen('.provisioning.completed', (data: ProvisioningData) => {
      console.log('Provisioning completed:', data);
      setProgress(data);
      setCompletedSteps(prev => new Set([...prev, 'completed']));
      
      if (data.success && onComplete && data.service) {
        setTimeout(() => onComplete(data.service), 1500);
      }
    });

    // Listen for failure events
    channel.listen('.provisioning.failed', (data: ProvisioningData) => {
      console.log('Provisioning failed:', data);
      setProgress(data);
      setError(data.error_message || 'Provisioning failed');
      
      if (onError) {
        onError(data.error_message || 'Provisioning failed');
      }
    });

    // Handle connection events
    channel.subscribed(() => {
      console.log('Subscribed to provisioning channel');
      setIsConnected(true);
    });

    channel.error((error: any) => {
      console.error('Channel error:', error);
      setIsConnected(false);
      setError('Connection error occurred');
    });

    return () => {
      console.log('Leaving provisioning channel');
      window.Echo.leave(`static-ip-provisioning.${sessionId}`);
    };
  }, [isOpen, sessionId, onComplete, onError]);

  const getStepStatus = (stepKey: string) => {
    if (error && stepKey !== 'failed') return 'pending';
    if (stepKey === 'failed' && error) return 'error';
    if (completedSteps.has(stepKey)) return 'completed';
    if (progress?.step === stepKey) return 'in_progress';
    return 'pending';
  };

  const getStepIcon = (stepKey: string) => {
    const status = getStepStatus(stepKey);
    const IconComponent = STEP_ICONS[stepKey as keyof typeof STEP_ICONS] || Clock;
    
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <IconComponent className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStepTextColor = (stepKey: string) => {
    const status = getStepStatus(stepKey);
    switch (status) {
      case 'completed':
        return 'text-green-700';
      case 'in_progress':
        return 'text-blue-700 font-medium';
      case 'error':
        return 'text-red-700';
      default:
        return 'text-gray-500';
    }
  };

  if (!isOpen) return null;

  const steps = ['started', 'creating_route', 'creating_nat', 'creating_queue', 'finalizing'];
  const currentProgress = progress?.progress || 0;
  const isCompleted = progress?.step === 'completed' && progress?.success;
  const isFailed = progress?.step === 'failed' || error;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            Static IP Provisioning
          </h3>
          {(isCompleted || isFailed) && onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Connection Status */}
          <div className="flex items-center mb-4">
            <div className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-gray-600">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">
                Progress
              </span>
              <span className="text-sm text-gray-500">
                {Math.round(currentProgress)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  isFailed ? 'bg-red-500' : isCompleted ? 'bg-green-500' : 'bg-blue-500'
                }`}
                style={{ width: `${currentProgress}%` }}
              />
            </div>
          </div>

          {/* Current Message */}
          {progress && (
            <div className="mb-6">
              <p className={`text-sm ${
                isFailed ? 'text-red-600' : isCompleted ? 'text-green-600' : 'text-gray-700'
              }`}>
                {progress.message}
              </p>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Steps List */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Steps:</h4>
            {steps.map((stepKey) => (
              <div key={stepKey} className="flex items-center space-x-3">
                {getStepIcon(stepKey)}
                <span className={`text-sm ${getStepTextColor(stepKey)}`}>
                  {STEP_LABELS[stepKey as keyof typeof STEP_LABELS]}
                </span>
              </div>
            ))}
          </div>

          {/* Loading State */}
          {!progress && !error && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />
              <span className="ml-3 text-gray-600">Connecting...</span>
            </div>
          )}
        </div>

        {/* Footer */}
        {(isCompleted || isFailed) && (
          <div className="px-6 py-4 border-t bg-gray-50 rounded-b-lg">
            <div className="flex justify-end">
              {onClose && (
                <button
                  onClick={onClose}
                  className={`px-4 py-2 text-white rounded-md transition-colors ${
                    isCompleted 
                      ? 'bg-green-500 hover:bg-green-600' 
                      : 'bg-red-500 hover:bg-red-600'
                  }`}
                >
                  {isCompleted ? 'Continue' : 'Close'}
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
