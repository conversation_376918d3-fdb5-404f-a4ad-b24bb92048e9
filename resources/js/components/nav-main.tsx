import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';

export function NavMain({ items = [] }: { items: NavItem[] }) {
    const page = usePage();

    // Better active state detection - check if current URL starts with the nav item href
    const isActiveRoute = (href: string) => {
        const currentPath = page.url;
        if (href === '/dashboard') {
            return currentPath === '/dashboard';
        }
        return currentPath.startsWith(href);
    };

    return (
        <SidebarGroup className="px-2 py-0">
            <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider">
                Navigation
            </SidebarGroupLabel>
            <SidebarMenu className="space-y-1">
                {items.map((item) => (
                    <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton
                            asChild
                            isActive={isActiveRoute(item.href)}
                            tooltip={{ children: item.title }}
                            className="group relative transition-all duration-200 hover:bg-sidebar-accent data-[active=true]:bg-sidebar-primary data-[active=true]:text-sidebar-primary-foreground"
                        >
                            <Link  href={item.href} prefetch="hover" className="flex items-center gap-3 px-3 py-2 rounded-lg">
                                {item.icon && (
                                    <item.icon className="h-4 w-4 transition-colors group-hover:text-sidebar-primary group-data-[active=true]:text-sidebar-primary-foreground" />
                                )}
                                <span className="font-medium">{item.title}</span>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                ))}
            </SidebarMenu>
        </SidebarGroup>
    );
}
