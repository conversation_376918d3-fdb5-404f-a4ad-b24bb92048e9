import React, { useEffect, useState } from 'react';
import { X, CheckCircle, AlertCircle, Clock, Loader2 } from 'lucide-react';

interface ProgressStep {
  index: number;
  name: string;
  status: 'pending' | 'in_progress' | 'completed' | 'error';
}

interface ProgressData {
  session_id: string;
  current_step: number;
  total_steps: number;
  percentage: number;
  current_message: string;
  status: 'pending' | 'in_progress' | 'completed' | 'error';
  steps: ProgressStep[];
  data?: any;
  timestamp: string;
}

interface ProgressModalProps {
  isOpen: boolean;
  sessionId: string;
  title: string;
  onComplete?: (data: any) => void;
  onError?: (error: string) => void;
  onClose?: () => void;
}

export default function ProgressModal({
  isOpen,
  sessionId,
  title,
  onComplete,
  onError,
  onClose
}: ProgressModalProps) {
  const [progress, setProgress] = useState<ProgressData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!isOpen || !sessionId) return;

    let eventSource: EventSource | null = null;
    let retryCount = 0;
    const maxRetries = 3;

    const connectToStream = () => {
      try {
        eventSource = new EventSource(`/progress/${sessionId}/stream`);
        
        eventSource.onopen = () => {
          setIsConnected(true);
          setError(null);
          retryCount = 0;
        };

        eventSource.onmessage = (event) => {
          try {
            const data: ProgressData = JSON.parse(event.data);
            setProgress(data);

            // Handle completion
            if (data.status === 'completed') {
              setIsConnected(false);
              if (onComplete) {
                onComplete(data.data);
              }
            }

            // Handle errors
            if (data.status === 'error') {
              setIsConnected(false);
              const errorMessage = data.data?.error || data.current_message || 'An error occurred';
              setError(errorMessage);
              if (onError) {
                onError(errorMessage);
              }
            }
          } catch (err) {
            console.error('Error parsing progress data:', err);
          }
        };

        eventSource.addEventListener('close', () => {
          setIsConnected(false);
          eventSource?.close();
        });

        eventSource.addEventListener('timeout', () => {
          setIsConnected(false);
          setError('Progress tracking timed out');
          eventSource?.close();
        });

        eventSource.onerror = (event) => {
          setIsConnected(false);
          
          if (retryCount < maxRetries) {
            retryCount++;
            console.log(`Connection failed, retrying... (${retryCount}/${maxRetries})`);
            setTimeout(() => {
              eventSource?.close();
              connectToStream();
            }, 2000 * retryCount); // Exponential backoff
          } else {
            setError('Failed to connect to progress stream');
            eventSource?.close();
          }
        };

      } catch (err) {
        console.error('Error creating EventSource:', err);
        setError('Failed to initialize progress tracking');
      }
    };

    connectToStream();

    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  }, [isOpen, sessionId, onComplete, onError]);

  const getStepIcon = (step: ProgressStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStepTextColor = (step: ProgressStep) => {
    switch (step.status) {
      case 'completed':
        return 'text-green-700';
      case 'in_progress':
        return 'text-blue-700 font-medium';
      case 'error':
        return 'text-red-700';
      default:
        return 'text-gray-500';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          {(progress?.status === 'completed' || progress?.status === 'error') && onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Connection Status */}
          <div className="flex items-center mb-4">
            <div className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-gray-600">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>

          {/* Progress Bar */}
          {progress && (
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Progress
                </span>
                <span className="text-sm text-gray-500">
                  {progress.percentage}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    progress.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${progress.percentage}%` }}
                />
              </div>
            </div>
          )}

          {/* Current Message */}
          {progress && (
            <div className="mb-6">
              <p className={`text-sm ${
                progress.status === 'error' ? 'text-red-600' : 'text-gray-700'
              }`}>
                {progress.current_message}
              </p>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Steps List */}
          {progress && progress.steps && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Steps:</h4>
              {progress.steps.map((step) => (
                <div key={step.index} className="flex items-center space-x-3">
                  {getStepIcon(step)}
                  <span className={`text-sm ${getStepTextColor(step)}`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
          )}

          {/* Loading State */}
          {!progress && !error && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />
              <span className="ml-3 text-gray-600">Initializing...</span>
            </div>
          )}
        </div>

        {/* Footer */}
        {(progress?.status === 'completed' || progress?.status === 'error') && (
          <div className="px-6 py-4 border-t bg-gray-50 rounded-b-lg">
            <div className="flex justify-end">
              {onClose && (
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                >
                  {progress?.status === 'completed' ? 'Continue' : 'Close'}
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
