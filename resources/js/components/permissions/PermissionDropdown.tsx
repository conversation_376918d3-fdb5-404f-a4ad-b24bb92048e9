import React from 'react';
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
  type DropdownMenuItemProps
} from '@/components/ui/dropdown-menu';
import { PermissionGate, type PermissionProps } from './PermissionGate';

interface PermissionDropdownItemProps extends DropdownMenuItemProps, PermissionProps {
  children: React.ReactNode;
}

/**
 * DropdownMenuItem that only renders if user has the required permission(s)
 */
export function PermissionDropdownItem({
  permission,
  permissions,
  requireAll = false,
  fallback = null,
  children,
  ...dropdownProps
}: PermissionDropdownItemProps) {
  return (
    <PermissionGate
      permission={permission}
      permissions={permissions}
      requireAll={requireAll}
      fallback={fallback}
    >
      <DropdownMenuItem {...dropdownProps}>
        {children}
      </DropdownMenuItem>
    </PermissionGate>
  );
}

/**
 * Convenience components for common dropdown patterns
 */

interface ResourceDropdownItemProps extends Omit<DropdownMenuItemProps, 'children'> {
  resource: string;
  action: 'view' | 'create' | 'edit' | 'delete';
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Dropdown item for resource-specific actions
 */
export function ResourceDropdownItem({ 
  resource, 
  action, 
  children, 
  fallback = null, 
  ...dropdownProps 
}: ResourceDropdownItemProps) {
  return (
    <PermissionDropdownItem 
      permission={`${action} ${resource}`} 
      fallback={fallback}
      {...dropdownProps}
    >
      {children}
    </PermissionDropdownItem>
  );
}

/**
 * View dropdown item - only shows if user can view the resource
 */
export function ViewDropdownItem({ 
  resource, 
  children, 
  fallback, 
  ...dropdownProps 
}: Omit<ResourceDropdownItemProps, 'action'>) {
  return (
    <ResourceDropdownItem 
      resource={resource} 
      action="view" 
      fallback={fallback}
      {...dropdownProps}
    >
      {children}
    </ResourceDropdownItem>
  );
}

/**
 * Edit dropdown item - only shows if user can edit the resource
 */
export function EditDropdownItem({ 
  resource, 
  children, 
  fallback, 
  ...dropdownProps 
}: Omit<ResourceDropdownItemProps, 'action'>) {
  return (
    <ResourceDropdownItem 
      resource={resource} 
      action="edit" 
      fallback={fallback}
      {...dropdownProps}
    >
      {children}
    </ResourceDropdownItem>
  );
}

/**
 * Delete dropdown item - only shows if user can delete the resource
 */
export function DeleteDropdownItem({ 
  resource, 
  children, 
  fallback, 
  ...dropdownProps 
}: Omit<ResourceDropdownItemProps, 'action'>) {
  return (
    <ResourceDropdownItem 
      resource={resource} 
      action="delete" 
      fallback={fallback}
      className={`text-red-600 ${dropdownProps.className || ''}`}
      {...dropdownProps}
    >
      {children}
    </ResourceDropdownItem>
  );
}

/**
 * Conditional separator that only shows if any of the surrounding items are visible
 */
interface ConditionalSeparatorProps {
  permissions: string[];
  requireAll?: boolean;
}

export function ConditionalSeparator({ permissions, requireAll = false }: ConditionalSeparatorProps) {
  return (
    <PermissionGate permissions={permissions} requireAll={requireAll}>
      <DropdownMenuSeparator />
    </PermissionGate>
  );
}

/**
 * Pre-built action dropdown items for common CRUD operations
 */
interface CrudDropdownItemsProps {
  resource: string;
  resourceId: string | number;
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  viewHref?: string;
  editHref?: string;
  showSeparator?: boolean;
  customActions?: React.ReactNode;
}

export function CrudDropdownItems({
  resource,
  resourceId,
  onView,
  onEdit,
  onDelete,
  viewHref,
  editHref,
  showSeparator = true,
  customActions
}: CrudDropdownItemsProps) {
  return (
    <>
      <ViewDropdownItem 
        resource={resource}
        onClick={onView}
        asChild={!!viewHref}
      >
        {viewHref ? (
          <a href={viewHref}>View Details</a>
        ) : (
          'View Details'
        )}
      </ViewDropdownItem>

      <EditDropdownItem 
        resource={resource}
        onClick={onEdit}
        asChild={!!editHref}
      >
        {editHref ? (
          <a href={editHref}>Edit</a>
        ) : (
          'Edit'
        )}
      </EditDropdownItem>

      {customActions}

      {showSeparator && (
        <ConditionalSeparator 
          permissions={[`delete ${resource}`]} 
        />
      )}

      <DeleteDropdownItem 
        resource={resource}
        onClick={onDelete}
      >
        Delete
      </DeleteDropdownItem>
    </>
  );
}
