# Permission-Based UI Components

This directory contains reusable components and hooks for implementing permission-based UI controls throughout the ISP Management System.

## Overview

The permission system provides both **backend route protection** (via middleware) and **frontend UI controls** (via these components) to ensure users only see and can access features they have permission for.

## Components

### 1. `usePermissions` Hook

Custom hook for checking user permissions in React components.

```tsx
import { usePermissions, PERMISSIONS } from '@/components/permissions';

function MyComponent() {
  const { hasPermission, canAccess, hasAnyPermission } = usePermissions();
  
  // Check single permission
  const canCreateCustomers = hasPermission('create customers');
  
  // Check resource action (convenience method)
  const canEditInvoices = canAccess('invoices', 'edit');
  
  // Check multiple permissions (any)
  const canAccessReports = hasAnyPermission(['view reports', 'export reports']);
  
  // Use predefined permission constants
  const canViewCustomers = hasPermission(PERMISSIONS.CUSTOMERS.VIEW);
}
```

### 2. `PermissionGate` Component

Conditionally renders content based on permissions.

```tsx
import { PermissionGate, CanCreate, CanEdit } from '@/components/permissions';

function MyComponent() {
  return (
    <div>
      {/* Single permission */}
      <PermissionGate permission="create customers">
        <CreateCustomerButton />
      </PermissionGate>
      
      {/* Multiple permissions (any) */}
      <PermissionGate permissions={['view reports', 'export reports']}>
        <ReportsSection />
      </PermissionGate>
      
      {/* Multiple permissions (all required) */}
      <PermissionGate permissions={['edit users', 'view roles']} requireAll>
        <AdvancedUserManagement />
      </PermissionGate>
      
      {/* Convenience components */}
      <CanCreate resource="customers">
        <CreateCustomerButton />
      </CanCreate>
      
      <CanEdit resource="invoices">
        <EditInvoiceButton />
      </CanEdit>
    </div>
  );
}
```

### 3. `PermissionButton` Components

Permission-aware button components.

```tsx
import { CreateButton, EditButton, DeleteButton, PermissionButton } from '@/components/permissions';

function CustomerActions({ customer }) {
  return (
    <div className="flex gap-2">
      {/* Resource-specific buttons */}
      <CreateButton resource="customers" asChild>
        <Link href="/customers/create">Add Customer</Link>
      </CreateButton>
      
      <EditButton resource="customers" asChild>
        <Link href={`/customers/${customer.id}/edit`}>Edit</Link>
      </EditButton>
      
      <DeleteButton resource="customers" onClick={() => handleDelete(customer.id)}>
        Delete
      </DeleteButton>
      
      {/* Custom permission button */}
      <PermissionButton permission="process payments" variant="outline">
        Process Payment
      </PermissionButton>
    </div>
  );
}
```

### 4. `PermissionDropdownItem` Components

Permission-aware dropdown menu items.

```tsx
import { 
  ViewDropdownItem, 
  EditDropdownItem, 
  DeleteDropdownItem,
  PermissionDropdownItem 
} from '@/components/permissions';

function CustomerDropdown({ customer }) {
  return (
    <DropdownMenuContent>
      <ViewDropdownItem resource="customers" asChild>
        <Link href={`/customers/${customer.id}`}>View Details</Link>
      </ViewDropdownItem>
      
      <EditDropdownItem resource="customers" asChild>
        <Link href={`/customers/${customer.id}/edit`}>Edit</Link>
      </EditDropdownItem>
      
      <PermissionDropdownItem permission="view subscriptions" asChild>
        <Link href={`/customers/${customer.id}/subscriptions`}>Subscriptions</Link>
      </PermissionDropdownItem>
      
      <DeleteDropdownItem resource="customers" onClick={() => handleDelete(customer.id)}>
        Delete
      </DeleteDropdownItem>
    </DropdownMenuContent>
  );
}
```

## Migration Guide

### Before (Manual Permission Checks)

```tsx
// Old way - manual permission checking
import { usePage } from '@inertiajs/react';

function CustomerIndex() {
  const { auth } = usePage().props as any;
  const permissions = auth.permissions || [];
  const hasPermission = (permission: string) => permissions.includes(permission);
  
  return (
    <div>
      {hasPermission('create customers') && (
        <Button asChild>
          <Link href="/customers/create">Add Customer</Link>
        </Button>
      )}
      
      <DropdownMenuContent>
        {hasPermission('view customers') && (
          <DropdownMenuItem asChild>
            <Link href={`/customers/${customer.id}`}>View</Link>
          </DropdownMenuItem>
        )}
        {hasPermission('edit customers') && (
          <DropdownMenuItem asChild>
            <Link href={`/customers/${customer.id}/edit`}>Edit</Link>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </div>
  );
}
```

### After (Using Permission Components)

```tsx
// New way - using permission components
import { CreateButton, ViewDropdownItem, EditDropdownItem } from '@/components/permissions';

function CustomerIndex() {
  return (
    <div>
      <CreateButton resource="customers" asChild>
        <Link href="/customers/create">Add Customer</Link>
      </CreateButton>
      
      <DropdownMenuContent>
        <ViewDropdownItem resource="customers" asChild>
          <Link href={`/customers/${customer.id}`}>View</Link>
        </ViewDropdownItem>
        <EditDropdownItem resource="customers" asChild>
          <Link href={`/customers/${customer.id}/edit`}>Edit</Link>
        </EditDropdownItem>
      </DropdownMenuContent>
    </div>
  );
}
```

## Permission Constants

Use the predefined permission constants for type safety:

```tsx
import { PERMISSIONS } from '@/components/permissions';

// Instead of: hasPermission('create customers')
// Use: hasPermission(PERMISSIONS.CUSTOMERS.CREATE)

const permissions = {
  CUSTOMERS: {
    VIEW: 'view customers',
    CREATE: 'create customers',
    EDIT: 'edit customers',
    DELETE: 'delete customers'
  },
  SUBSCRIPTIONS: {
    VIEW: 'view subscriptions',
    CREATE: 'create subscriptions',
    EDIT: 'edit subscriptions',
    DELETE: 'delete subscriptions'
  },
  // ... more permissions
};
```

## Best Practices

1. **Use Resource-Specific Components**: Prefer `CreateButton resource="customers"` over `PermissionButton permission="create customers"`

2. **Consistent Naming**: Follow the pattern `{action} {resource}` for permission names

3. **Fallback Content**: Provide fallback content when appropriate:
   ```tsx
   <PermissionGate permission="view reports" fallback={<div>Access Denied</div>}>
     <ReportsComponent />
   </PermissionGate>
   ```

4. **Combine with Backend Protection**: Always ensure routes are protected with middleware - UI controls are for UX, not security

5. **Use Constants**: Use `PERMISSIONS` constants instead of string literals for better maintainability

## Security Note

These components provide **UI-level permission controls** for better user experience. They should always be combined with **backend route middleware protection** for actual security. Users should never see actions they can't perform, but the backend must still validate permissions on every request.
