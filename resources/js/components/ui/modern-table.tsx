import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

interface ModernTableProps {
    children: ReactNode;
    className?: string;
}

interface ModernTableHeaderProps {
    children: ReactNode;
    className?: string;
}

interface ModernTableBodyProps {
    children: ReactNode;
    className?: string;
}

interface ModernTableRowProps {
    children: ReactNode;
    className?: string;
    clickable?: boolean;
    onClick?: () => void;
}

interface ModernTableCellProps {
    children: ReactNode;
    className?: string;
    header?: boolean;
    colSpan?: number;
}

export function ModernTable({ children, className }: ModernTableProps) {
    return (
        <div className={cn('table-modern', className)}>
            <Table>{children}</Table>
        </div>
    );
}

export function ModernTableHeader({ children, className }: ModernTableHeaderProps) {
    return <TableHeader className={className}>{children}</TableHeader>;
}

export function ModernTableBody({ children, className }: ModernTableBodyProps) {
    return <TableBody className={className}>{children}</TableBody>;
}

export function ModernTableRow({ children, className, clickable = false, onClick }: ModernTableRowProps) {
    return (
        <TableRow
            className={cn(
                clickable && 'cursor-pointer hover:bg-muted/50 transition-colors',
                className
            )}
            onClick={onClick}
        >
            {children}
        </TableRow>
    );
}

export function ModernTableCell({ children, className, header = false, colSpan }: ModernTableCellProps) {
    if (header) {
        return <TableHead className={className} colSpan={colSpan}>{children}</TableHead>;
    }
    return <TableCell className={className} colSpan={colSpan}>{children}</TableCell>;
}
