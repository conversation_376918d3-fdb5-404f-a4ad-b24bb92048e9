import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

interface StatsCardProps {
    title: string;
    value: string | number;
    change?: {
        value: string;
        type: 'positive' | 'negative' | 'neutral';
    };
    icon?: LucideIcon;
    className?: string;
    gradient?: boolean;
}

export function StatsCard({ title, value, change, icon: Icon, className, gradient = false }: StatsCardProps) {
    return (
        <Card className={cn('stats-card', gradient && 'gradient-primary text-primary-foreground', className)}>
            <CardContent className="p-6">
                <div className="flex items-center justify-between">
                    <div className="space-y-2">
                        <p className={cn('stats-card-label', gradient && 'text-primary-foreground/80')}>{title}</p>
                        <p className={cn('stats-card-value', gradient && 'text-primary-foreground')}>{value}</p>
                        {change && (
                            <p
                                className={cn(
                                    'stats-card-change',
                                    !gradient && change.type === 'positive' && 'positive',
                                    !gradient && change.type === 'negative' && 'negative',
                                    gradient && 'text-primary-foreground/90'
                                )}
                            >
                                {change.value}
                            </p>
                        )}
                    </div>
                    {Icon && (
                        <div className={cn('p-3 rounded-xl', gradient ? 'bg-white/20' : 'bg-muted')}>
                            <Icon className={cn('h-6 w-6', gradient ? 'text-primary-foreground' : 'text-muted-foreground')} />
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
