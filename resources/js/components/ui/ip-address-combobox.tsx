import * as React from "react"
import { Check, ChevronsUpDown, RefreshCcw } from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface IpAddressComboboxProps {
  value?: string
  onValueChange?: (value: string) => void
  availableIps?: string[]
  nextRecommended?: string
  totalAvailable?: number
  loading?: boolean
  disabled?: boolean
  onRefresh?: () => void
  placeholder?: string
  className?: string
}

export function IpAddressCombobox({
  value,
  onValueChange,
  availableIps = [],
  nextRecommended,
  totalAvailable,
  loading = false,
  disabled = false,
  onRefresh,
  placeholder = "Select an IP address",
  className,
}: IpAddressComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")

  // Filter IPs based on search input
  const filteredIps = React.useMemo(() => {
    if (!searchValue) return availableIps
    return availableIps.filter(ip => 
      ip.toLowerCase().includes(searchValue.toLowerCase())
    )
  }, [availableIps, searchValue])

  // Get display text for the trigger button
  const getDisplayText = () => {
    if (loading) return "Loading available IPs..."
    if (availableIps.length === 0) return "No available IPs in this pool"
    if (value) return value
    return placeholder
  }

  // Get placeholder text for search input
  const getSearchPlaceholder = () => {
    if (availableIps.length === 0) return "No IPs available"
    if (availableIps.length > 20) return "Search IP addresses..."
    return "Type to filter IPs..."
  }

  return (
    <div className={cn("flex", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="flex-1 justify-between font-mono"
            disabled={disabled || loading || availableIps.length === 0}
          >
            <span className={cn(
              "truncate",
              !value && "text-muted-foreground"
            )}>
              {getDisplayText()}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0" align="start">
          <Command shouldFilter={false}>
            <CommandInput
              placeholder={getSearchPlaceholder()}
              value={searchValue}
              onValueChange={setSearchValue}
              className="h-9"
            />
            <CommandList>
              <CommandEmpty>
                {searchValue 
                  ? `No IP addresses found matching "${searchValue}"`
                  : "No IP addresses available"
                }
              </CommandEmpty>
              {filteredIps.length > 0 && (
                <CommandGroup>
                  {/* Show total count if there are many IPs */}
                  {availableIps.length > 10 && (
                    <div className="px-2 py-1.5 text-xs text-muted-foreground border-b">
                      {filteredIps.length} of {availableIps.length} IP addresses
                      {searchValue && ` matching "${searchValue}"`}
                    </div>
                  )}
                  
                  {filteredIps.map((ip) => (
                    <CommandItem
                      key={ip}
                      value={ip}
                      onSelect={(currentValue) => {
                        onValueChange?.(currentValue === value ? "" : currentValue)
                        setOpen(false)
                        setSearchValue("")
                      }}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center">
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            value === ip ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <span className="font-mono">{ip}</span>
                      </div>
                      
                      {/* Show recommended badge */}
                      {ip === nextRecommended && (
                        <span className="ml-2 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 px-2 py-1 rounded">
                          Recommended
                        </span>
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      
      {/* Refresh button */}
      {onRefresh && (
        <Button
          type="button"
          variant="outline"
          className="ml-2"
          onClick={onRefresh}
          disabled={loading || disabled}
          title="Refresh available IPs"
        >
          <RefreshCcw className={cn("h-4 w-4", loading && "animate-spin")} />
        </Button>
      )}
    </div>
  )
}
