interface CurrencyInfo {
  name: string;
  symbol: string;
  code: string;
  decimal_places: number;
  thousands_separator: string;
  decimal_separator: string;
  symbol_position: 'before' | 'after';
}

interface CurrencyConfig {
  default_currency: string;
  currency_info: CurrencyInfo;
  display_format: string;
  decimal_places: number;
  supported_currencies: Array<{
    code: string;
    name: string;
    symbol: string;
    display: string;
  }>;
}

// Supported currencies configuration
const SUPPORTED_CURRENCIES: Record<string, CurrencyInfo> = {
  USD: {
    name: 'US Dollar',
    symbol: '$',
    code: 'USD',
    decimal_places: 2,
    thousands_separator: ',',
    decimal_separator: '.',
    symbol_position: 'before',
  },
  KES: {
    name: 'Kenyan Shilling',
    symbol: 'KSh',
    code: 'KES',
    decimal_places: 2,
    thousands_separator: ',',
    decimal_separator: '.',
    symbol_position: 'before',
  },
  ETB: {
    name: 'Ethiopian Birr',
    symbol: 'Br',
    code: 'ETB',
    decimal_places: 2,
    thousands_separator: ',',
    decimal_separator: '.',
    symbol_position: 'after',
  },
  UGX: {
    name: 'Ugandan Shilling',
    symbol: 'USh',
    code: 'UGX',
    decimal_places: 0,
    thousands_separator: ',',
    decimal_separator: '.',
    symbol_position: 'before',
  },
};

// Global currency configuration (will be set from backend)
let currencyConfig: CurrencyConfig = {
  default_currency: 'KES',
  currency_info: SUPPORTED_CURRENCIES.KES,
  display_format: 'symbol_before',
  decimal_places: 2,
  supported_currencies: Object.entries(SUPPORTED_CURRENCIES).map(([code, info]) => ({
    code,
    name: info.name,
    symbol: info.symbol,
    display: `${info.name} (${info.symbol})`,
  })),
};

/**
 * Initialize currency configuration from backend
 */
export function initializeCurrency(config: CurrencyConfig): void {
  currencyConfig = config;
}

/**
 * Get currency information
 */
export function getCurrencyInfo(currency?: string): CurrencyInfo {
  const currencyCode = currency || currencyConfig.default_currency;
  return SUPPORTED_CURRENCIES[currencyCode] || SUPPORTED_CURRENCIES.KES;
}

/**
 * Format amount with currency
 */
export function formatCurrency(
  amount: number,
  currency?: string,
  options: {
    decimal_places?: number;
    display_format?: string;
    thousands_separator?: string;
    decimal_separator?: string;
  } = {}
): string {
  const currencyCode = currency || currencyConfig.default_currency;
  const currencyInfo = getCurrencyInfo(currencyCode);
  
  // Override with options or use defaults
  const decimalPlaces = options.decimal_places ?? currencyConfig.decimal_places ?? currencyInfo.decimal_places;
  const displayFormat = options.display_format ?? currencyConfig.display_format;
  const thousandsSeparator = options.thousands_separator ?? currencyInfo.thousands_separator;
  const decimalSeparator = options.decimal_separator ?? currencyInfo.decimal_separator;

  // Format the number
  const formattedAmount = formatNumber(amount, decimalPlaces, thousandsSeparator, decimalSeparator);

  // Apply display format
  switch (displayFormat) {
    case 'symbol_before':
      return `${currencyInfo.symbol} ${formattedAmount}`;
    case 'symbol_after':
      return `${formattedAmount} ${currencyInfo.symbol}`;
    case 'code_before':
      return `${currencyInfo.code} ${formattedAmount}`;
    case 'code_after':
      return `${formattedAmount} ${currencyInfo.code}`;
    default:
      return `${currencyInfo.symbol} ${formattedAmount}`;
  }
}

/**
 * Format amount in compact format (for tables/lists)
 */
export function formatCurrencyCompact(amount: number, currency?: string): string {
  const currencyCode = currency || currencyConfig.default_currency;
  const currencyInfo = getCurrencyInfo(currencyCode);
  
  const formattedAmount = formatNumber(amount, currencyInfo.decimal_places, ',', '.');
  return `${currencyInfo.symbol}${formattedAmount}`;
}

/**
 * Get currency symbol
 */
export function getCurrencySymbol(currency?: string): string {
  const currencyInfo = getCurrencyInfo(currency);
  return currencyInfo.symbol;
}

/**
 * Get currency code
 */
export function getCurrencyCode(currency?: string): string {
  return currency || currencyConfig.default_currency;
}

/**
 * Get all supported currencies
 */
export function getSupportedCurrencies() {
  return currencyConfig.supported_currencies;
}

/**
 * Get default currency
 */
export function getDefaultCurrency(): string {
  return currencyConfig.default_currency;
}

/**
 * Validate currency code
 */
export function isValidCurrency(currency: string): boolean {
  return currency in SUPPORTED_CURRENCIES;
}

/**
 * Format number with separators
 */
function formatNumber(
  amount: number,
  decimalPlaces: number,
  thousandsSeparator: string,
  decimalSeparator: string
): string {
  const fixed = amount.toFixed(decimalPlaces);
  const parts = fixed.split('.');
  
  // Add thousands separators
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
  
  // Join with decimal separator
  return parts.join(decimalSeparator);
}

/**
 * Parse currency string to number
 */
export function parseCurrency(currencyString: string): number {
  // Remove all non-numeric characters except decimal point and minus sign
  const cleaned = currencyString.replace(/[^\d.-]/g, '');
  return parseFloat(cleaned) || 0;
}

/**
 * Currency input formatter for forms
 */
export function formatCurrencyInput(value: string, currency?: string): string {
  const numericValue = parseCurrency(value);
  if (isNaN(numericValue)) return '';
  
  return formatCurrency(numericValue, currency);
}

/**
 * Get currency configuration for forms/components
 */
export function getCurrencyConfig() {
  return currencyConfig;
}
