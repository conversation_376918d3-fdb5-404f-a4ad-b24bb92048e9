import AppLogoIcon from '@/components/app-logo-icon';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Link } from '@inertiajs/react';
import { ArrowLeft } from 'lucide-react';
import { type PropsWithChildren } from 'react';

interface AuthLayoutProps {
    name?: string;
    title?: string;
    description?: string;
}

export default function AuthSimpleLayout({ children, title, description }: PropsWithChildren<AuthLayoutProps>) {
    return (
        <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/30 flex items-center justify-center p-4">
            {/* Background decoration */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl"></div>
            </div>

            <div className="relative w-full max-w-md">
                {/* Back to home link */}
                <div className="mb-8">
                    <Link
                        href={route('home')}
                        className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Back to home
                    </Link>
                </div>

                {/* Main auth card */}
                <Card className="card-modern-hover">
                    <CardHeader className="text-center space-y-6 pb-6">
                        {/* Logo and branding */}
                        <Link href={route('home')} className="inline-flex flex-col items-center gap-3">
                            <div className="p-3 bg-primary/10 rounded-xl">
                                <AppLogoIcon className="size-8 fill-current text-primary" />
                            </div>
                            <div className="space-y-1">
                                <div className="text-xl font-semibold text-foreground">ISP Manager</div>
                                <div className="text-sm text-muted-foreground">Complete ISP Management Platform</div>
                            </div>
                        </Link>

                        {/* Page title and description */}
                        <div className="space-y-2">
                            <h1 className="text-2xl font-bold text-foreground">{title}</h1>
                            <p className="text-muted-foreground">{description}</p>
                        </div>
                    </CardHeader>

                    <CardContent className="space-y-6">
                        {children}
                    </CardContent>
                </Card>

                {/* Footer */}
                <div className="mt-8 text-center">
                    <p className="text-sm text-muted-foreground">
                        © 2024 ISP Manager. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    );
}
