import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
    ArrowLeft, 
    CreditCard, 
    Settings, 
    Eye, 
    RefreshCw,
    Users,
    AlertCircle,
    CheckCircle,
    Info
} from 'lucide-react';

interface Settings {
    mpesa_id_assignment_mode: string;
    mpesa_id_generation_method: string;
    mpesa_id_prefix: string;
    mpesa_id_suffix: string;
    mpesa_id_starting_number: number;
    mpesa_id_padding: number;
    mpesa_id_custom_format: string;
    mpesa_id_min_length: number;
    mpesa_id_max_length: number;
    mpesa_id_allowed_pattern: string;
    mpesa_id_auto_generate_on_create: boolean;
    mpesa_id_required_for_payments: boolean;
}

interface Props {
    settings: Settings;
    generationMethods: Record<string, string>;
    assignmentModes: Record<string, string>;
}

export default function MpesaIdSettings({ settings, generationMethods, assignmentModes }: Props) {
    const [previewId, setPreviewId] = useState<string>('');
    const [isGenerating, setIsGenerating] = useState(false);
    const [isBulkGenerating, setIsBulkGenerating] = useState(false);

    const { data, setData, put, post, processing, errors, reset } = useForm({
        mpesa_id_assignment_mode: settings.mpesa_id_assignment_mode,
        mpesa_id_generation_method: settings.mpesa_id_generation_method,
        mpesa_id_prefix: settings.mpesa_id_prefix,
        mpesa_id_suffix: settings.mpesa_id_suffix,
        mpesa_id_starting_number: settings.mpesa_id_starting_number,
        mpesa_id_padding: settings.mpesa_id_padding,
        mpesa_id_custom_format: settings.mpesa_id_custom_format,
        mpesa_id_min_length: settings.mpesa_id_min_length,
        mpesa_id_max_length: settings.mpesa_id_max_length,
        mpesa_id_allowed_pattern: settings.mpesa_id_allowed_pattern,
        mpesa_id_auto_generate_on_create: settings.mpesa_id_auto_generate_on_create,
        mpesa_id_required_for_payments: settings.mpesa_id_required_for_payments,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.settings.mpesa-id.update'));
    };

    const handlePreview = async () => {
        setIsGenerating(true);
        try {
            const response = await fetch(route('admin.settings.mpesa-id.preview'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify(data),
            });
            
            const result = await response.json();
            if (result.success) {
                setPreviewId(result.preview_id);
            }
        } catch (error) {
            console.error('Preview failed:', error);
        } finally {
            setIsGenerating(false);
        }
    };

    const handleBulkGenerate = () => {
        setIsBulkGenerating(true);
        post(route('admin.settings.mpesa-id.bulk-generate'), {}, {
            onFinish: () => setIsBulkGenerating(false),
        });
    };

    return (
        <AppLayout>
            <Head title="M-Pesa ID Settings" />
            
            <div className="page-container">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Button variant="ghost" size="sm" asChild>
                            <Link href={route('admin.settings.index')}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Settings
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                                <CreditCard className="h-8 w-8 mr-3 text-green-600" />
                                M-Pesa ID Settings
                            </h1>
                            <p className="text-gray-600 mt-2">
                                Configure how customer M-Pesa IDs are generated and managed
                            </p>
                        </div>
                    </div>
                </div>

                {/* Settings Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Main Settings */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Assignment Mode */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Assignment Mode</CardTitle>
                                    <CardDescription>
                                        Choose how M-Pesa IDs are assigned to customers
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="assignment_mode">Assignment Mode</Label>
                                        <Select 
                                            value={data.mpesa_id_assignment_mode} 
                                            onValueChange={(value) => setData('mpesa_id_assignment_mode', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(assignmentModes).map(([key, label]) => (
                                                    <SelectItem key={key} value={key}>
                                                        {label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.mpesa_id_assignment_mode && (
                                            <p className="text-sm text-red-600 mt-1">{errors.mpesa_id_assignment_mode}</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Generation Method */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Generation Method</CardTitle>
                                    <CardDescription>
                                        Select the method for generating M-Pesa IDs
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="generation_method">Generation Method</Label>
                                        <Select 
                                            value={data.mpesa_id_generation_method} 
                                            onValueChange={(value) => setData('mpesa_id_generation_method', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(generationMethods).map(([key, label]) => (
                                                    <SelectItem key={key} value={key}>
                                                        {label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.mpesa_id_generation_method && (
                                            <p className="text-sm text-red-600 mt-1">{errors.mpesa_id_generation_method}</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Format Settings */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Format Settings</CardTitle>
                                    <CardDescription>
                                        Configure the format and structure of M-Pesa IDs
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="prefix">Prefix</Label>
                                            <Input
                                                id="prefix"
                                                value={data.mpesa_id_prefix}
                                                onChange={(e) => setData('mpesa_id_prefix', e.target.value)}
                                                placeholder="MP"
                                            />
                                            {errors.mpesa_id_prefix && (
                                                <p className="text-sm text-red-600 mt-1">{errors.mpesa_id_prefix}</p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="suffix">Suffix</Label>
                                            <Input
                                                id="suffix"
                                                value={data.mpesa_id_suffix}
                                                onChange={(e) => setData('mpesa_id_suffix', e.target.value)}
                                                placeholder="Optional"
                                            />
                                            {errors.mpesa_id_suffix && (
                                                <p className="text-sm text-red-600 mt-1">{errors.mpesa_id_suffix}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="starting_number">Starting Number</Label>
                                            <Input
                                                id="starting_number"
                                                type="number"
                                                value={data.mpesa_id_starting_number}
                                                onChange={(e) => setData('mpesa_id_starting_number', parseInt(e.target.value))}
                                                min="1"
                                            />
                                            {errors.mpesa_id_starting_number && (
                                                <p className="text-sm text-red-600 mt-1">{errors.mpesa_id_starting_number}</p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="padding">Number Padding</Label>
                                            <Input
                                                id="padding"
                                                type="number"
                                                value={data.mpesa_id_padding}
                                                onChange={(e) => setData('mpesa_id_padding', parseInt(e.target.value))}
                                                min="1"
                                                max="10"
                                            />
                                            {errors.mpesa_id_padding && (
                                                <p className="text-sm text-red-600 mt-1">{errors.mpesa_id_padding}</p>
                                            )}
                                        </div>
                                    </div>

                                    {data.mpesa_id_generation_method === 'custom_format' && (
                                        <div>
                                            <Label htmlFor="custom_format">Custom Format</Label>
                                            <Input
                                                id="custom_format"
                                                value={data.mpesa_id_custom_format}
                                                onChange={(e) => setData('mpesa_id_custom_format', e.target.value)}
                                                placeholder="{prefix}{customer_id}{suffix}"
                                            />
                                            <p className="text-sm text-gray-500 mt-1">
                                                Available placeholders: {'{prefix}'}, {'{suffix}'}, {'{customer_id}'}, {'{phone_last_4}'}, {'{phone_last_6}'}, {'{sequential}'}
                                            </p>
                                            {errors.mpesa_id_custom_format && (
                                                <p className="text-sm text-red-600 mt-1">{errors.mpesa_id_custom_format}</p>
                                            )}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            {/* Preview */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center">
                                        <Eye className="h-5 w-5 mr-2" />
                                        Preview
                                    </CardTitle>
                                    <CardDescription>
                                        Test your current settings
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <Button 
                                        type="button" 
                                        variant="outline" 
                                        onClick={handlePreview}
                                        disabled={isGenerating}
                                        className="w-full"
                                    >
                                        {isGenerating ? (
                                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                        ) : (
                                            <Eye className="h-4 w-4 mr-2" />
                                        )}
                                        Generate Preview
                                    </Button>
                                    
                                    {previewId && (
                                        <div className="p-3 bg-gray-50 rounded-lg">
                                            <p className="text-sm text-gray-600 mb-1">Preview ID:</p>
                                            <p className="font-mono text-lg font-bold text-green-600">{previewId}</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Bulk Actions */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center">
                                        <Users className="h-5 w-5 mr-2" />
                                        Bulk Actions
                                    </CardTitle>
                                    <CardDescription>
                                        Generate IDs for existing customers
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <Button 
                                        type="button" 
                                        variant="outline" 
                                        onClick={handleBulkGenerate}
                                        disabled={isBulkGenerating}
                                        className="w-full"
                                    >
                                        {isBulkGenerating ? (
                                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                        ) : (
                                            <Users className="h-4 w-4 mr-2" />
                                        )}
                                        Generate for Existing Customers
                                    </Button>
                                </CardContent>
                            </Card>

                            {/* Additional Options */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Additional Options</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div className="space-y-0.5">
                                            <Label>Auto-generate on Create</Label>
                                            <p className="text-sm text-gray-500">
                                                Generate M-Pesa ID when creating customers
                                            </p>
                                        </div>
                                        <Switch
                                            checked={data.mpesa_id_auto_generate_on_create}
                                            onCheckedChange={(checked) => setData('mpesa_id_auto_generate_on_create', checked)}
                                        />
                                    </div>
                                    
                                    <Separator />
                                    
                                    <div className="flex items-center justify-between">
                                        <div className="space-y-0.5">
                                            <Label>Required for Payments</Label>
                                            <p className="text-sm text-gray-500">
                                                Require M-Pesa ID for payments
                                            </p>
                                        </div>
                                        <Switch
                                            checked={data.mpesa_id_required_for_payments}
                                            onCheckedChange={(checked) => setData('mpesa_id_required_for_payments', checked)}
                                        />
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end space-x-4">
                        <Button type="button" variant="outline" onClick={() => reset()}>
                            Reset
                        </Button>
                        <Button type="submit" disabled={processing}>
                            {processing ? (
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                                <Settings className="h-4 w-4 mr-2" />
                            )}
                            Save Settings
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
