import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, DollarSign } from 'lucide-react';
import { formatCurrency, initializeCurrency } from '@/lib/currency';

interface CurrencySettingsProps {
  settings: {
    default_currency: { value: string } | null;
    currency_display_format: { value: string } | null;
    currency_decimal_places: { value: string } | null;
  };
  supportedCurrencies: Array<{
    code: string;
    name: string;
    symbol: string;
    display: string;
  }>;
  displayFormats: Record<string, string>;
  currentConfig: any;
}

export default function CurrencySettings({
  settings,
  supportedCurrencies,
  displayFormats,
  currentConfig,
}: CurrencySettingsProps) {
  const [previewAmount, setPreviewAmount] = useState(1000);
  const [previewFormatted, setPreviewFormatted] = useState('');

  const { data, setData, post, processing, errors, reset } = useForm({
    default_currency: settings.default_currency?.value || 'KES',
    currency_display_format: settings.currency_display_format?.value || 'symbol_before',
    currency_decimal_places: parseInt(settings.currency_decimal_places?.value || '2'),
  });

  // Initialize currency configuration
  React.useEffect(() => {
    if (currentConfig) {
      initializeCurrency(currentConfig);
    }
  }, [currentConfig]);

  // Update preview when settings change
  React.useEffect(() => {
    const formatted = formatCurrency(previewAmount, data.default_currency, {
      display_format: data.currency_display_format,
      decimal_places: data.currency_decimal_places,
    });
    setPreviewFormatted(formatted);
  }, [data, previewAmount]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('admin.settings.currency-settings.update'));
  };

  const handleReset = () => {
    post(route('admin.settings.currency-settings.reset'));
  };

  return (
    <AppLayout>
      <Head title="Currency Settings" />

      <div className="page-container">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href={route('admin.settings.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Settings
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <DollarSign className="h-8 w-8 mr-3 text-emerald-600" />
                Currency Settings
              </h1>
              <p className="text-gray-600 mt-2">
                Configure the default currency and display format for the system
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Settings Form */}
          <Card>
            <CardHeader>
              <CardTitle>Currency Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Default Currency */}
                <div className="space-y-2">
                  <Label htmlFor="default_currency">Default Currency</Label>
                  <Select
                    value={data.default_currency}
                    onValueChange={(value) => setData('default_currency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      {supportedCurrencies.map((currency) => (
                        <SelectItem key={currency.code} value={currency.code}>
                          {currency.display}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.default_currency && (
                    <p className="text-sm text-red-600">{errors.default_currency}</p>
                  )}
                </div>

                {/* Display Format */}
                <div className="space-y-2">
                  <Label htmlFor="currency_display_format">Display Format</Label>
                  <Select
                    value={data.currency_display_format}
                    onValueChange={(value) => setData('currency_display_format', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(displayFormats).map(([key, label]) => (
                        <SelectItem key={key} value={key}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.currency_display_format && (
                    <p className="text-sm text-red-600">{errors.currency_display_format}</p>
                  )}
                </div>

                {/* Decimal Places */}
                <div className="space-y-2">
                  <Label htmlFor="currency_decimal_places">Decimal Places</Label>
                  <Select
                    value={data.currency_decimal_places.toString()}
                    onValueChange={(value) => setData('currency_decimal_places', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select decimal places" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">0 (No decimals)</SelectItem>
                      <SelectItem value="1">1 decimal place</SelectItem>
                      <SelectItem value="2">2 decimal places</SelectItem>
                      <SelectItem value="3">3 decimal places</SelectItem>
                      <SelectItem value="4">4 decimal places</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.currency_decimal_places && (
                    <p className="text-sm text-red-600">{errors.currency_decimal_places}</p>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <Button type="submit" disabled={processing} className="btn-gradient">
                    {processing ? 'Saving...' : 'Save Settings'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleReset}
                    disabled={processing}
                  >
                    Reset to Defaults
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="preview_amount">Preview Amount</Label>
                <Input
                  id="preview_amount"
                  type="number"
                  value={previewAmount}
                  onChange={(e) => setPreviewAmount(parseFloat(e.target.value) || 0)}
                  className="input-modern"
                />
              </div>

              <div className="space-y-2">
                <Label>Formatted Result</Label>
                <div className="p-3 bg-gray-50 rounded-md border">
                  <span className="text-lg font-semibold">{previewFormatted}</span>
                </div>
              </div>

              <Alert>
                <AlertDescription>
                  This preview shows how currency amounts will be displayed throughout the system
                  with your current settings.
                </AlertDescription>
              </Alert>

              {/* Currency Information */}
              <div className="space-y-2">
                <Label>Currency Information</Label>
                <div className="space-y-1 text-sm">
                  {supportedCurrencies
                    .filter((c) => c.code === data.default_currency)
                    .map((currency) => (
                      <div key={currency.code} className="space-y-1">
                        <p><strong>Name:</strong> {currency.name}</p>
                        <p><strong>Code:</strong> {currency.code}</p>
                        <p><strong>Symbol:</strong> {currency.symbol}</p>
                      </div>
                    ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
