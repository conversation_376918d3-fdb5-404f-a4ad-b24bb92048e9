import AppLayout  from '@/layouts/app-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Head, Link, usePage } from '@inertiajs/react';
import {
    ArrowLeft,
    Edit,
    Shield,
    Users,
    CheckCircle,
    XCircle
} from 'lucide-react';

interface Permission {
    id: number;
    name: string;
}

interface User {
    id: number;
    name: string;
    email: string;
}

interface Role {
    id: number;
    name: string;
    permissions: Permission[];
    users: User[];
    created_at: string;
    updated_at: string;
}

interface Props {
    role: Role;
}

export default function ShowRole({ role }: Props) {
    const { auth } = usePage().props as any;
    const permissions = auth.permissions || [];

    // Helper function to check if user has a specific permission
    const hasPermission = (permission: string) => permissions.includes(permission);

    // Group permissions by category for better display
    const groupedPermissions = role.permissions.reduce((groups, permission) => {
        const parts = permission.name.split(' ');
        const category = parts.length > 1 ? parts[1] : 'general';
        if (!groups[category]) {
            groups[category] = [];
        }
        groups[category].push(permission);
        return groups;
    }, {} as Record<string, Permission[]>);

    return (
        <AppLayout>
            <Head title={`Role - ${role.name}`} />

            <div className="page-container">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <div className="flex items-center space-x-2">
                            <Link href={route('admin.settings.roles.index')}>
                                <Button variant="ghost" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Roles
                                </Button>
                            </Link>
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mt-2">{role.name}</h1>
                        <p className="text-gray-600 mt-2">
                            Role details and permissions
                        </p>
                    </div>
                    <div className="flex items-center space-x-4">
                        {hasPermission('edit roles') && (
                            <Link href={route('admin.settings.roles.edit', role.id)}>
                                <Button>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit Role
                                </Button>
                            </Link>
                        )}
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                    {/* Role Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Shield className="h-5 w-5 mr-2" />
                                Role Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <p className="text-sm text-gray-500">Role Name</p>
                                <p className="font-medium text-lg">{role.name}</p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-500">Total Permissions</p>
                                <p className="font-medium">{role.permissions.length} permissions assigned</p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-500">Users with this Role</p>
                                <p className="font-medium">{role.users.length} users</p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-500">Created</p>
                                <p className="font-medium">
                                    {new Date(role.created_at).toLocaleDateString()}
                                </p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-500">Last Updated</p>
                                <p className="font-medium">
                                    {new Date(role.updated_at).toLocaleDateString()}
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Assigned Users */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Users className="h-5 w-5 mr-2" />
                                Assigned Users ({role.users.length})
                            </CardTitle>
                            <CardDescription>
                                Users who have been assigned this role
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {role.users.length > 0 ? (
                                <div className="space-y-3">
                                    {role.users.map((user) => (
                                        <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg">
                                            <div>
                                                <p className="font-medium">{user.name}</p>
                                                <p className="text-sm text-gray-500">{user.email}</p>
                                            </div>
                                            {hasPermission('view users') && (
                                                <Link href={route('admin.settings.users.show', user.id)}>
                                                    <Button variant="ghost" size="sm">
                                                        View User
                                                    </Button>
                                                </Link>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <p className="text-gray-500">No users assigned to this role</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Permissions */}
                <Card className="mt-6">
                    <CardHeader>
                        <CardTitle>Permissions ({role.permissions.length})</CardTitle>
                        <CardDescription>
                            All permissions assigned to this role
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {role.permissions.length > 0 ? (
                            <div className="space-y-6">
                                {Object.entries(groupedPermissions).map(([category, permissions]) => (
                                    <div key={category} className="border rounded-lg p-4">
                                        <h3 className="font-semibold text-lg mb-3 capitalize">
                                            {category.charAt(0).toUpperCase() + category.slice(1)} Permissions
                                        </h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                            {permissions.map((permission) => (
                                                <div key={permission.id} className="flex items-center space-x-2">
                                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                                    <Badge variant="outline" className="justify-start">
                                                        {permission.name}
                                                    </Badge>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <XCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                <p className="text-gray-500">No permissions assigned to this role</p>
                                <p className="text-sm text-gray-400 mt-2">
                                    Users with this role will have no special permissions
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
