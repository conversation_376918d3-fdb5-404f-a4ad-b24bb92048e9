import AppLayout  from '@/layouts/app-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { ArrowLeft, Save, Shield } from 'lucide-react';

interface Permission {
    id: number;
    name: string;
}

interface PermissionGroup {
    [key: string]: Permission[];
}

interface Role {
    id: number;
    name: string;
    permissions: Permission[];
}

interface Props {
    role: Role;
    permissions: PermissionGroup;
}

export default function EditRole({ role, permissions }: Props) {
    const { auth } = usePage().props as any;
    const userPermissions = auth.permissions || [];

    // Helper function to check if user has a specific permission
    const hasPermission = (permission: string) => userPermissions.includes(permission);

    const { data, setData, put, processing, errors } = useForm({
        name: role.name,
        permissions: role.permissions.map(p => p.id), // Load existing permissions
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.settings.roles.update', role.id));
    };

    const handlePermissionChange = (permissionId: number, checked: boolean) => {
        if (checked) {
            setData('permissions', [...data.permissions, permissionId]);
        } else {
            setData('permissions', data.permissions.filter(id => id !== permissionId));
        }
    };

    const handleGroupToggle = (groupPermissions: Permission[], checked: boolean) => {
        const groupIds = groupPermissions.map(p => p.id);
        if (checked) {
            const newPermissions = [...new Set([...data.permissions, ...groupIds])];
            setData('permissions', newPermissions);
        } else {
            setData('permissions', data.permissions.filter(id => !groupIds.includes(id)));
        }
    };

    const isGroupChecked = (groupPermissions: Permission[]) => {
        return groupPermissions.every(p => data.permissions.includes(p.id));
    };

    const isGroupIndeterminate = (groupPermissions: Permission[]) => {
        const checkedCount = groupPermissions.filter(p => data.permissions.includes(p.id)).length;
        return checkedCount > 0 && checkedCount < groupPermissions.length;
    };

    const handleSelectAll = () => {
        const allPermissionIds = Object.values(permissions).flat().map(p => p.id);
        setData('permissions', allPermissionIds);
    };

    const handleSelectNone = () => {
        setData('permissions', []);
    };

    return (
        <AppLayout>
            <Head title={`Edit Role - ${role.name}`} />

            <div className="page-container">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <div className="flex items-center space-x-2">
                            <Link href={route('admin.settings.roles.index')}>
                                <Button variant="ghost" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Roles
                                </Button>
                            </Link>
                        </div>
                        <h1 className="text-3xl font-bold text-gray-900 mt-2">Edit Role</h1>
                        <p className="text-gray-600 mt-2">
                            Update role name and modify permissions
                        </p>
                    </div>
                </div>

                {/* Edit Role Form */}
                <Card className="mt-6 max-w-4xl">
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Shield className="h-5 w-5 mr-2" />
                            Role Information
                        </CardTitle>
                        <CardDescription>
                            Update the role name and modify permissions as needed
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Role Name */}
                            <div className="space-y-2">
                                <Label htmlFor="name">Role Name</Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="Enter role name"
                                    className={errors.name ? 'border-red-500' : ''}
                                    disabled={!hasPermission('edit roles')}
                                />
                                {errors.name && (
                                    <p className="text-sm text-red-600">{errors.name}</p>
                                )}
                            </div>

                            {/* Permissions */}
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <Label>Permissions</Label>
                                    <div className="flex items-center space-x-4">
                                        <div className="text-sm text-gray-500">
                                            {data.permissions.length} of {Object.values(permissions).flat().length} selected
                                        </div>
                                        {hasPermission('edit roles') && (
                                            <div className="flex space-x-2">
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={handleSelectAll}
                                                >
                                                    Select All
                                                </Button>
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={handleSelectNone}
                                                >
                                                    Select None
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div className="text-sm text-gray-600 mb-4">
                                    {hasPermission('edit roles')
                                        ? 'Modify permissions for this role. You can add or remove any permissions freely.'
                                        : 'You are viewing this role in read-only mode. You do not have permission to edit roles.'
                                    }
                                </div>
                                <div className="space-y-6">
                                    {Object.entries(permissions).map(([groupName, groupPermissions]) => (
                                        <div key={groupName} className="border rounded-lg p-4">
                                            <div className="flex items-center justify-between mb-3">
                                                <div className="flex items-center space-x-2">
                                                    <Checkbox
                                                        id={`group-${groupName}`}
                                                        checked={isGroupChecked(groupPermissions)}
                                                        onCheckedChange={(checked) =>
                                                            handleGroupToggle(groupPermissions, checked as boolean)
                                                        }
                                                        className={isGroupIndeterminate(groupPermissions) ? 'data-[state=indeterminate]:bg-blue-600' : ''}
                                                        disabled={!hasPermission('edit roles')}
                                                    />
                                                    <Label
                                                        htmlFor={`group-${groupName}`}
                                                        className="text-sm font-semibold capitalize cursor-pointer"
                                                    >
                                                        {groupName.charAt(0).toUpperCase() + groupName.slice(1)} Permissions
                                                    </Label>
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    {groupPermissions.filter(p => data.permissions.includes(p.id)).length} of {groupPermissions.length} selected
                                                </div>
                                            </div>
                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 ml-6">
                                                {groupPermissions.map((permission) => (
                                                    <div key={permission.id} className="flex items-center space-x-2">
                                                        <Checkbox
                                                            id={`permission-${permission.id}`}
                                                            checked={data.permissions.includes(permission.id)}
                                                            onCheckedChange={(checked) =>
                                                                handlePermissionChange(permission.id, checked as boolean)
                                                            }
                                                            disabled={!hasPermission('edit roles')}
                                                        />
                                                        <Label
                                                            htmlFor={`permission-${permission.id}`}
                                                            className="text-sm font-normal cursor-pointer"
                                                        >
                                                            {permission.name}
                                                        </Label>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                {errors.permissions && (
                                    <p className="text-sm text-red-600">{errors.permissions}</p>
                                )}
                            </div>

                            {/* Submit Button */}
                            <div className="flex items-center justify-end space-x-4 pt-4">
                                <Link href={route('admin.settings.roles.index')}>
                                    <Button type="button" variant="outline">
                                        {hasPermission('edit roles') ? 'Cancel' : 'Back'}
                                    </Button>
                                </Link>
                                {hasPermission('edit roles') && (
                                    <Button type="submit" disabled={processing}>
                                        <Save className="h-4 w-4 mr-2" />
                                        {processing ? 'Updating...' : 'Update Role'}
                                    </Button>
                                )}
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
