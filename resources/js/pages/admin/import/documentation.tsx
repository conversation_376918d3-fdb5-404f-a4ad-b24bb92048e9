import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Download,
  FileSpreadsheet,
  CheckCircle,
  AlertCircle,
  Info,
  Users,
  Settings,
  Database,
  Upload
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ValidationRule {
  field: string;
  required: boolean;
  type: string;
  rules: string;
  example: string;
  notes: string;
}

interface TemplateInfo {
  version: string;
  last_updated: string;
  supported_formats: string[];
  max_file_size: string;
  max_records: number;
}

interface Props {
  templateInfo: TemplateInfo;
  validationRules: ValidationRule[];
  examples: {
    static_ip_customer: Record<string, string>;
    pppoe_customer: Record<string, string>;
  };
}

export default function ImportDocumentation({ templateInfo, validationRules, examples }: Props) {
  const handleDownloadTemplate = (format: 'excel' | 'csv' = 'excel') => {
    // Create a temporary link element to trigger download
    const link = document.createElement('a');
    if (format === 'csv') {
      link.href = route('admin.import.template.download-csv');
      link.download = 'customer_import_template.csv';
    } else {
      link.href = route('admin.import.template.download');
      link.download = 'customer_import_template.xlsx';
    }
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <AppLayout>
      <Head title="Customer Import Documentation" />

      <div className="container mx-auto py-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Customer Import Documentation</h1>
            <p className="text-gray-600 mt-2">
              Complete guide for importing customer data using our standardized Excel template
            </p>
          </div>
          <div className="flex space-x-2">
            <Button onClick={() => handleDownloadTemplate('excel')} className="flex items-center">
              <Download className="h-4 w-4 mr-2" />
              Download Excel
            </Button>
            <Button onClick={() => handleDownloadTemplate('csv')} variant="outline" className="flex items-center">
              <Download className="h-4 w-4 mr-2" />
              Download CSV
            </Button>
            <Button variant="secondary" asChild className="flex items-center">
              <Link href={route('admin.import.upload')}>
                <Upload className="h-4 w-4 mr-2" />
                Upload File
              </Link>
            </Button>
          </div>
        </div>

        {/* Template Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileSpreadsheet className="h-5 w-5 mr-2" />
              Template Information
            </CardTitle>
            <CardDescription>
              Current template specifications and requirements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{templateInfo.version}</div>
                <div className="text-sm text-gray-600">Current Version</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{templateInfo.max_records}</div>
                <div className="text-sm text-gray-600">Max Records</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{templateInfo.max_file_size}</div>
                <div className="text-sm text-gray-600">Max File Size</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {templateInfo.supported_formats.join(', ')}
                </div>
                <div className="text-sm text-gray-600">Supported Format</div>
              </div>
            </div>
            <div className="mt-4 text-sm text-gray-500">
              Last updated: {templateInfo.last_updated}
            </div>
          </CardContent>
        </Card>

        {/* Quick Start Guide */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              Quick Start Guide
            </CardTitle>
            <CardDescription>
              Follow these steps to import your customer data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                  1
                </div>
                <div>
                  <h4 className="font-semibold">Download Template</h4>
                  <p className="text-gray-600">Download the latest Excel template using the buttons above</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                  2
                </div>
                <div>
                  <h4 className="font-semibold">Prepare Your Data</h4>
                  <p className="text-gray-600">Remove example rows and fill in your customer data following the validation rules</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                  3
                </div>
                <div>
                  <h4 className="font-semibold">Validate Data</h4>
                  <p className="text-gray-600">Check your data against the validation rules before importing</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">
                  4
                </div>
                <div>
                  <h4 className="font-semibold">Upload & Import</h4>
                  <p className="text-gray-600">
                    Upload your completed Excel file using the
                    <Button variant="link" className="p-0 h-auto mx-1" asChild>
                      <Link href={route('admin.import.upload')}>Upload File</Link>
                    </Button>
                    button above
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Important Notes */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Column Headers:</strong> Do not modify the column headers in the template. They must remain exactly as provided.
            </AlertDescription>
          </Alert>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Required Fields:</strong> All columns marked with * are required and cannot be empty.
            </AlertDescription>
          </Alert>
          <Alert>
            <Users className="h-4 w-4" />
            <AlertDescription>
              <strong>Unique Emails:</strong> Customer email addresses must be unique across all customers in the system.
            </AlertDescription>
          </Alert>
          <Alert className="border-orange-200 bg-orange-50">
            <FileSpreadsheet className="h-4 w-4 text-orange-600" />
            <AlertDescription>
              <strong>Google Sheets:</strong> If using Google Sheets, download the CSV format for best compatibility. Excel files may have import issues.
            </AlertDescription>
          </Alert>
        </div>

        {/* Validation Rules */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Field Validation Rules
            </CardTitle>
            <CardDescription>
              Detailed requirements for each field in the template
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 px-4 py-2 text-left">Field Name</th>
                    <th className="border border-gray-300 px-4 py-2 text-center">Required</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Type</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Validation Rules</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">Example</th>
                  </tr>
                </thead>
                <tbody>
                  {validationRules.map((rule, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="border border-gray-300 px-4 py-2 font-medium">
                        {rule.field}
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-center">
                        {rule.required ? (
                          <Badge variant="destructive">Required</Badge>
                        ) : (
                          <Badge variant="secondary">Optional</Badge>
                        )}
                      </td>
                      <td className="border border-gray-300 px-4 py-2">
                        <Badge variant="outline">{rule.type}</Badge>
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-sm">
                        {rule.rules}
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-sm font-mono">
                        {rule.example}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Examples */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Data Examples
            </CardTitle>
            <CardDescription>
              Sample data for different service types
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Static IP Example */}
              <div>
                <h4 className="font-semibold mb-3 flex items-center">
                  <Badge className="mr-2">Static IP Customer</Badge>
                </h4>
                <div className="bg-gray-50 p-4 rounded-lg overflow-x-auto">
                  <table className="w-full text-sm">
                    <tbody>
                      {Object.entries(examples.static_ip_customer).map(([key, value]) => (
                        <tr key={key}>
                          <td className="font-medium py-1 pr-4">{key}:</td>
                          <td className="font-mono">{value || '(empty)'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* PPPoE Example */}
              <div>
                <h4 className="font-semibold mb-3 flex items-center">
                  <Badge variant="secondary" className="mr-2">PPPoE Customer</Badge>
                </h4>
                <div className="bg-gray-50 p-4 rounded-lg overflow-x-auto">
                  <table className="w-full text-sm">
                    <tbody>
                      {Object.entries(examples.pppoe_customer).map(([key, value]) => (
                        <tr key={key}>
                          <td className="font-medium py-1 pr-4">{key}:</td>
                          <td className="font-mono">{value || '(empty)'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Support Information */}
        <Card>
          <CardHeader>
            <CardTitle>Need Help?</CardTitle>
            <CardDescription>
              Contact information and additional resources
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold">Technical Support</h4>
                <p className="text-gray-600">
                  For technical issues with the import process, contact:
                  <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline ml-1">
                    <EMAIL>
                  </a>
                </p>
              </div>
              <div>
                <h4 className="font-semibold">Data Preparation Assistance</h4>
                <p className="text-gray-600">
                  Need help preparing your data? Our team can assist with data formatting and validation.
                </p>
              </div>
              <div className="flex space-x-4">
                <Button variant="outline" asChild>
                  <Link href={route('customers.index')}>
                    View Customers
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href={route('admin.settings.index')}>
                    Import Settings
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
