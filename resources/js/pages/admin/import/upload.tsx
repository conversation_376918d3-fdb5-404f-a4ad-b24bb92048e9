import React, { useState, useRef } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Upload,
  FileSpreadsheet,
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface TemplateInfo {
  version: string;
  last_updated: string;
  supported_formats: string[];
  max_file_size: string;
  max_records: number;
}

interface Props {
  templateInfo: TemplateInfo;
}

export default function ImportUpload({ templateInfo }: Props) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { data, setData, post, processing, errors, progress } = useForm({
    import_file: null as File | null,
  });

  const handleFileSelect = (file: File) => {
    console.log('File selected:', file.name, file.type, file.size);
    setSelectedFile(file);
    setData('import_file', file);
  };

  const handleChooseFileClick = () => {
    console.log('Choose file button clicked');
    fileInputRef.current?.click();
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (data.import_file) {
      console.log('Submitting file:', {
        name: data.import_file.name,
        type: data.import_file.type,
        size: data.import_file.size,
        lastModified: data.import_file.lastModified
      });
      post(route('admin.import.process'));
    } else {
      console.log('No file selected for submission');
    }
  };

  const isValidFileType = (file: File) => {
    const validExtensions = ['xlsx', 'xls', 'csv'];
    const extension = file.name.split('.').pop()?.toLowerCase();

    // Primarily check by extension since MIME types can be unreliable for CSV
    if (extension && validExtensions.includes(extension)) {
      return true;
    }

    // Fallback to MIME type check
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      'text/plain',
      'application/csv'
    ];

    return validTypes.includes(file.type);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <AppLayout>
      <Head title="Import Customer Data" />

      <div className="container mx-auto py-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={route('admin.import.documentation')}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Documentation
                </Link>
              </Button>
            </div>
            <h1 className="text-3xl font-bold">Import Customer Data</h1>
            <p className="text-gray-600 mt-2">
              Upload your Excel or CSV file to import customer data using our existing import system
            </p>
          </div>
        </div>

        {/* Upload Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="h-5 w-5 mr-2" />
              Upload Import File
            </CardTitle>
            <CardDescription>
              Select your Excel (.xlsx, .xls) or CSV file containing customer data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* File Upload Area */}
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
                  dragActive
                    ? 'border-blue-400 bg-blue-50'
                    : selectedFile
                      ? 'border-green-400 bg-green-50'
                      : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onClick={handleChooseFileClick}
              >
                {selectedFile ? (
                  <div className="space-y-2">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
                    <div>
                      <p className="font-semibold text-green-700">{selectedFile.name}</p>
                      <p className="text-sm text-gray-600">
                        {formatFileSize(selectedFile.size)} • {selectedFile.type || 'Unknown type'}
                      </p>
                      {!isValidFileType(selectedFile) && (
                        <p className="text-sm text-red-600 mt-1">
                          Warning: File type may not be supported
                        </p>
                      )}
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedFile(null);
                        setData('import_file', null);
                      }}
                    >
                      Choose Different File
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <FileSpreadsheet className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-lg font-semibold">Drop your file here or click to browse</p>
                      <p className="text-gray-600">
                        Supports Excel (.xlsx, .xls) and CSV files up to {templateInfo.max_file_size}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleChooseFileClick}
                        className="bg-white hover:bg-gray-50"
                      >
                        Choose File
                      </Button>
                      <p className="text-xs text-gray-500">
                        Click the button above or drag & drop a file
                      </p>
                    </div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".xlsx,.xls,.csv"
                      style={{ display: 'none' }}
                      onChange={(e) => {
                        console.log('File input changed:', e.target.files);
                        const file = e.target.files?.[0];
                        if (file) {
                          handleFileSelect(file);
                          // Clear the input so the same file can be selected again
                          e.target.value = '';
                        }
                      }}
                    />
                  </div>
                )}
              </div>

              {/* Fallback file input for testing */}
              <div className="border-t pt-4">
                <Label htmlFor="fallback-file-input" className="text-sm font-medium">
                  Alternative File Selection (if button above doesn't work):
                </Label>
                <Input
                  id="fallback-file-input"
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  className="mt-2"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      console.log('Fallback file input used:', file.name);
                      handleFileSelect(file);
                    }
                  }}
                />
              </div>

              {errors.import_file && (
                <Alert className="border-red-200 bg-red-50">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-700">
                    {errors.import_file}
                  </AlertDescription>
                </Alert>
              )}

              {/* Import Progress */}
              {processing && progress && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Upload progress: {Math.round(progress.percentage || 0)}%
                  </AlertDescription>
                </Alert>
              )}

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <Button variant="outline" asChild>
                  <Link href={route('admin.import.documentation')}>
                    Cancel
                  </Link>
                </Button>
                <Button
                  type="submit"
                  disabled={!selectedFile || processing || !isValidFileType(selectedFile || new File([], ''))}
                  className="flex items-center"
                >
                  {processing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Start Import
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Important Notes */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Processing:</strong> Import uses your existing queue system. Large files will be processed in batches.
            </AlertDescription>
          </Alert>
          <Alert>
            <FileSpreadsheet className="h-4 w-4" />
            <AlertDescription>
              <strong>Template:</strong> Make sure your file follows the standardized template format for best results.
            </AlertDescription>
          </Alert>
        </div>

        {/* File Requirements */}
        <Card>
          <CardHeader>
            <CardTitle>File Requirements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <h4 className="font-semibold mb-2">Supported Formats</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• Excel (.xlsx, .xls)</li>
                  <li>• CSV (.csv)</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Size Limits</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• Max file size: {templateInfo.max_file_size}</li>
                  <li>• Max records: {templateInfo.max_records.toLocaleString()}</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Processing</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• Queue-based processing</li>
                  <li>• Batch size: 50 records</li>
                  <li>• Real-time validation</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
