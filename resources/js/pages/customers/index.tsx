import { useState } from 'react';
import { <PERSON>, Link, router, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ModernTable, ModernTableBody, ModernTableCell, ModernTableHeader, ModernTableRow } from '@/components/ui/modern-table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ResponsivePagination } from '@/components/ui/simplified-pagination';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash,
  UserPlus,
  FileText,
  UserX,
  User<PERSON><PERSON><PERSON>,
  Alert<PERSON>riangle,
  CheckCircle,

} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  CreateButton,
  ViewDropdownItem,
  EditDropdownItem,
  DeleteDropdownItem,
  PermissionDropdownItem,
  PERMISSIONS
} from '@/components/permissions';

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string | null;
  address: string | null;
  city: string | null;
  state: string | null;
  postal_code: string | null;
  country: string | null;
  status: string;
  created_at: string;
  updated_at: string;
  full_address: string;
  subscriptions_count: number;
  service_status_summary: {
    total: number;
    active: number;
    suspended: number;
    has_suspended: boolean;
    all_suspended: boolean;
    suspended_service_names: string[];
  };
}

interface CustomerIndexProps {
  customers: {
    data: Customer[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  };
  filters: {
    search: string;
    status: string;
  };
}

export default function CustomerIndex({ customers, filters }: CustomerIndexProps) {

  const [searchQuery, setSearchQuery] = useState(filters.search || '');
  const [statusFilter, setStatusFilter] = useState(filters.status || '');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    router.get(route('customers.index'), {
      search: searchQuery,
      status: statusFilter === 'all' ? '' : statusFilter
    }, {
      preserveState: true,
      replace: true
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="status-badge-active">Active</Badge>;
      case 'inactive':
        return <Badge className="status-badge-inactive">Inactive</Badge>;
      case 'suspended':
        return <Badge className="status-badge-suspended">Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getServiceStatusIndicator = (customer: Customer) => {
    const { service_status_summary } = customer;

    if (!service_status_summary || service_status_summary.total === 0) {
      return (
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 rounded-full bg-gray-300"></div>
          <span className="text-xs text-gray-500">No Services</span>
        </div>
      );
    }

    if (service_status_summary.has_suspended) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center space-x-1 cursor-help">
                <AlertTriangle className="w-4 h-4 text-orange-500" />
                <span className="text-xs text-orange-600">
                  {service_status_summary.suspended} Suspended
                </span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm">
                <div className="font-medium mb-1">Suspended Services:</div>
                <ul className="list-disc list-inside">
                  {service_status_summary.suspended_service_names.map((name, index) => (
                    <li key={index}>{name}</li>
                  ))}
                </ul>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return (
      <div className="flex items-center space-x-1">
        <CheckCircle className="w-4 h-4 text-green-500" />
        <span className="text-xs text-green-600">
          {service_status_summary.active} Active
        </span>
      </div>
    );
  };

  const handleDelete = (customerId: number) => {
    if (confirm('Are you sure you want to delete this customer?')) {
      router.delete(route('customers.destroy', customerId));
    }
  };

  return (
    <AppLayout>
      <Head title="Customers" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <h1>Customers</h1>
            <p>Manage your customer base and their services</p>
          </div>
          <CreateButton resource="customers" asChild>
            <Link href={route('customers.create')}>
              <Plus className="h-4 w-4 mr-2" />
              Add Customer
            </Link>
          </CreateButton>
        </div>

        {/* Filters Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Search & Filter
            </CardTitle>
            <CardDescription>Find customers by name, email, phone, or status</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name, email or phone..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 input-modern"
                  />
                </div>
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="input-modern">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button type="submit" className="btn-gradient">Search</Button>
            </form>
          </CardContent>
        </Card>

        {/* Customers Table */}
        <Card>
          <CardHeader>
            <CardTitle>Customer List</CardTitle>
            <CardDescription>
              {customers.total} total customers • Showing {customers.from}-{customers.to}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <ModernTable>
                <ModernTableHeader>
                  <ModernTableRow>
                    <ModernTableCell header className="min-w-[150px]">Name</ModernTableCell>
                    <ModernTableCell header className="min-w-[200px]">Email</ModernTableCell>
                    <ModernTableCell header className="min-w-[120px] hidden sm:table-cell">Phone</ModernTableCell>
                    <ModernTableCell header className="min-w-[150px] hidden md:table-cell">Location</ModernTableCell>
                    <ModernTableCell header className="min-w-[100px] hidden lg:table-cell">Mpesa ID</ModernTableCell>
                    <ModernTableCell header className="min-w-[100px]">Status</ModernTableCell>
                    <ModernTableCell header className="min-w-[120px] hidden lg:table-cell">Services</ModernTableCell>
                    <ModernTableCell header className="min-w-[100px] hidden lg:table-cell">Subscriptions</ModernTableCell>
                    <ModernTableCell header className="text-right min-w-[80px]">Actions</ModernTableCell>
                  </ModernTableRow>
                </ModernTableHeader>
              <ModernTableBody>
                {customers.data.length === 0 ? (
                  <ModernTableRow>
                    <ModernTableCell colSpan={8} className="text-center py-12 text-muted-foreground">
                      <div className="flex flex-col items-center gap-2">
                        <Search className="h-8 w-8 text-muted-foreground/50" />
                        <p>No customers found</p>
                        <p className="text-sm">Try adjusting your filters or add a new customer</p>
                      </div>
                    </ModernTableCell>
                  </ModernTableRow>
                ) : (
                  customers.data.map((customer) => (
                    <ModernTableRow key={customer.id} clickable>
                      <ModernTableCell className="font-medium">
                        <Link href={route('customers.show', customer.id)} className="hover:text-primary transition-colors">
                          {customer.name}
                        </Link>
                      </ModernTableCell>
                      <ModernTableCell className="text-muted-foreground">{customer.email}</ModernTableCell>
                      <ModernTableCell className="text-muted-foreground hidden sm:table-cell">{customer.phone || '-'}</ModernTableCell>
                      <ModernTableCell className="text-muted-foreground hidden md:table-cell">
                        {customer.city ? `${customer.city}, ${customer.country || ''}` : '-'}
                      </ModernTableCell>
                      <ModernTableCell className="hidden lg:table-cell">{customer.mpesa_id || '-'}</ModernTableCell>
                      <ModernTableCell>{getStatusBadge(customer.status)}</ModernTableCell>
                      <ModernTableCell className="hidden lg:table-cell">{getServiceStatusIndicator(customer)}</ModernTableCell>
                      <ModernTableCell className="hidden lg:table-cell">
                        <Badge variant="outline">{customer.subscriptions_count}</Badge>
                      </ModernTableCell>
                      <ModernTableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <ViewDropdownItem resource="customers" asChild>
                              <Link href={route('customers.show', customer.id)}>
                                View Details
                              </Link>
                            </ViewDropdownItem>
                            <EditDropdownItem resource="customers" asChild>
                              <Link href={route('customers.edit', customer.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </Link>
                            </EditDropdownItem>
                            {customer.status === 'active' ? (
                              <PermissionDropdownItem
                                permission="edit customers"
                                onClick={() => {
                                  if (confirm('Are you sure you want to suspend this customer? This will suspend all their services.')) {
                                    router.put(route('customers.update', customer.id), {
                                      name: customer.name,
                                      email: customer.email,
                                      phone: customer.phone,
                                      address: customer.address,
                                      city: customer.city,
                                      state: customer.state,
                                      postal_code: customer.postal_code,
                                      country: customer.country,
                                      status: 'suspended'
                                    });
                                  }
                                }}
                                className="text-orange-600"
                              >
                                <UserX className="h-4 w-4 mr-2" />
                                Suspend
                              </PermissionDropdownItem>
                            ) : (
                              <PermissionDropdownItem
                                permission="edit customers"
                                onClick={() => {
                                  if (confirm('Are you sure you want to activate this customer? This will activate all their services.')) {
                                    router.put(route('customers.update', customer.id), {
                                      name: customer.name,
                                      email: customer.email,
                                      phone: customer.phone,
                                      address: customer.address,
                                      city: customer.city,
                                      state: customer.state,
                                      postal_code: customer.postal_code,
                                      country: customer.country,
                                      status: 'active'
                                    });
                                  }
                                }}
                                className="text-green-600"
                              >
                                <UserCheck className="h-4 w-4 mr-2" />
                                Activate
                              </PermissionDropdownItem>
                            )}
                            <PermissionDropdownItem permission="view subscriptions" asChild>
                              <Link href={route('customers.subscriptions.index', customer.id)}>
                                <UserPlus className="h-4 w-4 mr-2" />
                                Subscriptions
                              </Link>
                            </PermissionDropdownItem>
                            <PermissionDropdownItem permission="view invoices" asChild>
                              <Link href={route('customers.invoices.index', customer.id)}>
                                <FileText className="h-4 w-4 mr-2" />
                                Invoices
                              </Link>
                            </PermissionDropdownItem>
                            <DeleteDropdownItem resource="customers" onClick={() => handleDelete(customer.id)}>
                              <Trash className="h-4 w-4 mr-2" />
                              Delete
                            </DeleteDropdownItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </ModernTableCell>
                    </ModernTableRow>
                  ))
                )}
              </ModernTableBody>
            </ModernTable>
            </div>
          </CardContent>
        </Card>

        {/* Simplified Pagination */}
        <div className="mt-6">
          <ResponsivePagination
            currentPage={customers.current_page}
            lastPage={customers.last_page}
            total={customers.total}
            from={customers.from}
            to={customers.to}
            onPageChange={(page) => {
              router.get(route('customers.index'), {
                page,
                search: searchQuery,
                status: statusFilter === 'all' ? '' : statusFilter
              }, {
                preserveState: true,
                replace: true
              });
            }}
          />
        </div>
      </div>
    </AppLayout>
  );
}
