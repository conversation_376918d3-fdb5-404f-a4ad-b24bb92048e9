import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ArrowLeft, Save, User } from 'lucide-react';
import { getCurrencySymbol } from '@/lib/utils';

interface Customer {
  id: number;
  name: string;
  email: string;
}

interface CreateCustomerSubscriptionProps {
  customer: Customer;
}

export default function CreateCustomerSubscription({ customer }: CreateCustomerSubscriptionProps) {
  const { data, setData, post, processing, errors, reset } = useForm({
    customer_id: customer.id,
    name: '',
    description: '',
    price: '',
    billing_cycle: 'monthly',
    start_date: new Date().toISOString().split('T')[0],
    status: 'active',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('customers.subscriptions.store', customer.id), {
      onSuccess: () => reset()
    });
  };

  return (
    <AppLayout>
      <Head title={`Add Subscription for ${customer.name}`} />

      <div className="container mx-auto p-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('customers.subscriptions.index', customer.id)}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Customer Subscriptions
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Add Subscription</h1>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              {/* Customer Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Customer Information</CardTitle>
                  <CardDescription>Creating subscription for this customer</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center p-4 border rounded-md">
                    <User className="h-10 w-10 text-gray-400 mr-4" />
                    <div>
                      <h3 className="font-medium">{customer.name}</h3>
                      <p className="text-sm text-gray-500">{customer.email}</p>
                    </div>
                    <Button variant="outline" asChild className="ml-auto">
                      <Link href={route('customers.show', customer.id)}>
                        View Customer
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Subscription Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Subscription Details</CardTitle>
                  <CardDescription>Enter the subscription details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name <span className="text-red-500">*</span></Label>
                    <Input
                      id="name"
                      value={data.name}
                      onChange={e => setData('name', e.target.value)}
                      placeholder="Subscription name"
                    />
                    {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={data.description}
                      onChange={e => setData('description', e.target.value)}
                      placeholder="Subscription description"
                      rows={3}
                    />
                    {errors.description && <p className="text-red-500 text-sm">{errors.description}</p>}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              {/* Billing Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Billing Information</CardTitle>
                  <CardDescription>Set up the billing details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Price <span className="text-red-500">*</span></Label>
                    <div className="relative">
                      <span className="absolute left-3 top-2.5">{getCurrencySymbol()}</span>
                      <Input
                        id="price"
                        type="number"
                        step="0.01"
                        min="0"
                        value={data.price}
                        onChange={e => setData('price', e.target.value)}
                        placeholder="0.00"
                        className="pl-7"
                      />
                    </div>
                    {errors.price && <p className="text-red-500 text-sm">{errors.price}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="billing_cycle">Billing Cycle <span className="text-red-500">*</span></Label>
                    <Select
                      value={data.billing_cycle}
                      onValueChange={value => setData('billing_cycle', value)}
                    >
                      <SelectTrigger id="billing_cycle">
                        <SelectValue placeholder="Select billing cycle" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="biannually">Biannually</SelectItem>
                        <SelectItem value="annually">Annually</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.billing_cycle && <p className="text-red-500 text-sm">{errors.billing_cycle}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="start_date">Start Date <span className="text-red-500">*</span></Label>
                    <Input
                      id="start_date"
                      type="date"
                      value={data.start_date}
                      onChange={e => setData('start_date', e.target.value)}
                    />
                    {errors.start_date && <p className="text-red-500 text-sm">{errors.start_date}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={data.status}
                      onValueChange={value => setData('status', value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.status && <p className="text-red-500 text-sm">{errors.status}</p>}
                  </div>
                </CardContent>
              </Card>

              {/* Submit Button */}
              <Card>
                <CardContent className="pt-6">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={processing}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Create Subscription
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
