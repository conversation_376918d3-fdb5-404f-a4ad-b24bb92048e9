import { useState } from 'react';
import { <PERSON>, Link, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ModernTable, ModernTableBody, ModernTableCell, ModernTableHeader, ModernTableRow } from '@/components/ui/modern-table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreH<PERSON>zon<PERSON>,
  <PERSON>,
  Trash,
  ArrowLeft
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface Customer {
  id: number;
  name: string;
  email: string;
}

interface Subscription {
  id: number;
  name: string;
  description: string | null;
  status: string;
  price: string;
  billing_cycle: string;
  start_date: string;
  end_date: string | null;
  next_billing_date: string;
}

interface CustomerSubscriptionsProps {
  customer: Customer;
  subscriptions: {
    data: Subscription[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  };
  filters: {
    search: string;
    status: string;
    billing_cycle: string;
  };
}

export default function CustomerSubscriptions({ customer, subscriptions, filters }: CustomerSubscriptionsProps) {
  const [searchQuery, setSearchQuery] = useState(filters.search || '');
  const [statusFilter, setStatusFilter] = useState(filters.status || '');
  const [billingCycleFilter, setBillingCycleFilter] = useState(filters.billing_cycle || '');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    router.get(route('customers.subscriptions.index', customer.id), {
      search: searchQuery,
      status: statusFilter === 'all' ? '' : statusFilter,
      billing_cycle: billingCycleFilter === 'all' ? '' : billingCycleFilter
    }, {
      preserveState: true,
      replace: true
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-yellow-500">Inactive</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-500">Cancelled</Badge>;
      case 'expired':
        return <Badge className="bg-gray-500">Expired</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const handleDelete = (subscriptionId: number) => {
    if (confirm('Are you sure you want to delete this subscription?')) {
      router.delete(route('subscriptions.destroy', subscriptionId));
    }
  };

  return (
    <AppLayout>
      <Head title={`${customer.name} - Subscriptions`} />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center mb-2">
              <Button variant="ghost" asChild className="mr-2">
                <Link href={route('customers.show', customer.id)}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Customer
                </Link>
              </Button>
            </div>
            <h1>{customer.name} - Subscriptions</h1>
            <p>Manage subscriptions for this customer</p>
          </div>
          <Button asChild>
            <Link href={route('customers.subscriptions.create', customer.id)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Subscription
            </Link>
          </Button>
        </div>

        {/* Filters Section */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filters</CardTitle>
            <CardDescription>Filter the list of subscriptions</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search by name or description..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full sm:w-48">
                <Select value={billingCycleFilter} onValueChange={setBillingCycleFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Billing Cycle" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Cycles</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="biannually">Biannually</SelectItem>
                    <SelectItem value="annually">Annually</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button type="submit">Filter</Button>
            </form>
          </CardContent>
        </Card>

        {/* Subscriptions Table */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Subscriptions</CardTitle>
            <CardDescription>
              {subscriptions.total} total subscriptions • Showing {subscriptions.from}-{subscriptions.to}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <ModernTable>
              <ModernTableHeader>
                <ModernTableRow>
                  <ModernTableCell header>Name</ModernTableCell>
                  <ModernTableCell header>Price</ModernTableCell>
                  <ModernTableCell header>Billing Cycle</ModernTableCell>
                  <ModernTableCell header>Start Date</ModernTableCell>
                  <ModernTableCell header>Next Billing</ModernTableCell>
                  <ModernTableCell header>Status</ModernTableCell>
                  <ModernTableCell header className="text-right">Actions</ModernTableCell>
                </ModernTableRow>
              </ModernTableHeader>
              <ModernTableBody>
                {subscriptions.data.length === 0 ? (
                  <ModernTableRow>
                    <ModernTableCell colSpan={7} className="text-center py-12 text-muted-foreground">
                      <div className="flex flex-col items-center gap-2">
                        <Search className="h-8 w-8 text-muted-foreground/50" />
                        <p>No subscriptions found</p>
                        <p className="text-sm">Try adjusting your filters or add a new subscription</p>
                      </div>
                    </ModernTableCell>
                  </ModernTableRow>
                ) : (
                  subscriptions.data.map((subscription) => (
                    <ModernTableRow key={subscription.id} clickable>
                      <ModernTableCell className="font-medium">
                        <Link href={route('subscriptions.show', subscription.id)} className="hover:text-primary transition-colors">
                          {subscription.name}
                        </Link>
                      </ModernTableCell>
                      <ModernTableCell className="font-medium">${subscription.price}</ModernTableCell>
                      <ModernTableCell className="capitalize">{subscription.billing_cycle}</ModernTableCell>
                      <ModernTableCell>{formatDate(subscription.start_date)}</ModernTableCell>
                      <ModernTableCell>{formatDate(subscription.next_billing_date)}</ModernTableCell>
                      <ModernTableCell>{getStatusBadge(subscription.status)}</ModernTableCell>
                      <ModernTableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={route('subscriptions.show', subscription.id)}>
                                View Details
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={route('subscriptions.edit', subscription.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(subscription.id)} className="text-red-600">
                              <Trash className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </ModernTableCell>
                    </ModernTableRow>
                  ))
                )}
              </ModernTableBody>
            </ModernTable>
          </CardContent>
        </Card>

        {subscriptions.last_page > 1 && (
          <div className="mt-6">
            <Pagination>
              <PaginationContent>
                {subscriptions.current_page > 1 && (
                  <PaginationItem>
                    <PaginationPrevious href={route('customers.subscriptions.index', { customerId: customer.id, page: subscriptions.current_page - 1, search: searchQuery, status: statusFilter, billing_cycle: billingCycleFilter })} />
                  </PaginationItem>
                )}

                {Array.from({ length: subscriptions.last_page }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      href={route('customers.subscriptions.index', { customerId: customer.id, page, search: searchQuery, status: statusFilter, billing_cycle: billingCycleFilter })}
                      isActive={page === subscriptions.current_page}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                {subscriptions.current_page < subscriptions.last_page && (
                  <PaginationItem>
                    <PaginationNext href={route('customers.subscriptions.index', { customerId: customer.id, page: subscriptions.current_page + 1, search: searchQuery, status: statusFilter, billing_cycle: billingCycleFilter })} />
                  </PaginationItem>
                )}
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
