import React, { useEffect, useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ArrowLeft, Save, CreditCard, Info, Lock } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string | null;
  address: string | null;
  city: string | null;
  state: string | null;
  postal_code: string | null;
  country: string | null;
  status: string;
  mpesa_id: string | null;
}

interface MpesaSettings {
  assignment_mode: string;
  auto_generate_on_create: boolean;
  min_length: number;
  max_length: number;
  allowed_pattern: string;
}

interface EditCustomerProps {
  customer: Customer;
  mpesaSettings: MpesaSettings;
}

export default function EditCustomer({ customer, mpesaSettings }: EditCustomerProps) {
  const [mpesaIdValidation, setMpesaIdValidation] = useState<string>('');

  const { data, setData, put, processing, errors, reset } = useForm({
    name: customer.name || '',
    email: customer.email || '',
    phone: customer.phone || '',
    address: customer.address || '',
    city: customer.city || '',
    state: customer.state || '',
    postal_code: customer.postal_code || '',
    country: customer.country || '',
    status: customer.status || 'active',
    mpesa_id: customer.mpesa_id || '',
  });

  // Determine M-Pesa ID field behavior
  const showMpesaIdField = mpesaSettings.assignment_mode !== 'auto';
  const mpesaIdReadOnly = mpesaSettings.assignment_mode === 'auto';
  const mpesaIdEditable = mpesaSettings.assignment_mode === 'manual' || mpesaSettings.assignment_mode === 'hybrid';

  // Validate M-Pesa ID format
  const validateMpesaId = (value: string) => {
    if (!value) {
      setMpesaIdValidation('');
      return;
    }

    if (value.length < mpesaSettings.min_length) {
      setMpesaIdValidation(`M-Pesa ID must be at least ${mpesaSettings.min_length} characters`);
      return;
    }

    if (value.length > mpesaSettings.max_length) {
      setMpesaIdValidation(`M-Pesa ID must not exceed ${mpesaSettings.max_length} characters`);
      return;
    }

    // Basic pattern validation (alphanumeric uppercase)
    if (!/^[A-Z0-9]+$/.test(value)) {
      setMpesaIdValidation('M-Pesa ID must contain only uppercase letters and numbers');
      return;
    }

    setMpesaIdValidation('');
  };

  const handleMpesaIdChange = (value: string) => {
    setData('mpesa_id', value.toUpperCase());
    validateMpesaId(value.toUpperCase());
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put(route('customers.update', customer.id));
  };

  return (
    <AppLayout>
      <Head title={`Edit Customer: ${customer.name}`} />

      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('customers.show', customer.id)}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Customer
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Edit Customer</h1>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>Update the customer's basic details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name <span className="text-red-500">*</span></Label>
                    <Input
                      id="name"
                      value={data.name}
                      onChange={e => setData('name', e.target.value)}
                      placeholder="Customer name"
                    />
                    {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email <span className="text-red-500">*</span></Label>
                    <Input
                      id="email"
                      type="email"
                      value={data.email}
                      onChange={e => setData('email', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                    {errors.email && <p className="text-red-500 text-sm">{errors.email}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      value={data.phone}
                      onChange={e => setData('phone', e.target.value)}
                      placeholder="+****************"
                    />
                    {errors.phone && <p className="text-red-500 text-sm">{errors.phone}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={data.status}
                      onValueChange={value => setData('status', value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="suspended">Suspended</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.status && <p className="text-red-500 text-sm">{errors.status}</p>}
                  </div>

                  {/* M-Pesa ID Field - Conditional Display */}
                  {showMpesaIdField && (
                    <div className="space-y-2">
                      <Label htmlFor="mpesa_id" className="flex items-center">
                        <CreditCard className="h-4 w-4 mr-2" />
                        M-Pesa ID
                        {mpesaIdReadOnly && <Lock className="h-3 w-3 ml-2 text-gray-400" />}
                      </Label>

                      {mpesaIdEditable ? (
                        <Input
                          id="mpesa_id"
                          value={data.mpesa_id}
                          onChange={e => handleMpesaIdChange(e.target.value)}
                          placeholder="Enter M-Pesa ID"
                          className={mpesaIdValidation ? "border-red-500" : ""}
                        />
                      ) : (
                        <Input
                          id="mpesa_id"
                          value={data.mpesa_id || 'Not assigned'}
                          readOnly
                          className="bg-gray-50 text-gray-600"
                        />
                      )}

                      {mpesaIdValidation && (
                        <p className="text-red-500 text-sm">{mpesaIdValidation}</p>
                      )}
                      {errors.mpesa_id && (
                        <p className="text-red-500 text-sm">{errors.mpesa_id}</p>
                      )}

                      {/* Help text based on assignment mode */}
                      {mpesaSettings.assignment_mode === 'auto' && (
                        <Alert>
                          <Lock className="h-4 w-4" />
                          <AlertDescription>
                            Auto mode: M-Pesa ID is automatically managed and cannot be edited manually.
                          </AlertDescription>
                        </Alert>
                      )}
                      {mpesaSettings.assignment_mode === 'manual' && (
                        <Alert>
                          <Info className="h-4 w-4" />
                          <AlertDescription>
                            Manual mode: You can edit the M-Pesa ID. Ensure it's unique across all customers.
                          </AlertDescription>
                        </Alert>
                      )}
                      {mpesaSettings.assignment_mode === 'hybrid' && (
                        <Alert>
                          <Info className="h-4 w-4" />
                          <AlertDescription>
                            Hybrid mode: You can edit the M-Pesa ID or leave it for automatic management.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              {/* Address Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Address Information</CardTitle>
                  <CardDescription>Update the customer's address details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="address">Street Address</Label>
                    <Input
                      id="address"
                      value={data.address}
                      onChange={e => setData('address', e.target.value)}
                      placeholder="123 Main St"
                    />
                    {errors.address && <p className="text-red-500 text-sm">{errors.address}</p>}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        value={data.city}
                        onChange={e => setData('city', e.target.value)}
                        placeholder="City"
                      />
                      {errors.city && <p className="text-red-500 text-sm">{errors.city}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="state">State/Province</Label>
                      <Input
                        id="state"
                        value={data.state}
                        onChange={e => setData('state', e.target.value)}
                        placeholder="State"
                      />
                      {errors.state && <p className="text-red-500 text-sm">{errors.state}</p>}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="postal_code">Postal Code</Label>
                      <Input
                        id="postal_code"
                        value={data.postal_code}
                        onChange={e => setData('postal_code', e.target.value)}
                        placeholder="Postal Code"
                      />
                      {errors.postal_code && <p className="text-red-500 text-sm">{errors.postal_code}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <Input
                        id="country"
                        value={data.country}
                        onChange={e => setData('country', e.target.value)}
                        placeholder="Country"
                      />
                      {errors.country && <p className="text-red-500 text-sm">{errors.country}</p>}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Submit Button */}
              <Card>
                <CardContent className="pt-6">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={processing}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Update Customer
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
