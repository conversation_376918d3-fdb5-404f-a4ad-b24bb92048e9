import React, { useEffect, useState } from 'react';
import AppLayout from '@/layouts/AppLayout';

export default function TestWebSocket() {
  const [connectionStatus, setConnectionStatus] = useState('Disconnected');
  const [messages, setMessages] = useState<string[]>([]);
  const [testSessionId] = useState(() => Math.random().toString(36).substring(2, 15));

  useEffect(() => {
    if (!window.Echo) {
      setMessages(prev => [...prev, 'Echo not available']);
      return;
    }

    setMessages(prev => [...prev, 'Attempting to connect...']);
    setConnectionStatus('Connecting...');

    try {
      // Test connection to a public channel first
      const publicChannel = window.Echo.channel('test-channel');

      publicChannel.subscribed(() => {
        setConnectionStatus('Connected to public channel');
        setMessages(prev => [...prev, 'Successfully connected to public channel']);
      });

      publicChannel.listen('.test.message', (data: any) => {
        setMessages(prev => [...prev, `Received test message: ${JSON.stringify(data)}`]);
      });

      publicChannel.error((error: any) => {
        setConnectionStatus('Public channel error');
        setMessages(prev => [...prev, `Public channel error: ${JSON.stringify(error)}`]);
      });

      // Test private channel
      const privateChannel = window.Echo.private(`static-ip-provisioning.${testSessionId}`);

      privateChannel.subscribed(() => {
        setConnectionStatus('Connected to private channel');
        setMessages(prev => [...prev, 'Successfully connected to private channel']);
      });

      privateChannel.error((error: any) => {
        setConnectionStatus('Private channel error');
        setMessages(prev => [...prev, `Private channel error: ${JSON.stringify(error)}`]);
      });

      return () => {
        window.Echo.leave('test-channel');
        window.Echo.leave(`static-ip-provisioning.${testSessionId}`);
      };
    } catch (error) {
      setConnectionStatus('Connection failed');
      setMessages(prev => [...prev, `Connection error: ${error}`]);
    }
  }, [testSessionId]);

  const testBroadcast = async () => {
    try {
      const response = await fetch('/test-broadcast', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        body: JSON.stringify({ sessionId: testSessionId }),
      });

      const result = await response.json();
      setMessages(prev => [...prev, `Broadcast test: ${JSON.stringify(result)}`]);
    } catch (error) {
      setMessages(prev => [...prev, `Broadcast test error: ${error}`]);
    }
  };

  return (
    <AppLayout title="WebSocket Connection Test">
      <div className="page-container">
        <div className="page-header">
          <h1 className="page-title">WebSocket Connection Test</h1>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="mb-4">
            <h2 className="text-lg font-semibold mb-2">Connection Status</h2>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                connectionStatus.includes('Connected') ? 'bg-green-500' :
                connectionStatus.includes('Connecting') ? 'bg-yellow-500' : 'bg-red-500'
              }`}></div>
              <span>{connectionStatus}</span>
            </div>
          </div>

          <div className="mb-4">
            <h2 className="text-lg font-semibold mb-2">Test Session ID</h2>
            <code className="bg-gray-100 px-2 py-1 rounded">{testSessionId}</code>
          </div>

          <div className="mb-4">
            <button
              onClick={testBroadcast}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Test Broadcast
            </button>
          </div>

          <div>
            <h2 className="text-lg font-semibold mb-2">Messages</h2>
            <div className="bg-gray-50 p-4 rounded max-h-64 overflow-y-auto">
              {messages.map((message, index) => (
                <div key={index} className="text-sm mb-1 font-mono">
                  {new Date().toLocaleTimeString()}: {message}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
