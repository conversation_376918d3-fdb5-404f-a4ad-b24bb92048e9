import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ArrowLeft, Save, Database, Plus, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export default function EditIpPool({ ipPool, devices }) {
  const { data, setData, put, processing, errors } = useForm({
    name: ipPool.name || '',
    description: ipPool.description || '',
    gateway: ipPool.gateway || '',
    dns_servers: ipPool.dns_servers || ['*******', '*******'],
    excluded_ips: ipPool.excluded_ips || [],
    status: ipPool.status || 'active',
  });

  const [newExcludedIp, setNewExcludedIp] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    put(route('services.ip-pools.update', ipPool.id));
  };

  const addExcludedIp = () => {
    if (newExcludedIp && !data.excluded_ips.includes(newExcludedIp)) {
      setData('excluded_ips', [...data.excluded_ips, newExcludedIp]);
      setNewExcludedIp('');
    }
  };

  const removeExcludedIp = (ip) => {
    setData('excluded_ips', data.excluded_ips.filter(item => item !== ip));
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-yellow-500">Inactive</Badge>;
      case 'depleted':
        return <Badge className="bg-red-500">Depleted</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  return (
    <>
      <Head title={`Edit IP Pool - ${ipPool.name}`} />
      
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('services.ip-pools.show', ipPool.id)}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to IP Pool Details
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Edit IP Pool</h1>
          </div>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>Edit the basic details for this IP pool</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Pool Name <span className="text-red-500">*</span></Label>
                    <Input 
                      id="name" 
                      value={data.name} 
                      onChange={e => setData('name', e.target.value)}
                      placeholder="e.g. Customer Static IPs"
                    />
                    {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea 
                      id="description" 
                      value={data.description} 
                      onChange={e => setData('description', e.target.value)}
                      placeholder="Add a description for this IP pool"
                      rows={3}
                    />
                    {errors.description && <p className="text-red-500 text-sm">{errors.description}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">Pool Status</Label>
                    <Select 
                      value={data.status} 
                      onValueChange={value => setData('status', value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="depleted">Depleted</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.status && <p className="text-red-500 text-sm">{errors.status}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Router Device (read-only)</Label>
                    <div className="p-2 bg-gray-50 rounded border">
                      <Link href={route('network.devices.show', ipPool.device.id)} className="text-blue-500 hover:underline">
                        {ipPool.device.name} ({ipPool.device.ip_address})
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Network Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle>Network Configuration</CardTitle>
                  <CardDescription>Edit the network details for this IP pool</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Network Address (read-only)</Label>
                      <div className="p-2 bg-gray-50 rounded border font-mono">
                        {ipPool.network_address}
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Subnet Mask (read-only)</Label>
                      <div className="p-2 bg-gray-50 rounded border font-mono">
                        {ipPool.subnet_mask}
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="gateway">Gateway</Label>
                      <Input 
                        id="gateway" 
                        value={data.gateway} 
                        onChange={e => setData('gateway', e.target.value)}
                        placeholder="e.g. ***********"
                      />
                      {errors.gateway && <p className="text-red-500 text-sm">{errors.gateway}</p>}
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="dns_servers">DNS Servers</Label>
                      <Input 
                        id="dns_servers" 
                        value={Array.isArray(data.dns_servers) ? data.dns_servers.join(', ') : data.dns_servers} 
                        onChange={e => setData('dns_servers', e.target.value.split(',').map(s => s.trim()))}
                        placeholder="e.g. *******, *******"
                      />
                      <p className="text-sm text-gray-500">Separate multiple DNS servers with commas</p>
                      {errors.dns_servers && <p className="text-red-500 text-sm">{errors.dns_servers}</p>}
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Excluded IPs */}
              <Card>
                <CardHeader>
                  <CardTitle>Excluded IP Addresses</CardTitle>
                  <CardDescription>Specify IP addresses that should not be assigned to customers</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex space-x-2">
                    <Input 
                      value={newExcludedIp} 
                      onChange={e => setNewExcludedIp(e.target.value)}
                      placeholder="e.g. ***********"
                      className="flex-1"
                    />
                    <Button type="button" onClick={addExcludedIp}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add
                    </Button>
                  </div>
                  
                  {data.excluded_ips.length > 0 ? (
                    <div className="space-y-2">
                      <Label>Excluded IP Addresses</Label>
                      <div className="flex flex-wrap gap-2">
                        {data.excluded_ips.map(ip => (
                          <div key={ip} className="flex items-center bg-gray-100 rounded-md px-3 py-1">
                            <span className="font-mono text-sm">{ip}</span>
                            <Button 
                              type="button" 
                              variant="ghost" 
                              size="sm" 
                              className="h-6 w-6 p-0 ml-2" 
                              onClick={() => removeExcludedIp(ip)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No IP addresses excluded. Consider excluding gateway, network equipment, and other reserved IPs.</p>
                  )}
                  
                  {errors.excluded_ips && <p className="text-red-500 text-sm">{errors.excluded_ips}</p>}
                </CardContent>
              </Card>
            </div>
            
            {/* Summary Card */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>IP Pool Summary</CardTitle>
                  <CardDescription>Review the IP pool details before updating</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-center p-6 bg-gray-50 rounded-md">
                    <Database className="h-16 w-16 text-primary" />
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="font-medium">Pool ID:</h3>
                    <p className="text-gray-700">{ipPool.id}</p>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="font-medium">Pool Name:</h3>
                    <p className="text-gray-700">{data.name}</p>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="font-medium">Network:</h3>
                    <p className="text-gray-700 font-mono">{ipPool.network_address}/{ipPool.subnet_mask}</p>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="font-medium">Current Status:</h3>
                    <p className="text-gray-700">{getStatusBadge(ipPool.status)}</p>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="font-medium">New Status:</h3>
                    <p className="text-gray-700">{getStatusBadge(data.status)}</p>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="font-medium">Usage:</h3>
                    <p className="text-gray-700">
                      {ipPool.used_ips} / {ipPool.total_ips} IPs ({ipPool.usage_percentage}%)
                    </p>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col space-y-2">
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={processing}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Update IP Pool
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    className="w-full" 
                    asChild
                  >
                    <Link href={route('services.ip-pools.show', ipPool.id)}>
                      Cancel
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
              
              <Alert>
                <AlertTitle>Important Note</AlertTitle>
                <AlertDescription>
                  This will update the IP pool on the MikroTik router. If you change the status to inactive, new IPs will not be assigned from this pool.
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </form>
      </div>
    </>
  );
}
