import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Ta<PERSON>, 
  <PERSON><PERSON><PERSON>onte<PERSON>, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Database, 
  AlertTriangle, 
  Server, 
  Network, 
  CheckCircle2,
  XCircle,
  Plus
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import AppLayout from '@/layouts/app-layout';

export default function ShowIpPool({ ipPool }) {
  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-yellow-500">Inactive</Badge>;
      case 'depleted':
        return <Badge className="bg-red-500">Depleted</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const getUsageColor = (percentage) => {
    if (percentage < 50) return 'bg-green-500';
    if (percentage < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <AppLayout>
      <Head title={`IP Pool - ${ipPool.name}`} />
      
      <div className="page-container">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('services.ip-pools.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to IP Pools
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">IP Pool Details</h1>
          </div>
          <div className="flex space-x-2">
            <Button asChild variant="outline">
              <Link href={route('services.ip-pools.edit', ipPool.id)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit IP Pool
              </Link>
            </Button>
            <Button asChild variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-100">
              <Link href={route('services.static-ip.create', { ip_pool_id: ipPool.id })}>
                <Plus className="h-4 w-4 mr-2" />
                Create Static IP Service
              </Link>
            </Button>
            {ipPool.status === 'active' ? (
              <Button asChild variant="outline" className="bg-yellow-50 text-yellow-700 hover:bg-yellow-100">
                <Link href={route('services.ip-pools.update', ipPool.id)} method="put" data={{ status: 'inactive' }} as="button">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Deactivate Pool
                </Link>
              </Button>
            ) : (
              <Button asChild variant="outline" className="bg-green-50 text-green-700 hover:bg-green-100">
                <Link href={route('services.ip-pools.update', ipPool.id)} method="put" data={{ status: 'active' }} as="button">
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  Activate Pool
                </Link>
              </Button>
            )}
            <Button asChild variant="destructive">
              <Link href={route('services.ip-pools.destroy', ipPool.id)} method="delete" as="button">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Pool
              </Link>
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Tabs defaultValue="overview">
              <TabsList className="mb-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="services">IP Services</TabsTrigger>
                <TabsTrigger value="network">Network Details</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-6">
                {/* Pool Status Card */}
                <Card>
                  <CardHeader>
                    <CardTitle>Pool Status</CardTitle>
                    <CardDescription>Current status of the IP pool</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {ipPool.status === 'active' ? (
                          <Database className="h-8 w-8 text-green-500 mr-4" />
                        ) : (
                          <XCircle className="h-8 w-8 text-yellow-500 mr-4" />
                        )}
                        <div>
                          <h3 className="text-lg font-medium">Status: {getStatusBadge(ipPool.status)}</h3>
                          <p className="text-gray-500">
                            {ipPool.status === 'active' 
                              ? 'Pool is active and available for IP assignment' 
                              : ipPool.status === 'inactive'
                                ? 'Pool is inactive and not available for IP assignment'
                                : 'Pool is depleted and has no available IPs'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                {/* Usage Card */}
                <Card>
                  <CardHeader>
                    <CardTitle>IP Usage</CardTitle>
                    <CardDescription>Current usage of IP addresses in this pool</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-medium">
                          {ipPool.used_ips} / {ipPool.total_ips} IPs Used
                        </h3>
                        <p className="text-gray-500">
                          {ipPool.available_ips} IPs available for assignment
                        </p>
                      </div>
                      <div className="text-xl font-bold">
                        {ipPool.usage_percentage}%
                      </div>
                    </div>
                    
                    <Progress 
                      value={ipPool.usage_percentage} 
                      className={getUsageColor(ipPool.usage_percentage)}
                    />
                    
                    {ipPool.usage_percentage > 80 && (
                      <Alert variant="warning" className="mt-4">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertTitle>Pool Nearly Depleted</AlertTitle>
                        <AlertDescription>
                          This IP pool is nearly depleted. Consider adding a new IP pool soon.
                        </AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
                
                {/* Pool Details Card */}
                <Card>
                  <CardHeader>
                    <CardTitle>Pool Details</CardTitle>
                    <CardDescription>Basic information about this IP pool</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Pool Name</h3>
                          <p>{ipPool.name}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Network Address</h3>
                          <p className="font-mono">{ipPool.network_address}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Subnet Mask</h3>
                          <p className="font-mono">{ipPool.subnet_mask}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">CIDR Notation</h3>
                          <p className="font-mono">{ipPool.cidr}</p>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Gateway</h3>
                          <p className="font-mono">{ipPool.gateway || 'Not set'}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">DNS Servers</h3>
                          <p className="font-mono">
                            {Array.isArray(ipPool.dns_servers) 
                              ? ipPool.dns_servers.join(', ') 
                              : ipPool.dns_servers || 'Not set'}
                          </p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Created</h3>
                          <p>{new Date(ipPool.created_at).toLocaleString()}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Last Updated</h3>
                          <p>{new Date(ipPool.updated_at).toLocaleString()}</p>
                        </div>
                      </div>
                    </div>
                    
                    {ipPool.description && (
                      <div className="mt-4 p-3 bg-gray-50 rounded-md">
                        <h3 className="text-sm font-medium text-gray-500">Description</h3>
                        <p className="whitespace-pre-line">{ipPool.description}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="services">
                <Card>
                  <CardHeader>
                    <CardTitle>IP Services</CardTitle>
                    <CardDescription>Static IP services using addresses from this pool</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {ipPool.staticIpServices && ipPool.staticIpServices.length > 0 ? (
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>IP Address</TableHead>
                              <TableHead>Customer</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {ipPool.staticIpServices.map(service => (
                              <TableRow key={service.id}>
                                <TableCell className="font-mono">
                                  <Link href={route('services.static-ip.show', service.id)} className="text-blue-500 hover:underline">
                                    {service.ip_address}
                                  </Link>
                                </TableCell>
                                <TableCell>
                                  <Link href={route('customers.show', service.customer_id)} className="text-blue-500 hover:underline">
                                    {service.customer.name}
                                  </Link>
                                </TableCell>
                                <TableCell>{getStatusBadge(service.status)}</TableCell>
                                <TableCell className="text-right">
                                  <Button asChild variant="outline" size="sm">
                                    <Link href={route('services.static-ip.show', service.id)}>
                                      View
                                    </Link>
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <Database className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium">No IP Services</h3>
                        <p className="text-gray-500 max-w-md mx-auto mt-2">
                          There are no static IP services using addresses from this pool yet.
                        </p>
                        <Button asChild className="mt-4">
                          <Link href={route('services.static-ip.create', { ip_pool_id: ipPool.id })}>
                            <Plus className="h-4 w-4 mr-2" />
                            Create Static IP Service
                          </Link>
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="network">
                <Card>
                  <CardHeader>
                    <CardTitle>Network Configuration</CardTitle>
                    <CardDescription>Network details for this IP pool</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center">
                      <Network className="h-8 w-8 text-blue-500 mr-4" />
                      <div>
                        <h3 className="text-lg font-medium">Network Details</h3>
                        <p className="text-gray-500">
                          Configuration for the IP pool on the router
                        </p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Network Address</h3>
                          <p className="font-mono">{ipPool.network_address}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Subnet Mask</h3>
                          <p className="font-mono">{ipPool.subnet_mask}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">CIDR Notation</h3>
                          <p className="font-mono">{ipPool.cidr}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Total IPs</h3>
                          <p>{ipPool.total_ips}</p>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Gateway</h3>
                          <p className="font-mono">{ipPool.gateway || 'Not set'}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">DNS Servers</h3>
                          <p className="font-mono">
                            {Array.isArray(ipPool.dns_servers) 
                              ? ipPool.dns_servers.join(', ') 
                              : ipPool.dns_servers || 'Not set'}
                          </p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Router Device</h3>
                          <p>
                            <Link href={route('network.devices.show', ipPool.device.id)} className="text-blue-500 hover:underline">
                              {ipPool.device.name}
                            </Link>
                          </p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">MikroTik Pool ID</h3>
                          <p className="font-mono">{ipPool.mikrotik_pool_id || 'Not available'}</p>
                        </div>
                      </div>
                    </div>
                    
                    {ipPool.excluded_ips && ipPool.excluded_ips.length > 0 && (
                      <div className="space-y-2">
                        <h3 className="text-sm font-medium text-gray-500">Excluded IP Addresses</h3>
                        <div className="flex flex-wrap gap-2">
                          {ipPool.excluded_ips.map(ip => (
                            <div key={ip} className="bg-gray-100 rounded-md px-3 py-1">
                              <span className="font-mono text-sm">{ip}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div className="p-4 bg-gray-50 rounded-md">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">MikroTik Configuration</h3>
                      <pre className="text-xs font-mono bg-gray-100 p-3 rounded overflow-x-auto">
                        {`/ip pool add name=${ipPool.name} ranges=${ipPool.network_address.split('.').slice(0, 3).join('.')}.10-${ipPool.network_address.split('.').slice(0, 3).join('.')}.254 comment="${ipPool.description || 'IP pool for ' + ipPool.cidr}"`}
                      </pre>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
          
          <div className="space-y-6">
            {/* Summary Card */}
            <Card>
              <CardHeader>
                <CardTitle>IP Pool Summary</CardTitle>
                <CardDescription>Quick overview of this IP pool</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-center p-6 bg-gray-50 rounded-md">
                  <Database className="h-16 w-16 text-primary" />
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium">Pool Name:</h3>
                  <p className="text-gray-700">{ipPool.name}</p>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium">Network:</h3>
                  <p className="text-gray-700 font-mono">{ipPool.cidr}</p>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium">Status:</h3>
                  <p className="text-gray-700">{getStatusBadge(ipPool.status)}</p>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium">IP Usage:</h3>
                  <div className="flex flex-col gap-1">
                    <div className="flex justify-between text-sm">
                      <span>{ipPool.used_ips} / {ipPool.total_ips} IPs</span>
                      <span>{ipPool.usage_percentage}%</span>
                    </div>
                    <Progress 
                      value={ipPool.usage_percentage} 
                      className={getUsageColor(ipPool.usage_percentage)}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium">Available IPs:</h3>
                  <p className="text-gray-700">{ipPool.available_ips} IPs available</p>
                </div>
              </CardContent>
              <CardFooter>
                <Button asChild className="w-full">
                  <Link href={route('services.static-ip.create', { ip_pool_id: ipPool.id })}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Static IP Service
                  </Link>
                </Button>
              </CardFooter>
            </Card>
            
            {/* Router Device Card */}
            <Card>
              <CardHeader>
                <CardTitle>Router Device</CardTitle>
                <CardDescription>Device hosting this IP pool</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <Server className="h-10 w-10 text-primary bg-primary/10 p-2 rounded-full mr-4" />
                  <div>
                    <Link href={route('network.devices.show', ipPool.device.id)} className="text-lg font-medium text-blue-500 hover:underline">
                      {ipPool.device.name}
                    </Link>
                    <p className="text-gray-500">{ipPool.device.ip_address}</p>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Type</h3>
                  <p>{ipPool.device.type}</p>
                </div>
                
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Model</h3>
                  <p>{ipPool.device.model || 'Not specified'}</p>
                </div>
                
                <Button asChild variant="outline" className="w-full">
                  <Link href={route('network.devices.show', ipPool.device.id)}>
                    View Device Details
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
