import { useState, useEffect } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import axios from 'axios';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ArrowLeft, Save, Database, Plus, X } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

export default function CreateIpPool({ devices }) {
  const { data, setData, post, processing, errors, reset } = useForm({
    name: '',
    description: '',
    network_cidr: '',
    network_address: '',
    subnet_mask: '*************',
    gateway: '',
    dns_servers: ['*******', '*******'],
    device_id: '',
    interface: '',
    excluded_ips: []
  });

  const [newExcludedIp, setNewExcludedIp] = useState('');
  const [interfaces, setInterfaces] = useState([]);
  const [loadingInterfaces, setLoadingInterfaces] = useState(false);
  const [interfaceError, setInterfaceError] = useState('');
  const [cidrError, setCidrError] = useState('');

  // Parse CIDR notation and auto-generate network details
  const parseCidr = (cidr) => {
    setCidrError('');

    if (!cidr) {
      setData(prev => ({
        ...prev,
        network_address: '',
        gateway: '',
        excluded_ips: []
      }));
      return;
    }

    // Validate CIDR format (e.g., ***********/24)
    const cidrRegex = /^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\/(\d{1,2})$/;
    const match = cidr.match(cidrRegex);

    if (!match) {
      setCidrError('Invalid CIDR format. Use format like ***********/24');
      return;
    }

    const [, networkIp, prefixLength] = match;
    const prefix = parseInt(prefixLength);

    // Validate prefix length
    if (prefix < 16 || prefix > 30) {
      setCidrError('Prefix length must be between /16 and /30');
      return;
    }

    // Validate IP address format
    const ipParts = networkIp.split('.').map(Number);
    if (ipParts.some(part => part < 0 || part > 255)) {
      setCidrError('Invalid IP address');
      return;
    }

    // Calculate network base and gateway
    const networkBase = networkIp;
    const ipSegments = networkBase.split('.');
    const gateway = `${ipSegments[0]}.${ipSegments[1]}.${ipSegments[2]}.1`;
    const networkAddress = `${ipSegments[0]}.${ipSegments[1]}.${ipSegments[2]}.0`;

    // Auto-exclude network (.0) and gateway (.1)
    const autoExcluded = [networkAddress, gateway];

    setData(prev => ({
      ...prev,
      network_address: networkBase,
      gateway: gateway,
      excluded_ips: autoExcluded
    }));
  };

  // Handle CIDR input change
  const handleCidrChange = (value) => {
    setData('network_cidr', value);
    parseCidr(value);
  };

  // Load interfaces when device is selected
  const loadInterfaces = async (deviceId) => {
    if (!deviceId) {
      setInterfaces([]);
      setData('interface', '');
      return;
    }

    setLoadingInterfaces(true);
    setInterfaceError('');

    try {
      const response = await axios.get(route('services.ip-pools.device-interfaces', deviceId));

      if (response.data.success) {
        setInterfaces(response.data.interfaces);
        setData('interface', ''); // Reset interface selection
      } else {
        setInterfaceError(response.data.error || 'Failed to load interfaces');
        setInterfaces([]);
      }
    } catch (error) {
      setInterfaceError('Failed to load interfaces from device');
      setInterfaces([]);
      console.error('Interface loading error:', error);
    } finally {
      setLoadingInterfaces(false);
    }
  };

  // Load interfaces when device changes
  useEffect(() => {
    if (data.device_id) {
      loadInterfaces(data.device_id);
    }
  }, [data.device_id]);

  const handleSubmit = (e) => {
    e.preventDefault();
    post(route('services.ip-pools.store'));
  };

  const addExcludedIp = () => {
    if (newExcludedIp && !data.excluded_ips.includes(newExcludedIp)) {
      setData('excluded_ips', [...data.excluded_ips, newExcludedIp]);
      setNewExcludedIp('');
    }
  };

  const removeExcludedIp = (ip) => {
    setData('excluded_ips', data.excluded_ips.filter(item => item !== ip));
  };

  // Get auto-excluded IPs (network .0 and gateway .1)
  const getAutoExcludedIps = () => {
    if (!data.network_cidr || cidrError) return [];

    const match = data.network_cidr.match(/^(\d{1,3}\.\d{1,3}\.\d{1,3})\.\d{1,3}\/\d{1,2}$/);
    if (!match) return [];

    const networkBase = match[1];
    return [`${networkBase}.0`, `${networkBase}.1`];
  };

  // Get additional excluded IPs (user-added)
  const getAdditionalExcludedIps = () => {
    const autoExcluded = getAutoExcludedIps();
    return data.excluded_ips.filter(ip => !autoExcluded.includes(ip));
  };

  return (
    <AppLayout>
      <Head title="Create IP Pool" />

      <div className="page-container">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('services.ip-pools.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to IP Pools
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Create IP Pool</h1>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>Enter the basic details for this IP pool</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Pool Name <span className="text-red-500">*</span></Label>
                    <Input
                      id="name"
                      value={data.name}
                      onChange={e => setData('name', e.target.value)}
                      placeholder="e.g. Customer Static IPs"
                    />
                    {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={data.description}
                      onChange={e => setData('description', e.target.value)}
                      placeholder="Add a description for this IP pool"
                      rows={3}
                    />
                    {errors.description && <p className="text-red-500 text-sm">{errors.description}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="device_id">Router Device <span className="text-red-500">*</span></Label>
                    <Select
                      value={data.device_id}
                      onValueChange={value => setData('device_id', value)}
                    >
                      <SelectTrigger id="device_id">
                        <SelectValue placeholder="Select a router" />
                      </SelectTrigger>
                      <SelectContent>
                        {devices.map(device => (
                          <SelectItem key={device.id} value={device.id.toString()}>
                            {device.name} ({device.ip_address})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.device_id && <p className="text-red-500 text-sm">{errors.device_id}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="interface">Network Interface <span className="text-red-500">*</span></Label>
                    <Select
                      value={data.interface}
                      onValueChange={value => setData('interface', value)}
                      disabled={!data.device_id || loadingInterfaces}
                    >
                      <SelectTrigger id="interface">
                        <SelectValue placeholder={
                          !data.device_id
                            ? "Select a router first"
                            : loadingInterfaces
                              ? "Loading interfaces..."
                              : "Select an interface"
                        } />
                      </SelectTrigger>
                      <SelectContent>
                        {interfaces.map(networkInterface => (
                          <SelectItem key={networkInterface.name} value={networkInterface.name}>
                            {networkInterface.display_name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {interfaceError && <p className="text-red-500 text-sm">{interfaceError}</p>}
                    {errors.interface && <p className="text-red-500 text-sm">{errors.interface}</p>}
                    {data.device_id && !loadingInterfaces && interfaces.length === 0 && !interfaceError && (
                      <p className="text-yellow-600 text-sm">No usable interfaces found on this device</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Network Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle>Network Configuration</CardTitle>
                  <CardDescription>Configure the network details for this IP pool</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* CIDR Input */}
                  <div className="space-y-2">
                    <Label htmlFor="network_cidr">Network Address (CIDR) <span className="text-red-500">*</span></Label>
                    <Input
                      id="network_cidr"
                      value={data.network_cidr}
                      onChange={e => handleCidrChange(e.target.value)}
                      placeholder="e.g. ***********/24, ***********/26, ***********/25"
                    />
                    <p className="text-sm text-gray-500">
                      Examples: ***********/24 (254 IPs), ***********/26 (62 IPs), ***********/25 (126 IPs)
                    </p>
                    {cidrError && <p className="text-red-500 text-sm">{cidrError}</p>}
                    {errors.network_address && <p className="text-red-500 text-sm">{errors.network_address}</p>}
                  </div>

                  {/* Auto-generated fields */}
                  {data.network_cidr && !cidrError && (
                    <div className="bg-blue-50 p-4 rounded-lg space-y-3">
                      <h4 className="font-medium text-blue-900">Auto-generated Configuration</h4>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label className="text-blue-700">Gateway Address</Label>
                          <Input
                            value={data.gateway}
                            readOnly
                            className="bg-blue-100 border-blue-200"
                          />
                          <p className="text-xs text-blue-600">Always set to .1 address</p>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-blue-700">Subnet Mask</Label>
                          <Input
                            value={data.subnet_mask}
                            readOnly
                            className="bg-blue-100 border-blue-200"
                          />
                          <p className="text-xs text-blue-600">Always ************* for private networks</p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-blue-700">DNS Servers</Label>
                        <Input
                          value={data.dns_servers.join(', ')}
                          onChange={e => setData('dns_servers', e.target.value.split(',').map(s => s.trim()))}
                          className="bg-white border-blue-200"
                        />
                        <p className="text-xs text-blue-600">Default: Google DNS (*******) + Level3 DNS (*******)</p>
                      </div>
                    </div>
                  )}

                  {errors.gateway && <p className="text-red-500 text-sm">{errors.gateway}</p>}
                  {errors.subnet_mask && <p className="text-red-500 text-sm">{errors.subnet_mask}</p>}
                  {errors.dns_servers && <p className="text-red-500 text-sm">{errors.dns_servers}</p>}
                </CardContent>
              </Card>

              {/* Excluded IPs */}
              <Card>
                <CardHeader>
                  <CardTitle>IP Address Exclusions</CardTitle>
                  <CardDescription>IP addresses that will not be assigned to customers</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Auto-excluded IPs */}
                  {getAutoExcludedIps().length > 0 && (
                    <div className="space-y-2">
                      <Label className="text-green-700">Automatically Excluded</Label>
                      <div className="bg-green-50 p-3 rounded-lg">
                        <div className="flex flex-wrap gap-2">
                          {getAutoExcludedIps().map(ip => (
                            <div key={ip} className="flex items-center bg-green-100 border border-green-200 rounded-md px-3 py-1">
                              <span className="font-mono text-sm text-green-800">{ip}</span>
                              <span className="ml-2 text-xs text-green-600">
                                {ip.endsWith('.0') ? '(network)' : '(gateway)'}
                              </span>
                            </div>
                          ))}
                        </div>
                        <p className="text-xs text-green-600 mt-2">
                          Network address (.0) and gateway (.1) are automatically excluded to prevent conflicts
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Additional exclusions */}
                  <div className="space-y-2">
                    <Label>Additional Exclusions</Label>
                    <div className="flex space-x-2">
                      <Input
                        value={newExcludedIp}
                        onChange={e => setNewExcludedIp(e.target.value)}
                        placeholder="e.g. ************, ************"
                        className="flex-1"
                      />
                      <Button type="button" onClick={addExcludedIp}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add
                      </Button>
                    </div>
                    <p className="text-sm text-gray-500">
                      Add any additional IPs to exclude (servers, printers, etc.)
                    </p>
                  </div>

                  {getAdditionalExcludedIps().length > 0 && (
                    <div className="space-y-2">
                      <Label>User-defined Exclusions</Label>
                      <div className="flex flex-wrap gap-2">
                        {getAdditionalExcludedIps().map(ip => (
                          <div key={ip} className="flex items-center bg-gray-100 rounded-md px-3 py-1">
                            <span className="font-mono text-sm">{ip}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 ml-2"
                              onClick={() => removeExcludedIp(ip)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {errors.excluded_ips && <p className="text-red-500 text-sm">{errors.excluded_ips}</p>}
                </CardContent>
              </Card>
            </div>

            {/* Summary Card */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>IP Pool Summary</CardTitle>
                  <CardDescription>Review the IP pool details before creating</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-center p-6 bg-gray-50 rounded-md">
                    <Database className="h-16 w-16 text-primary" />
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Pool Name:</h3>
                    <p className="text-gray-700">
                      {data.name || 'Not set'}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Network (CIDR):</h3>
                    <p className="text-gray-700 font-mono">
                      {data.network_cidr || 'Not set'}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Gateway:</h3>
                    <p className="text-gray-700 font-mono">
                      {data.gateway || 'Auto-generated'}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">DNS Servers:</h3>
                    <p className="text-gray-700 font-mono">
                      {data.dns_servers.join(', ')}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Router Device:</h3>
                    <p className="text-gray-700">
                      {data.device_id ? devices.find(d => d.id.toString() === data.device_id)?.name : 'None selected'}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Interface:</h3>
                    <p className="text-gray-700 font-mono">
                      {data.interface ? interfaces.find(netInterface => netInterface.name === data.interface)?.display_name || data.interface : 'None selected'}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Excluded IPs:</h3>
                    <div className="text-gray-700 font-mono text-sm">
                      {getAutoExcludedIps().length > 0 && (
                        <div className="text-green-600">
                          Auto: {getAutoExcludedIps().join(', ')}
                        </div>
                      )}
                      {getAdditionalExcludedIps().length > 0 && (
                        <div className="text-gray-600">
                          Manual: {getAdditionalExcludedIps().join(', ')}
                        </div>
                      )}
                      {data.excluded_ips.length === 0 && (
                        <span className="text-gray-500">None</span>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col space-y-2">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={processing}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Create IP Pool
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => reset()}
                  >
                    Reset Form
                  </Button>
                </CardFooter>
              </Card>

              <Alert>
                <AlertTitle>Important Note</AlertTitle>
                <AlertDescription>
                  This will create an IP pool on the selected MikroTik router. Make sure the router is online and accessible.
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
