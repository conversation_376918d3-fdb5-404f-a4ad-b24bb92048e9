import React, { useState } from 'react';
import { Head, Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  MoreHorizontal,
  Plus,
  Search,
  RefreshCw,
  Download
} from 'lucide-react';

export default function IpPoolIndex({ ipPools, filters }) {
  const [searchQuery, setSearchQuery] = useState(filters.search || '');
  const [statusFilter, setStatusFilter] = useState(filters.status || '');

  const handleSearch = (e) => {
    e.preventDefault();
    window.location.href = route('services.ip-pools.index', {
      search: searchQuery,
      status: statusFilter === 'all' ? '' : statusFilter
    });
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-yellow-500">Inactive</Badge>;
      case 'depleted':
        return <Badge className="bg-red-500">Depleted</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const getUsageColor = (percentage) => {
    if (percentage < 50) return 'bg-green-500';
    if (percentage < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <AppLayout>
      <Head title="IP Pools" />

      <div className="container mx-auto p-6 py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">IP Pools</h1>
          <Button asChild>
            <Link href={route('services.ip-pools.create')}>
              <Plus className="mr-2 h-4 w-4" /> New IP Pool
            </Link>
          </Button>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filters</CardTitle>
            <CardDescription>Filter the list of IP pools</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search by name or network..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                  icon={<Search className="h-4 w-4" />}
                />
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="depleted">Depleted</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button type="submit">
                <Search className="mr-2 h-4 w-4" /> Search
              </Button>
              <Button variant="outline" type="button" onClick={() => window.location.href = route('services.ip-pools.index')}>
                <RefreshCw className="mr-2 h-4 w-4" /> Reset
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>IP Pools</CardTitle>
            <CardDescription>
              Showing {ipPools.data.length} of {ipPools.total} IP pools
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Network</TableHead>
                    <TableHead>Gateway</TableHead>
                    <TableHead>Device</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {ipPools.data.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        No IP pools found. <Link href={route('services.ip-pools.create')} className="text-blue-500 hover:underline">Create one</Link>
                      </TableCell>
                    </TableRow>
                  ) : (
                    ipPools.data.map((pool) => (
                      <TableRow key={pool.id}>
                        <TableCell className="font-medium">
                          <Link href={route('services.ip-pools.show', pool.id)} className="text-blue-500 hover:underline">
                            {pool.name}
                          </Link>
                        </TableCell>
                        <TableCell>{pool.network_address}/{pool.subnet_mask}</TableCell>
                        <TableCell>{pool.gateway || 'None'}</TableCell>
                        <TableCell>
                          <Link href={route('network.devices.show', pool.device.id)} className="text-blue-500 hover:underline">
                            {pool.device.name}
                          </Link>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            <div className="flex justify-between text-xs">
                              <span>{pool.used_ips} / {pool.total_ips} IPs</span>
                              <span>{pool.usage_percentage}%</span>
                            </div>
                            <Progress
                              value={pool.usage_percentage}
                              className={getUsageColor(pool.usage_percentage)}
                            />
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(pool.status)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={route('services.ip-pools.show', pool.id)}>View Details</Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link href={route('services.ip-pools.edit', pool.id)}>Edit IP Pool</Link>
                              </DropdownMenuItem>
                              {pool.status === 'active' ? (
                                <DropdownMenuItem asChild>
                                  <Link href={route('services.ip-pools.update', pool.id)} method="put" data={{ status: 'inactive' }} as="button">
                                    Deactivate Pool
                                  </Link>
                                </DropdownMenuItem>
                              ) : null}
                              {pool.status === 'inactive' ? (
                                <DropdownMenuItem asChild>
                                  <Link href={route('services.ip-pools.update', pool.id)} method="put" data={{ status: 'active' }} as="button">
                                    Activate Pool
                                  </Link>
                                </DropdownMenuItem>
                              ) : null}
                              <DropdownMenuItem asChild>
                                <Link href={route('services.ip-pools.destroy', pool.id)} method="delete" as="button" className="text-red-500">
                                  Delete Pool
                                </Link>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                Showing {ipPools.from || 0} to {ipPools.to || 0} of {ipPools.total} IP pools
              </div>
              <div className="flex gap-2">
                {ipPools.prev_page_url && (
                  <Button variant="outline" asChild>
                    <Link href={ipPools.prev_page_url}>Previous</Link>
                  </Button>
                )}
                {ipPools.next_page_url && (
                  <Button variant="outline" asChild>
                    <Link href={ipPools.next_page_url}>Next</Link>
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
