import React, { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  MoreHorizontal,
  Plus,
  Search,
  RefreshCw,
  Download
} from 'lucide-react';

export default function StaticIpServiceIndex({ services, filters }) {
  const [searchQuery, setSearchQuery] = useState(filters.search || '');
  const [statusFilter, setStatusFilter] = useState(filters.status || '');
  const [ipPoolFilter, setIpPoolFilter] = useState(filters.ip_pool_id || '');

  const handleSearch = (e) => {
    e.preventDefault();
    window.location.href = route('services.static-ip.index', {
      search: searchQuery,
      status: statusFilter === 'all' ? '' : statusFilter,
      ip_pool_id: ipPoolFilter === 'all' ? '' : ipPoolFilter
    });
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'suspended':
        return <Badge className="bg-yellow-500">Suspended</Badge>;
      case 'terminated':
        return <Badge className="bg-red-500">Terminated</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  return (
    <AppLayout>
      <Head title="Static IP Services" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <h1>Static IP Services</h1>
            <p>Manage static IP service assignments and configurations</p>
          </div>
          <Button asChild>
            <Link href={route('services.static-ip.create')}>
              <Plus className="mr-2 h-4 w-4" /> New Static IP Service
            </Link>
          </Button>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filters</CardTitle>
            <CardDescription>Filter the list of Static IP services</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search by IP address..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                  icon={<Search className="h-4 w-4" />}
                />
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                    <SelectItem value="terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full sm:w-48">
                <Select value={ipPoolFilter} onValueChange={setIpPoolFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="IP Pool" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All IP Pools</SelectItem>
                    {services.data.length > 0 && services.data.map(service =>
                      service.ip_pool && (
                        <SelectItem key={service.ip_pool.id} value={service.ip_pool.id.toString()}>
                          {service.ip_pool.name}
                        </SelectItem>
                      )
                    )}
                  </SelectContent>
                </Select>
              </div>
              <Button type="submit">
                <Search className="mr-2 h-4 w-4" /> Search
              </Button>
              <Button variant="outline" type="button" onClick={() => window.location.href = route('services.static-ip.index')}>
                <RefreshCw className="mr-2 h-4 w-4" /> Reset
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Static IP Services</CardTitle>
            <CardDescription>
              Showing {services.data.length} of {services.total} services
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>IP Address</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Subnet Mask</TableHead>
                    <TableHead>Gateway</TableHead>
                    <TableHead>IP Pool</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {services.data.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        No Static IP services found. <Link href={route('services.static-ip.create')} className="text-blue-500 hover:underline">Create one</Link>
                      </TableCell>
                    </TableRow>
                  ) : (
                    services.data.map((service) => (
                      <TableRow key={service.id}>
                        <TableCell className="font-medium">
                          <Link href={route('services.static-ip.show', service.id)} className="text-blue-500 hover:underline">
                            {service.ip_address}
                          </Link>
                        </TableCell>
                        <TableCell>
                          <Link href={route('customers.show', service.customer_id)} className="text-blue-500 hover:underline">
                            {service.customer.name}
                          </Link>
                        </TableCell>
                        <TableCell>{service.subnet_mask}</TableCell>
                        <TableCell>{service.gateway}</TableCell>
                        <TableCell>
                          {service.ip_pool ? (
                            <Link href={route('services.ip-pools.show', service.ip_pool.id)} className="text-blue-500 hover:underline">
                              {service.ip_pool.name}
                            </Link>
                          ) : 'None'}
                        </TableCell>
                        <TableCell>{getStatusBadge(service.status)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={route('services.static-ip.show', service.id)}>View Details</Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link href={route('services.static-ip.edit', service.id)}>Edit Service</Link>
                              </DropdownMenuItem>
                              {service.status === 'active' ? (
                                <DropdownMenuItem asChild>
                                  <Link href={route('services.static-ip.update', service.id)} method="put" data={{ status: 'suspended' }} as="button">
                                    Suspend Service
                                  </Link>
                                </DropdownMenuItem>
                              ) : null}
                              {service.status === 'suspended' ? (
                                <DropdownMenuItem asChild>
                                  <Link href={route('services.static-ip.update', service.id)} method="put" data={{ status: 'active' }} as="button">
                                    Activate Service
                                  </Link>
                                </DropdownMenuItem>
                              ) : null}
                              <DropdownMenuItem asChild>
                                <Link href={route('services.static-ip.destroy', service.id)} method="delete" as="button" className="text-red-500">
                                  Delete Service
                                </Link>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                Showing {services.from || 0} to {services.to || 0} of {services.total} services
              </div>
              <div className="flex gap-2">
                {services.prev_page_url && (
                  <Button variant="outline" asChild>
                    <Link href={services.prev_page_url}>Previous</Link>
                  </Button>
                )}
                {services.next_page_url && (
                  <Button variant="outline" asChild>
                    <Link href={services.next_page_url}>Next</Link>
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
