import React from 'react';
import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ArrowLeft, Save, Globe } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export default function EditStaticIpService({ service, bandwidthPlans }) {
  const { data, setData, put, processing, errors } = useForm({
    gateway: service.gateway || '',
    dns_servers: service.dns_servers || ['*******', '*******'],
    bandwidth_plan_id: service.bandwidth_plan_id ? service.bandwidth_plan_id.toString() : '',
    comment: service.comment || '',
    status: service.status || 'active',
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    put(route('services.static-ip.update', service.id));
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'suspended':
        return <Badge className="bg-yellow-500">Suspended</Badge>;
      case 'terminated':
        return <Badge className="bg-red-500">Terminated</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  return (
    <>
      <Head title={`Edit Static IP Service - ${service.ip_address}`} />

      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('services.static-ip.show', service.id)}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Service Details
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Edit Static IP Service</h1>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              {/* Customer & Subscription Info (Read-only) */}
              <Card>
                <CardHeader>
                  <CardTitle>Customer & Subscription</CardTitle>
                  <CardDescription>Customer and subscription information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Customer</Label>
                      <div className="p-2 bg-gray-50 rounded border">
                        <Link href={route('customers.show', service.customer.id)} className="text-blue-500 hover:underline">
                          {service.customer.name}
                        </Link>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Subscription</Label>
                      <div className="p-2 bg-gray-50 rounded border">
                        <Link href={route('subscriptions.show', service.subscription.id)} className="text-blue-500 hover:underline">
                          {service.subscription.name}
                        </Link>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* IP Address Info (Read-only) */}
              <Card>
                <CardHeader>
                  <CardTitle>IP Address Information</CardTitle>
                  <CardDescription>Static IP address details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>IP Address</Label>
                      <div className="p-2 bg-gray-50 rounded border font-mono">
                        {service.ip_address}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Subnet Mask</Label>
                      <div className="p-2 bg-gray-50 rounded border font-mono">
                        {service.subnet_mask}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Router Device</Label>
                      <div className="p-2 bg-gray-50 rounded border">
                        <Link href={route('network.devices.show', service.device.id)} className="text-blue-500 hover:underline">
                          {service.device.name} ({service.device.ip_address})
                        </Link>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>IP Pool</Label>
                      <div className="p-2 bg-gray-50 rounded border">
                        {service.ip_pool ? (
                          <Link href={route('services.ip-pools.show', service.ip_pool.id)} className="text-blue-500 hover:underline">
                            {service.ip_pool.name}
                          </Link>
                        ) : 'No IP Pool'}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Service Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Service Details</CardTitle>
                  <CardDescription>Configure the Static IP service details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="gateway">Gateway</Label>
                      <Input
                        id="gateway"
                        value={data.gateway}
                        onChange={e => setData('gateway', e.target.value)}
                        placeholder="e.g. ***********"
                      />
                      {errors.gateway && <p className="text-red-500 text-sm">{errors.gateway}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="dns_servers">DNS Servers</Label>
                      <Input
                        id="dns_servers"
                        value={Array.isArray(data.dns_servers) ? data.dns_servers.join(', ') : data.dns_servers}
                        onChange={e => setData('dns_servers', e.target.value.split(',').map(s => s.trim()))}
                        placeholder="e.g. *******, *******"
                      />
                      <p className="text-sm text-gray-500">Separate multiple DNS servers with commas</p>
                      {errors.dns_servers && <p className="text-red-500 text-sm">{errors.dns_servers}</p>}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bandwidth_plan_id">Bandwidth Plan</Label>
                    <Select
                      value={data.bandwidth_plan_id}
                      onValueChange={value => setData('bandwidth_plan_id', value)}
                    >
                      <SelectTrigger id="bandwidth_plan_id">
                        <SelectValue placeholder="Select a bandwidth plan (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No bandwidth plan</SelectItem>
                        {bandwidthPlans.map(plan => (
                          <SelectItem key={plan.id} value={plan.id.toString()}>
                            {plan.name} ({plan.download_speed}/{plan.upload_speed} Kbps)
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.bandwidth_plan_id && <p className="text-red-500 text-sm">{errors.bandwidth_plan_id}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Service Status</Label>
                    <Select
                      value={data.status}
                      onValueChange={value => setData('status', value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="suspended">Suspended</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.status && <p className="text-red-500 text-sm">{errors.status}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="comment">Comment</Label>
                    <Textarea
                      id="comment"
                      value={data.comment}
                      onChange={e => setData('comment', e.target.value)}
                      placeholder="Add any notes about this service"
                      rows={3}
                    />
                    {errors.comment && <p className="text-red-500 text-sm">{errors.comment}</p>}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Summary Card */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Service Summary</CardTitle>
                  <CardDescription>Review the service details before updating</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-center p-6 bg-gray-50 rounded-md">
                    <Globe className="h-16 w-16 text-primary" />
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Service ID:</h3>
                    <p className="text-gray-700">{service.id}</p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Customer:</h3>
                    <p className="text-gray-700">{service.customer.name}</p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">IP Address:</h3>
                    <p className="text-gray-700 font-mono">{service.ip_address}</p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">CIDR Notation:</h3>
                    <p className="text-gray-700 font-mono">{service.cidr}</p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Current Status:</h3>
                    <p className="text-gray-700">{getStatusBadge(service.status)}</p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">New Status:</h3>
                    <p className="text-gray-700">{getStatusBadge(data.status)}</p>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col space-y-2">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={processing}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Update Static IP Service
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    asChild
                  >
                    <Link href={route('services.static-ip.show', service.id)}>
                      Cancel
                    </Link>
                  </Button>
                </CardFooter>
              </Card>

              <Alert>
                <AlertTitle>Important Note</AlertTitle>
                <AlertDescription>
                  This will update the static route and firewall rules on the MikroTik router. If you change the status to suspended, traffic from this IP will be blocked.
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </form>
      </div>
    </>
  );
}
