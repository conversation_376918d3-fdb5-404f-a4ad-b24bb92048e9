import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Ta<PERSON>List,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Globe,
  AlertTriangle,
  User,
  Server,
  Activity,
  Download,
  Upload,
  CheckCircle2,
  XCircle,
  Network,
  Shield
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import AppLayout from '@/layouts/app-layout';

export default function ShowStaticIpService({ service }) {
  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'suspended':
        return <Badge className="bg-yellow-500">Suspended</Badge>;
      case 'terminated':
        return <Badge className="bg-red-500">Terminated</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  return (
    <AppLayout>
      <Head title={`Static IP Service - ${service.ip_address}`} />

      <div className="page-container">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('services.static-ip.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Static IP Services
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Static IP Service Details</h1>
          </div>
          <div className="flex space-x-2">
            <Button asChild variant="outline">
              <Link href={route('services.static-ip.edit', service.id)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Service
              </Link>
            </Button>
            {service.status === 'active' ? (
              <Button asChild variant="outline" className="bg-yellow-50 text-yellow-700 hover:bg-yellow-100">
                <Link href={route('services.static-ip.update', service.id)} method="put" data={{ status: 'suspended' }} as="button">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Suspend Service
                </Link>
              </Button>
            ) : (
              <Button asChild variant="outline" className="bg-green-50 text-green-700 hover:bg-green-100">
                <Link href={route('services.static-ip.update', service.id)} method="put" data={{ status: 'active' }} as="button">
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  Activate Service
                </Link>
              </Button>
            )}
            <Button asChild variant="destructive">
              <Link href={route('services.static-ip.destroy', service.id)} method="delete" as="button">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Service
              </Link>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Tabs defaultValue="overview">
              <TabsList className="mb-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="network">Network Details</TabsTrigger>
                <TabsTrigger value="firewall">Firewall Rules</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                {/* Service Status Card */}
                <Card>
                  <CardHeader>
                    <CardTitle>Service Status</CardTitle>
                    <CardDescription>Current status of the Static IP service</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {service.status === 'active' ? (
                          <Globe className="h-8 w-8 text-green-500 mr-4" />
                        ) : (
                          <XCircle className="h-8 w-8 text-yellow-500 mr-4" />
                        )}
                        <div>
                          <h3 className="text-lg font-medium">Status: {getStatusBadge(service.status)}</h3>
                          <p className="text-gray-500">
                            {service.status === 'active'
                              ? 'Service is active and traffic is allowed'
                              : 'Service is suspended and traffic is blocked'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Service Details Card */}
                <Card>
                  <CardHeader>
                    <CardTitle>Service Details</CardTitle>
                    <CardDescription>Static IP service configuration</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">IP Address</h3>
                          <p className="font-mono">{service.ip_address}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Subnet Mask</h3>
                          <p className="font-mono">{service.subnet_mask}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">CIDR Notation</h3>
                          <p className="font-mono">{service.ip_address}/32</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Gateway</h3>
                          <p className="font-mono">{service.gateway || 'Not set'}</p>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">DNS Servers</h3>
                          <p className="font-mono">
                            {Array.isArray(service.dns_servers)
                              ? service.dns_servers.join(', ')
                              : service.dns_servers || 'Not set'}
                          </p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Created</h3>
                          <p>{new Date(service.created_at).toLocaleString()}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Last Updated</h3>
                          <p>{new Date(service.updated_at).toLocaleString()}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">IP Pool</h3>
                          <p>
                            {service.ip_pool ? (
                              <Link href={route('services.ip-pools.show', service.ip_pool.id)} className="text-blue-500 hover:underline">
                                {service.ip_pool.name}
                              </Link>
                            ) : 'None'}
                          </p>
                        </div>
                      </div>
                    </div>

                    {service.comment && (
                      <div className="mt-4 p-3 bg-gray-50 rounded-md">
                        <h3 className="text-sm font-medium text-gray-500">Comment</h3>
                        <p className="whitespace-pre-line">{service.comment}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Bandwidth Plan Card */}
                {service.bandwidth_plan && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Bandwidth Plan</CardTitle>
                      <CardDescription>Bandwidth limitations for this service</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-medium">{service.bandwidth_plan.name}</h3>
                          <p className="text-gray-500">{service.bandwidth_plan.description}</p>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-center">
                            <Download className="h-5 w-5 mx-auto text-blue-500" />
                            <p className="text-sm font-medium">{service.bandwidth_plan.download_speed} Kbps</p>
                            <p className="text-xs text-gray-500">Download</p>
                          </div>
                          <div className="text-center">
                            <Upload className="h-5 w-5 mx-auto text-green-500" />
                            <p className="text-sm font-medium">{service.bandwidth_plan.upload_speed} Kbps</p>
                            <p className="text-xs text-gray-500">Upload</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="network" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Network Configuration</CardTitle>
                    <CardDescription>Network details for this Static IP service</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center">
                      <Network className="h-8 w-8 text-blue-500 mr-4" />
                      <div>
                        <h3 className="text-lg font-medium">Network Details</h3>
                        <p className="text-gray-500">
                          Configuration for the static IP address on the router
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">IP Address</h3>
                          <p className="font-mono">{service.ip_address}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Subnet Mask</h3>
                          <p className="font-mono">{service.subnet_mask}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">CIDR Notation</h3>
                          <p className="font-mono">{service.ip_address}/32</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Network Address</h3>
                          <p className="font-mono">{service.network_address}</p>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Gateway</h3>
                          <p className="font-mono">{service.gateway || 'Not set'}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">DNS Servers</h3>
                          <p className="font-mono">
                            {Array.isArray(service.dns_servers)
                              ? service.dns_servers.join(', ')
                              : service.dns_servers || 'Not set'}
                          </p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Router Device</h3>
                          <p>
                            <Link href={route('network.devices.show', service.device.id)} className="text-blue-500 hover:underline">
                              {service.device.name}
                            </Link>
                          </p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">MikroTik Route ID</h3>
                          <p className="font-mono">{service.mikrotik_route_id || 'Not available'}</p>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 bg-gray-50 rounded-md">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">MikroTik Configuration</h3>
                      <pre className="text-xs font-mono bg-gray-100 p-3 rounded overflow-x-auto">
                        {`/ip route add dst-address=${service.ip_address}/32 gateway=${service.gateway || 'gateway'} comment="Customer: ${service.customer.name}, ID: ${service.customer.id}"`}
                      </pre>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="firewall" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Firewall Rules</CardTitle>
                    <CardDescription>Firewall configuration for this Static IP service</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center">
                      <Shield className="h-8 w-8 text-green-500 mr-4" />
                      <div>
                        <h3 className="text-lg font-medium">Firewall Configuration</h3>
                        <p className="text-gray-500">
                          Firewall rules applied to this static IP address
                        </p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Firewall Rule ID</h3>
                        <p className="font-mono">{service.mikrotik_firewall_id || 'Not available'}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">NAT Rule ID</h3>
                        <p className="font-mono">{service.mikrotik_nat_id || 'Not available'}</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Firewall Rules</h3>
                      <div className="p-4 bg-gray-50 rounded-md">
                        <pre className="text-xs font-mono bg-gray-100 p-3 rounded overflow-x-auto">
                          {`/ip firewall filter add chain=forward src-address=${service.ip_address}/32 action=accept comment="Allow traffic from customer ${service.customer.name}"`}
                        </pre>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">NAT Rules</h3>
                      <div className="p-4 bg-gray-50 rounded-md">
                        <pre className="text-xs font-mono bg-gray-100 p-3 rounded overflow-x-auto">
                          {`/ip firewall nat add chain=srcnat src-address=${service.ip_address}/32 action=masquerade comment="NAT for customer ${service.customer.name}"`}
                        </pre>
                      </div>
                    </div>

                    {service.status === 'suspended' && (
                      <Alert variant="warning">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertTitle>Service Suspended</AlertTitle>
                        <AlertDescription>
                          This service is currently suspended. A blocking firewall rule has been added to prevent traffic from this IP address.
                        </AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            {/* Customer Card */}
            <Card>
              <CardHeader>
                <CardTitle>Customer</CardTitle>
                <CardDescription>Customer information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <User className="h-10 w-10 text-primary bg-primary/10 p-2 rounded-full mr-4" />
                  <div>
                    <Link href={route('customers.show', service.customer.id)} className="text-lg font-medium text-blue-500 hover:underline">
                      {service.customer.name}
                    </Link>
                    <p className="text-gray-500">{service.customer.email}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Phone</h3>
                  <p>{service.customer.phone || 'Not provided'}</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Address</h3>
                  <p className="whitespace-pre-line">{service.customer.full_address || 'Not provided'}</p>
                </div>

                <Button asChild variant="outline" className="w-full">
                  <Link href={route('customers.show', service.customer.id)}>
                    View Customer Details
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Subscription Card */}
            <Card>
              <CardHeader>
                <CardTitle>Subscription</CardTitle>
                <CardDescription>Subscription information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <div>
                    <Link href={route('subscriptions.show', service.subscription.id)} className="text-lg font-medium text-blue-500 hover:underline">
                      {service.subscription.name}
                    </Link>
                    <p className="text-gray-500">${service.subscription.price} / {service.subscription.billing_cycle}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Status</h3>
                  <p>{service.subscription.status}</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Start Date</h3>
                  <p>{new Date(service.subscription.start_date).toLocaleDateString()}</p>
                </div>

                {service.subscription.end_date && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium text-gray-500">End Date</h3>
                    <p>{new Date(service.subscription.end_date).toLocaleDateString()}</p>
                  </div>
                )}

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Next Billing</h3>
                  <p>{new Date(service.subscription.next_billing_date).toLocaleDateString()}</p>
                </div>

                <Button asChild variant="outline" className="w-full">
                  <Link href={route('subscriptions.show', service.subscription.id)}>
                    View Subscription Details
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Router Device Card */}
            <Card>
              <CardHeader>
                <CardTitle>Router Device</CardTitle>
                <CardDescription>Device hosting this Static IP service</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <Server className="h-10 w-10 text-primary bg-primary/10 p-2 rounded-full mr-4" />
                  <div>
                    <Link href={route('network.devices.show', service.device.id)} className="text-lg font-medium text-blue-500 hover:underline">
                      {service.device.name}
                    </Link>
                    <p className="text-gray-500">{service.device.ip_address}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Type</h3>
                  <p>{service.device.type}</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Model</h3>
                  <p>{service.device.model || 'Not specified'}</p>
                </div>

                <Button asChild variant="outline" className="w-full">
                  <Link href={route('network.devices.show', service.device.id)}>
                    View Device Details
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
