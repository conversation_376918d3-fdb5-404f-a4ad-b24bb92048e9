import { useState, useEffect } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import axios from 'axios';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';

import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { IpAddressCombobox } from '@/components/ui/ip-address-combobox';
import { CustomerCombobox } from '@/components/ui/customer-combobox';
import { ArrowLeft, Save, Globe, User, Network } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Customer {
  id: number;
  name: string;
  email: string;
  subscriptions?: Subscription[];
}

interface Subscription {
  id: number;
  name: string;
  price: string;
  bandwidth_plan?: BandwidthPlan;
}

interface BandwidthPlan {
  id: number;
  name: string;
  download_speed: number;
  upload_speed: number;
}

interface Device {
  id: number;
  name: string;
  ip_address: string;
}

interface IpPool {
  id: number;
  name: string;
  network_address: string;
  subnet_mask: string;
  gateway?: string;
  dns_servers?: string[];
}

interface Props {
  customers: Customer[];
  devices: Device[];
  ipPools: IpPool[];
}

export default function CreateStaticIpService({ customers, devices, ipPools }: Props) {
  const { data, setData, post, processing, errors, reset } = useForm({
    customer_id: '',
    device_id: '',
    ip_pool_id: '',
    ip_address: '',
    comment: ''
  });

  const [customerData, setCustomerData] = useState<{
    subscription?: Subscription;
    bandwidthPlan?: BandwidthPlan;
  }>({});
  const [poolData, setPoolData] = useState<{
    gateway?: string;
    subnet_mask?: string;
    dns_servers?: string[];
    availableIps?: string[];
    totalAvailable?: number;
    nextRecommended?: string;
  }>({});
  const [filteredIpPools, setFilteredIpPools] = useState<IpPool[]>(ipPools);
  const [loadingCustomerData, setLoadingCustomerData] = useState(false);
  const [loadingAvailableIps, setLoadingAvailableIps] = useState(false);
  const [loadingIpPools, setLoadingIpPools] = useState(false);

  // Auto-fetch customer data when customer changes
  useEffect(() => {
    if (data.customer_id) {
      setLoadingCustomerData(true);

      axios.get(`/api/services/static-ip/customer/${data.customer_id}/data`)
        .then(response => {
          if (response.data.success) {
            setCustomerData({
              subscription: response.data.subscription,
              bandwidthPlan: response.data.subscription.bandwidth_plan
            });
          }
        })
        .catch(error => {
          console.error('Failed to fetch customer data:', error);
        })
        .finally(() => {
          setLoadingCustomerData(false);
        });
    } else {
      setCustomerData({});
    }
  }, [data.customer_id]);

  // Auto-fetch available IPs and pool data when IP pool changes
  useEffect(() => {
    if (data.ip_pool_id) {
      setLoadingAvailableIps(true);

      axios.get(`/api/services/static-ip/ip-pool/${data.ip_pool_id}/available-ips`)
        .then(response => {
          if (response.data.success) {
            setData('ip_address', response.data.next_recommended);
            setPoolData({
              gateway: response.data.gateway,
              subnet_mask: response.data.subnet_mask,
              dns_servers: response.data.dns_servers,
              availableIps: response.data.available_ips,
              totalAvailable: response.data.total_available,
              nextRecommended: response.data.next_recommended
            });
          }
        })
        .catch(error => {
          console.error('Failed to fetch available IPs:', error);
          setPoolData({
            availableIps: [],
            totalAvailable: 0
          });
        })
        .finally(() => {
          setLoadingAvailableIps(false);
        });
    } else {
      setPoolData({});
      setData('ip_address', '');
    }
  }, [data.ip_pool_id]);

  // Auto-fetch IP pools when device changes
  useEffect(() => {
    if (data.device_id) {
      setLoadingIpPools(true);

      axios.get(`/api/services/static-ip/device/${data.device_id}/ip-pools`)
        .then(response => {
          if (response.data.success) {
            setFilteredIpPools(response.data.ip_pools);
          } else {
            setFilteredIpPools([]);
          }
          // Clear IP pool and IP address selections when device changes
          setData(prev => ({
            ...prev,
            ip_pool_id: '',
            ip_address: ''
          }));
          setPoolData({});
        })
        .catch(error => {
          console.error('Failed to fetch IP pools for device:', error);
          setFilteredIpPools([]);
        })
        .finally(() => {
          setLoadingIpPools(false);
        });
    } else {
      // If no device selected, show all IP pools
      setFilteredIpPools(ipPools);
      setData(prev => ({
        ...prev,
        ip_pool_id: '',
        ip_address: ''
      }));
      setPoolData({});
    }
  }, [data.device_id]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('services.static-ip.store'));
  };

  const handleRefreshAvailableIps = () => {
    if (data.ip_pool_id) {
      setLoadingAvailableIps(true);

      axios.get(`/api/services/static-ip/ip-pool/${data.ip_pool_id}/available-ips`)
        .then(response => {
          if (response.data.success) {
            setPoolData(prev => ({
              ...prev,
              availableIps: response.data.available_ips,
              totalAvailable: response.data.total_available,
              nextRecommended: response.data.next_recommended
            }));
            // Only update IP if current selection is no longer available
            if (!response.data.available_ips.includes(data.ip_address)) {
              setData('ip_address', response.data.next_recommended);
            }
          }
        })
        .catch(error => {
          console.error('Failed to refresh available IPs:', error);
        })
        .finally(() => {
          setLoadingAvailableIps(false);
        });
    }
  };

  return (
    <AppLayout>
      <Head title="Create Static IP Service" />

      <div className="page-container">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('services.static-ip.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Static IP Services
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Create Static IP Service</h1>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              {/* Customer Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Customer Selection
                  </CardTitle>
                  <CardDescription>
                    Select the customer. Their active subscription and bandwidth plan will be automatically detected.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="customer_id">Customer <span className="text-red-500">*</span></Label>
                    <CustomerCombobox
                      value={data.customer_id}
                      onValueChange={value => setData('customer_id', value)}
                      initialCustomers={customers}
                      placeholder="Search and select a customer"
                      className="w-full"
                      showStatus={true}
                      filterActiveOnly={true}
                      minSearchLength={2}
                      debounceMs={400}
                      searchEndpoint="/api/customers/search"
                    />
                    {errors.customer_id && <p className="text-red-500 text-sm">{errors.customer_id}</p>}
                  </div>

                  {/* Auto-detected subscription info */}
                  {loadingCustomerData && (
                    <Alert>
                      <AlertDescription>
                        Loading customer subscription data...
                      </AlertDescription>
                    </Alert>
                  )}

                  {customerData.subscription && (
                    <Alert>
                      <AlertDescription>
                        <strong>Active Subscription:</strong> {customerData.subscription.name} (${customerData.subscription.price})
                        {customerData.bandwidthPlan && (
                          <span className="block mt-1">
                            <strong>Bandwidth Plan:</strong> {customerData.bandwidthPlan.name} - {customerData.bandwidthPlan.download_speed}/{customerData.bandwidthPlan.upload_speed} Mbps
                          </span>
                        )}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>

              {/* Network Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Network className="h-5 w-5" />
                    Network Configuration
                  </CardTitle>
                  <CardDescription>
                    Select router device and IP pool. Network settings will be auto-populated.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="device_id">Router Device <span className="text-red-500">*</span></Label>
                      <Select
                        value={data.device_id}
                        onValueChange={value => setData('device_id', value)}
                      >
                        <SelectTrigger id="device_id">
                          <SelectValue placeholder="Select a router" />
                        </SelectTrigger>
                        <SelectContent>
                          {devices.map(device => (
                            <SelectItem key={device.id} value={device.id.toString()}>
                              {device.name} ({device.ip_address})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.device_id && <p className="text-red-500 text-sm">{errors.device_id}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="ip_pool_id">IP Pool <span className="text-red-500">*</span></Label>
                      <Select
                        value={data.ip_pool_id}
                        onValueChange={value => setData('ip_pool_id', value)}
                        disabled={!data.device_id || loadingIpPools}
                      >
                        <SelectTrigger id="ip_pool_id">
                          <SelectValue placeholder={
                            !data.device_id
                              ? "Select a router device first"
                              : loadingIpPools
                                ? "Loading IP pools..."
                                : filteredIpPools.length === 0
                                  ? "No IP pools available for this device"
                                  : "Select an IP pool"
                          } />
                        </SelectTrigger>
                        <SelectContent>
                          {filteredIpPools.map(pool => (
                            <SelectItem key={pool.id} value={pool.id.toString()}>
                              {pool.name} ({pool.network_address})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.ip_pool_id && <p className="text-red-500 text-sm">{errors.ip_pool_id}</p>}
                      {data.device_id && !loadingIpPools && filteredIpPools.length === 0 && (
                        <p className="text-sm text-amber-600">
                          ⚠️ No IP pools are configured for the selected router device.
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="ip_address">IP Address <span className="text-red-500">*</span></Label>
                      {poolData.totalAvailable && (
                        <span className="text-sm text-muted-foreground">
                          {poolData.totalAvailable} available
                        </span>
                      )}
                    </div>

                    <IpAddressCombobox
                      value={data.ip_address}
                      onValueChange={value => setData('ip_address', value)}
                      availableIps={poolData.availableIps || []}
                      nextRecommended={poolData.nextRecommended}
                      totalAvailable={poolData.totalAvailable}
                      loading={loadingAvailableIps}
                      disabled={!data.ip_pool_id}
                      onRefresh={data.ip_pool_id ? handleRefreshAvailableIps : undefined}
                      placeholder={
                        !data.ip_pool_id
                          ? "Select an IP pool first"
                          : poolData.availableIps?.length === 0
                            ? "No available IPs in this pool"
                            : "Select an IP address"
                      }
                      className="w-full"
                    />

                    {errors.ip_address && <p className="text-red-500 text-sm">{errors.ip_address}</p>}

                    {/* IP Pool Status */}
                    {data.ip_pool_id && poolData.totalAvailable !== undefined && (
                      <div className="text-sm text-muted-foreground">
                        {poolData.totalAvailable === 0 ? (
                          <span className="text-red-600">⚠️ No available IP addresses in this pool</span>
                        ) : (
                          <span>
                            💡 {poolData.totalAvailable} IP{poolData.totalAvailable !== 1 ? 's' : ''} available for assignment
                            {poolData.totalAvailable > 20 && (
                              <span className="block mt-1 text-xs">
                                💡 Use the search box to quickly find specific IP addresses
                              </span>
                            )}
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Auto-populated network settings */}
                  {poolData.subnet_mask && (
                    <Alert>
                      <AlertDescription>
                        <strong>Auto-populated from IP pool:</strong><br />
                        Gateway: {poolData.gateway}<br />
                        Subnet Mask: {poolData.subnet_mask}<br />
                        DNS Servers: {poolData.dns_servers?.join(', ')}
                      </AlertDescription>
                    </Alert>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="comment">Comment</Label>
                    <Textarea
                      id="comment"
                      value={data.comment}
                      onChange={e => setData('comment', e.target.value)}
                      placeholder="Add any notes about this service"
                      rows={3}
                    />
                    {errors.comment && <p className="text-red-500 text-sm">{errors.comment}</p>}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Summary Card */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Service Summary</CardTitle>
                  <CardDescription>Review the service details before creating</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-center p-6 bg-gray-50 rounded-md">
                    <Globe className="h-16 w-16 text-primary" />
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Customer:</h3>
                    <p className="text-gray-700">
                      {data.customer_id ? customers.find(c => c.id.toString() === data.customer_id)?.name : 'None selected'}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">IP Address:</h3>
                    <p className="text-gray-700 font-mono">
                      {data.ip_address || 'Not set'}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Gateway:</h3>
                    <p className="text-gray-700 font-mono">
                      {poolData.gateway || 'Not set'}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Router Device:</h3>
                    <p className="text-gray-700">
                      {data.device_id ? devices.find(d => d.id.toString() === data.device_id)?.name : 'None selected'}
                    </p>
                  </div>

                  {data.ip_pool_id && (
                    <div className="space-y-2">
                      <h3 className="font-medium">IP Pool:</h3>
                      <p className="text-gray-700">
                        {filteredIpPools.find(p => p.id.toString() === data.ip_pool_id)?.name}
                      </p>
                    </div>
                  )}

                  {customerData.bandwidthPlan && (
                    <div className="space-y-2">
                      <h3 className="font-medium">Bandwidth Plan:</h3>
                      <p className="text-gray-700">
                        {customerData.bandwidthPlan.name} ({customerData.bandwidthPlan.download_speed}/{customerData.bandwidthPlan.upload_speed} Mbps)
                      </p>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex flex-col space-y-2">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={processing}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Create Static IP Service
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => reset()}
                  >
                    Reset Form
                  </Button>
                </CardFooter>
              </Card>

              <Alert>
                <AlertTitle>Streamlined Workflow</AlertTitle>
                <AlertDescription>
                  This form automatically detects customer subscription, bandwidth plan, and network settings from the selected IP pool. Only essential fields require manual input.
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
