import { Head, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { StatsCard } from '@/components/ui/stats-card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Network,
  Wifi,
  Globe,
  Server,
  Users,
  Plus,
  Search,
  Eye,
  Edit,
  Activity
} from 'lucide-react';
import { useState } from 'react';

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  status: string;
}

interface BandwidthPlan {
  id: number;
  name: string;
  download_speed: number;
  upload_speed: number;
}

interface Device {
  id: number;
  name: string;
  ip_address: string;
}

interface IpPool {
  id: number;
  name: string;
  network_address: string;
}

interface Service {
  id: number;
  customer: Customer;
  bandwidth_plan?: BandwidthPlan;
  device: Device;
  ip_pool?: IpPool;
  ip_address?: string;
  username?: string;
  status: string;
  service_type: string;
  service_type_label: string;
  created_at: string;
  updated_at: string;
}

interface Stats {
  total_services: number;
  static_ip_services: number;
  pppoe_services: number;
  ip_pools: number;
  active_customers: number;
  total_customers: number;
}

interface ServiceStatusBreakdown {
  active: number;
  suspended: number;
  pending: number;
}

interface Props {
  stats: Stats;
  serviceStatusBreakdown: ServiceStatusBreakdown;
  recentServices: Service[];
  staticIpServices: Service[];
  pppoeServices: Service[];
}

export default function ServicesDashboard({
  stats,
  serviceStatusBreakdown,
  recentServices,
  staticIpServices,
  pppoeServices
}: Props) {
  const { auth } = usePage().props as any;
  const permissions = auth.permissions || [];

  // Helper function to check if user has a specific permission
  const hasPermission = (permission: string) => permissions.includes(permission);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedServiceType, setSelectedServiceType] = useState('all');

  // Filter recent services based on search and type
  const filteredServices = recentServices.filter(service => {
    const matchesSearch = searchTerm === '' ||
      service.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.ip_address?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.username?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = selectedServiceType === 'all' || service.service_type === selectedServiceType;

    return matchesSearch && matchesType;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'suspended':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getServiceTypeIcon = (serviceType: string) => {
    switch (serviceType) {
      case 'static_ip':
        return <Globe className="h-4 w-4" />;
      case 'pppoe':
        return <Wifi className="h-4 w-4" />;
      default:
        return <Network className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  return (
    <AppLayout>
      <Head title="Services Dashboard" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <h1>Services</h1>
            <p>Manage PPPoE, Static IP services, and network configurations</p>
          </div>
          <div className="flex gap-3">
            {hasPermission('create services') && (
              <Button asChild>
                <Link href={route('services.pppoe.create')}>
                  <Plus className="h-4 w-4 mr-2" />
                  New PPPoE Service
                </Link>
              </Button>
            )}
            {hasPermission('create services') && (
              <Button asChild variant="outline">
                <Link href={route('services.static-ip.create')}>
                  <Plus className="h-4 w-4 mr-2" />
                  New Static IP Service
                </Link>
              </Button>
            )}
          </div>
        </div>

        {/* Stats Cards with Real Data */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="Total Services"
            value={stats.total_services.toString()}
            change={{
              value: `${serviceStatusBreakdown.active} active`,
              type: "neutral"
            }}
            icon={Wifi}
          />
          <StatsCard
            title="PPPoE Services"
            value={stats.pppoe_services.toString()}
            change={{
              value: `${Math.round((stats.pppoe_services / stats.total_services) * 100)}% of total`,
              type: "neutral"
            }}
            icon={Network}
          />
          <StatsCard
            title="Static IP Services"
            value={stats.static_ip_services.toString()}
            change={{
              value: `${Math.round((stats.static_ip_services / stats.total_services) * 100)}% of total`,
              type: "neutral"
            }}
            icon={Globe}
          />
          <StatsCard
            title="IP Pools"
            value={stats.ip_pools.toString()}
            change={{
              value: `${stats.active_customers} customers`,
              type: "neutral"
            }}
            icon={Server}
          />
        </div>

        {/* Recent Services Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Recent Services
                </CardTitle>
                <CardDescription>
                  Latest service activations and modifications
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search services..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
                <select
                  value={selectedServiceType}
                  onChange={(e) => setSelectedServiceType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Types</option>
                  <option value="static_ip">Static IP</option>
                  <option value="pppoe">PPPoE</option>
                </select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Service Type</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>IP Address / Username</TableHead>
                    <TableHead>Bandwidth Plan</TableHead>
                    <TableHead>Device</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredServices.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                        No services found matching your criteria
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredServices.map((service) => (
                      <TableRow key={`${service.service_type}-${service.id}`} className="hover:bg-muted/50">
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getServiceTypeIcon(service.service_type)}
                            <span className="font-medium">{service.service_type_label}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{service.customer.name}</div>
                            <div className="text-sm text-muted-foreground">{service.customer.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-mono text-sm">
                            {service.service_type === 'static_ip' ? service.ip_address : service.username}
                          </div>
                          {service.service_type === 'static_ip' && service.ip_pool && (
                            <div className="text-xs text-muted-foreground">
                              Pool: {service.ip_pool.name}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          {service.bandwidth_plan ? (
                            <div>
                              <div className="font-medium">{service.bandwidth_plan.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {service.bandwidth_plan.download_speed}/{service.bandwidth_plan.upload_speed} Mbps
                              </div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">No plan</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{service.device.name}</div>
                            <div className="text-sm text-muted-foreground font-mono">{service.device.ip_address}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(service.status)}>
                            {service.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{formatDate(service.created_at)}</div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            {hasPermission('view services') && (
                              <Button
                                variant="ghost"
                                size="sm"
                                asChild
                              >
                                <Link href={route(`services.${service.service_type.replace('_', '-')}.show`, service.id)}>
                                  <Eye className="h-4 w-4" />
                                </Link>
                              </Button>
                            )}
                            {hasPermission('edit services') && (
                              <Button
                                variant="ghost"
                                size="sm"
                                asChild
                              >
                                <Link href={route(`services.${service.service_type.replace('_', '-')}.edit`, service.id)}>
                                  <Edit className="h-4 w-4" />
                                </Link>
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {/* PPPoE Services Card */}
          <Card className="card-modern-hover group">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <Wifi className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <CardTitle className="text-lg">PPPoE Services</CardTitle>
                  <CardDescription>Manage PPPoE accounts</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Active Services</span>
                <span className="font-semibold">{stats.pppoe_services}</span>
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                <Link href={route('services.pppoe.index')}>
                  Manage PPPoE Services
                </Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Static IP Services Card */}
          <Card className="card-modern-hover group">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/30">
                  <Globe className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <CardTitle className="text-lg">Static IP Services</CardTitle>
                  <CardDescription>Manage IP assignments</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Active Services</span>
                <span className="font-semibold">{stats.static_ip_services}</span>
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                <Link href={route('services.static-ip.index')}>
                  Manage Static IP Services
                </Link>
              </Button>
            </CardFooter>
          </Card>

          {/* IP Pools Card */}
          <Card className="card-modern-hover group">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/30">
                  <Server className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <CardTitle className="text-lg">IP Pools</CardTitle>
                  <CardDescription>Manage address pools</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Total Pools</span>
                <span className="font-semibold">{stats.ip_pools}</span>
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                <Link href={route('services.ip-pools.index')}>
                  Manage IP Pools
                </Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Customers Card */}
          <Card className="card-modern-hover group">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/30">
                  <Users className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <CardTitle className="text-lg">Customers</CardTitle>
                  <CardDescription>View customer services</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Total Customers</span>
                <span className="font-semibold">{stats.total_customers}</span>
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                <Link href={route('customers.index')}>
                  View Customers
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
