import { useState, useEffect } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import axios from 'axios';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CustomerCombobox } from '@/components/ui/customer-combobox';
import { ArrowLeft, Save, Wifi, User, RefreshCcw, Eye, EyeOff } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  status: string;
  subscriptions?: Subscription[];
}

interface Subscription {
  id: number;
  name: string;
  description: string;
  price: number;
  plan?: BandwidthPlan;
}

interface BandwidthPlan {
  id: number;
  name: string;
  download_speed: number;
  upload_speed: number;
  price: number;
}

interface Device {
  id: number;
  name: string;
  ip_address: string;
  type: string;
}

interface Props {
  customers: Customer[];
  devices: Device[];
  bandwidthPlans: BandwidthPlan[];
}

export default function CreatePppoeService({ customers, devices, bandwidthPlans }: Props) {
  const { data, setData, post, processing, errors, reset } = useForm({
    customer_id: '',
    device_id: '',
    username: '',
    password: '',
    service_profile: '',
    bandwidth_plan_id: 'none',
    comment: '',
  });

  const [customerData, setCustomerData] = useState<{
    subscription: Subscription | null;
    bandwidthPlan: BandwidthPlan | null;
  }>({
    subscription: null,
    bandwidthPlan: null,
  });

  const [loadingCustomerData, setLoadingCustomerData] = useState(false);
  const [loadingCredentials, setLoadingCredentials] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Auto-fetch customer data when customer changes
  useEffect(() => {
    if (data.customer_id) {
      setLoadingCustomerData(true);

      axios.get(`/api/services/pppoe/customer/${data.customer_id}/data`)
        .then(response => {
          if (response.data.success) {
            setCustomerData({
              subscription: response.data.subscription,
              bandwidthPlan: response.data.bandwidthPlan
            });

            // Auto-populate bandwidth plan if found
            if (response.data.bandwidthPlan) {
              setData('bandwidth_plan_id', response.data.bandwidthPlan.id.toString());
            }
          }
        })
        .catch(error => {
          console.error('Failed to fetch customer data:', error);
        })
        .finally(() => {
          setLoadingCustomerData(false);
        });
    } else {
      setCustomerData({ subscription: null, bandwidthPlan: null });
      setData('bandwidth_plan_id', 'none');
    }
  }, [data.customer_id]);

  // Auto-generate credentials when customer changes
  useEffect(() => {
    if (data.customer_id && !data.username) {
      handleGenerateCredentials();
    }
  }, [data.customer_id]);

  const handleGenerateCredentials = () => {
    if (!data.customer_id) return;

    setLoadingCredentials(true);

    axios.get(`/api/services/pppoe/customer/${data.customer_id}/credentials`)
      .then(response => {
        if (response.data.success) {
          setData(prev => ({
            ...prev,
            username: response.data.username,
            password: response.data.password,
          }));
        }
      })
      .catch(error => {
        console.error('Failed to generate credentials:', error);
      })
      .finally(() => {
        setLoadingCredentials(false);
      });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('services.pppoe.store'));
  };

  return (
    <AppLayout>
      <Head title="Create PPPoE Service" />

      <div className="page-container">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('services.pppoe.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to PPPoE Services
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Create PPPoE Service</h1>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              {/* Customer Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Customer Selection
                  </CardTitle>
                  <CardDescription>
                    Select the customer. Their active subscription and bandwidth plan will be automatically detected.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="customer_id">Customer <span className="text-red-500">*</span></Label>
                    <CustomerCombobox
                      value={data.customer_id}
                      onValueChange={value => setData('customer_id', value)}
                      initialCustomers={customers}
                      placeholder="Search and select a customer"
                      className="w-full"
                      showStatus={true}
                      filterActiveOnly={true}
                      minSearchLength={2}
                      debounceMs={400}
                      searchEndpoint="/api/customers/search"
                    />
                    {errors.customer_id && <p className="text-red-500 text-sm">{errors.customer_id}</p>}
                  </div>

                  {/* Auto-detected subscription info */}
                  {loadingCustomerData && (
                    <Alert>
                      <AlertDescription>
                        Loading customer subscription data...
                      </AlertDescription>
                    </Alert>
                  )}

                  {customerData.subscription && (
                    <Alert>
                      <AlertDescription>
                        <strong>Active Subscription:</strong> {customerData.subscription.name} (${customerData.subscription.price})
                        {customerData.bandwidthPlan && (
                          <span className="block mt-1">
                            <strong>Bandwidth Plan:</strong> {customerData.bandwidthPlan.name} - {customerData.bandwidthPlan.download_speed}/{customerData.bandwidthPlan.upload_speed} Mbps
                          </span>
                        )}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>

              {/* Service Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wifi className="h-5 w-5" />
                    Service Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure router device, bandwidth plan, and PPPoE credentials.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="device_id">Router Device <span className="text-red-500">*</span></Label>
                      <Select
                        value={data.device_id}
                        onValueChange={value => setData('device_id', value)}
                      >
                        <SelectTrigger id="device_id">
                          <SelectValue placeholder="Select a router" />
                        </SelectTrigger>
                        <SelectContent>
                          {devices.map(device => (
                            <SelectItem key={device.id} value={device.id.toString()}>
                              {device.name} ({device.ip_address})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.device_id && <p className="text-red-500 text-sm">{errors.device_id}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bandwidth_plan_id">Bandwidth Plan</Label>
                      <Select
                        value={data.bandwidth_plan_id}
                        onValueChange={value => setData('bandwidth_plan_id', value)}
                      >
                        <SelectTrigger id="bandwidth_plan_id">
                          <SelectValue placeholder="Select a bandwidth plan" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">No bandwidth plan</SelectItem>
                          {bandwidthPlans.map(plan => (
                            <SelectItem key={plan.id} value={plan.id.toString()}>
                              {plan.name} ({plan.download_speed}/{plan.upload_speed} Mbps - ${plan.price}/month)
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.bandwidth_plan_id && <p className="text-red-500 text-sm">{errors.bandwidth_plan_id}</p>}
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="username">Username <span className="text-red-500">*</span></Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleGenerateCredentials}
                          disabled={!data.customer_id || loadingCredentials}
                        >
                          <RefreshCcw className={`h-4 w-4 mr-2 ${loadingCredentials ? 'animate-spin' : ''}`} />
                          Generate
                        </Button>
                      </div>
                      <Input
                        id="username"
                        value={data.username}
                        onChange={e => setData('username', e.target.value)}
                        placeholder="Enter username"
                        disabled={loadingCredentials}
                      />
                      {errors.username && <p className="text-red-500 text-sm">{errors.username}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="password">Password <span className="text-red-500">*</span></Label>
                      <div className="relative">
                        <Input
                          id="password"
                          type={showPassword ? 'text' : 'password'}
                          value={data.password}
                          onChange={e => setData('password', e.target.value)}
                          placeholder="Enter password"
                          disabled={loadingCredentials}
                          className="pr-10"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                      {errors.password && <p className="text-red-500 text-sm">{errors.password}</p>}
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="service_profile">Service Profile</Label>
                      <Input
                        id="service_profile"
                        value={data.service_profile}
                        onChange={e => setData('service_profile', e.target.value)}
                        placeholder="default"
                      />
                      {errors.service_profile && <p className="text-red-500 text-sm">{errors.service_profile}</p>}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="comment">Comment</Label>
                    <Textarea
                      id="comment"
                      value={data.comment}
                      onChange={e => setData('comment', e.target.value)}
                      placeholder="Add any notes about this service"
                      rows={3}
                    />
                    {errors.comment && <p className="text-red-500 text-sm">{errors.comment}</p>}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Summary Card */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Service Summary</CardTitle>
                  <CardDescription>Review the service details before creating</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-center p-6 bg-gray-50 rounded-md">
                    <Wifi className="h-16 w-16 text-primary" />
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Customer:</h3>
                    <p className="text-gray-700">
                      {data.customer_id ? customers.find(c => c.id.toString() === data.customer_id)?.name : 'None selected'}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Username:</h3>
                    <p className="text-gray-700 font-mono">
                      {data.username || 'Not set'}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Router Device:</h3>
                    <p className="text-gray-700">
                      {data.device_id ? devices.find(d => d.id.toString() === data.device_id)?.name : 'None selected'}
                    </p>
                  </div>

                  {data.bandwidth_plan_id && data.bandwidth_plan_id !== 'none' && (
                    <div className="space-y-2">
                      <h3 className="font-medium">Bandwidth Plan:</h3>
                      <p className="text-gray-700">
                        {bandwidthPlans.find(p => p.id.toString() === data.bandwidth_plan_id)?.name}
                      </p>
                    </div>
                  )}

                  {customerData.bandwidthPlan && (
                    <div className="space-y-2">
                      <h3 className="font-medium">Auto-detected Plan:</h3>
                      <p className="text-gray-700">
                        {customerData.bandwidthPlan.name} ({customerData.bandwidthPlan.download_speed}/{customerData.bandwidthPlan.upload_speed} Mbps)
                      </p>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex flex-col space-y-2">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={processing}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Create PPPoE Service
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => reset()}
                  >
                    Reset Form
                  </Button>
                </CardFooter>
              </Card>

              <Alert>
                <AlertDescription>
                  This form automatically detects customer subscription and bandwidth plan. PPPoE credentials are auto-generated but can be customized.
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
