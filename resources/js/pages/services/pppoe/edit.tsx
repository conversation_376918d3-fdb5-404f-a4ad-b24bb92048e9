import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ArrowLeft, RefreshCcw, Save, Wifi } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export default function EditPppoeService({ service, customers, devices, bandwidthPlans }) {
  const { data, setData, put, processing, errors } = useForm({
    password: service.password || '',
    service_profile: service.service_profile || 'default',
    bandwidth_plan_id: service.bandwidth_plan_id ? service.bandwidth_plan_id.toString() : '',
    comment: service.comment || '',
    status: service.status || 'active',
  });

  const [generatingPassword, setGeneratingPassword] = useState(false);

  // Generate random password
  const generatePassword = () => {
    setGeneratingPassword(true);

    // Generate random password
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    setData('password', password);
    setGeneratingPassword(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    put(route('services.pppoe.update', service.id));
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'suspended':
        return <Badge className="bg-yellow-500">Suspended</Badge>;
      case 'terminated':
        return <Badge className="bg-red-500">Terminated</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  return (
    <>
      <Head title={`Edit PPPoE Service - ${service.username}`} />

      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('services.pppoe.show', service.id)}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Service Details
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Edit PPPoE Service</h1>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              {/* Customer & Subscription Info (Read-only) */}
              <Card>
                <CardHeader>
                  <CardTitle>Customer & Subscription</CardTitle>
                  <CardDescription>Customer and subscription information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Customer</Label>
                      <div className="p-2 bg-gray-50 rounded border">
                        <Link href={route('customers.show', service.customer.id)} className="text-blue-500 hover:underline">
                          {service.customer.name}
                        </Link>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Subscription</Label>
                      <div className="p-2 bg-gray-50 rounded border">
                        <Link href={route('subscriptions.show', service.subscription.id)} className="text-blue-500 hover:underline">
                          {service.subscription.name}
                        </Link>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Service Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Service Details</CardTitle>
                  <CardDescription>Configure the PPPoE service details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Router Device</Label>
                      <div className="p-2 bg-gray-50 rounded border">
                        <Link href={route('network.devices.show', service.device.id)} className="text-blue-500 hover:underline">
                          {service.device.name} ({service.device.ip_address})
                        </Link>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="service_profile">Service Profile</Label>
                      <Input
                        id="service_profile"
                        value={data.service_profile}
                        onChange={e => setData('service_profile', e.target.value)}
                        placeholder="default"
                      />
                      <p className="text-sm text-gray-500">Leave as 'default' unless you have custom profiles</p>
                      {errors.service_profile && <p className="text-red-500 text-sm">{errors.service_profile}</p>}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bandwidth_plan_id">Bandwidth Plan</Label>
                    <Select
                      value={data.bandwidth_plan_id}
                      onValueChange={value => setData('bandwidth_plan_id', value)}
                    >
                      <SelectTrigger id="bandwidth_plan_id">
                        <SelectValue placeholder="Select a bandwidth plan (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No bandwidth plan</SelectItem>
                        {bandwidthPlans.map(plan => (
                          <SelectItem key={plan.id} value={plan.id.toString()}>
                            {plan.name} ({plan.download_speed}/{plan.upload_speed} Kbps)
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.bandwidth_plan_id && <p className="text-red-500 text-sm">{errors.bandwidth_plan_id}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Service Status</Label>
                    <Select
                      value={data.status}
                      onValueChange={value => setData('status', value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="suspended">Suspended</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.status && <p className="text-red-500 text-sm">{errors.status}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="comment">Comment</Label>
                    <Textarea
                      id="comment"
                      value={data.comment}
                      onChange={e => setData('comment', e.target.value)}
                      placeholder="Add any notes about this service"
                      rows={3}
                    />
                    {errors.comment && <p className="text-red-500 text-sm">{errors.comment}</p>}
                  </div>
                </CardContent>
              </Card>

              {/* Credentials */}
              <Card>
                <CardHeader>
                  <CardTitle>PPPoE Credentials</CardTitle>
                  <CardDescription>Update the password for this PPPoE service</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Username (cannot be changed)</Label>
                      <div className="p-2 bg-gray-50 rounded border font-mono">
                        {service.username}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="password">Password</Label>
                      <div className="flex">
                        <Input
                          id="password"
                          type="text"
                          value={data.password}
                          onChange={e => setData('password', e.target.value)}
                          placeholder="Password for PPPoE login"
                          className="flex-1"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          className="ml-2"
                          onClick={generatePassword}
                          disabled={generatingPassword}
                        >
                          <RefreshCcw className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-sm text-gray-500">Leave empty to keep current password</p>
                      {errors.password && <p className="text-red-500 text-sm">{errors.password}</p>}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Summary Card */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Service Summary</CardTitle>
                  <CardDescription>Review the service details before updating</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-center p-6 bg-gray-50 rounded-md">
                    <Wifi className="h-16 w-16 text-primary" />
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Service ID:</h3>
                    <p className="text-gray-700">{service.id}</p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Customer:</h3>
                    <p className="text-gray-700">{service.customer.name}</p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Username:</h3>
                    <p className="text-gray-700 font-mono">{service.username}</p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Current Status:</h3>
                    <p className="text-gray-700">{getStatusBadge(service.status)}</p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">New Status:</h3>
                    <p className="text-gray-700">{getStatusBadge(data.status)}</p>
                  </div>

                  {service.last_connected && (
                    <div className="space-y-2">
                      <h3 className="font-medium">Last Connected:</h3>
                      <p className="text-gray-700">{new Date(service.last_connected).toLocaleString()}</p>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex flex-col space-y-2">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={processing}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Update PPPoE Service
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    asChild
                  >
                    <Link href={route('services.pppoe.show', service.id)}>
                      Cancel
                    </Link>
                  </Button>
                </CardFooter>
              </Card>

              <Alert>
                <AlertTitle>Important Note</AlertTitle>
                <AlertDescription>
                  This will update the PPPoE user on the MikroTik router. If you change the status to suspended, the user will be disabled on the router.
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </form>
      </div>
    </>
  );
}
