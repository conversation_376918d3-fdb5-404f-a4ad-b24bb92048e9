import React, { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  MoreHorizontal,
  Plus,
  Search,
  RefreshCw,
  Download
} from 'lucide-react';

export default function PppoeServiceIndex({ services, filters }) {
  const [searchQuery, setSearchQuery] = useState(filters.search || '');
  const [statusFilter, setStatusFilter] = useState(filters.status || '');

  const handleSearch = (e) => {
    e.preventDefault();
    window.location.href = route('services.pppoe.index', {
      search: searchQuery,
      status: statusFilter === 'all' ? '' : statusFilter
    });
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'suspended':
        return <Badge className="bg-yellow-500">Suspended</Badge>;
      case 'terminated':
        return <Badge className="bg-red-500">Terminated</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  return (
    <AppLayout>
      <Head title="PPPoE Services" />

      <div className="container mx-auto p-6 py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">PPPoE Services</h1>
          <Button asChild>
            <Link href={route('services.pppoe.create')}>
              <Plus className="mr-2 h-4 w-4" /> New PPPoE Service
            </Link>
          </Button>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filters</CardTitle>
            <CardDescription>Filter the list of PPPoE services</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search by username or IP..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                  icon={<Search className="h-4 w-4" />}
                />
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                    <SelectItem value="terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button type="submit">
                <Search className="mr-2 h-4 w-4" /> Search
              </Button>
              <Button variant="outline" type="button" onClick={() => window.location.href = route('services.pppoe.index')}>
                <RefreshCw className="mr-2 h-4 w-4" /> Reset
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>PPPoE Services</CardTitle>
            <CardDescription>
              Showing {services.data.length} of {services.total} services
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Username</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>IP Address</TableHead>
                    <TableHead>Device</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Connected</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {services.data.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        No PPPoE services found. <Link href={route('services.pppoe.create')} className="text-blue-500 hover:underline">Create one</Link>
                      </TableCell>
                    </TableRow>
                  ) : (
                    services.data.map((service) => (
                      <TableRow key={service.id}>
                        <TableCell className="font-medium">
                          <Link href={route('services.pppoe.show', service.id)} className="text-blue-500 hover:underline">
                            {service.username}
                          </Link>
                        </TableCell>
                        <TableCell>
                          <Link href={route('customers.show', service.customer_id)} className="text-blue-500 hover:underline">
                            {service.customer.name}
                          </Link>
                        </TableCell>
                        <TableCell>{service.ip_address || 'Dynamic'}</TableCell>
                        <TableCell>{service.device.name}</TableCell>
                        <TableCell>{getStatusBadge(service.status)}</TableCell>
                        <TableCell>{service.last_connected ? new Date(service.last_connected).toLocaleString() : 'Never'}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={route('services.pppoe.show', service.id)}>View Details</Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link href={route('services.pppoe.edit', service.id)}>Edit Service</Link>
                              </DropdownMenuItem>
                              {service.status === 'active' ? (
                                <DropdownMenuItem asChild>
                                  <Link href={route('services.pppoe.disconnect', service.id)} method="post" as="button">
                                    Disconnect Session
                                  </Link>
                                </DropdownMenuItem>
                              ) : null}
                              {service.status === 'active' ? (
                                <DropdownMenuItem asChild>
                                  <Link href={route('services.pppoe.update', service.id)} method="put" data={{ status: 'suspended' }} as="button">
                                    Suspend Service
                                  </Link>
                                </DropdownMenuItem>
                              ) : null}
                              {service.status === 'suspended' ? (
                                <DropdownMenuItem asChild>
                                  <Link href={route('services.pppoe.update', service.id)} method="put" data={{ status: 'active' }} as="button">
                                    Activate Service
                                  </Link>
                                </DropdownMenuItem>
                              ) : null}
                              <DropdownMenuItem asChild>
                                <Link href={route('services.pppoe.destroy', service.id)} method="delete" as="button" className="text-red-500">
                                  Delete Service
                                </Link>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                Showing {services.from || 0} to {services.to || 0} of {services.total} services
              </div>
              <div className="flex gap-2">
                {services.prev_page_url && (
                  <Button variant="outline" asChild>
                    <Link href={services.prev_page_url}>Previous</Link>
                  </Button>
                )}
                {services.next_page_url && (
                  <Button variant="outline" asChild>
                    <Link href={services.next_page_url}>Next</Link>
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
