import React from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  ArrowLeft,
  Edit,
  Trash,
  User,
  Calendar,
  CreditCard,
  Clock,
  FileText
} from 'lucide-react';
import { formatDate, formatCurrency } from '@/lib/utils';

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string | null;
}

interface Invoice {
  id: number;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  total_amount: string;
  status: string;
}

interface Subscription {
  id: number;
  name: string;
  description: string | null;
  status: string;
  price: string;
  billing_cycle: string;
  start_date: string;
  end_date: string | null;
  next_billing_date: string;
  customer: Customer;
  invoices: Invoice[];
  created_at: string;
  updated_at: string;
}

interface SubscriptionShowProps {
  subscription: Subscription;
}

export default function SubscriptionShow({ subscription }: SubscriptionShowProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-yellow-500">Inactive</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-500">Cancelled</Badge>;
      case 'expired':
        return <Badge className="bg-gray-500">Expired</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const getInvoiceStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-500">Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case 'overdue':
        return <Badge className="bg-red-500">Overdue</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-500">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this subscription?')) {
      router.delete(route('subscriptions.destroy', subscription.id));
    }
  };

  const handleStatusChange = (newStatus: string) => {
    if (confirm(`Are you sure you want to change the status to ${newStatus}?`)) {
      router.put(route('subscriptions.update-status', subscription.id), {
        status: newStatus
      });
    }
  };

  return (
    <AppLayout>
      <Head title={`Subscription: ${subscription.name}`} />

      <div className="page-container">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('subscriptions.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Subscriptions
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">{subscription.name}</h1>
            <div className="ml-4">{getStatusBadge(subscription.status)}</div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" asChild>
              <Link href={route('subscriptions.edit', subscription.id)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              <Trash className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            {/* Subscription Details */}
            <Card>
              <CardHeader>
                <CardTitle>Subscription Details</CardTitle>
                <CardDescription>Basic information about the subscription</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <User className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <div className="text-sm font-medium text-gray-500">Customer</div>
                      <div>
                        <Link href={route('customers.show', subscription.customer.id)} className="hover:underline">
                          {subscription.customer.name}
                        </Link>
                      </div>
                      <div className="text-sm text-gray-500">{subscription.customer.email}</div>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CreditCard className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <div className="text-sm font-medium text-gray-500">Billing</div>
                      <div>{formatCurrency(parseFloat(subscription.price))} / <span className="capitalize">{subscription.billing_cycle}</span></div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <Calendar className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <div className="text-sm font-medium text-gray-500">Start Date</div>
                      <div>{formatDate(subscription.start_date)}</div>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Clock className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <div className="text-sm font-medium text-gray-500">Next Billing Date</div>
                      <div>{formatDate(subscription.next_billing_date)}</div>
                    </div>
                  </div>
                </div>

                {subscription.description && (
                  <div>
                    <div className="text-sm font-medium text-gray-500">Description</div>
                    <div className="mt-1">{subscription.description}</div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Invoices */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Invoices</CardTitle>
                  <CardDescription>Billing history for this subscription</CardDescription>
                </div>
                <Button asChild>
                  <Link href={route('subscriptions.invoices.create', subscription.id)}>
                    <FileText className="h-4 w-4 mr-2" />
                    Create Invoice
                  </Link>
                </Button>
              </CardHeader>
              <CardContent>
                {subscription.invoices.length === 0 ? (
                  <div className="text-center py-6 text-gray-500">
                    No invoices found for this subscription.
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Invoice #</TableHead>
                        <TableHead>Issue Date</TableHead>
                        <TableHead>Due Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {subscription.invoices.map((invoice) => (
                        <TableRow key={invoice.id}>
                          <TableCell className="font-medium">
                            <Link href={route('invoices.show', invoice.id)} className="hover:underline">
                              {invoice.invoice_number}
                            </Link>
                          </TableCell>
                          <TableCell>{formatDate(invoice.issue_date)}</TableCell>
                          <TableCell>{formatDate(invoice.due_date)}</TableCell>
                          <TableCell>{formatCurrency(parseFloat(invoice.total_amount))}</TableCell>
                          <TableCell>{getInvoiceStatusBadge(invoice.status)}</TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" asChild>
                              <Link href={route('invoices.show', invoice.id)}>
                                View
                              </Link>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" asChild className="w-full">
                  <Link href={route('subscriptions.invoices.index', subscription.id)}>
                    View All Invoices
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </div>

          <div className="space-y-6">
            {/* Status Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Status Actions</CardTitle>
                <CardDescription>Manage subscription status</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                {subscription.status !== 'active' && (
                  <Button
                    className="w-full bg-green-600 hover:bg-green-700"
                    onClick={() => handleStatusChange('active')}
                  >
                    Activate Subscription
                  </Button>
                )}

                {subscription.status !== 'inactive' && (
                  <Button
                    className="w-full bg-yellow-600 hover:bg-yellow-700"
                    onClick={() => handleStatusChange('inactive')}
                  >
                    Pause Subscription
                  </Button>
                )}

                {subscription.status !== 'cancelled' && (
                  <Button
                    className="w-full bg-red-600 hover:bg-red-700"
                    onClick={() => handleStatusChange('cancelled')}
                  >
                    Cancel Subscription
                  </Button>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button asChild className="w-full">
                  <Link href={route('subscriptions.invoices.create', subscription.id)}>
                    <FileText className="h-4 w-4 mr-2" />
                    Create Invoice
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link href={route('customers.show', subscription.customer.id)}>
                    <User className="h-4 w-4 mr-2" />
                    View Customer
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Subscription Info */}
            <Card>
              <CardHeader>
                <CardTitle>Subscription Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="text-sm font-medium text-gray-500">Created</div>
                  <div>{formatDate(subscription.created_at)}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Last Updated</div>
                  <div>{formatDate(subscription.updated_at)}</div>
                </div>
                {subscription.end_date && (
                  <div>
                    <div className="text-sm font-medium text-gray-500">End Date</div>
                    <div>{formatDate(subscription.end_date)}</div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
