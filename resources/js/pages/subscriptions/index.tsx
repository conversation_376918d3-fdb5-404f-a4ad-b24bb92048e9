import { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ModernTable, ModernTableBody, ModernTableCell, ModernTableHeader, ModernTableRow } from '@/components/ui/modern-table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ResponsivePagination } from '@/components/ui/simplified-pagination';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash,
  User,
  Zap
} from 'lucide-react';
import { formatDate, formatCurrency } from '@/lib/utils';

interface Customer {
  id: number;
  name: string;
  email: string;
}

interface Subscription {
  id: number;
  name: string;
  description: string | null;
  status: string;
  price: string;
  billing_cycle: string;
  start_date: string;
  end_date: string | null;
  next_billing_date: string;
  customer: Customer;
}

interface SubscriptionIndexProps {
  subscriptions: {
    data: Subscription[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  };
  filters: {
    search: string;
    status: string;
    billing_cycle: string;
  };
}

export default function SubscriptionIndex({ subscriptions, filters }: SubscriptionIndexProps) {
  const { auth } = usePage().props as any;
  const permissions = auth.permissions || [];

  // Helper function to check if user has a specific permission
  const hasPermission = (permission: string) => permissions.includes(permission);

  const [searchQuery, setSearchQuery] = useState(filters.search || '');
  const [statusFilter, setStatusFilter] = useState(filters.status || '');
  const [billingCycleFilter, setBillingCycleFilter] = useState(filters.billing_cycle || '');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    router.get(route('subscriptions.index'), {
      search: searchQuery,
      status: statusFilter === 'all' ? '' : statusFilter,
      billing_cycle: billingCycleFilter === 'all' ? '' : billingCycleFilter
    }, {
      preserveState: true,
      replace: true
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-yellow-500">Inactive</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-500">Cancelled</Badge>;
      case 'expired':
        return <Badge className="bg-gray-500">Expired</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const handleDelete = (subscriptionId: number) => {
    if (confirm('Are you sure you want to delete this subscription?')) {
      router.delete(route('subscriptions.destroy', subscriptionId));
    }
  };

  return (
    <AppLayout>
      <Head title="Subscriptions" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <h1>Subscriptions</h1>
            <p>Manage customer subscriptions and billing cycles</p>
          </div>
          <div className="flex gap-3">
            {hasPermission('create subscriptions') && (
              <Button asChild variant="outline">
                <Link href={route('subscriptions.create')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Manual Create
                </Link>
              </Button>
            )}
            {hasPermission('create subscriptions') && (
              <Button asChild className="btn-gradient">
                <Link href={route('subscriptions.streamlined.create')}>
                  <Zap className="h-4 w-4 mr-2" />
                  From Plan
                </Link>
              </Button>
            )}
          </div>
        </div>

        {/* Filters Section */}
        <Card className="card-modern mb-6">
          <CardHeader>
            <CardTitle>Filters</CardTitle>
            <CardDescription>Filter the list of subscriptions</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search by name or description..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full sm:w-48">
                <Select value={billingCycleFilter} onValueChange={setBillingCycleFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Billing Cycle" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Cycles</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="biannually">Biannually</SelectItem>
                    <SelectItem value="annually">Annually</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button type="submit" className="btn-gradient">Filter</Button>
            </form>
          </CardContent>
        </Card>

        {/* Subscriptions Table */}
        <Card className="card-modern">
          <CardHeader>
            <CardTitle>Subscription List</CardTitle>
            <CardDescription>
              {subscriptions.total} total subscriptions • Showing {subscriptions.from}-{subscriptions.to}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <ModernTable>
                <ModernTableHeader>
                  <ModernTableRow>
                    <ModernTableCell header className="min-w-[150px]">Name</ModernTableCell>
                    <ModernTableCell header className="min-w-[150px]">Customer</ModernTableCell>
                    <ModernTableCell header className="min-w-[100px] hidden sm:table-cell">Price</ModernTableCell>
                    <ModernTableCell header className="min-w-[120px] hidden md:table-cell">Billing Cycle</ModernTableCell>
                    <ModernTableCell header className="min-w-[120px] hidden lg:table-cell">Next Billing</ModernTableCell>
                    <ModernTableCell header className="min-w-[100px]">Status</ModernTableCell>
                    <ModernTableCell header className="text-right min-w-[80px]">Actions</ModernTableCell>
                  </ModernTableRow>
                </ModernTableHeader>
              <ModernTableBody>
                {subscriptions.data.length === 0 ? (
                  <ModernTableRow>
                    <ModernTableCell colSpan={7} className="text-center py-12 text-muted-foreground">
                      <div className="flex flex-col items-center gap-2">
                        <Search className="h-8 w-8 text-muted-foreground/50" />
                        <p>No subscriptions found</p>
                        <p className="text-sm">Try adjusting your filters or add a new subscription</p>
                      </div>
                    </ModernTableCell>
                  </ModernTableRow>
                ) : (
                  subscriptions.data.map((subscription) => (
                    <ModernTableRow key={subscription.id} clickable>
                      <ModernTableCell className="font-medium">
                        <Link href={route('subscriptions.show', subscription.id)} className="hover:text-primary transition-colors">
                          {subscription.name}
                        </Link>
                      </ModernTableCell>
                      <ModernTableCell>
                        <Link href={route('customers.show', subscription.customer.id)} className="hover:text-primary transition-colors flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          {subscription.customer.name}
                        </Link>
                      </ModernTableCell>
                      <ModernTableCell className="font-medium hidden sm:table-cell">{formatCurrency(parseFloat(subscription.price))}</ModernTableCell>
                      <ModernTableCell className="capitalize hidden md:table-cell">{subscription.billing_cycle}</ModernTableCell>
                      <ModernTableCell className="hidden lg:table-cell">{formatDate(subscription.next_billing_date)}</ModernTableCell>
                      <ModernTableCell>{getStatusBadge(subscription.status)}</ModernTableCell>
                      <ModernTableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {hasPermission('view subscriptions') && (
                              <DropdownMenuItem asChild>
                                <Link href={route('subscriptions.show', subscription.id)}>
                                  View Details
                                </Link>
                              </DropdownMenuItem>
                            )}
                            {hasPermission('edit subscriptions') && (
                              <DropdownMenuItem asChild>
                                <Link href={route('subscriptions.edit', subscription.id)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </Link>
                              </DropdownMenuItem>
                            )}
                            {hasPermission('delete subscriptions') && (
                              <DropdownMenuItem onClick={() => handleDelete(subscription.id)} className="text-red-600">
                                <Trash className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </ModernTableCell>
                    </ModernTableRow>
                  ))
                )}
              </ModernTableBody>
            </ModernTable>
            </div>
          </CardContent>
        </Card>

        {/* Simplified Pagination */}
        <div className="mt-6">
          <ResponsivePagination
            currentPage={subscriptions.current_page}
            lastPage={subscriptions.last_page}
            total={subscriptions.total}
            from={subscriptions.from}
            to={subscriptions.to}
            onPageChange={(page) => {
              router.get(route('subscriptions.index'), {
                page,
                search: searchQuery,
                status: statusFilter === 'all' ? '' : statusFilter,
                billing_cycle: billingCycleFilter === 'all' ? '' : billingCycleFilter
              }, {
                preserveState: true,
                replace: true
              });
            }}
          />
        </div>
      </div>
    </AppLayout>
  );
}
