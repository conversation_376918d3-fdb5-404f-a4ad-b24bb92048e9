import React, { useState } from 'react';
import { Head, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ModernTable,
  ModernTableBody,
  ModernTableCell,
  ModernTableHeader,
  ModernTableRow
} from '@/components/ui/modern-table';
import { CustomerCombobox } from '@/components/ui/customer-combobox';
import { PermissionButton } from '@/components/permissions';
import {
  DollarSign,
  Smartphone,
  CreditCard,
  Calendar,
  Filter,
  Search,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  Plus,
  Eye,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { formatCurrency, formatDate, cn } from '@/lib/utils';

interface Customer {
  id: number;
  name: string;
  email: string;
}

interface Invoice {
  id: number;
  invoice_number: string;
  total_amount: number;
}

interface Payment {
  id: number;
  amount: number;
  payment_method: string;
  status: string;
  payment_date: string | null;
  reference_number: string | null;
  transaction_id: string | null;
  phone_number: string | null;
  notes: string | null;
  customer: Customer;
  invoice: Invoice;
  confirmed_by_user: string | null;
  created_at: string;
}

interface PaymentAnalytics {
  total_payments: number;
  total_amount: number;
  method_breakdown: Array<{
    method: string;
    count: number;
    total: number;
    percentage: number;
  }>;
  status_breakdown: Array<{
    status: string;
    count: number;
    total: number;
  }>;
}

interface PaginationData {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
}

interface PaymentsListProps {
  payments: {
    data: Payment[];
  } & PaginationData;
  filters: {
    search?: string;
    payment_method?: string;
    status?: string;
    date_from?: string;
    date_to?: string;
    customer_id?: string;
    month?: string;
  };
  analytics: PaymentAnalytics;
}

export default function PaymentsList({
  payments,
  filters,
  analytics
}: PaymentsListProps) {
  const [searchValue, setSearchValue] = useState(filters.search || '');
  const [paymentMethod, setPaymentMethod] = useState(filters.payment_method || '');
  const [status, setStatus] = useState(filters.status || '');
  const [customerId, setCustomerId] = useState(filters.customer_id || '');
  const [month, setMonth] = useState(filters.month || new Date().toISOString().slice(0, 7));
  const [dateFrom, setDateFrom] = useState(filters.date_from || '');
  const [dateTo, setDateTo] = useState(filters.date_to || '');

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: {
        icon: CheckCircle,
        className: 'status-badge-active',
        label: 'Completed'
      },
      pending: {
        icon: Clock,
        className: 'status-badge-suspended',
        label: 'Pending'
      },
      failed: {
        icon: XCircle,
        className: 'status-badge-inactive',
        label: 'Failed'
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      icon: Clock,
      className: 'status-badge-inactive',
      label: status
    };

    const Icon = config.icon;

    return (
      <Badge className={config.className}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getPaymentMethodIcon = (method: string) => {
    const icons = {
      mpesa: Smartphone,
      cash: DollarSign,
    };

    const Icon = icons[method as keyof typeof icons] || CreditCard;
    return <Icon className="h-4 w-4" />;
  };

  const getPaymentMethodBadge = (method: string) => {
    const methodConfig = {
      mpesa: {
        className: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
        label: 'M-Pesa'
      },
      cash: {
        className: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
        label: 'Cash'
      },
    };

    const config = methodConfig[method as keyof typeof methodConfig] || {
      className: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
      label: method
    };

    return (
      <Badge className={config.className}>
        {getPaymentMethodIcon(method)}
        <span className="ml-1">{config.label}</span>
      </Badge>
    );
  };

  const handleFilterChange = () => {
    const params: any = {
      list: '1',
      search: searchValue || undefined,
      payment_method: paymentMethod || undefined,
      status: status || undefined,
      customer_id: customerId || undefined,
      month: month || undefined,
      date_from: dateFrom || undefined,
      date_to: dateTo || undefined,
    };

    // Remove undefined values
    Object.keys(params).forEach(key => {
      if (params[key] === undefined) {
        delete params[key];
      }
    });

    router.get(route('payments.index'), params, { preserveState: true });
  };

  const clearFilters = () => {
    setSearchValue('');
    setPaymentMethod('');
    setStatus('');
    setCustomerId('');
    setMonth(new Date().toISOString().slice(0, 7));
    setDateFrom('');
    setDateTo('');

    router.get(route('payments.index'), { list: '1' }, { preserveState: true });
  };

  const exportData = () => {
    const params = new URLSearchParams();
    if (searchValue) params.append('search', searchValue);
    if (paymentMethod) params.append('payment_method', paymentMethod);
    if (status) params.append('status', status);
    if (customerId) params.append('customer_id', customerId);
    if (month) params.append('month', month);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    const url = route('payments.export') + '?' + params.toString();
    window.open(url, '_blank');
  };

  return (
    <AppLayout>
      <Head title="Payments List" />

      <div className="page-container">
        <div className="page-header">
          <div className="page-title">
            <h1>Payments</h1>
            <p>Manage and track all payment transactions</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportData}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <PermissionButton permission="process payments" className="btn-gradient">
              <Plus className="h-4 w-4 mr-2" />
              Record Payment
            </PermissionButton>
          </div>
        </div>

        {/* Analytics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <Card className="card-modern">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Payments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.total_payments}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(analytics.total_amount)}
              </p>
            </CardContent>
          </Card>

          {analytics.method_breakdown.map((method) => (
            <Card key={method.method} className="card-modern">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  {getPaymentMethodIcon(method.method)}
                  {method.method === 'mpesa' ? 'M-Pesa' : 'Cash'} Payments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{method.count}</div>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(method.total)} ({method.percentage}%)
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Payments Table */}
        <Card className="card-modern">
          <CardHeader>
            <CardTitle>Payment Transactions</CardTitle>
            <CardDescription>
              Showing {payments.from} to {payments.to} of {payments.total} payments
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {payments.data.length === 0 ? (
              <div className="text-center py-12">
                <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No payments found</h3>
                <p className="text-muted-foreground mb-4">
                  No payment transactions match your current filters.
                </p>
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
              </div>
            ) : (
              <ModernTable>
                <ModernTableHeader>
                  <ModernTableRow>
                    <ModernTableCell header>Customer</ModernTableCell>
                    <ModernTableCell header>Invoice</ModernTableCell>
                    <ModernTableCell header>Method</ModernTableCell>
                    <ModernTableCell header>Amount</ModernTableCell>
                    <ModernTableCell header>Status</ModernTableCell>
                    <ModernTableCell header>Date</ModernTableCell>
                    <ModernTableCell header>Reference</ModernTableCell>
                    <ModernTableCell header>Confirmed By</ModernTableCell>
                    <ModernTableCell header>Actions</ModernTableCell>
                  </ModernTableRow>
                </ModernTableHeader>
                <ModernTableBody>
                  {payments.data.map((payment) => (
                    <ModernTableRow key={payment.id}>
                      <ModernTableCell>
                        <div>
                          <div className="font-medium">{payment.customer.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {payment.customer.email}
                          </div>
                        </div>
                      </ModernTableCell>
                      <ModernTableCell>
                        <Link
                          href={route('invoices.show', payment.invoice.id)}
                          className="text-primary hover:underline font-medium"
                        >
                          {payment.invoice.invoice_number}
                        </Link>
                        <div className="text-sm text-muted-foreground">
                          {formatCurrency(payment.invoice.total_amount)}
                        </div>
                      </ModernTableCell>
                      <ModernTableCell>
                        {getPaymentMethodBadge(payment.payment_method)}
                      </ModernTableCell>
                      <ModernTableCell>
                        <div className="font-medium">
                          {formatCurrency(payment.amount)}
                        </div>
                      </ModernTableCell>
                      <ModernTableCell>
                        {getStatusBadge(payment.status)}
                      </ModernTableCell>
                      <ModernTableCell>
                        <div className="flex items-center text-sm">
                          <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                          {payment.payment_date ? formatDate(payment.payment_date) : 'N/A'}
                        </div>
                      </ModernTableCell>
                      <ModernTableCell>
                        <div className="text-sm">
                          {payment.reference_number && (
                            <div>Ref: {payment.reference_number}</div>
                          )}
                          {payment.transaction_id && (
                            <div>TxID: {payment.transaction_id}</div>
                          )}
                          {payment.phone_number && (
                            <div>Phone: {payment.phone_number}</div>
                          )}
                        </div>
                      </ModernTableCell>
                      <ModernTableCell>
                        <div className="text-sm">
                          {payment.confirmed_by_user || 'System'}
                        </div>
                      </ModernTableCell>
                      <ModernTableCell>
                        <PermissionButton
                          permission="view payments"
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link href={route('payments.show', payment.id)}>
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Link>
                        </PermissionButton>
                      </ModernTableCell>
                    </ModernTableRow>
                  ))}
                </ModernTableBody>
              </ModernTable>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {payments.last_page > 1 && (
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-muted-foreground">
              Showing {payments.from} to {payments.to} of {payments.total} results
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={payments.current_page === 1}
                onClick={() => {
                  const params = { ...filters, list: '1', page: payments.current_page - 1 };
                  router.get(route('payments.index'), params, { preserveState: true });
                }}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, payments.last_page) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={payments.current_page === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        const params = { ...filters, list: '1', page };
                        router.get(route('payments.index'), params, { preserveState: true });
                      }}
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                disabled={payments.current_page === payments.last_page}
                onClick={() => {
                  const params = { ...filters, list: '1', page: payments.current_page + 1 };
                  router.get(route('payments.index'), params, { preserveState: true });
                }}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
