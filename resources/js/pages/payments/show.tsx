import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft, 
  User, 
  Calendar, 
  CreditCard, 
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Smartphone
} from 'lucide-react';
import { formatDate, formatCurrency } from '@/lib/utils';

interface Customer {
  id: number;
  name: string;
  email: string;
}

interface Invoice {
  id: number;
  invoice_number: string;
  total_amount: string;
  customer: Customer;
}

interface ConfirmedBy {
  id: number;
  name: string;
}

interface Payment {
  id: number;
  payment_method: string;
  amount: string;
  reference_number: string | null;
  transaction_id: string | null;
  status: string;
  payment_date: string | null;
  phone_number: string | null;
  mpesa_receipt_number: string | null;
  notes: string | null;
  gateway_response: any;
  confirmed_by: ConfirmedBy | null;
  invoice: Invoice;
  created_at: string;
  updated_at: string;
}

interface PaymentShowProps {
  payment: Payment;
}

export default function PaymentShow({ payment }: PaymentShowProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-500"><CheckCircle className="h-3 w-3 mr-1" />Completed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500"><Clock className="h-3 w-3 mr-1" />Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-500"><XCircle className="h-3 w-3 mr-1" />Failed</Badge>;
      case 'refunded':
        return <Badge className="bg-gray-500">Refunded</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'mpesa':
        return <Smartphone className="h-5 w-5" />;
      case 'cash':
        return <DollarSign className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  return (
    <AppLayout>
      <Head title={`Payment #${payment.id}`} />
      
      <div className="container mx-auto p-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" asChild>
              <Link href={route('payments.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Payments
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Payment #{payment.id}</h1>
              <p className="text-gray-600 mt-2">Payment details and transaction information</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusBadge(payment.status)}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Payment Details */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  Payment Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium text-gray-500">Payment Method</div>
                    <div className="flex items-center mt-1">
                      {getPaymentMethodIcon(payment.payment_method)}
                      <span className="ml-2 capitalize font-medium">{payment.payment_method}</span>
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">Amount</div>
                    <div className="text-2xl font-bold text-green-600 mt-1">
                      {formatCurrency(payment.amount)}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium text-gray-500">Payment Date</div>
                    <div className="flex items-center mt-1">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      {payment.payment_date ? formatDate(payment.payment_date) : 'N/A'}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">Status</div>
                    <div className="mt-1">
                      {getStatusBadge(payment.status)}
                    </div>
                  </div>
                </div>

                {payment.payment_method === 'mpesa' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-medium text-gray-500">Transaction ID</div>
                      <div className="mt-1 font-mono text-sm">
                        {payment.transaction_id || 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-500">Phone Number</div>
                      <div className="mt-1">
                        {payment.phone_number || 'N/A'}
                      </div>
                    </div>
                  </div>
                )}

                {payment.payment_method === 'cash' && payment.reference_number && (
                  <div>
                    <div className="text-sm font-medium text-gray-500">Reference Number</div>
                    <div className="mt-1 font-mono text-sm">
                      {payment.reference_number}
                    </div>
                  </div>
                )}

                {payment.notes && (
                  <div>
                    <div className="text-sm font-medium text-gray-500">Notes</div>
                    <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                      {payment.notes}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Invoice Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Related Invoice
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">
                      <Link 
                        href={route('invoices.show', payment.invoice.id)}
                        className="text-blue-600 hover:underline"
                      >
                        {payment.invoice.invoice_number}
                      </Link>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      Total: {formatCurrency(payment.invoice.total_amount)}
                    </div>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={route('invoices.show', payment.invoice.id)}>
                      View Invoice
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Customer & System Info */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <div className="text-sm font-medium text-gray-500">Name</div>
                    <div className="mt-1 font-medium">{payment.invoice.customer.name}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">Email</div>
                    <div className="mt-1">{payment.invoice.customer.email}</div>
                  </div>
                  <div className="pt-2">
                    <Button variant="outline" size="sm" asChild className="w-full">
                      <Link href={route('customers.show', payment.invoice.customer.id)}>
                        View Customer
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <div className="text-sm font-medium text-gray-500">Confirmed By</div>
                  <div className="mt-1">
                    {payment.confirmed_by ? payment.confirmed_by.name : 'System'}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Created</div>
                  <div className="mt-1 text-sm">
                    {formatDate(payment.created_at)}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Last Updated</div>
                  <div className="mt-1 text-sm">
                    {formatDate(payment.updated_at)}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
