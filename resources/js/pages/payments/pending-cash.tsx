import React from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  CheckCircle, 
  AlertCircle,
  User,
  Calendar,
  CreditCard
} from 'lucide-react';
import { formatDate, formatCurrency } from '@/lib/utils';

interface Customer {
  id: number;
  name: string;
  email: string;
}

interface Invoice {
  id: number;
  invoice_number: string;
  total_amount: string;
  customer: Customer;
}

interface Payment {
  id: number;
  payment_method: string;
  amount: string;
  reference_number: string | null;
  status: string;
  payment_date: string | null;
  notes: string | null;
  invoice: Invoice;
  created_at: string;
}

interface PendingCashPaymentsProps {
  payments: {
    data: Payment[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  };
}

export default function PendingCashPayments({ payments }: PendingCashPaymentsProps) {
  const handleConfirmPayment = (paymentId: number) => {
    if (confirm('Are you sure you want to confirm this cash payment?')) {
      router.post(route('payments.confirm', paymentId));
    }
  };

  return (
    <AppLayout>
      <Head title="Pending Cash Payments" />
      
      <div className="container mx-auto p-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">Pending Cash Payments</h1>
            <p className="text-gray-600 mt-2">Cash payments awaiting admin confirmation</p>
          </div>
          <Badge variant="outline" className="text-lg px-3 py-1">
            {payments.total} pending
          </Badge>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Cash Payments Requiring Confirmation</CardTitle>
            <CardDescription>
              Review and confirm cash payments submitted by staff
            </CardDescription>
          </CardHeader>
          <CardContent>
            {payments.data.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No pending payments</h3>
                <p className="text-gray-500">All cash payments have been processed.</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Invoice</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Payment Date</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payments.data.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-gray-500" />
                          <div>
                            <div className="font-medium">{payment.invoice.customer.name}</div>
                            <div className="text-sm text-gray-500">{payment.invoice.customer.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Link 
                          href={route('invoices.show', payment.invoice.id)}
                          className="text-blue-600 hover:underline font-medium"
                        >
                          {payment.invoice.invoice_number}
                        </Link>
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(payment.amount)}
                      </TableCell>
                      <TableCell>
                        {payment.reference_number || 'N/A'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                          {payment.payment_date ? formatDate(payment.payment_date) : 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <AlertCircle className="h-4 w-4 mr-2 text-yellow-500" />
                          {formatDate(payment.created_at)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          onClick={() => handleConfirmPayment(payment.id)}
                          size="sm"
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Confirm
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
