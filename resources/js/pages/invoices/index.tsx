import React, { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ResponsivePagination } from '@/components/ui/simplified-pagination';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  MoreHorizontal,
  Edit,
  Trash,
  User,
  Download,
  Mail,
  FileText
} from 'lucide-react';
import { formatDate, formatCurrency } from '@/lib/utils';

interface Customer {
  id: number;
  name: string;
  email: string;
}

interface Subscription {
  id: number;
  name: string;
}

interface Invoice {
  id: number;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  total_amount: string;
  status: string;
  customer: Customer;
  subscription: Subscription | null;
}

interface InvoiceIndexProps {
  invoices: {
    data: Invoice[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  };
  filters: {
    search: string;
    status: string;
    from_date: string;
    to_date: string;
  };
}

export default function InvoiceIndex({ invoices, filters }: InvoiceIndexProps) {
  const { auth } = usePage().props as any;
  const permissions = auth.permissions || [];

  // Helper function to check if user has a specific permission
  const hasPermission = (permission: string) => permissions.includes(permission);

  const [searchQuery, setSearchQuery] = useState(filters.search || '');
  const [statusFilter, setStatusFilter] = useState(filters.status || '');
  const [fromDate, setFromDate] = useState(filters.from_date || '');
  const [toDate, setToDate] = useState(filters.to_date || '');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    router.get(route('invoices.index'), {
      search: searchQuery,
      status: statusFilter === 'all' ? '' : statusFilter,
      from_date: fromDate,
      to_date: toDate
    }, {
      preserveState: true,
      replace: true
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-500">Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case 'overdue':
        return <Badge className="bg-red-500">Overdue</Badge>;
      case 'draft':
        return <Badge className="bg-gray-400">Draft</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-500">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const handleDelete = (invoiceId: number) => {
    if (confirm('Are you sure you want to delete this invoice?')) {
      router.delete(route('invoices.destroy', invoiceId));
    }
  };

  return (
    <AppLayout>
      <Head title="Invoices" />

      <div className="container mx-auto p-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Invoices</h1>
          {hasPermission('create invoices') && (
            <Button asChild>
              <Link href={route('invoices.create')}>
                <Plus className="h-4 w-4 mr-2" />
                Create Invoice
              </Link>
            </Button>
          )}
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filters</CardTitle>
            <CardDescription>Filter the list of invoices</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search by invoice number or customer..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="overdue">Overdue</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full sm:w-48">
                <Input
                  type="date"
                  placeholder="From Date"
                  value={fromDate}
                  onChange={(e) => setFromDate(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="w-full sm:w-48">
                <Input
                  type="date"
                  placeholder="To Date"
                  value={toDate}
                  onChange={(e) => setToDate(e.target.value)}
                  className="w-full"
                />
              </div>
              <Button type="submit">Filter</Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[120px]">Invoice #</TableHead>
                    <TableHead className="min-w-[150px]">Customer</TableHead>
                    <TableHead className="min-w-[100px] hidden sm:table-cell">Issue Date</TableHead>
                    <TableHead className="min-w-[100px] hidden md:table-cell">Due Date</TableHead>
                    <TableHead className="min-w-[100px]">Amount</TableHead>
                    <TableHead className="min-w-[100px]">Status</TableHead>
                    <TableHead className="text-right min-w-[80px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {invoices.data.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                      No invoices found. Try adjusting your filters or create a new invoice.
                    </TableCell>
                  </TableRow>
                ) : (
                  invoices.data.map((invoice) => (
                    <TableRow key={invoice.id}>
                      <TableCell className="font-medium">
                        <Link href={route('invoices.show', invoice.id)} className="hover:underline">
                          {invoice.invoice_number}
                        </Link>
                      </TableCell>
                      <TableCell>
                        {invoice.customer ? (
                          <Link href={route('customers.show', invoice.customer.id)} className="hover:underline flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            {invoice.customer.name}
                          </Link>
                        ) : (
                          <span className="text-muted-foreground flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            No customer assigned
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="hidden sm:table-cell">{formatDate(invoice.issue_date)}</TableCell>
                      <TableCell className="hidden md:table-cell">{formatDate(invoice.due_date)}</TableCell>
                      <TableCell>{formatCurrency(invoice.total_amount)}</TableCell>
                      <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {hasPermission('view invoices') && (
                              <DropdownMenuItem asChild>
                                <Link href={route('invoices.show', invoice.id)}>
                                  View Details
                                </Link>
                              </DropdownMenuItem>
                            )}
                            {hasPermission('edit invoices') && (
                              <DropdownMenuItem asChild>
                                <Link href={route('invoices.edit', invoice.id)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </Link>
                              </DropdownMenuItem>
                            )}
                            {hasPermission('view invoices') && (
                              <DropdownMenuItem asChild>
                                <a href={route('invoices.view-pdf', invoice.id)} target="_blank">
                                  <FileText className="h-4 w-4 mr-2" />
                                  View PDF
                                </a>
                              </DropdownMenuItem>
                            )}
                            {hasPermission('view invoices') && (
                              <DropdownMenuItem asChild>
                                <a href={`/invoices/${invoice.id}/download`}>
                                  <Download className="h-4 w-4 mr-2" />
                                  Download PDF
                                </a>
                              </DropdownMenuItem>
                            )}
                            {hasPermission('edit invoices') && (
                              <DropdownMenuItem asChild>
                                <Link href={`/invoices/${invoice.id}/send`}>
                                  <Mail className="h-4 w-4 mr-2" />
                                  Send to Customer
                                </Link>
                              </DropdownMenuItem>
                            )}
                            {hasPermission('delete invoices') && (
                              <DropdownMenuItem onClick={() => handleDelete(invoice.id)} className="text-red-600">
                                <Trash className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            </div>
          </CardContent>
        </Card>

        {/* Simplified Pagination */}
        <div className="mt-6">
          <ResponsivePagination
            currentPage={invoices.current_page}
            lastPage={invoices.last_page}
            total={invoices.total}
            from={invoices.from}
            to={invoices.to}
            onPageChange={(page) => {
              router.get(route('invoices.index'), {
                page,
                search: searchQuery,
                status: statusFilter === 'all' ? '' : statusFilter,
                from_date: fromDate,
                to_date: toDate
              }, {
                preserveState: true,
                replace: true
              });
            }}
          />
        </div>
      </div>
    </AppLayout>
  );
}
