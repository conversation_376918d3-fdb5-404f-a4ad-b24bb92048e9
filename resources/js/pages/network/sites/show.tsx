import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  MapPin,
  Building2,
  Edit,
  Trash2,
  Server,
  Network,
  Activity,
  Calendar,
  Clock
} from 'lucide-react';

interface Site {
  id: number;
  name: string;
  description: string | null;
  address: string | null;
  status: string;
  created_at: string;
  updated_at: string;
  devices?: Device[];
}

interface Device {
  id: number;
  name: string;
  description: string | null;
  ip_address: string;
  status: string;
  detected_model: string | null;
  last_connected_at: string | null;
}

interface Props {
  site: Site;
}

export default function ShowNetworkSite({ site }: Props) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="status-badge-active">Active</Badge>;
      case 'inactive':
        return <Badge className="status-badge-inactive">Inactive</Badge>;
      case 'maintenance':
        return <Badge className="status-badge-suspended">Maintenance</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const deviceStats = {
    total: site.devices?.length || 0,
    active: site.devices?.filter(d => d.status === 'active').length || 0,
    inactive: site.devices?.filter(d => d.status === 'inactive').length || 0,
  };

  return (
    <AppLayout>
      <Head title={`${site.name} - Network Site`} />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center gap-3">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/network/sites">
                  <ArrowLeft className="h-4 w-4" />
                </Link>
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <Building2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1>{site.name}</h1>
                  <p>Network site details and device management</p>
                </div>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href={`/network/sites/${site.id}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Site
              </Link>
            </Button>
            <Button variant="destructive">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Site
            </Button>
          </div>
        </div>

        {/* Site Information */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Site Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Name</label>
                <p className="text-lg font-medium">{site.name}</p>
              </div>
              
              {site.description && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Description</label>
                  <p className="text-sm">{site.description}</p>
                </div>
              )}
              
              {site.address && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Address</label>
                  <p className="text-sm">{site.address}</p>
                </div>
              )}
              
              <div>
                <label className="text-sm font-medium text-muted-foreground">Status</label>
                <div className="mt-1">
                  {getStatusBadge(site.status)}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Site Statistics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-primary">{deviceStats.total}</div>
                  <div className="text-sm text-muted-foreground">Total Devices</div>
                </div>
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{deviceStats.active}</div>
                  <div className="text-sm text-muted-foreground">Active Devices</div>
                </div>
              </div>
              
              <div className="space-y-2 pt-4 border-t">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Created</span>
                  <span>{new Date(site.created_at).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Last Updated</span>
                  <span>{new Date(site.updated_at).toLocaleDateString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Devices at this Site */}
        <Card className="card-modern">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  Network Devices
                </CardTitle>
                <CardDescription>
                  Devices located at this site
                </CardDescription>
              </div>
              <Button asChild>
                <Link href="/network/devices/create">
                  Add Device
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {!site.devices || site.devices.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <Server className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                <p className="text-lg mb-2">No devices found</p>
                <p className="text-sm mb-4">Add network devices to this site to get started</p>
                <Button asChild>
                  <Link href="/network/devices/create">
                    Add First Device
                  </Link>
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {site.devices.map((device) => (
                  <div key={device.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                        <Network className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <h4 className="font-medium">{device.name}</h4>
                        <p className="text-sm text-muted-foreground">{device.ip_address}</p>
                        {device.description && (
                          <p className="text-xs text-muted-foreground">{device.description}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-right text-sm">
                        <div>{device.detected_model || 'Unknown Model'}</div>
                        <div className="text-muted-foreground">
                          {device.last_connected_at 
                            ? `Connected ${new Date(device.last_connected_at).toLocaleDateString()}`
                            : 'Never connected'
                          }
                        </div>
                      </div>
                      {getStatusBadge(device.status)}
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/network/devices/${device.id}`}>
                          View
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
