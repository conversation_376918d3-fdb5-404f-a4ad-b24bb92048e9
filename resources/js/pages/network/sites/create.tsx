import AppLayout from '@/layouts/app-layout';
import { Head, useForm, Link } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Network',
    href: '/network',
  },
  {
    title: 'Sites',
    href: '/network/sites',
  },
  {
    title: 'Create',
    href: '/network/sites/create',
  },
];

interface FormData {
  name: string;
  description: string;
  address: string;
  status: string;
}

export default function CreateNetworkSite() {
  const { data, setData, post, processing, errors, reset } = useForm<FormData>({
    name: '',
    description: '',
    address: '',
    status: 'active',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setData(name as keyof FormData, value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post('/network/sites');
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Network Site" />
      <div className="flex flex-col gap-6 p-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-800">Create Network Site</h1>
        </div>

        <div className="bg-white shadow-sm rounded-lg p-6">
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 gap-6">
              {/* Site Name */}
              <div>
                <Label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Site Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  type="text"
                  name="name"
                  id="name"
                  value={data.name}
                  onChange={handleChange}
                  className={`mt-1 ${errors.name ? 'border-red-300 ring-red-300' : ''}`}
                  aria-invalid={errors.name ? 'true' : 'false'}
                  placeholder="Enter site name (e.g., Main Office, Tower Site 1)"
                />
                {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
              </div>

              {/* Description */}
              <div>
                <Label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </Label>
                <Textarea
                  name="description"
                  id="description"
                  rows={3}
                  value={data.description}
                  onChange={handleChange}
                  className="mt-1"
                  placeholder="Optional description of the site"
                />
              </div>

              {/* Address */}
              <div>
                <Label htmlFor="address" className="block text-sm font-medium text-gray-700">
                  Address
                </Label>
                <Input
                  type="text"
                  name="address"
                  id="address"
                  value={data.address}
                  onChange={handleChange}
                  className="mt-1"
                  placeholder="Full address of the site"
                />
              </div>

              {/* Status */}
              <div>
                <Label htmlFor="status" className="block text-sm font-medium text-gray-700">
                  Status
                </Label>
                <Select name="status" value={data.status} onValueChange={(value) => {
                  setData('status', value);
                }}>
                  <SelectTrigger id="status" className="mt-1">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <Link
                href="/network/sites"
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={processing}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {processing ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </>
                ) : (
                  'Create Site'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AppLayout>
  );
}
