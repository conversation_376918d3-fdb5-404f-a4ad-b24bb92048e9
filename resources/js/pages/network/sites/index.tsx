import { useState } from 'react';
import { Head, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ModernTable, ModernTableBody, ModernTableCell, ModernTableHeader, ModernTableRow } from '@/components/ui/modern-table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { StatsCard } from '@/components/ui/stats-card';
import {
  MapPin,
  Building2,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Activity,
  AlertCircle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

import { type BreadcrumbItem } from '@/types';

interface Site {
  id: number;
  name: string;
  description: string | null;
  address: string | null;
  status: string;
  created_at: string;
  updated_at: string;
}

interface Props {
  sites: Site[];
}

export default function NetworkSites({ sites }: Props) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const filteredSites = sites.filter(site => {
    const matchesSearch = site.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (site.description && site.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         (site.address && site.address.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'all' || site.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const stats = {
    total: sites.length,
    active: sites.filter(s => s.status === 'active').length,
    inactive: sites.filter(s => s.status === 'inactive').length,
    maintenance: sites.filter(s => s.status === 'maintenance').length,
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="status-badge-active">Active</Badge>;
      case 'inactive':
        return <Badge className="status-badge-inactive">Inactive</Badge>;
      case 'maintenance':
        return <Badge className="status-badge-suspended">Maintenance</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleDelete = (siteId: number) => {
    if (confirm('Are you sure you want to delete this site?')) {
      router.delete(`/network/sites/${siteId}`);
    }
  };

  return (
    <AppLayout>
      <Head title="Network Sites" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <h1>Network Sites</h1>
            <p>Manage your network infrastructure locations and facilities</p>
          </div>
          <Button asChild className="btn-gradient">
            <Link href="/network/sites/create">
              <Plus className="h-4 w-4 mr-2" />
              Add Site
            </Link>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="Total Sites"
            value={stats.total}
            icon={Building2}
            trend={{ value: 0, isPositive: true }}
          />
          <StatsCard
            title="Active Sites"
            value={stats.active}
            icon={Activity}
            trend={{ value: 0, isPositive: true }}
            className="text-green-600"
          />
          <StatsCard
            title="Inactive Sites"
            value={stats.inactive}
            icon={AlertCircle}
            trend={{ value: 0, isPositive: false }}
            className="text-yellow-600"
          />
          <StatsCard
            title="Maintenance"
            value={stats.maintenance}
            icon={AlertCircle}
            trend={{ value: 0, isPositive: false }}
            className="text-red-600"
          />
        </div>

        {/* Filters and Search */}
        <Card className="card-modern">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search sites by name, description, or address..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 input-modern"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sites Table */}
        <Card className="card-modern">
          <CardHeader>
            <CardTitle>Network Sites</CardTitle>
            <CardDescription>
              {filteredSites.length} of {sites.length} sites
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <ModernTable>
              <ModernTableHeader>
                <ModernTableRow>
                  <ModernTableCell header>Name</ModernTableCell>
                  <ModernTableCell header>Description</ModernTableCell>
                  <ModernTableCell header>Address</ModernTableCell>
                  <ModernTableCell header>Status</ModernTableCell>
                  <ModernTableCell header className="text-right">Actions</ModernTableCell>
                </ModernTableRow>
              </ModernTableHeader>
              <ModernTableBody>
                {filteredSites.length === 0 ? (
                  <ModernTableRow>
                    <ModernTableCell colSpan={5} className="text-center py-12 text-muted-foreground">
                      <div className="flex flex-col items-center gap-2">
                        <MapPin className="h-8 w-8 text-muted-foreground/50" />
                        <p>No sites found</p>
                        <p className="text-sm">Try adjusting your filters or add a new site</p>
                      </div>
                    </ModernTableCell>
                  </ModernTableRow>
                ) : (
                  filteredSites.map((site) => (
                    <ModernTableRow key={site.id} clickable>
                      <ModernTableCell className="font-medium">
                        <Link href={`/network/sites/${site.id}`} className="hover:text-primary transition-colors">
                          {site.name}
                        </Link>
                      </ModernTableCell>
                      <ModernTableCell className="text-muted-foreground">
                        {site.description || '-'}
                      </ModernTableCell>
                      <ModernTableCell className="text-muted-foreground">
                        {site.address || '-'}
                      </ModernTableCell>
                      <ModernTableCell>
                        {getStatusBadge(site.status)}
                      </ModernTableCell>
                      <ModernTableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/network/sites/${site.id}`}>
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/network/sites/${site.id}/edit`}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Site
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDelete(site.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete Site
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </ModernTableCell>
                    </ModernTableRow>
                  ))
                )}
              </ModernTableBody>
            </ModernTable>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
