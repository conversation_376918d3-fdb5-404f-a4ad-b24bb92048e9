import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Network',
    href: '/network',
  },
  {
    title: 'Devices',
    href: '/network/devices',
  },
  {
    title: 'Create',
    href: '/network/devices/create',
  },
];

interface FormData {
  name: string;
  description: string;
  ip_address: string;
  status: string;
  site_id: string;
  api_username: string;
  api_password: string;
  api_port: string;
}

interface Props {
  sites: { id: number; name: string }[];
}

export default function CreateNetworkDevice({ sites }: Props) {
  const { data, setData, post, processing, errors, reset } = useForm<FormData>({
    name: '',
    description: '',
    ip_address: '',
    status: 'active',
    site_id: '',
    api_username: '',
    api_password: '',
    api_port: '8728',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setData(name as keyof FormData, value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    post(route('network.devices.store'), {
      onSuccess: () => {
        // Reset form after successful submission
        reset();
        // Redirect to devices list after successful creation
        window.location.href = '/network/devices';
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Network Device" />
      <div className="flex flex-col gap-6 p-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-800">Create Network Device</h1>
        </div>

        <div className="bg-white shadow-sm rounded-lg p-6">
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Device Name */}
              <div className="col-span-2">
                <Label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Device Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  type="text"
                  name="name"
                  id="name"
                  value={data.name}
                  onChange={handleChange}
                  className={`mt-1 ${errors.name ? 'border-red-300 ring-red-300' : ''}`}
                  aria-invalid={errors.name ? 'true' : 'false'}
                  placeholder="Enter device name (e.g., Router-Main, Switch-01)"
                />
                {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
              </div>

              {/* Site */}
              <div>
                <Label htmlFor="site_id" className="block text-sm font-medium text-gray-700">
                  Site <span className="text-red-500">*</span>
                </Label>
                <Select name="site_id" value={data.site_id} onValueChange={(value) => {
                  setData('site_id', value);
                }}>
                  <SelectTrigger id="site_id" className="mt-1">
                    <SelectValue placeholder="Select site" />
                  </SelectTrigger>
                  <SelectContent>
                    {sites.map((site) => (
                      <SelectItem key={site.id} value={site.id.toString()}>
                        {site.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.site_id && <p className="mt-1 text-sm text-red-600">{errors.site_id}</p>}
              </div>

              {/* IP Address */}
              <div>
                <Label htmlFor="ip_address" className="block text-sm font-medium text-gray-700">
                  IP Address <span className="text-red-500">*</span>
                </Label>
                <Input
                  type="text"
                  name="ip_address"
                  id="ip_address"
                  value={data.ip_address}
                  onChange={handleChange}
                  className={`mt-1 ${errors.ip_address ? 'border-red-300 ring-red-300' : ''}`}
                  aria-invalid={errors.ip_address ? 'true' : 'false'}
                  placeholder="***********"
                />
                {errors.ip_address && <p className="mt-1 text-sm text-red-600">{errors.ip_address}</p>}
              </div>

              {/* MikroTik API Username */}
              <div>
                <Label htmlFor="api_username" className="block text-sm font-medium text-gray-700">
                  API Username
                </Label>
                <Input
                  type="text"
                  name="api_username"
                  id="api_username"
                  value={data.api_username}
                  onChange={handleChange}
                  className="mt-1"
                  placeholder="admin"
                />
              </div>

              {/* MikroTik API Password */}
              <div>
                <Label htmlFor="api_password" className="block text-sm font-medium text-gray-700">
                  API Password
                </Label>
                <Input
                  type="password"
                  name="api_password"
                  id="api_password"
                  value={data.api_password}
                  onChange={handleChange}
                  className="mt-1"
                  placeholder="Enter API password"
                />
              </div>

              {/* API Port */}
              <div>
                <Label htmlFor="api_port" className="block text-sm font-medium text-gray-700">
                  API Port
                </Label>
                <Input
                  type="number"
                  name="api_port"
                  id="api_port"
                  value={data.api_port}
                  onChange={handleChange}
                  className="mt-1"
                  placeholder="8728"
                />
              </div>

              {/* Status */}
              <div>
                <Label htmlFor="status" className="block text-sm font-medium text-gray-700">
                  Status
                </Label>
                <Select name="status" value={data.status} onValueChange={(value) => {
                  setData('status', value);
                }}>
                  <SelectTrigger id="status" className="mt-1">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Description */}
              <div className="col-span-2">
                <Label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </Label>
                <Textarea
                  name="description"
                  id="description"
                  rows={3}
                  value={data.description}
                  onChange={handleChange}
                  className="mt-1"
                  placeholder="Optional description of the MikroTik device"
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => window.location.href = '/network/devices'}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={processing}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {processing ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </>
                ) : (
                  'Create Device'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AppLayout>
  );
}
