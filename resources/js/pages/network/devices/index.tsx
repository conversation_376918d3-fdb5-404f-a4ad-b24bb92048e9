import { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ModernTable, ModernTableBody, ModernTableCell, ModernTableHeader, ModernTableRow } from '@/components/ui/modern-table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { StatsCard } from '@/components/ui/stats-card';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import {
  Server,
  Router,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Activity,
  AlertCircle,
  Network,
  Wifi,
  Cable
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { type BreadcrumbItem } from '@/types';

interface Device {
  id: number;
  name: string;
  description: string | null;
  ip_address: string;
  status: string;
  api_username: string | null;
  api_port: number;
  detected_model: string | null;
  detected_version: string | null;
  last_connected_at: string | null;
  site: {
    id: number;
    name: string;
  };
}

interface Props {
  devices: {
    data: Device[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export default function NetworkDevices({ devices }: Props) {
  const { auth } = usePage().props as any;
  const permissions = auth.permissions || [];

  // Helper function to check if user has a specific permission
  const hasPermission = (permission: string) => permissions.includes(permission);

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [siteFilter, setSiteFilter] = useState('all');

  const filteredDevices = devices.data.filter(device => {
    const matchesSearch = device.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         device.ip_address.includes(searchTerm) ||
                         (device.description && device.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         device.site.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || device.status === statusFilter;
    const matchesSite = siteFilter === 'all' || device.site.id.toString() === siteFilter;
    return matchesSearch && matchesStatus && matchesSite;
  });

  const stats = {
    total: devices.data.length,
    active: devices.data.filter(d => d.status === 'active').length,
    inactive: devices.data.filter(d => d.status === 'inactive').length,
    maintenance: devices.data.filter(d => d.status === 'maintenance').length,
  };

  const uniqueSites = Array.from(new Set(devices.data.filter(d => d.site).map(d => d.site.id)))
    .map(id => devices.data.find(d => d.site && d.site.id === id)?.site)
    .filter(Boolean);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="status-badge-active">Active</Badge>;
      case 'inactive':
        return <Badge className="status-badge-inactive">Inactive</Badge>;
      case 'maintenance':
        return <Badge className="status-badge-suspended">Maintenance</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleDelete = (deviceId: number) => {
    if (confirm('Are you sure you want to delete this device?')) {
      router.delete(`/network/devices/${deviceId}`);
    }
  };

  return (
    <AppLayout>
      <Head title="Network Devices" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <h1>Network Devices</h1>
            <p>Manage your MikroTik routers, switches, and network equipment</p>
          </div>
          {hasPermission('manage network devices') && (
            <Button asChild className="btn-gradient">
              <Link href="/network/devices/create">
                <Plus className="h-4 w-4 mr-2" />
                Add Device
              </Link>
            </Button>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="Total Devices"
            value={stats.total}
            icon={Server}
            trend={{ value: 0, isPositive: true }}
          />
          <StatsCard
            title="Active Devices"
            value={stats.active}
            icon={Activity}
            trend={{ value: 0, isPositive: true }}
            className="text-green-600"
          />
          <StatsCard
            title="Inactive Devices"
            value={stats.inactive}
            icon={AlertCircle}
            trend={{ value: 0, isPositive: false }}
            className="text-yellow-600"
          />
          <StatsCard
            title="Maintenance"
            value={stats.maintenance}
            icon={AlertCircle}
            trend={{ value: 0, isPositive: false }}
            className="text-red-600"
          />
        </div>

        {/* Filters and Search */}
        <Card className="card-modern">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search devices by name, IP address, or site..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 input-modern"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={siteFilter} onValueChange={setSiteFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by site" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sites</SelectItem>
                    {uniqueSites.map((site) => (
                      <SelectItem key={site?.id} value={site?.id.toString() || ''}>
                        {site?.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Devices Table */}
        <Card className="card-modern">
          <CardHeader>
            <CardTitle>Network Devices</CardTitle>
            <CardDescription>
              {filteredDevices.length} of {devices.data.length} devices • Page {devices.current_page} of {devices.last_page}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <ModernTable>
              <ModernTableHeader>
                <ModernTableRow>
                  <ModernTableCell header>Device</ModernTableCell>
                  <ModernTableCell header>IP Address</ModernTableCell>
                  <ModernTableCell header>Site</ModernTableCell>
                  <ModernTableCell header>Model</ModernTableCell>
                  <ModernTableCell header>Status</ModernTableCell>
                  <ModernTableCell header>Last Connected</ModernTableCell>
                  <ModernTableCell header className="text-right">Actions</ModernTableCell>
                </ModernTableRow>
              </ModernTableHeader>
              <ModernTableBody>
                {filteredDevices.length === 0 ? (
                  <ModernTableRow>
                    <ModernTableCell colSpan={7} className="text-center py-12 text-muted-foreground">
                      <div className="flex flex-col items-center gap-2">
                        <Server className="h-8 w-8 text-muted-foreground/50" />
                        <p>No devices found</p>
                        <p className="text-sm">Try adjusting your filters or add a new device</p>
                      </div>
                    </ModernTableCell>
                  </ModernTableRow>
                ) : (
                  filteredDevices.map((device) => (
                    <ModernTableRow key={device.id} clickable>
                      <ModernTableCell className="font-medium">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                            <Router className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <Link href={`/network/devices/${device.id}`} className="hover:text-primary transition-colors">
                              {device.name}
                            </Link>
                            {device.description && (
                              <div className="text-xs text-muted-foreground">{device.description}</div>
                            )}
                          </div>
                        </div>
                      </ModernTableCell>
                      <ModernTableCell className="text-muted-foreground font-mono">
                        {device.ip_address}
                      </ModernTableCell>
                      <ModernTableCell className="text-muted-foreground">
                        {device.site ? (
                          <Link href={`/network/sites/${device.site.id}`} className="hover:text-primary transition-colors">
                            {device.site.name}
                          </Link>
                        ) : (
                          <span className="text-muted-foreground">No site assigned</span>
                        )}
                      </ModernTableCell>
                      <ModernTableCell className="text-muted-foreground">
                        {device.detected_model || 'Unknown'}
                      </ModernTableCell>
                      <ModernTableCell>
                        {getStatusBadge(device.status)}
                      </ModernTableCell>
                      <ModernTableCell className="text-muted-foreground">
                        {device.last_connected_at
                          ? new Date(device.last_connected_at).toLocaleDateString()
                          : 'Never'
                        }
                      </ModernTableCell>
                      <ModernTableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {hasPermission('view network') && (
                              <DropdownMenuItem asChild>
                                <Link href={`/network/devices/${device.id}`}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  View Details
                                </Link>
                              </DropdownMenuItem>
                            )}
                            {hasPermission('manage network devices') && (
                              <DropdownMenuItem asChild>
                                <Link href={`/network/devices/${device.id}/interfaces`}>
                                  <Network className="h-4 w-4 mr-2" />
                                  Manage Interfaces
                                </Link>
                              </DropdownMenuItem>
                            )}
                            {hasPermission('manage network devices') && (
                              <DropdownMenuItem asChild>
                                <Link href={`/network/devices/${device.id}/edit`}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit Device
                                </Link>
                              </DropdownMenuItem>
                            )}
                            {hasPermission('manage network devices') && (
                              <DropdownMenuItem
                                onClick={() => handleDelete(device.id)}
                                className="text-destructive"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete Device
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </ModernTableCell>
                    </ModernTableRow>
                  ))
                )}
              </ModernTableBody>
            </ModernTable>
          </CardContent>
        </Card>

        {/* Pagination */}
        {devices.last_page > 1 && (
          <div className="flex justify-center">
            <Pagination>
              <PaginationContent>
                {devices.current_page > 1 && (
                  <PaginationItem>
                    <PaginationPrevious href={`?page=${devices.current_page - 1}`} />
                  </PaginationItem>
                )}

                {Array.from({ length: devices.last_page }, (_, i) => i + 1)
                  .filter(page =>
                    page === 1 ||
                    page === devices.last_page ||
                    Math.abs(page - devices.current_page) <= 2
                  )
                  .map((page, index, array) => (
                    <PaginationItem key={page}>
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="px-2">...</span>
                      )}
                      <PaginationLink
                        href={`?page=${page}`}
                        isActive={page === devices.current_page}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                {devices.current_page < devices.last_page && (
                  <PaginationItem>
                    <PaginationNext href={`?page=${devices.current_page + 1}`} />
                  </PaginationItem>
                )}
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
