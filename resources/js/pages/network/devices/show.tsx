import { useState } from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { StatsCard } from '@/components/ui/stats-card';
import { ModernTable, ModernTableBody, ModernTableCell, ModernTableHeader, ModernTableRow } from '@/components/ui/modern-table';
import {
  ArrowLeft,
  Router,
  Network,
  Edit,
  Trash2,
  Activity,
  Zap,
  Server,
  Cpu,
  HardDrive,
  MemoryStick,
  Calendar,
  Clock,
  MapPin,
  Settings,
  Wifi,
  Cable,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Eye
} from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import axios from 'axios';

interface DeviceData {
  id: number;
  name: string;
  description: string | null;
  ip_address: string;
  status: string;
  api_username: string | null;
  api_port: number;
  detected_model: string | null;
  detected_version: string | null;
  last_connected_at: string | null;
  site: {
    id: number;
    name: string;
  };
  interfaces: {
    id: number;
    name: string;
    type: string;
    status: string;
  }[];
}

interface DeviceCapabilities {
  status: string;
  system_capabilities: {
    hardware: {
      CPU: string;
      'CPU Count': number;
      Architecture: string;
    };
    software: {
      Identity: string;
      OS: string;
      Version: string;
    };
    memory: {
      Total: string;
      Free: string;
    };
    storage: {
      Total: string;
      Free: string;
    };
  };
  timestamp: string;
}

interface Props {
  device: DeviceData;
}

export default function ShowNetworkDevice({ device }: Props) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [capabilities, setCapabilities] = useState<DeviceCapabilities | null>(null);

  const testConnection = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.post(`/network/devices/${device.id}/test-connection`);
      setCapabilities(response.data);
    } catch (err) {
      console.error('Error testing connection:', err);
      setError(err.response?.data?.message || 'Failed to test connection to device');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="status-badge-active">Active</Badge>;
      case 'inactive':
        return <Badge className="status-badge-inactive">Inactive</Badge>;
      case 'maintenance':
        return <Badge className="status-badge-suspended">Maintenance</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getInterfaceIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'ethernet':
      case 'ether':
        return Cable;
      case 'wireless':
      case 'wlan':
        return Wifi;
      default:
        return Network;
    }
  };

  const getInterfaceStatusBadge = (status: string) => {
    switch (status) {
      case 'up':
        return <Badge className="status-badge-active">Up</Badge>;
      case 'down':
        return <Badge className="status-badge-inactive">Down</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this device?')) {
      router.delete(`/network/devices/${device.id}`);
    }
  };

  // Calculate interface stats
  const interfaceStats = {
    total: device.interfaces.length,
    up: device.interfaces.filter(i => i.status === 'up').length,
    down: device.interfaces.filter(i => i.status === 'down').length,
    ethernet: device.interfaces.filter(i => i.type === 'ethernet').length,
  };

  return (
    <AppLayout>
      <Head title={`${device.name} - Network Device`} />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center gap-3">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/network/devices">
                  <ArrowLeft className="h-4 w-4" />
                </Link>
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <Router className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1>{device.name}</h1>
                  <p>MikroTik network device management and monitoring</p>
                </div>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href={`/network/devices/${device.id}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Device
              </Link>
            </Button>
            <Button asChild>
              <Link href={`/network/devices/${device.id}/interfaces`}>
                <Network className="h-4 w-4 mr-2" />
                Manage Interfaces
              </Link>
            </Button>
            <Button
              onClick={testConnection}
              disabled={loading}
              variant="secondary"
            >
              {loading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Test Connection
                </>
              )}
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="Total Interfaces"
            value={interfaceStats.total}
            icon={Network}
            trend={{ value: 0, isPositive: true }}
          />
          <StatsCard
            title="Interfaces Up"
            value={interfaceStats.up}
            icon={CheckCircle}
            trend={{ value: 0, isPositive: true }}
            className="text-green-600"
          />
          <StatsCard
            title="Interfaces Down"
            value={interfaceStats.down}
            icon={XCircle}
            trend={{ value: 0, isPositive: false }}
            className="text-red-600"
          />
          <StatsCard
            title="Ethernet Ports"
            value={interfaceStats.ethernet}
            icon={Cable}
            trend={{ value: 0, isPositive: true }}
            className="text-blue-600"
          />
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Device Information Card */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Device Information
              </CardTitle>
              <CardDescription>
                Basic device configuration and status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Device ID</label>
                  <p className="text-sm font-mono">{device.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Name</label>
                  <p className="text-sm font-medium">{device.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">IP Address</label>
                  <p className="text-sm font-mono">{device.ip_address}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <div className="mt-1">
                    {getStatusBadge(device.status)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Site</label>
                  <Link
                    href={`/network/sites/${device.site.id}`}
                    className="text-sm text-primary hover:underline"
                  >
                    {device.site.name}
                  </Link>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">API Port</label>
                  <p className="text-sm font-mono">{device.api_port}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Detected Model</label>
                  <p className="text-sm">{device.detected_model || 'Not detected'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">RouterOS Version</label>
                  <p className="text-sm">{device.detected_version || 'Not detected'}</p>
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  <span>
                    Last connected: {device.last_connected_at
                      ? new Date(device.last_connected_at).toLocaleString()
                      : 'Never'
                    }
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Device Description Card */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                Description
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                {device.description || 'No description available for this device.'}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* System Capabilities Card */}
        <Card className="card-modern">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cpu className="h-5 w-5" />
              System Capabilities
            </CardTitle>
            <CardDescription>
              Real-time system information from MikroTik device
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Error Alert */}
            {error && (
              <Card className="border-destructive bg-destructive/10 mb-4">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    <span>{error}</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {!capabilities && !loading && !error && (
              <div className="text-center py-8 text-muted-foreground">
                <Zap className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                <p className="text-lg mb-2">System capabilities not loaded</p>
                <p className="text-sm mb-4">Click "Test Connection" to fetch real-time system information</p>
              </div>
            )}

            {loading && (
              <div className="text-center py-8">
                <div className="flex flex-col items-center gap-4">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                  <p className="text-muted-foreground">Testing connection to device...</p>
                </div>
              </div>
            )}

            {capabilities && capabilities.status === 'success' && (
              <div className="space-y-6">
                {/* Hardware Information */}
                <div>
                  <h3 className="flex items-center gap-2 text-lg font-medium mb-4">
                    <Cpu className="h-4 w-4" />
                    Hardware
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <label className="text-sm font-medium text-muted-foreground">CPU</label>
                      <p className="text-sm font-medium">{capabilities.system_capabilities.hardware.CPU}</p>
                    </div>
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <label className="text-sm font-medium text-muted-foreground">CPU Count</label>
                      <p className="text-sm font-medium">{capabilities.system_capabilities.hardware['CPU Count']}</p>
                    </div>
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <label className="text-sm font-medium text-muted-foreground">Architecture</label>
                      <p className="text-sm font-medium">{capabilities.system_capabilities.hardware.Architecture}</p>
                    </div>
                  </div>
                </div>

                {/* Software Information */}
                <div>
                  <h3 className="flex items-center gap-2 text-lg font-medium mb-4">
                    <Settings className="h-4 w-4" />
                    Software
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <label className="text-sm font-medium text-muted-foreground">Identity</label>
                      <p className="text-sm font-medium">{capabilities.system_capabilities.software.Identity}</p>
                    </div>
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <label className="text-sm font-medium text-muted-foreground">OS</label>
                      <p className="text-sm font-medium">{capabilities.system_capabilities.software.OS}</p>
                    </div>
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <label className="text-sm font-medium text-muted-foreground">Version</label>
                      <p className="text-sm font-medium">{capabilities.system_capabilities.software.Version}</p>
                    </div>
                  </div>
                </div>

                {/* Memory Information */}
                {capabilities.system_capabilities.memory && Object.keys(capabilities.system_capabilities.memory).length > 0 && (
                  <div>
                    <h3 className="flex items-center gap-2 text-lg font-medium mb-4">
                      <MemoryStick className="h-4 w-4" />
                      Memory
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 bg-muted/50 rounded-lg">
                        <label className="text-sm font-medium text-muted-foreground">Total Memory</label>
                        <p className="text-sm font-medium">{capabilities.system_capabilities.memory.Total}</p>
                      </div>
                      <div className="p-4 bg-muted/50 rounded-lg">
                        <label className="text-sm font-medium text-muted-foreground">Free Memory</label>
                        <p className="text-sm font-medium">{capabilities.system_capabilities.memory.Free}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Storage Information */}
                {capabilities.system_capabilities.storage && Object.keys(capabilities.system_capabilities.storage).length > 0 && (
                  <div>
                    <h3 className="flex items-center gap-2 text-lg font-medium mb-4">
                      <HardDrive className="h-4 w-4" />
                      Storage
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 bg-muted/50 rounded-lg">
                        <label className="text-sm font-medium text-muted-foreground">Total Storage</label>
                        <p className="text-sm font-medium">{capabilities.system_capabilities.storage.Total}</p>
                      </div>
                      <div className="p-4 bg-muted/50 rounded-lg">
                        <label className="text-sm font-medium text-muted-foreground">Free Storage</label>
                        <p className="text-sm font-medium">{capabilities.system_capabilities.storage.Free}</p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-2 text-xs text-muted-foreground pt-4 border-t">
                  <Clock className="h-3 w-3" />
                  <span>Last updated: {new Date(capabilities.timestamp).toLocaleString()}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Device Interfaces Card */}
        <Card className="card-modern">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Network className="h-5 w-5" />
                  Network Interfaces
                </CardTitle>
                <CardDescription>
                  {device.interfaces.length} interfaces configured on this device
                </CardDescription>
              </div>
              <Button asChild>
                <Link href={`/network/devices/${device.id}/interfaces`}>
                  <Settings className="h-4 w-4 mr-2" />
                  Manage Interfaces
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <ModernTable>
              <ModernTableHeader>
                <ModernTableRow>
                  <ModernTableCell header>Interface</ModernTableCell>
                  <ModernTableCell header>Type</ModernTableCell>
                  <ModernTableCell header>Status</ModernTableCell>
                  <ModernTableCell header className="text-right">Actions</ModernTableCell>
                </ModernTableRow>
              </ModernTableHeader>
              <ModernTableBody>
                {device.interfaces.length === 0 ? (
                  <ModernTableRow>
                    <ModernTableCell colSpan={4} className="text-center py-12 text-muted-foreground">
                      <div className="flex flex-col items-center gap-2">
                        <Network className="h-8 w-8 text-muted-foreground/50" />
                        <p>No interfaces found</p>
                        <p className="text-sm">Interfaces will appear here when detected from the device</p>
                        <Button asChild className="mt-2">
                          <Link href={`/network/devices/${device.id}/interfaces`}>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Fetch Interfaces
                          </Link>
                        </Button>
                      </div>
                    </ModernTableCell>
                  </ModernTableRow>
                ) : (
                  device.interfaces.map((iface) => {
                    const IconComponent = getInterfaceIcon(iface.type);
                    return (
                      <ModernTableRow key={iface.id}>
                        <ModernTableCell className="font-medium">
                          <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                              <IconComponent className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <div className="font-medium">{iface.name}</div>
                              <div className="text-xs text-muted-foreground">ID: {iface.id}</div>
                            </div>
                          </div>
                        </ModernTableCell>
                        <ModernTableCell className="text-muted-foreground">
                          {iface.type}
                        </ModernTableCell>
                        <ModernTableCell>
                          {getInterfaceStatusBadge(iface.status)}
                        </ModernTableCell>
                        <ModernTableCell className="text-right">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/network/devices/${device.id}/interfaces`}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </Link>
                          </Button>
                        </ModernTableCell>
                      </ModernTableRow>
                    );
                  })
                )}
              </ModernTableBody>
            </ModernTable>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
