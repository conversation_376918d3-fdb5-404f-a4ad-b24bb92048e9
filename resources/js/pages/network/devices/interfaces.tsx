import { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ModernTable, ModernTableBody, ModernTableCell, ModernTableHeader, ModernTableRow } from '@/components/ui/modern-table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { StatsCard } from '@/components/ui/stats-card';
import {
  ArrowLeft,
  Network,
  Cable,
  Wifi,
  Search,
  Filter,
  RefreshCw,
  Activity,
  AlertCircle,
  CheckCircle,
  XCircle,
  Router,
  Zap,
  Signal
} from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import axios from 'axios';

interface Props {
  device: Device;
  interfaces: Interface[];
  error?: string;
}

interface Interface {
  id: string;
  name: string;
  type: string;
  status: string;
  ip_address: string;
  subnet_mask: string;
  mac_address: string;
  speed: number;
  is_management: boolean;
}

interface Device {
  id: number;
  name: string;
  type: string;
  ip_address: string;
  site: {
    id: number;
    name: string;
  };
}

export default function DeviceInterfaces({ device, interfaces: initialInterfaces, error: initialError }: Props) {
  const [interfaces, setInterfaces] = useState<Interface[]>(initialInterfaces || []);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(initialError || null);
  const [realtimeEnabled, setRealtimeEnabled] = useState<boolean>(false); // Disabled by default
  const [lastUpdated, setLastUpdated] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const fetchInterfaces = async (showLoading = true) => {
    if (showLoading) {
      setLoading(true);
    }
    setError(null);

    try {
      // Fetch device interfaces via AJAX API for real-time updates
      const interfacesResponse = await axios.get(`/network/api/devices/${device.id}/interfaces`);
      setInterfaces(interfacesResponse.data.data || []);
      setLastUpdated(new Date().toLocaleTimeString());
    } catch (err) {
      console.error('Error fetching device interfaces:', err);
      setError('Failed to load device interfaces. Please try again.');
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  };

  // Start or stop real-time polling
  useEffect(() => {
    // Clear any existing interval
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }

    // If real-time is enabled, start polling
    if (realtimeEnabled) {
      // Set up polling every 5 seconds (don't fetch immediately since we have initial data)
      pollingIntervalRef.current = setInterval(() => {
        fetchInterfaces(false);
      }, 5000);
    }

    // Cleanup function to clear interval when component unmounts or dependencies change
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [device.id, realtimeEnabled]);

  // Filter interfaces based on search and filters
  const filteredInterfaces = interfaces.filter(interfaceItem => {
    const matchesSearch = interfaceItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (interfaceItem.ip_address && interfaceItem.ip_address.includes(searchTerm)) ||
                         (interfaceItem.mac_address && interfaceItem.mac_address.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'all' || interfaceItem.status === statusFilter;
    const matchesType = typeFilter === 'all' || interfaceItem.type === typeFilter;
    return matchesSearch && matchesStatus && matchesType;
  });

  // Get unique interface types for filter
  const uniqueTypes = Array.from(new Set(interfaces.map(i => i.type))).filter(Boolean);

  // Calculate stats
  const stats = {
    total: interfaces.length,
    up: interfaces.filter(i => i.status === 'up').length,
    down: interfaces.filter(i => i.status === 'down').length,
    ethernet: interfaces.filter(i => i.type === 'ethernet').length,
    wireless: interfaces.filter(i => i.type === 'wireless').length,
  };

  const getInterfaceIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'ethernet':
      case 'ether':
        return Cable;
      case 'wireless':
      case 'wlan':
        return Wifi;
      default:
        return Network;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'up':
        return <Badge className="status-badge-active">Up</Badge>;
      case 'down':
        return <Badge className="status-badge-inactive">Down</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <AppLayout>
      <Head title={`Interfaces - ${device.name}`} />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center gap-3">
              <Button variant="ghost" size="sm" asChild>
                <Link href={`/network/devices/${device.id}`}>
                  <ArrowLeft className="h-4 w-4" />
                </Link>
              </Button>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <Router className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1>{device.name} - Interfaces</h1>
                  <p>Interface monitoring from MikroTik device with optional real-time updates</p>
                </div>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant={realtimeEnabled ? "destructive" : "default"}
              onClick={() => setRealtimeEnabled(!realtimeEnabled)}
            >
              {realtimeEnabled ? (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  Disable Real-time
                </>
              ) : (
                <>
                  <Activity className="h-4 w-4 mr-2" />
                  Enable Real-time
                </>
              )}
            </Button>
            <Button onClick={() => fetchInterfaces()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Now
            </Button>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <Card className="border-destructive bg-destructive/10">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Stats Cards */}
        {!loading && interfaces.length > 0 && (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <StatsCard
              title="Total Interfaces"
              value={stats.total}
              icon={Network}
              trend={{ value: 0, isPositive: true }}
            />
            <StatsCard
              title="Interfaces Up"
              value={stats.up}
              icon={CheckCircle}
              trend={{ value: 0, isPositive: true }}
              className="text-green-600"
            />
            <StatsCard
              title="Interfaces Down"
              value={stats.down}
              icon={XCircle}
              trend={{ value: 0, isPositive: false }}
              className="text-red-600"
            />
            <StatsCard
              title="Ethernet Ports"
              value={stats.ethernet}
              icon={Cable}
              trend={{ value: 0, isPositive: true }}
              className="text-blue-600"
            />
          </div>
        )}

        {/* Filters and Search */}
        {!loading && interfaces.length > 0 && (
          <Card className="card-modern">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search interfaces by name, IP, or MAC address..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 input-modern"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[140px]">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="up">Up</SelectItem>
                      <SelectItem value="down">Down</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      {uniqueTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {loading ? (
          <Card className="card-modern">
            <CardContent className="flex justify-center items-center h-64">
              <div className="flex flex-col items-center gap-4">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                <p className="text-muted-foreground">Loading interfaces from MikroTik device...</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="card-modern">
            <CardHeader>
              <CardTitle>Network Interfaces</CardTitle>
              <CardDescription>
                {filteredInterfaces.length} of {interfaces.length} interfaces
                {realtimeEnabled ? (
                  <span className="ml-2 inline-flex items-center">
                    <span className="h-2 w-2 bg-green-500 rounded-full mr-1 animate-pulse"></span>
                    Live updates enabled
                  </span>
                ) : (
                  <span className="ml-2 text-muted-foreground">
                    • Enable real-time updates for live monitoring
                  </span>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <ModernTable>
                <ModernTableHeader>
                  <ModernTableRow>
                    <ModernTableCell header>Interface</ModernTableCell>
                    <ModernTableCell header>Type</ModernTableCell>
                    <ModernTableCell header>Status</ModernTableCell>
                    <ModernTableCell header>IP Address</ModernTableCell>
                    <ModernTableCell header>MAC Address</ModernTableCell>
                    <ModernTableCell header>Speed</ModernTableCell>
                    <ModernTableCell header>Details</ModernTableCell>
                  </ModernTableRow>
                </ModernTableHeader>
                <ModernTableBody>
                  {filteredInterfaces.length === 0 ? (
                    <ModernTableRow>
                      <ModernTableCell colSpan={7} className="text-center py-12 text-muted-foreground">
                        <div className="flex flex-col items-center gap-2">
                          <Network className="h-8 w-8 text-muted-foreground/50" />
                          <p>No interfaces found</p>
                          <p className="text-sm">
                            {interfaces.length === 0
                              ? "Unable to retrieve interfaces from the MikroTik device"
                              : "Try adjusting your search or filters"
                            }
                          </p>
                          {interfaces.length === 0 && (
                            <Button
                              onClick={() => fetchInterfaces()}
                              className="mt-2"
                            >
                              <RefreshCw className="h-4 w-4 mr-2" />
                              Retry Connection
                            </Button>
                          )}
                        </div>
                      </ModernTableCell>
                    </ModernTableRow>
                  ) : (
                    filteredInterfaces.map((interfaceItem) => {
                      const IconComponent = getInterfaceIcon(interfaceItem.type);
                      return (
                        <ModernTableRow key={interfaceItem.id}>
                          <ModernTableCell className="font-medium">
                            <div className="flex items-center gap-3">
                              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                                <IconComponent className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                              </div>
                              <div>
                                <div className="font-medium">{interfaceItem.name}</div>
                                {interfaceItem.is_management && (
                                  <Badge variant="secondary" className="text-xs mt-1">
                                    Management
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </ModernTableCell>
                          <ModernTableCell className="text-muted-foreground">
                            {interfaceItem.type}
                          </ModernTableCell>
                          <ModernTableCell>
                            {getStatusBadge(interfaceItem.status)}
                          </ModernTableCell>
                          <ModernTableCell className="text-muted-foreground font-mono">
                            <div>
                              {interfaceItem.ip_address || '-'}
                              {interfaceItem.subnet_mask && (
                                <div className="text-xs text-muted-foreground/70">
                                  {interfaceItem.subnet_mask}
                                </div>
                              )}
                            </div>
                          </ModernTableCell>
                          <ModernTableCell className="text-muted-foreground font-mono text-xs">
                            {interfaceItem.mac_address || '-'}
                          </ModernTableCell>
                          <ModernTableCell className="text-muted-foreground">
                            <div className="flex items-center gap-1">
                              {interfaceItem.speed && <Zap className="h-3 w-3" />}
                              {interfaceItem.speed ? `${interfaceItem.speed} Mbps` : 'N/A'}
                            </div>
                          </ModernTableCell>
                          <ModernTableCell className="text-muted-foreground">
                            <div className="text-xs space-y-1">
                              <div>ID: {interfaceItem.id}</div>
                              {interfaceItem.is_management && (
                                <Badge variant="outline" className="text-xs">
                                  <Signal className="h-3 w-3 mr-1" />
                                  Management
                                </Badge>
                              )}
                            </div>
                          </ModernTableCell>
                        </ModernTableRow>
                      );
                    })
                  )}
                </ModernTableBody>
              </ModernTable>
            </CardContent>
          </Card>
        )}

        {/* Status Footer */}
        {!loading && interfaces.length > 0 && (
          <Card className="card-modern">
            <CardContent className="p-4">
              <div className="flex justify-between items-center text-sm text-muted-foreground">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {realtimeEnabled ? (
                      <>
                        <span className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></span>
                        <span>Real-time updates enabled</span>
                      </>
                    ) : (
                      <>
                        <span className="h-2 w-2 bg-gray-400 rounded-full"></span>
                        <span>Real-time updates disabled</span>
                      </>
                    )}
                  </div>
                  {lastUpdated && (
                    <div>Last updated: {lastUpdated}</div>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    <Activity className="h-3 w-3 mr-1" />
                    {stats.up} Up
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    {stats.down} Down
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
