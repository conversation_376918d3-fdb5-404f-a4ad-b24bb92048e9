import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Network',
    href: '/network',
  },
  {
    title: 'Maps',
    href: '/network/maps',
  },
];

interface Map {
  id: number;
  name: string;
  description: string;
  site_name: string;
  width: number;
  height: number;
  background_image: string | null;
  device_count: number;
}

export default function NetworkMaps() {
  const [maps, setMaps] = useState<Map[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // This would normally fetch data from the server
  useEffect(() => {
    // Simulating data fetch with placeholder data
    const placeholderMaps: Map[] = [
      {
        id: 1,
        name: 'Headquarters Network',
        description: 'Complete network map of headquarters',
        site_name: 'Headquarters',
        width: 1200,
        height: 800,
        background_image: '/images/maps/hq-floor-plan.jpg',
        device_count: 15
      },
      {
        id: 2,
        name: 'Data Center Rack Layout',
        description: 'Physical layout of data center racks',
        site_name: 'Data Center',
        width: 1000,
        height: 1200,
        background_image: '/images/maps/datacenter-layout.jpg',
        device_count: 24
      },
      {
        id: 3,
        name: 'Branch Office Network',
        description: 'Network topology of branch office',
        site_name: 'Branch Office',
        width: 800,
        height: 600,
        background_image: null,
        device_count: 8
      },
    ];

    setMaps(placeholderMaps);
    setLoading(false);
  }, []);

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Network Maps" />
      <div className="flex flex-col gap-6 p-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-800">Network Maps</h1>
          <div className="flex gap-2">
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              onClick={() => window.location.href = '/network/maps/create'}
            >
              Add Map
            </button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Site
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dimensions
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Devices
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {maps.map((map) => (
                  <tr key={map.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{map.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{map.description}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{map.site_name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {map.width} x {map.height}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {map.device_count}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <a href={`/network/maps/${map.id}`} className="text-blue-600 hover:text-blue-900 mr-3">
                        View
                      </a>
                      <a href={`/network/maps/${map.id}/edit`} className="text-indigo-600 hover:text-indigo-900">
                        Edit
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
