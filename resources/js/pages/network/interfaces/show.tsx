import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';
import axios from 'axios';
import { useLocation } from 'react-router-dom';

function useQuery() {
  return new URLSearchParams(typeof window !== 'undefined' ? window.location.search : '');
}

interface Props {
  interfaceId: string; // MikroTik interface ID (e.g. '*6')
  deviceId?: string;   // Device ID (from URL or parent)
}

export default function InterfaceShow({ interfaceId, deviceId: propDeviceId }: Props) {
  const query = useQuery();
  const deviceId = propDeviceId || query.get('device_id') || '';
  const [interfaceData, setInterfaceData] = useState<any | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!deviceId) {
      setError('Device ID is required to fetch interface details.');
      setLoading(false);
      return;
    }
    const fetchInterfaceData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch live data from MikroTik
        const response = await axios.get(`/network/devices/${deviceId}/interfaces/${interfaceId}/live`);
        if (response.data && response.data.status === 'success') {
          setInterfaceData({
            ...response.data.data,
            clients_count: Math.floor(Math.random() * 30), // Demo only
          });
        } else {
          setError(response.data?.message || `Interface with ID ${interfaceId} not found.`);
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to load interface details. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    fetchInterfaceData();
  }, [interfaceId, deviceId]);

  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: 'Dashboard',
      href: '/dashboard',
    },
    {
      title: 'Network',
      href: '/network',
    },
    {
      title: 'Interfaces',
      href: '/network/interfaces',
    },
    {
      title: interfaceData?.name || 'Interface Details',
      href: `/network/interfaces/${interfaceId}`,
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Interface - ${interfaceData?.name || 'Details'}`} />
      <div className="flex flex-col gap-6 p-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-800">
            {interfaceData?.name || 'Interface Details'}
          </h1>
          <div className="flex gap-2">
            {interfaceData && (
              <button
                className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
                onClick={() => window.location.href = `/network/interfaces/${interfaceId}/edit`}
              >
                Edit Interface
              </button>
            )}
            <button
              className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
              onClick={() => window.location.href = '/network/interfaces'}
            >
              Back to Interfaces
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : interfaceData ? (
          <>
            {/* Connected Clients Card */}
            <div className="bg-white shadow-sm rounded-lg overflow-hidden mb-6">
              <div className="p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Connected Clients</h2>
                <div className="flex items-center">
                  <div className="text-4xl font-bold text-blue-600">
                    {interfaceData.clients_count}
                  </div>
                  <div className="ml-4 text-gray-500">
                    clients connected to this interface
                  </div>
                </div>
              </div>
            </div>

            {/* Interface Details Card */}
            <div className="bg-white shadow-sm rounded-lg overflow-hidden">
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-800 mb-4">Interface Information</h2>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-500">Name:</span>
                        <p className="text-gray-800">{interfaceData.name}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">Device:</span>
                        <p className="text-gray-800">
                          <a href={`/network/devices/${interfaceData.device_id}`} className="text-blue-600 hover:text-blue-800">
                            {interfaceData.device_name}
                          </a>
                        </p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">Type:</span>
                        <p className="text-gray-800">{interfaceData.type}</p>
                      </div>
                      <div>
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          interfaceData.status === 'up' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {interfaceData.status}
                        </span>
                      </div>
                      {interfaceData.is_management && (
                        <div>
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            Management Interface
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-800 mb-4">Network Configuration</h2>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-500">IP Address:</span>
                        <p className="text-gray-800">{interfaceData.ip_address}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">Subnet Mask:</span>
                        <p className="text-gray-800">{interfaceData.subnet_mask}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">MAC Address:</span>
                        <p className="text-gray-800">{interfaceData.mac_address}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">Speed:</span>
                        <p className="text-gray-800">{interfaceData.speed} Mbps</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <div className="p-6 text-center text-gray-500">
              No interface found with ID: {interfaceId}
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
