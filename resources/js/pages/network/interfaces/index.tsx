import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Network',
    href: '/network',
  },
  {
    title: 'Interfaces',
    href: '/network/interfaces',
  },
];

interface Interface {
  id: number;
  name: string;
  device_name: string;
  type: string;
  status: string;
  ip_address: string;
  subnet_mask: string;
  mac_address: string;
  speed: number;
  is_management: boolean;
}

export default function NetworkInterfaces() {
  const [interfaces, setInterfaces] = useState<Interface[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // This would normally fetch data from the server
  useEffect(() => {
    // Simulating data fetch with placeholder data
    const placeholderInterfaces: Interface[] = [
      {
        id: 1,
        name: 'GigabitEthernet0/0',
        device_name: 'Router-Main',
        type: 'ethernet',
        status: 'up',
        ip_address: '***********',
        subnet_mask: '*************',
        mac_address: '00:11:22:33:44:55',
        speed: 1000,
        is_management: true
      },
      {
        id: 2,
        name: 'GigabitEthernet0/1',
        device_name: 'Router-Main',
        type: 'ethernet',
        status: 'up',
        ip_address: '********',
        subnet_mask: '*************',
        mac_address: '00:11:22:33:44:56',
        speed: 1000,
        is_management: false
      },
      {
        id: 3,
        name: 'GigabitEthernet1/0/1',
        device_name: 'Switch-Floor1',
        type: 'ethernet',
        status: 'up',
        ip_address: '***********',
        subnet_mask: '*************',
        mac_address: '00:11:22:33:44:57',
        speed: 1000,
        is_management: true
      },
      {
        id: 4,
        name: 'WLAN1',
        device_name: 'AP-Conference',
        type: 'wireless',
        status: 'up',
        ip_address: '***********',
        subnet_mask: '*************',
        mac_address: '00:11:22:33:44:58',
        speed: 1200,
        is_management: false
      },
      {
        id: 5,
        name: 'GigabitEthernet0/0',
        device_name: 'Router-Branch',
        type: 'ethernet',
        status: 'down',
        ip_address: '********',
        subnet_mask: '*************',
        mac_address: '00:11:22:33:44:59',
        speed: 1000,
        is_management: true
      },
    ];

    setInterfaces(placeholderInterfaces);
    setLoading(false);
  }, []);

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Network Interfaces" />
      <div className="flex flex-col gap-6 p-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-800">Network Interfaces</h1>
          <div className="flex gap-2">
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              onClick={() => window.location.href = '/network/interfaces/create'}
            >
              Add Interface
            </button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Device
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    IP Address
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Speed
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {interfaces.map((interfaceItem) => (
                  <tr key={interfaceItem.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{interfaceItem.name}</div>
                      {interfaceItem.is_management && (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 ml-2">
                          Management
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{interfaceItem.device_name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{interfaceItem.type}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        interfaceItem.status === 'up' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {interfaceItem.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {interfaceItem.ip_address}
                      <div className="text-xs text-gray-400">{interfaceItem.subnet_mask}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {interfaceItem.speed} Mbps
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <a href={`/network/interfaces/${interfaceItem.id}`} className="text-blue-600 hover:text-blue-900 mr-3">
                        View
                      </a>
                      <a href={`/network/interfaces/${interfaceItem.id}/edit`} className="text-indigo-600 hover:text-indigo-900">
                        Edit
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
