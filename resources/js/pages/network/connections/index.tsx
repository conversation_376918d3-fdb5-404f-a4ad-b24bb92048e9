import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Network',
    href: '/network',
  },
  {
    title: 'Connections',
    href: '/network/connections',
  },
];

interface Connection {
  id: number;
  source_device: string;
  source_interface: string;
  target_device: string;
  target_interface: string;
  type: string;
  status: string;
  bandwidth: number;
  latency: number;
}

export default function NetworkConnections() {
  const [connections, setConnections] = useState<Connection[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // This would normally fetch data from the server
  useEffect(() => {
    // Simulating data fetch with placeholder data
    const placeholderConnections: Connection[] = [
      {
        id: 1,
        source_device: 'Router-Main',
        source_interface: 'GigabitEthernet0/0',
        target_device: 'Switch-Floor1',
        target_interface: 'GigabitEthernet1/0/1',
        type: 'ethernet',
        status: 'active',
        bandwidth: 1000,
        latency: 2
      },
      {
        id: 2,
        source_device: 'Switch-Floor1',
        source_interface: 'GigabitEthernet1/0/24',
        target_device: 'AP-Conference',
        target_interface: 'GigabitEthernet0',
        type: 'ethernet',
        status: 'active',
        bandwidth: 1000,
        latency: 1
      },
      {
        id: 3,
        source_device: 'Router-Main',
        source_interface: 'GigabitEthernet0/1',
        target_device: 'Router-Branch',
        target_interface: 'GigabitEthernet0/1',
        type: 'fiber',
        status: 'active',
        bandwidth: 10000,
        latency: 5
      },
      {
        id: 4,
        source_device: 'Router-Branch',
        source_interface: 'GigabitEthernet0/0',
        target_device: 'Switch-Branch',
        target_interface: 'GigabitEthernet1/0/1',
        type: 'ethernet',
        status: 'inactive',
        bandwidth: 1000,
        latency: 0
      },
    ];

    setConnections(placeholderConnections);
    setLoading(false);
  }, []);

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Network Connections" />
      <div className="flex flex-col gap-6 p-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-800">Network Connections</h1>
          <div className="flex gap-2">
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              onClick={() => window.location.href = '/network/connections/create'}
            >
              Add Connection
            </button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Source Device
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Source Interface
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Target Device
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Target Interface
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Bandwidth
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {connections.map((connection) => (
                  <tr key={connection.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{connection.source_device}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{connection.source_interface}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{connection.target_device}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{connection.target_interface}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{connection.type}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        connection.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {connection.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {connection.bandwidth} Mbps
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <a href={`/network/connections/${connection.id}`} className="text-blue-600 hover:text-blue-900 mr-3">
                        View
                      </a>
                      <a href={`/network/connections/${connection.id}/edit`} className="text-indigo-600 hover:text-indigo-900">
                        Edit
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
