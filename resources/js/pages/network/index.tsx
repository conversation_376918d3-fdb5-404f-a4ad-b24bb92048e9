import { useState } from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Network,
  Building2,
  Router,
  Globe,
  Activity,
  Plus,
  Map,
  Server,
  Zap,
  Settings,
  TrendingUp,
  CheckCircle,
  BarChart3
} from 'lucide-react';

interface NetworkStats {
  sites: {
    total: number;
    active: number;
    inactive: number;
    maintenance: number;
    by_status: Record<string, number>;
  };
  devices: {
    total: number;
    active: number;
    inactive: number;
    recently_connected: number;
    by_model: Record<string, number>;
    services_distribution: Array<{
      device_name: string;
      total_services: number;
      static_ip_count: number;
      pppoe_count: number;
    }>;
  };
  ip_pools: {
    total_pools: number;
    total_addresses: number;
    used_addresses: number;
    available_addresses: number;
    utilization_percentage: number;
  };
  services: {
    total: number;
    active: number;
    suspended: number;
    static_ip_count: number;
    pppoe_count: number;
  };
  bandwidth: {
    total_plans: number;
    plan_usage: Array<{
      plan_name: string;
      total_usage: number;
      download_speed: number;
      upload_speed: number;
    }>;
  };
  maps: {
    total: number;
  };
  health: {
    sites_online_percentage: number;
    devices_active_percentage: number;
    ip_utilization_percentage: number;
    services_active_percentage: number;
    overall_health: {
      score: number;
      status: string;
      color: string;
    };
  };
}

interface NetworkActivity {
  type: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  timestamp: string;
  time_ago: string;
  url: string;
}

interface NetworkDashboardProps {
  stats: NetworkStats;
  recentActivities: NetworkActivity[];
}

export default function NetworkDashboard({ stats, recentActivities }: NetworkDashboardProps) {
  const [loading] = useState<boolean>(false);

  return (
    <AppLayout>
      <Head title="Network Management Dashboard" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                <Network className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1>Network Management</h1>
                <p>Monitor and manage your network infrastructure</p>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/network/maps">
                <Map className="h-4 w-4 mr-2" />
                Network Maps
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/network/sites/create">
                <Plus className="h-4 w-4 mr-2" />
                Add Site
              </Link>
            </Button>
          </div>
        </div>

        {loading ? (
          <Card className="card-modern">
            <CardContent className="flex justify-center items-center h-64">
              <div className="flex flex-col items-center gap-4">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                <p className="text-muted-foreground">Loading network infrastructure data...</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Network Infrastructure Stats */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <Link href="/network/sites" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Network Sites</p>
                        <p className="text-3xl font-bold">{stats.sites.total}</p>
                        <p className="text-sm text-muted-foreground">
                          {stats.sites.active} active, {stats.sites.inactive} inactive
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-blue-100 dark:bg-blue-900/30">
                        <Building2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/network/devices" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Network Devices</p>
                        <p className="text-3xl font-bold">{stats.devices.total}</p>
                        <p className="text-sm text-muted-foreground">
                          {stats.devices.active} active, {stats.devices.recently_connected} recently connected
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-green-100 dark:bg-green-900/30">
                        <Router className="h-6 w-6 text-green-600 dark:text-green-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/services/ip-pools" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">IP Pools</p>
                        <p className="text-3xl font-bold">{stats.ip_pools.total_addresses}</p>
                        <p className="text-sm text-muted-foreground">
                          {stats.ip_pools.used_addresses} used, {stats.ip_pools.available_addresses} available
                          <span className="text-xs"> ({stats.ip_pools.utilization_percentage}% used)</span>
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-purple-100 dark:bg-purple-900/30">
                        <Globe className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/network/maps" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Network Maps</p>
                        <p className="text-3xl font-bold">{stats.maps.total}</p>
                        <p className="text-sm text-muted-foreground">
                          Visual network topology
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-orange-100 dark:bg-orange-900/30">
                        <Map className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </div>

            {/* Quick Actions */}
            <Card className="card-modern">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Quick Actions
                </CardTitle>
                <CardDescription>
                  Common network management tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Link href="/network/sites/create" className="group">
                    <Card className="transition-all duration-200 group-hover:shadow-md group-hover:scale-[1.02] border-2 border-dashed border-muted-foreground/20 hover:border-blue-500/50">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors">
                            <Plus className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <p className="font-medium">Add New Site</p>
                            <p className="text-sm text-muted-foreground">Create network site</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>

                  <Link href="/network/devices/create" className="group">
                    <Card className="transition-all duration-200 group-hover:shadow-md group-hover:scale-[1.02] border-2 border-dashed border-muted-foreground/20 hover:border-green-500/50">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/30 group-hover:bg-green-200 dark:group-hover:bg-green-900/50 transition-colors">
                            <Server className="h-5 w-5 text-green-600 dark:text-green-400" />
                          </div>
                          <div>
                            <p className="font-medium">Add New Device</p>
                            <p className="text-sm text-muted-foreground">Register MikroTik device</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>

                  <Link href="/network/maps" className="group">
                    <Card className="transition-all duration-200 group-hover:shadow-md group-hover:scale-[1.02] border-2 border-dashed border-muted-foreground/20 hover:border-purple-500/50">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/30 group-hover:bg-purple-200 dark:group-hover:bg-purple-900/50 transition-colors">
                            <BarChart3 className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                          </div>
                          <div>
                            <p className="font-medium">View Analytics</p>
                            <p className="text-sm text-muted-foreground">Network performance</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>

                  <Link href="/network/settings" className="group">
                    <Card className="transition-all duration-200 group-hover:shadow-md group-hover:scale-[1.02] border-2 border-dashed border-muted-foreground/20 hover:border-orange-500/50">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/30 group-hover:bg-orange-200 dark:group-hover:bg-orange-900/50 transition-colors">
                            <Settings className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                          </div>
                          <div>
                            <p className="font-medium">Network Settings</p>
                            <p className="text-sm text-muted-foreground">Configure network</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Network Health Overview */}
            <div className="grid gap-6 md:grid-cols-2">
              <Card className="card-modern">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Network Health
                  </CardTitle>
                  <CardDescription>
                    Overall network infrastructure status
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">Sites Online</span>
                      </div>
                      <span className="text-sm font-medium">{stats.sites.active}/{stats.sites.total}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">Active Services</span>
                      </div>
                      <span className="text-sm font-medium">{stats.services.active}/{stats.services.total}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">IP Utilization</span>
                      </div>
                      <span className="text-sm font-medium">{stats.ip_pools.utilization_percentage}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`h-4 w-4 rounded-full ${
                          stats.health.overall_health.color === 'green' ? 'bg-green-500' :
                          stats.health.overall_health.color === 'blue' ? 'bg-blue-500' :
                          stats.health.overall_health.color === 'yellow' ? 'bg-yellow-500' : 'bg-red-500'
                        }`} />
                        <span className="text-sm">Overall Health</span>
                      </div>
                      <span className="text-sm font-medium">{stats.health.overall_health.score}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="card-modern">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Recent Activity
                  </CardTitle>
                  <CardDescription>
                    Latest network management activities
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentActivities.length > 0 ? (
                      recentActivities.map((activity, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${
                            activity.color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/30' :
                            activity.color === 'green' ? 'bg-green-100 dark:bg-green-900/30' :
                            activity.color === 'purple' ? 'bg-purple-100 dark:bg-purple-900/30' :
                            activity.color === 'orange' ? 'bg-orange-100 dark:bg-orange-900/30' :
                            'bg-gray-100 dark:bg-gray-900/30'
                          }`}>
                            {activity.icon === 'building' && <Building2 className={`h-4 w-4 ${
                              activity.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                              activity.color === 'green' ? 'text-green-600 dark:text-green-400' :
                              activity.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                              activity.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                              'text-gray-600 dark:text-gray-400'
                            }`} />}
                            {activity.icon === 'router' && <Router className={`h-4 w-4 ${
                              activity.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                              activity.color === 'green' ? 'text-green-600 dark:text-green-400' :
                              activity.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                              activity.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                              'text-gray-600 dark:text-gray-400'
                            }`} />}
                            {activity.icon === 'map' && <Map className={`h-4 w-4 ${
                              activity.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                              activity.color === 'green' ? 'text-green-600 dark:text-green-400' :
                              activity.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                              activity.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                              'text-gray-600 dark:text-gray-400'
                            }`} />}
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium">{activity.title}</p>
                            <p className="text-xs text-muted-foreground">{activity.time_ago}</p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4">
                        <p className="text-sm text-muted-foreground">No recent activities</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}
      </div>
    </AppLayout>
  );
}
