import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Bandwidth',
    href: '/bandwidth',
  },
  {
    title: 'Queue Usage',
    href: '/bandwidth/queue-usage',
  },
];

interface QueueUsage {
  id: number;
  queue_name: string;
  target_ip: string;
  download_bytes: number;
  upload_bytes: number;
  total_bytes: number;
  period_start: string;
  period_end: string;
  is_mapped: boolean;
  device: {
    id: number;
    name: string;
    ip_address: string;
  };
  customer?: {
    id: number;
    name: string;
  };
}

interface Device {
  id: number;
  name: string;
  ip_address: string;
}

interface QueueUsageIndexProps {
  queueUsage: {
    data: QueueUsage[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  devices: Device[];
  filters: {
    search?: string;
    device_id?: number;
    show_mapped?: string;
    period_start?: string;
    period_end?: string;
    sort_field?: string;
    sort_direction?: string;
    per_page?: number;
  };
}

export default function QueueUsageIndex({ queueUsage, devices, filters }: QueueUsageIndexProps) {
  const [search, setSearch] = useState(filters.search || '');
  const [deviceId, setDeviceId] = useState(filters.device_id?.toString() || '');
  const [showMapped, setShowMapped] = useState(filters.show_mapped || 'all');
  const [periodStart, setPeriodStart] = useState(filters.period_start || '');
  const [periodEnd, setPeriodEnd] = useState(filters.period_end || '');
  const [perPage, setPerPage] = useState(filters.per_page || 15);
  const [loading, setLoading] = useState(false);

  // Format bytes for display
  const formatBytes = (bytes: number): string => {
    if (bytes >= 1024 * 1024 * 1024) {
      return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
    } else if (bytes >= 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    } else if (bytes >= 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`;
    } else {
      return `${bytes} B`;
    }
  };

  // Apply filters
  const applyFilters = () => {
    setLoading(true);
    const params = new URLSearchParams();

    if (search) params.append('search', search);
    if (deviceId) params.append('device_id', deviceId);
    if (showMapped !== 'all') params.append('show_mapped', showMapped);
    if (periodStart) params.append('period_start', periodStart);
    if (periodEnd) params.append('period_end', periodEnd);
    if (perPage !== 15) params.append('per_page', perPage.toString());

    router.get(`/bandwidth/queue-usage?${params.toString()}`, {}, {
      preserveState: true,
      onFinish: () => setLoading(false),
    });
  };

  // Reset filters
  const resetFilters = () => {
    setSearch('');
    setDeviceId('');
    setShowMapped('all');
    setPeriodStart('');
    setPeriodEnd('');
    setPerPage(15);
    router.get('/bandwidth/queue-usage');
  };

  // Export data
  const exportData = () => {
    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (deviceId) params.append('device_id', deviceId);
    if (showMapped !== 'all') params.append('show_mapped', showMapped);
    if (periodStart) params.append('period_start', periodStart);
    if (periodEnd) params.append('period_end', periodEnd);
    params.append('format', 'csv');

    window.open(`/bandwidth/queue-usage/export?${params.toString()}`);
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Queue Usage - Bandwidth Monitoring" />

      <div className="flex flex-col gap-4 p-4">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">Queue Usage</h1>
            <p className="text-gray-600">Real-time bandwidth usage from all MikroTik queues</p>
          </div>
          <div className="flex gap-2">
            <button
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
              onClick={exportData}
            >
              Export CSV
            </button>
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              onClick={() => window.location.reload()}
            >
              Refresh
            </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div className="bg-white p-4 rounded-lg border">
            <div className="text-sm font-medium text-gray-600">Total Queues</div>
            <div className="text-2xl font-bold">{queueUsage.total}</div>
            <p className="text-xs text-gray-500">Active bandwidth queues</p>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="text-sm font-medium text-gray-600">Mapped Customers</div>
            <div className="text-2xl font-bold">
              {queueUsage.data.filter(q => q.is_mapped).length}
            </div>
            <p className="text-xs text-gray-500">Linked to database</p>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="text-sm font-medium text-gray-600">Total Download</div>
            <div className="text-2xl font-bold">
              {formatBytes(queueUsage.data.reduce((sum, q) => sum + q.download_bytes, 0))}
            </div>
            <p className="text-xs text-gray-500">Current period</p>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="text-sm font-medium text-gray-600">Total Upload</div>
            <div className="text-2xl font-bold">
              {formatBytes(queueUsage.data.reduce((sum, q) => sum + q.upload_bytes, 0))}
            </div>
            <p className="text-xs text-gray-500">Current period</p>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg border mb-4">
          <h3 className="text-lg font-medium mb-4">Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div>
              <label className="text-sm font-medium">Search</label>
              <input
                type="text"
                placeholder="Queue name or IP..."
                className="w-full px-3 py-2 border rounded-md"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Device</label>
              <select
                className="w-full px-3 py-2 border rounded-md"
                value={deviceId}
                onChange={(e) => setDeviceId(e.target.value)}
              >
                <option value="">All devices</option>
                {devices.map((device) => (
                  <option key={device.id} value={device.id.toString()}>
                    {device.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-sm font-medium">Mapping</label>
              <select
                className="w-full px-3 py-2 border rounded-md"
                value={showMapped}
                onChange={(e) => setShowMapped(e.target.value)}
              >
                <option value="all">All queues</option>
                <option value="mapped">Mapped only</option>
                <option value="unmapped">Unmapped only</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium">Start Date</label>
              <input
                type="date"
                className="w-full px-3 py-2 border rounded-md"
                value={periodStart}
                onChange={(e) => setPeriodStart(e.target.value)}
              />
            </div>

            <div>
              <label className="text-sm font-medium">End Date</label>
              <input
                type="date"
                className="w-full px-3 py-2 border rounded-md"
                value={periodEnd}
                onChange={(e) => setPeriodEnd(e.target.value)}
              />
            </div>

            <div className="flex items-end space-x-2">
              <button
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
                onClick={applyFilters}
                disabled={loading}
              >
                {loading ? 'Loading...' : 'Apply'}
              </button>
              <button
                className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                onClick={resetFilters}
              >
                Reset
              </button>
            </div>
          </div>
        </div>

        {/* Queue Usage Table */}
        <div className="bg-white rounded-lg border">
          <div className="p-4 border-b">
            <h3 className="text-lg font-medium">Queue Usage Data</h3>
            <p className="text-gray-600">
              Showing {queueUsage.data.length} of {queueUsage.total} queues
            </p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Queue Name</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Target IP</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Customer</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Download</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Upload</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Total</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Status</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {queueUsage.data.map((usage) => (
                  <tr key={usage.id} className="hover:bg-gray-50">
                    <td className="px-4 py-2 font-medium text-sm">
                      {usage.queue_name}
                    </td>
                    <td className="px-4 py-2 text-sm">{usage.target_ip}</td>
                    <td className="px-4 py-2 text-sm">
                      {usage.customer ? (
                        <Link
                          href={`/customers/${usage.customer.id}`}
                          className="text-blue-600 hover:underline"
                        >
                          {usage.customer.name}
                        </Link>
                      ) : (
                        <span className="text-gray-500">Unmapped</span>
                      )}
                    </td>
                    <td className="px-4 py-2 text-sm">{formatBytes(usage.download_bytes)}</td>
                    <td className="px-4 py-2 text-sm">{formatBytes(usage.upload_bytes)}</td>
                    <td className="px-4 py-2 text-sm font-medium">
                      {formatBytes(usage.total_bytes)}
                    </td>
                    <td className="px-4 py-2 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        usage.is_mapped
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {usage.is_mapped ? "Mapped" : "Unmapped"}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {queueUsage.data.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No queue usage data found for the selected filters.
              </div>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
