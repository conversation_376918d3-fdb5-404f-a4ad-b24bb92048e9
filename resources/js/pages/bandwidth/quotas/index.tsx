import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';
import axios from 'axios';

// Define the BandwidthQuota type
interface BandwidthQuota {
  id: number;
  name: string;
  description: string | null;
  download_limit: number;
  upload_limit: number;
  total_limit: number;
  period: 'daily' | 'weekly' | 'monthly' | 'custom';
  custom_period: number | null;
  action_on_exceed: 'notify' | 'throttle' | 'block';
  action_parameters: Record<string, unknown> | null;
  active: boolean;
  created_at: string;
  updated_at: string;
}

// Define the pagination type
interface Pagination {
  current_page: number;
  data: BandwidthQuota[];
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Bandwidth',
    href: '/bandwidth',
  },
  {
    title: 'Quotas',
    href: '/bandwidth/quotas',
  },
];

export default function BandwidthQuotas() {
  const [quotas, setQuotas] = useState<Pagination | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [search, setSearch] = useState<string>('');
  const [period, setPeriod] = useState<string>('');
  const [action, setAction] = useState<string>('');
  const [active, setActive] = useState<string>('');
  const [sortField, setSortField] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<string>('asc');
  const [perPage, setPerPage] = useState<number>(15);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const fetchQuotas = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/bandwidth/quotas', {
        params: {
          search,
          period: period === 'all' ? undefined : period,
          action_on_exceed: action === 'all' ? undefined : action,
          active: active === '' ? undefined : active === 'true',
          sort_field: sortField,
          sort_direction: sortDirection,
          per_page: perPage,
          page: currentPage,
        },
      });
      setQuotas(response.data);
    } catch (error) {
      console.error('Error fetching bandwidth quotas:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQuotas();
  }, [search, period, action, active, sortField, sortDirection, perPage, currentPage]);

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const formatBytes = (megabytes: number): string => {
    if (megabytes >= 1024) {
      return (megabytes / 1024).toFixed(2) + ' GB';
    }
    return megabytes + ' MB';
  };

  const formatPeriod = (quota: BandwidthQuota): string => {
    switch (quota.period) {
      case 'daily':
        return 'Daily';
      case 'weekly':
        return 'Weekly';
      case 'monthly':
        return 'Monthly';
      case 'custom':
        return `Custom (${quota.custom_period} days)`;
      default:
        return quota.period;
    }
  };

  const formatAction = (action: string): string => {
    return action.charAt(0).toUpperCase() + action.slice(1);
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Bandwidth Quotas" />
      <div className="flex flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Bandwidth Quotas</h1>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            onClick={() => window.location.href = '/bandwidth/quotas/create'}
          >
            Add Quota
          </button>
        </div>

        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search quotas..."
              className="w-full px-4 py-2 border rounded-md"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
            >
              <option value="all">All Periods</option>
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="custom">Custom</option>
            </select>
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={action}
              onChange={(e) => setAction(e.target.value)}
            >
              <option value="all">All Actions</option>
              <option value="notify">Notify</option>
              <option value="throttle">Throttle</option>
              <option value="block">Block</option>
            </select>
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={active}
              onChange={(e) => setActive(e.target.value)}
            >
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={perPage.toString()}
              onChange={(e) => setPerPage(parseInt(e.target.value))}
            >
              <option value="10">10 per page</option>
              <option value="15">15 per page</option>
              <option value="25">25 per page</option>
              <option value="50">50 per page</option>
            </select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border rounded-md">
                <thead className="bg-gray-100">
                  <tr>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('name')}
                    >
                      Name
                      {sortField === 'name' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-4 py-2 text-left">Description</th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('total_limit')}
                    >
                      Total Limit
                      {sortField === 'total_limit' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('period')}
                    >
                      Period
                      {sortField === 'period' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('action_on_exceed')}
                    >
                      Action on Exceed
                      {sortField === 'action_on_exceed' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('active')}
                    >
                      Status
                      {sortField === 'active' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-4 py-2 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {quotas && quotas.data.length > 0 ? (
                    quotas.data.map((quota) => (
                      <tr key={quota.id} className="border-t hover:bg-gray-50">
                        <td className="px-4 py-2">{quota.name}</td>
                        <td className="px-4 py-2">{quota.description || '-'}</td>
                        <td className="px-4 py-2">{formatBytes(quota.total_limit)}</td>
                        <td className="px-4 py-2">{formatPeriod(quota)}</td>
                        <td className="px-4 py-2">{formatAction(quota.action_on_exceed)}</td>
                        <td className="px-4 py-2">
                          <span className={`px-2 py-1 rounded-full text-xs ${quota.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {quota.active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-4 py-2">
                          <div className="flex space-x-2">
                            <button
                              className="px-2 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                              onClick={() => window.location.href = `/bandwidth/quotas/${quota.id}`}
                            >
                              View
                            </button>
                            <button
                              className="px-2 py-1 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                              onClick={() => window.location.href = `/bandwidth/quotas/${quota.id}/edit`}
                            >
                              Edit
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="px-4 py-8 text-center text-gray-500">
                        No bandwidth quotas found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {quotas && (
              <div className="flex justify-between items-center mt-4">
                <div>
                  Showing {quotas.from} to {quotas.to} of {quotas.total} quotas
                </div>
                <div className="flex space-x-2">
                  {Array.from({ length: quotas.last_page }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      className={`px-3 py-1 rounded-md ${
                        page === quotas.current_page
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 hover:bg-gray-300'
                      }`}
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </AppLayout>
  );
}
