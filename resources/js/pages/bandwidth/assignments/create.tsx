import { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Users,
  ArrowLeft,
  User,
  Gauge,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import axios from 'axios';

interface BandwidthAssignmentsCreateProps {
  customers?: Array<{
    id: number;
    name: string;
    email: string;
  }>;
  plans?: Array<{
    id: number;
    name: string;
    download_speed: number;
    upload_speed: number;
    price: number | null;
  }>;
}

export default function CreateBandwidthAssignment({ customers = [], plans = [] }: BandwidthAssignmentsCreateProps) {
  const [formData, setFormData] = useState({
    customer_id: '',
    plan_id: '',
    start_date: new Date().toISOString().split('T')[0],
    end_date: '',
    notes: '',
    active: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});

    try {
      await axios.post('/bandwidth/assignments', formData);
      setSuccess(true);

      setTimeout(() => {
        window.location.href = '/bandwidth/assignments';
      }, 1500);
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.errors) {
        setErrors(error.response.data.errors);
      } else {
        setErrors({ general: 'An error occurred while creating the bandwidth assignment.' });
      }
    } finally {
      setLoading(false);
    }
  };

  const selectedPlan = plans.find(plan => plan.id.toString() === formData.plan_id);

  const formatSpeed = (speed: number): string => {
    return speed.toFixed(2) + ' Mbps';
  };

  const formatPrice = (price: number | null): string => {
    if (!price) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  return (
    <AppLayout>
      <Head title="Create Bandwidth Assignment" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/30">
                <Users className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <h1>Create Bandwidth Assignment</h1>
                <p>Assign bandwidth plan to customer</p>
              </div>
            </div>
          </div>
          <Button variant="outline" asChild>
            <Link href="/bandwidth/assignments">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Assignments
            </Link>
          </Button>
        </div>

        {/* Success Alert */}
        {success && (
          <Alert className="border-green-200 bg-green-50 text-green-800">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Bandwidth assignment created successfully! Redirecting...
            </AlertDescription>
          </Alert>
        )}

        {/* Error Alerts */}
        {errors.general && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {errors.general}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Assignment Details */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Assignment Details
              </CardTitle>
              <CardDescription>
                Select customer and bandwidth plan
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customer_id">
                    Customer <span className="text-destructive">*</span>
                  </Label>
                  <Select value={formData.customer_id} onValueChange={(value) => setFormData(prev => ({ ...prev, customer_id: value }))}>
                    <SelectTrigger className={errors.customer_id ? 'border-destructive' : ''}>
                      <SelectValue placeholder="Select customer" />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id.toString()}>
                          {customer.name} ({customer.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.customer_id && <p className="text-sm text-destructive">{errors.customer_id}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="plan_id">
                    Bandwidth Plan <span className="text-destructive">*</span>
                  </Label>
                  <Select value={formData.plan_id} onValueChange={(value) => setFormData(prev => ({ ...prev, plan_id: value }))}>
                    <SelectTrigger className={errors.plan_id ? 'border-destructive' : ''}>
                      <SelectValue placeholder="Select bandwidth plan" />
                    </SelectTrigger>
                    <SelectContent>
                      {plans.map((plan) => (
                        <SelectItem key={plan.id} value={plan.id.toString()}>
                          {plan.name} - {formatSpeed(plan.download_speed)}/{formatSpeed(plan.upload_speed)} - {formatPrice(plan.price)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.plan_id && <p className="text-sm text-destructive">{errors.plan_id}</p>}
                </div>
              </div>

              {/* Plan Preview */}
              {selectedPlan && (
                <Card className="bg-muted/50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Gauge className="h-4 w-4" />
                      <span className="font-medium">Selected Plan Details</span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Download:</span>
                        <span className="ml-2 font-medium">{formatSpeed(selectedPlan.download_speed)}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Upload:</span>
                        <span className="ml-2 font-medium">{formatSpeed(selectedPlan.upload_speed)}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Price:</span>
                        <span className="ml-2 font-medium">{formatPrice(selectedPlan.price)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start_date">
                    Start Date <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="start_date"
                    name="start_date"
                    type="date"
                    value={formData.start_date}
                    onChange={handleChange}
                    className={errors.start_date ? 'border-destructive' : ''}
                    required
                  />
                  {errors.start_date && <p className="text-sm text-destructive">{errors.start_date}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end_date">
                    End Date (Optional)
                  </Label>
                  <Input
                    id="end_date"
                    name="end_date"
                    type="date"
                    value={formData.end_date}
                    onChange={handleChange}
                  />
                  <p className="text-xs text-muted-foreground">Leave empty for indefinite assignment</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                  placeholder="Optional notes about this assignment"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="active"
                  checked={formData.active}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, active: !!checked }))}
                />
                <Label htmlFor="active">Active Assignment</Label>
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-4">
            <Button variant="outline" asChild>
              <Link href="/bandwidth/assignments">Cancel</Link>
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Assignment'}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
