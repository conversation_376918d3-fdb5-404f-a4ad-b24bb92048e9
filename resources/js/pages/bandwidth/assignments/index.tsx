import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';
import axios from 'axios';

// Define the BandwidthAssignment type
interface BandwidthAssignment {
  id: number;
  assignable_type: string;
  assignable_id: number;
  assignee_type: string;
  assignee_id: number;
  starts_at: string | null;
  ends_at: string | null;
  active: boolean;
  created_at: string;
  updated_at: string;
  assignable?: {
    id: number;
    name: string;
    [key: string]: unknown;
  };
  assignee?: {
    id: number;
    name?: string;
    username?: string;
    [key: string]: unknown;
  };
}

// Define the pagination type
interface Pagination {
  current_page: number;
  data: BandwidthAssignment[];
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Bandwidth',
    href: '/bandwidth',
  },
  {
    title: 'Assignments',
    href: '/bandwidth/assignments',
  },
];

export default function BandwidthAssignments() {
  const [assignments, setAssignments] = useState<Pagination | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [search, setSearch] = useState<string>('');
  const [assignableType, setAssignableType] = useState<string>('');
  const [assigneeType, setAssigneeType] = useState<string>('');
  const [active, setActive] = useState<string>('');
  const [current, setCurrent] = useState<boolean>(false);
  const [sortField, setSortField] = useState<string>('created_at');
  const [sortDirection, setSortDirection] = useState<string>('desc');
  const [perPage, setPerPage] = useState<number>(15);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const fetchAssignments = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/bandwidth/assignments', {
        params: {
          search,
          assignable_type: assignableType === 'all' ? undefined : assignableType,
          assignee_type: assigneeType === 'all' ? undefined : assigneeType,
          active: active === '' ? undefined : active === 'true',
          current: current || undefined,
          sort_field: sortField,
          sort_direction: sortDirection,
          per_page: perPage,
          page: currentPage,
        },
      });
      setAssignments(response.data);
    } catch (error) {
      console.error('Error fetching bandwidth assignments:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAssignments();
  }, [search, assignableType, assigneeType, active, current, sortField, sortDirection, perPage, currentPage]);

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc'); // Default to descending for newest first
    }
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getAssignableName = (assignment: BandwidthAssignment): string => {
    if (!assignment.assignable) {
      return `${formatAssignableType(assignment.assignable_type)} #${assignment.assignable_id}`;
    }

    return assignment.assignable.name || `${formatAssignableType(assignment.assignable_type)} #${assignment.assignable_id}`;
  };

  const getAssigneeName = (assignment: BandwidthAssignment): string => {
    if (!assignment.assignee) {
      return `${formatAssigneeType(assignment.assignee_type)} #${assignment.assignee_id}`;
    }

    if (assignment.assignee.name) {
      return assignment.assignee.name;
    }

    if (assignment.assignee.username) {
      return assignment.assignee.username;
    }

    return `${formatAssigneeType(assignment.assignee_type)} #${assignment.assignee_id}`;
  };

  const formatAssignableType = (type: string): string => {
    // Convert "App\Models\Bandwidth\BandwidthPlan" to "Plan"
    const parts = type.split('\\');
    const lastPart = parts[parts.length - 1];
    return lastPart.replace('Bandwidth', '');
  };

  const formatAssigneeType = (type: string): string => {
    // Convert "App\Models\User" to "User"
    const parts = type.split('\\');
    return parts[parts.length - 1];
  };

  const isCurrentlyActive = (assignment: BandwidthAssignment): boolean => {
    const now = new Date();
    const startsAt = assignment.starts_at ? new Date(assignment.starts_at) : null;
    const endsAt = assignment.ends_at ? new Date(assignment.ends_at) : null;

    if (!assignment.active) return false;

    if (startsAt && startsAt > now) return false;
    if (endsAt && endsAt < now) return false;

    return true;
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Bandwidth Assignments" />
      <div className="flex flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Bandwidth Assignments</h1>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            onClick={() => window.location.href = '/bandwidth/assignments/create'}
          >
            Create Assignment
          </button>
        </div>

        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search..."
              className="w-full px-4 py-2 border rounded-md"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={assignableType}
              onChange={(e) => setAssignableType(e.target.value)}
            >
              <option value="all">All Assignable Types</option>
              <option value="App\Models\Bandwidth\BandwidthPlan">Plan</option>
              <option value="App\Models\Bandwidth\BandwidthPolicy">Policy</option>
              <option value="App\Models\Bandwidth\BandwidthQuota">Quota</option>
            </select>
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={assigneeType}
              onChange={(e) => setAssigneeType(e.target.value)}
            >
              <option value="">All Assignee Types</option>
              <option value="App\Models\User">User</option>
              <option value="App\Models\Device">Device</option>
              <option value="App\Models\Network">Network</option>
            </select>
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={active}
              onChange={(e) => setActive(e.target.value)}
            >
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>
          <div>
            <div className="flex items-center px-4 py-2 border rounded-md">
              <input
                type="checkbox"
                id="current"
                className="mr-2"
                checked={current}
                onChange={(e) => setCurrent(e.target.checked)}
              />
              <label htmlFor="current">Currently In Effect</label>
            </div>
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={perPage.toString()}
              onChange={(e) => setPerPage(parseInt(e.target.value))}
            >
              <option value="10">10 per page</option>
              <option value="15">15 per page</option>
              <option value="25">25 per page</option>
              <option value="50">50 per page</option>
            </select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border rounded-md">
                <thead className="bg-gray-100">
                  <tr>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('assignable_type')}
                    >
                      Assignable
                      {sortField === 'assignable_type' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('assignee_type')}
                    >
                      Assignee
                      {sortField === 'assignee_type' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('starts_at')}
                    >
                      Starts At
                      {sortField === 'starts_at' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('ends_at')}
                    >
                      Ends At
                      {sortField === 'ends_at' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('active')}
                    >
                      Status
                      {sortField === 'active' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-4 py-2 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {assignments && assignments.data.length > 0 ? (
                    assignments.data.map((assignment) => (
                      <tr key={assignment.id} className="border-t hover:bg-gray-50">
                        <td className="px-4 py-2">
                          <div>
                            <span className="font-semibold">{formatAssignableType(assignment.assignable_type)}</span>
                          </div>
                          <a
                            href={`/bandwidth/${formatAssignableType(assignment.assignable_type).toLowerCase()}s/${assignment.assignable_id}`}
                            className="text-blue-500 hover:underline"
                          >
                            {getAssignableName(assignment)}
                          </a>
                        </td>
                        <td className="px-4 py-2">
                          <div>
                            <span className="font-semibold">{formatAssigneeType(assignment.assignee_type)}</span>
                          </div>
                          <a
                            href={`/${formatAssigneeType(assignment.assignee_type).toLowerCase()}s/${assignment.assignee_id}`}
                            className="text-blue-500 hover:underline"
                          >
                            {getAssigneeName(assignment)}
                          </a>
                        </td>
                        <td className="px-4 py-2">{formatDate(assignment.starts_at)}</td>
                        <td className="px-4 py-2">{formatDate(assignment.ends_at)}</td>
                        <td className="px-4 py-2">
                          <div className="flex flex-col">
                            <span className={`px-2 py-1 rounded-full text-xs ${assignment.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                              {assignment.active ? 'Active' : 'Inactive'}
                            </span>
                            {assignment.active && (
                              <span className={`mt-1 px-2 py-1 rounded-full text-xs ${isCurrentlyActive(assignment) ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}`}>
                                {isCurrentlyActive(assignment) ? 'In Effect' : 'Not In Effect'}
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-2">
                          <div className="flex space-x-2">
                            <button
                              className="px-2 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                              onClick={() => window.location.href = `/bandwidth/assignments/${assignment.id}`}
                            >
                              View
                            </button>
                            <button
                              className="px-2 py-1 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                              onClick={() => window.location.href = `/bandwidth/assignments/${assignment.id}/edit`}
                            >
                              Edit
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                        No bandwidth assignments found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {assignments && (
              <div className="flex justify-between items-center mt-4">
                <div>
                  Showing {assignments.from} to {assignments.to} of {assignments.total} assignments
                </div>
                <div className="flex space-x-2">
                  {Array.from({ length: assignments.last_page }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      className={`px-3 py-1 rounded-md ${
                        page === assignments.current_page
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 hover:bg-gray-300'
                      }`}
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </AppLayout>
  );
}
