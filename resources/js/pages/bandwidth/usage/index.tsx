import { useState, useEffect } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ModernTable, ModernTableBody, ModernTableCell, ModernTableHeader, ModernTableRow } from '@/components/ui/modern-table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ResponsivePagination } from '@/components/ui/simplified-pagination';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Search,
  Download,
  BarChart3,
  MoreHorizontal,
  Eye,
  Calendar,
  Filter,
  TrendingUp,
  TrendingDown,
  Activity,
  RefreshCw,
  FileText,
  Database
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  PermissionButton,
  PermissionGate,
  PERMISSIONS
} from '@/components/permissions';
import axios from 'axios';

// Define the BandwidthUsage type
interface BandwidthUsage {
  id: number;
  usageable_type: string;
  usageable_id: number;
  download: number;
  upload: number;
  total: number;
  period_start: string;
  period_end: string;
  created_at: string;
  updated_at: string;
  usageable?: {
    id: number;
    name?: string;
    username?: string;
    email?: string;
    [key: string]: unknown;
  };
}

// Define the pagination type
interface BandwidthUsagePagination {
  current_page: number;
  data: BandwidthUsage[];
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

// Define filters interface
interface BandwidthUsageFilters {
  search: string;
  usageable_type: string;
  period_start: string;
  period_end: string;
  sort_field: string;
  sort_direction: string;
  per_page: number;
}

// Define stats interface for summary cards
interface BandwidthUsageStats {
  total_records: number;
  total_download: number;
  total_upload: number;
  total_bandwidth: number;
  unique_entities: number;
  period_range: {
    start: string;
    end: string;
  };
}

export default function BandwidthUsageIndex() {
  const [usageData, setUsageData] = useState<BandwidthUsagePagination | null>(null);
  const [usageStats, setUsageStats] = useState<BandwidthUsageStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // Filter states
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [usageableTypeFilter, setUsageableTypeFilter] = useState<string>('all');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [sortField, setSortField] = useState<string>('total');
  const [sortDirection, setSortDirection] = useState<string>('desc');
  const [perPage, setPerPage] = useState<number>(15);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const fetchUsage = async (showRefreshing = false) => {
    if (showRefreshing) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      // Get customer_id from URL if present
      const urlParams = new URLSearchParams(window.location.search);
      const customerId = urlParams.get('customer_id');

      const response = await axios.get('/api/bandwidth/usage', {
        params: {
          search: searchQuery,
          customer_id: customerId || undefined,
          usageable_type: usageableTypeFilter === 'all' ? undefined : usageableTypeFilter,
          period_start: startDate || undefined,
          period_end: endDate || undefined,
          sort_field: sortField,
          sort_direction: sortDirection,
          per_page: perPage,
          page: currentPage,
        },
      });
      setUsageData(response.data);
    } catch (error) {
      console.error('Error fetching bandwidth usage:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/bandwidth/usage/statistics', {
        params: {
          period_start: startDate || undefined,
          period_end: endDate || undefined,
          usageable_type: usageableTypeFilter === 'all' ? undefined : usageableTypeFilter,
        },
      });
      setUsageStats(response.data);
    } catch (error) {
      console.error('Error fetching bandwidth usage statistics:', error);
    }
  };

  useEffect(() => {
    fetchUsage();
    fetchStats();
  }, [searchQuery, usageableTypeFilter, startDate, endDate, sortField, sortDirection, perPage, currentPage]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page when searching
    fetchUsage();
    fetchStats();
  };

  const handleRefresh = () => {
    fetchUsage(true);
    fetchStats();
  };

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc'); // Default to descending for usage data
    }
  };

  const handleExport = () => {
    const params = new URLSearchParams({
      search: searchQuery,
      usageable_type: usageableTypeFilter === 'all' ? '' : usageableTypeFilter,
      period_start: startDate || '',
      period_end: endDate || '',
      sort_field: sortField,
      sort_direction: sortDirection,
    });

    window.open(`/bandwidth/usage/export?${params.toString()}`, '_blank');
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDateRange = (startDate: string, endDate: string): string => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start.toDateString() === end.toDateString()) {
      return start.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }

    return `${start.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${end.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
  };

  const getUsageableName = (usage: BandwidthUsage): string => {
    if (!usage.usageable) {
      return `${formatUsageableType(usage.usageable_type)} #${usage.usageable_id}`;
    }

    if (usage.usageable.name) {
      return usage.usageable.name;
    }

    if (usage.usageable.username) {
      return usage.usageable.username;
    }

    if (usage.usageable.email) {
      return usage.usageable.email;
    }

    return `${formatUsageableType(usage.usageable_type)} #${usage.usageable_id}`;
  };

  const formatUsageableType = (type: string): string => {
    // Convert "App\Models\Services\StaticIpService" to "Static IP"
    const parts = type.split('\\');
    const className = parts[parts.length - 1];

    // Handle specific service types
    if (className === 'StaticIpService') return 'Static IP';
    if (className === 'PppoeService') return 'PPPoE';
    if (className === 'User') return 'User';
    if (className === 'Customer') return 'Customer';

    // Default: convert CamelCase to readable format
    return className.replace(/([A-Z])/g, ' $1').trim();
  };

  const getUsageableTypeBadge = (type: string) => {
    const formattedType = formatUsageableType(type);

    switch (formattedType) {
      case 'Static IP':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Static IP</Badge>;
      case 'PPPoE':
        return <Badge className="bg-green-100 text-green-800 border-green-200">PPPoE</Badge>;
      case 'User':
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">User</Badge>;
      case 'Customer':
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Customer</Badge>;
      default:
        return <Badge variant="outline">{formattedType}</Badge>;
    }
  };

  return (
    <AppLayout>
      <Head title="Bandwidth Usage" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <h1>Bandwidth Usage</h1>
            <p>Monitor and analyze bandwidth consumption across your network</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <PermissionButton permission={PERMISSIONS.REPORTS.EXPORT} onClick={handleExport} className="btn-gradient">
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </PermissionButton>
            <PermissionButton permission={PERMISSIONS.BANDWIDTH.VIEW} asChild>
              <Link href="/bandwidth/usage/dashboard">
                <BarChart3 className="h-4 w-4 mr-2" />
                Dashboard
              </Link>
            </PermissionButton>
          </div>
        </div>

        {/* Statistics Cards */}
        <PermissionGate permission={PERMISSIONS.BANDWIDTH.VIEW}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Records</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {usageStats?.total_records?.toLocaleString() || '0'}
                </div>
                <p className="text-xs text-muted-foreground">
                  Usage entries tracked
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Download</CardTitle>
                <TrendingDown className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {formatBytes(usageStats?.total_download || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Data downloaded
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Upload</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatBytes(usageStats?.total_upload || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Data uploaded
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Bandwidth</CardTitle>
                <Activity className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {formatBytes(usageStats?.total_bandwidth || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Combined usage
                </p>
              </CardContent>
            </Card>
          </div>
        </PermissionGate>

        {/* Filters Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Search & Filter
            </CardTitle>
            <CardDescription>Filter bandwidth usage data by entity, type, and date range</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="lg:col-span-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by entity name, username, or email..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 input-modern"
                    />
                  </div>
                </div>

                <div>
                  <Select value={usageableTypeFilter} onValueChange={setUsageableTypeFilter}>
                    <SelectTrigger className="input-modern">
                      <SelectValue placeholder="Entity Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="App\Models\Services\StaticIpService">Static IP Services</SelectItem>
                      <SelectItem value="App\Models\Services\PppoeService">PPPoE Services</SelectItem>
                      <SelectItem value="App\Models\User">Users</SelectItem>
                      <SelectItem value="App\Models\Customer">Customers</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Select value={perPage.toString()} onValueChange={(value) => setPerPage(parseInt(value))}>
                    <SelectTrigger className="input-modern">
                      <SelectValue placeholder="Per Page" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10 per page</SelectItem>
                      <SelectItem value="15">15 per page</SelectItem>
                      <SelectItem value="25">25 per page</SelectItem>
                      <SelectItem value="50">50 per page</SelectItem>
                      <SelectItem value="100">100 per page</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <Calendar className="inline h-4 w-4 mr-1" />
                    Start Date
                  </label>
                  <Input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="input-modern"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <Calendar className="inline h-4 w-4 mr-1" />
                    End Date
                  </label>
                  <Input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="input-modern"
                  />
                </div>

                <div className="flex items-end">
                  <Button type="submit" className="btn-gradient w-full">
                    <Search className="h-4 w-4 mr-2" />
                    Apply Filters
                  </Button>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Usage Data Table */}
        <Card>
          <CardHeader>
            <CardTitle>Bandwidth Usage Data</CardTitle>
            <CardDescription>
              {usageData ? (
                <>
                  {usageData.total} total records • Showing {usageData.from}-{usageData.to}
                  {usageStats?.period_range && (
                    <> • Period: {formatDateRange(usageStats.period_range.start, usageStats.period_range.end)}</>
                  )}
                </>
              ) : (
                'Loading usage data...'
              )}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="flex flex-col items-center gap-4">
                  <RefreshCw className="h-8 w-8 animate-spin text-primary" />
                  <p className="text-muted-foreground">Loading bandwidth usage data...</p>
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <ModernTable>
                  <ModernTableHeader>
                    <ModernTableRow>
                      <ModernTableCell
                        header
                        className="min-w-[120px] cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('usageable_type')}
                      >
                        <div className="flex items-center gap-1">
                          Type
                          {sortField === 'usageable_type' && (
                            <span className="text-primary">
                              {sortDirection === 'asc' ? '↑' : '↓'}
                            </span>
                          )}
                        </div>
                      </ModernTableCell>
                      <ModernTableCell header className="min-w-[200px]">Entity</ModernTableCell>
                      <ModernTableCell
                        header
                        className="min-w-[120px] cursor-pointer hover:bg-muted/50 hidden sm:table-cell"
                        onClick={() => handleSort('download')}
                      >
                        <div className="flex items-center gap-1">
                          <TrendingDown className="h-4 w-4 text-blue-600" />
                          Download
                          {sortField === 'download' && (
                            <span className="text-primary">
                              {sortDirection === 'asc' ? '↑' : '↓'}
                            </span>
                          )}
                        </div>
                      </ModernTableCell>
                      <ModernTableCell
                        header
                        className="min-w-[120px] cursor-pointer hover:bg-muted/50 hidden sm:table-cell"
                        onClick={() => handleSort('upload')}
                      >
                        <div className="flex items-center gap-1">
                          <TrendingUp className="h-4 w-4 text-green-600" />
                          Upload
                          {sortField === 'upload' && (
                            <span className="text-primary">
                              {sortDirection === 'asc' ? '↑' : '↓'}
                            </span>
                          )}
                        </div>
                      </ModernTableCell>
                      <ModernTableCell
                        header
                        className="min-w-[120px] cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('total')}
                      >
                        <div className="flex items-center gap-1">
                          <Activity className="h-4 w-4 text-purple-600" />
                          Total
                          {sortField === 'total' && (
                            <span className="text-primary">
                              {sortDirection === 'asc' ? '↑' : '↓'}
                            </span>
                          )}
                        </div>
                      </ModernTableCell>
                      <ModernTableCell
                        header
                        className="min-w-[180px] cursor-pointer hover:bg-muted/50 hidden lg:table-cell"
                        onClick={() => handleSort('period_start')}
                      >
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Period
                          {sortField === 'period_start' && (
                            <span className="text-primary">
                              {sortDirection === 'asc' ? '↑' : '↓'}
                            </span>
                          )}
                        </div>
                      </ModernTableCell>
                      <ModernTableCell header className="text-right min-w-[80px]">Actions</ModernTableCell>
                    </ModernTableRow>
                  </ModernTableHeader>
                  <ModernTableBody>
                    {usageData && usageData.data.length > 0 ? (
                      usageData.data.map((usage) => (
                        <ModernTableRow key={usage.id} clickable>
                          <ModernTableCell>
                            {getUsageableTypeBadge(usage.usageable_type)}
                          </ModernTableCell>
                          <ModernTableCell className="font-medium">
                            <div className="flex flex-col">
                              <span className="font-medium text-foreground">
                                {getUsageableName(usage)}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                ID: {usage.usageable_id}
                              </span>
                            </div>
                          </ModernTableCell>
                          <ModernTableCell className="hidden sm:table-cell">
                            <div className="flex items-center gap-1">
                              <TrendingDown className="h-3 w-3 text-blue-600" />
                              <span className="font-mono text-blue-600">
                                {formatBytes(usage.download)}
                              </span>
                            </div>
                          </ModernTableCell>
                          <ModernTableCell className="hidden sm:table-cell">
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3 text-green-600" />
                              <span className="font-mono text-green-600">
                                {formatBytes(usage.upload)}
                              </span>
                            </div>
                          </ModernTableCell>
                          <ModernTableCell>
                            <div className="flex items-center gap-1">
                              <Activity className="h-3 w-3 text-purple-600" />
                              <span className="font-mono font-semibold text-purple-600">
                                {formatBytes(usage.total)}
                              </span>
                            </div>
                          </ModernTableCell>
                          <ModernTableCell className="hidden lg:table-cell">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="text-sm text-muted-foreground cursor-help">
                                    {formatDateRange(usage.period_start, usage.period_end)}
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <div className="text-xs">
                                    <div>Start: {formatDate(usage.period_start)}</div>
                                    <div>End: {formatDate(usage.period_end)}</div>
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </ModernTableCell>
                          <ModernTableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-8 w-8">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Actions</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem asChild>
                                  <Link href={`/bandwidth/usage/${usage.id}`}>
                                    <Eye className="h-4 w-4 mr-2" />
                                    View Details
                                  </Link>
                                </DropdownMenuItem>
                                <PermissionGate permission={PERMISSIONS.REPORTS.EXPORT}>
                                  <DropdownMenuItem onClick={() => {
                                    const params = new URLSearchParams({
                                      usage_id: usage.id.toString(),
                                      format: 'csv'
                                    });
                                    window.open(`/bandwidth/usage/export?${params.toString()}`, '_blank');
                                  }}>
                                    <FileText className="h-4 w-4 mr-2" />
                                    Export Record
                                  </DropdownMenuItem>
                                </PermissionGate>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </ModernTableCell>
                        </ModernTableRow>
                      ))
                    ) : (
                      <ModernTableRow>
                        <ModernTableCell colSpan={7} className="text-center py-12 text-muted-foreground">
                          <div className="flex flex-col items-center gap-2">
                            <Database className="h-8 w-8 text-muted-foreground/50" />
                            <p>No bandwidth usage data found</p>
                            <p className="text-sm">Try adjusting your filters or date range</p>
                          </div>
                        </ModernTableCell>
                      </ModernTableRow>
                    )}
                  </ModernTableBody>
                </ModernTable>
              </div>

            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {usageData && usageData.last_page > 1 && (
          <div className="mt-6">
            <ResponsivePagination
              currentPage={usageData.current_page}
              lastPage={usageData.last_page}
              total={usageData.total}
              from={usageData.from}
              to={usageData.to}
              onPageChange={(page) => {
                setCurrentPage(page);
              }}
            />
          </div>
        )}
      </div>
    </AppLayout>
  );
}
