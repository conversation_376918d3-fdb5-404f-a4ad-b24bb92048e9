import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Bandwidth',
    href: '/bandwidth',
  },
  {
    title: 'Usage',
    href: '/bandwidth/usage',
  },
  {
    title: 'Dashboard',
    href: '/bandwidth/usage/dashboard',
  },
];

interface UsageSummary {
  total_download: number;
  total_upload: number;
  total_bandwidth: number;
  daily_usage: {
    date: string;
    download: number;
    upload: number;
    total: number;
  }[];
  top_users: {
    id: number;
    name: string;
    download: number;
    upload: number;
    total: number;
  }[];
  top_devices: {
    id: number;
    name: string;
    download: number;
    upload: number;
    total: number;
  }[];
}

export default function BandwidthUsageDashboard() {
  const [usageSummary, setUsageSummary] = useState<UsageSummary | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [period, setPeriod] = useState<string>('week');

  const fetchUsageSummary = async () => {
    setLoading(true);
    try {
      // Fetch real data from our API
      const response = await fetch(`/api/bandwidth/usage/dashboard?period=${period}`);
      const data = await response.json();

      // Transform API data to match our interface
      const transformedData: UsageSummary = {
        total_download: data.summary.total_download || 0,
        total_upload: data.summary.total_upload || 0,
        total_bandwidth: data.summary.total_bandwidth || 0,
        daily_usage: data.hourly_usage?.map((item: any) => ({
          date: item.hour,
          download: item.download || 0,
          upload: item.upload || 0,
          total: item.total || 0,
        })) || [],
        top_users: data.top_users?.map((user: any) => ({
          id: user.usageable_id,
          name: user.usageable?.name || user.usageable?.username || `Customer #${user.usageable_id}`,
          download: user.download || 0,
          upload: user.upload || 0,
          total: user.total_usage || 0,
        })) || [],
        top_devices: data.device_usage?.map((device: any) => ({
          id: device.device.id,
          name: device.device.name,
          download: Math.floor(device.total_usage * 0.6), // Estimate download as 60%
          upload: Math.floor(device.total_usage * 0.4), // Estimate upload as 40%
          total: device.total_usage || 0,
        })) || [],
      };

      setUsageSummary(transformedData);
    } catch (error) {
      console.error('Error fetching bandwidth usage summary:', error);
      // Fallback to empty data
      setUsageSummary({
        total_download: 0,
        total_upload: 0,
        total_bandwidth: 0,
        daily_usage: [],
        top_users: [],
        top_devices: [],
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsageSummary();
  }, [period]);

  const formatBytes = (bytes: number): string => {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const Card = ({ title, value, description, link }: { title: string; value: string | number; description?: string; link?: string }) => (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
      <h3 className="text-lg font-medium text-gray-700">{title}</h3>
      <p className="text-3xl font-bold mt-2 text-blue-600">{value}</p>
      {description && <p className="text-sm text-gray-500 mt-1">{description}</p>}
      {link && (
        <a
          href={link}
          className="mt-4 inline-block text-blue-500 hover:text-blue-700 font-medium text-sm"
        >
          View Details →
        </a>
      )}
    </div>
  );

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Bandwidth Usage Dashboard" />
      <div className="flex flex-col gap-6 p-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-800">Bandwidth Usage Dashboard</h1>
          <div className="flex gap-2">
            <select
              className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
            >
              <option value="day">Last 24 Hours</option>
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="year">Last 365 Days</option>
            </select>
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              onClick={() => window.location.href = '/bandwidth/usage/export'}
            >
              Export Data
            </button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card
                title="Total Download"
                value={formatBytes(usageSummary?.total_download || 0)}
                description="All time download usage"
              />
              <Card
                title="Total Upload"
                value={formatBytes(usageSummary?.total_upload || 0)}
                description="All time upload usage"
              />
              <Card
                title="Total Bandwidth"
                value={formatBytes(usageSummary?.total_bandwidth || 0)}
                description="Combined download and upload"
              />
            </div>

            <div className="grid grid-cols-1 gap-6 mt-6">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <h2 className="text-xl font-semibold mb-4">Daily Usage Trends</h2>
                <div className="h-64 flex items-center justify-center">
                  <p className="text-gray-500">Chart would be displayed here in a real application</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <h2 className="text-xl font-semibold mb-4">Top Users by Bandwidth</h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Download
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Upload
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {usageSummary?.top_users.map((user) => (
                        <tr key={user.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {user.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatBytes(user.download)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatBytes(user.upload)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatBytes(user.total)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <h2 className="text-xl font-semibold mb-4">Top Devices by Bandwidth</h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Device
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Download
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Upload
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {usageSummary?.top_devices.map((device) => (
                        <tr key={device.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {device.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatBytes(device.download)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatBytes(device.upload)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatBytes(device.total)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 mt-6">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <a
                    href="/bandwidth/usage"
                    className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <span className="text-3xl mb-2">📊</span>
                    <span className="text-sm font-medium">View All Usage</span>
                  </a>
                  <a
                    href="/bandwidth/usage/export"
                    className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <span className="text-3xl mb-2">📥</span>
                    <span className="text-sm font-medium">Export Data</span>
                  </a>
                  <a
                    href="/bandwidth/quotas"
                    className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <span className="text-3xl mb-2">📏</span>
                    <span className="text-sm font-medium">Manage Quotas</span>
                  </a>
                  <a
                    href="/bandwidth"
                    className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <span className="text-3xl mb-2">🔙</span>
                    <span className="text-sm font-medium">Back to Dashboard</span>
                  </a>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </AppLayout>
  );
}
