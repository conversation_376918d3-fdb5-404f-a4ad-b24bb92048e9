import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import {
  Gauge,
  Zap,
  Settings,
  BarChart3,
  Plus,
  Activity,
  Download,
  Upload,
  TrendingUp,
  Users,
  Clock,
  CheckCircle
} from 'lucide-react';
import axios from 'axios';
import { CreateButton } from '@/components/permissions';
import { PermissionButton } from '@/components/permissions';

interface StatsSummary {
  total_plans: number;
  active_plans: number;
  total_policies: number;
  active_policies: number;
  total_rules: number;
  active_rules: number;
  total_quotas: number;
  active_quotas: number;
  total_assignments: number;
  active_assignments: number;
  total_usage_records: number;
  total_download: number;
  total_upload: number;
  total_bandwidth: number;
}

export default function BandwidthDashboard() {
  const [stats, setStats] = useState<StatsSummary | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  const fetchStats = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/bandwidth/stats');
      setStats(response.data);
    } catch (error) {
      console.error('Error fetching bandwidth statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  const formatBytes = (bytes: number): string => {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <AppLayout>
      <Head title="Bandwidth Management Dashboard" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                <Gauge className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1>Bandwidth Management</h1>
                <p>Monitor and manage network bandwidth allocation and usage</p>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/bandwidth/usage/dashboard">
                <BarChart3 className="h-4 w-4 mr-2" />
                Usage Dashboard
              </Link>
            </Button>
            <PermissionButton permission="manage bandwidth plans" asChild>
              <Link href={route('bandwidth.plans.create')}>
                <Plus className="h-4 w-4 mr-2" />
                Add Plan
              </Link>
            </PermissionButton>
      
          </div>
        </div>

        {loading ? (
          <Card className="card-modern">
            <CardContent className="flex justify-center items-center h-64">
              <div className="flex flex-col items-center gap-4">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                <p className="text-muted-foreground">Loading bandwidth management data...</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Bandwidth Management Stats */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <Link href="/bandwidth/plans" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Bandwidth Plans</p>
                        <p className="text-3xl font-bold">{stats?.total_plans || 0}</p>
                        <p className="text-sm text-muted-foreground">
                          {stats?.active_plans || 0} active plans
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-blue-100 dark:bg-blue-900/30">
                        <Gauge className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/bandwidth/policies" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Bandwidth Policies</p>
                        <p className="text-3xl font-bold">{stats?.total_policies || 0}</p>
                        <p className="text-sm text-muted-foreground">
                          {stats?.active_policies || 0} active policies
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-green-100 dark:bg-green-900/30">
                        <Settings className="h-6 w-6 text-green-600 dark:text-green-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/bandwidth/rules" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Bandwidth Rules</p>
                        <p className="text-3xl font-bold">{stats?.total_rules || 0}</p>
                        <p className="text-sm text-muted-foreground">
                          {stats?.active_rules || 0} active rules
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-purple-100 dark:bg-purple-900/30">
                        <Zap className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/bandwidth/quotas" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Bandwidth Quotas</p>
                        <p className="text-3xl font-bold">{stats?.total_quotas || 0}</p>
                        <p className="text-sm text-muted-foreground">
                          {stats?.active_quotas || 0} active quotas
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-orange-100 dark:bg-orange-900/30">
                        <Clock className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </div>

            {/* Additional Stats */}
            <div className="grid gap-6 md:grid-cols-2">
              <Link href="/bandwidth/assignments" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Assignments</p>
                        <p className="text-3xl font-bold">{stats?.total_assignments || 0}</p>
                        <p className="text-sm text-muted-foreground">
                          {stats?.active_assignments || 0} active assignments
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-indigo-100 dark:bg-indigo-900/30">
                        <Users className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/bandwidth/usage" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Usage Records</p>
                        <p className="text-3xl font-bold">{stats?.total_usage_records || 0}</p>
                        <p className="text-sm text-muted-foreground">
                          Total usage records
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-teal-100 dark:bg-teal-900/30">
                        <Activity className="h-6 w-6 text-teal-600 dark:text-teal-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </div>

            {/* Usage Statistics */}
            <div className="grid gap-6 md:grid-cols-3">
              <Link href="/bandwidth/usage" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Total Download</p>
                        <p className="text-2xl font-bold">{formatBytes(stats?.total_download || 0)}</p>
                        <p className="text-sm text-muted-foreground">
                          Cumulative download
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-green-100 dark:bg-green-900/30">
                        <Download className="h-6 w-6 text-green-600 dark:text-green-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/bandwidth/usage" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Total Upload</p>
                        <p className="text-2xl font-bold">{formatBytes(stats?.total_upload || 0)}</p>
                        <p className="text-sm text-muted-foreground">
                          Cumulative upload
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-blue-100 dark:bg-blue-900/30">
                        <Upload className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/bandwidth/usage" className="group">
                <Card className="card-modern transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-muted-foreground">Total Bandwidth</p>
                        <p className="text-2xl font-bold">{formatBytes(stats?.total_bandwidth || 0)}</p>
                        <p className="text-sm text-muted-foreground">
                          Combined usage
                        </p>
                      </div>
                      <div className="p-3 rounded-xl bg-purple-100 dark:bg-purple-900/30">
                        <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </div>

            {/* Quick Actions */}
            <Card className="card-modern">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Quick Actions
                </CardTitle>
                <CardDescription>
                  Common bandwidth management tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Link href="/bandwidth/plans/create" className="group">
                    <Card className="transition-all duration-200 group-hover:shadow-md group-hover:scale-[1.02] border-2 border-dashed border-muted-foreground/20 hover:border-blue-500/50">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors">
                            <Plus className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <p className="font-medium">Add Plan</p>
                            <p className="text-sm text-muted-foreground">Create bandwidth plan</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>

                  <Link href="/bandwidth/policies/create" className="group">
                    <Card className="transition-all duration-200 group-hover:shadow-md group-hover:scale-[1.02] border-2 border-dashed border-muted-foreground/20 hover:border-green-500/50">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/30 group-hover:bg-green-200 dark:group-hover:bg-green-900/50 transition-colors">
                            <Settings className="h-5 w-5 text-green-600 dark:text-green-400" />
                          </div>
                          <div>
                            <p className="font-medium">Add Policy</p>
                            <p className="text-sm text-muted-foreground">Create bandwidth policy</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>

                  <Link href="/bandwidth/rules/create" className="group">
                    <Card className="transition-all duration-200 group-hover:shadow-md group-hover:scale-[1.02] border-2 border-dashed border-muted-foreground/20 hover:border-purple-500/50">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/30 group-hover:bg-purple-200 dark:group-hover:bg-purple-900/50 transition-colors">
                            <Zap className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                          </div>
                          <div>
                            <p className="font-medium">Add Rule</p>
                            <p className="text-sm text-muted-foreground">Create bandwidth rule</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>

                  <Link href="/bandwidth/assignments/create" className="group">
                    <Card className="transition-all duration-200 group-hover:shadow-md group-hover:scale-[1.02] border-2 border-dashed border-muted-foreground/20 hover:border-orange-500/50">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/30 group-hover:bg-orange-200 dark:group-hover:bg-orange-900/50 transition-colors">
                            <Users className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                          </div>
                          <div>
                            <p className="font-medium">Create Assignment</p>
                            <p className="text-sm text-muted-foreground">Assign bandwidth plan</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </AppLayout>
  );
}
