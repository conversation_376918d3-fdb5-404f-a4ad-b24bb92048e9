import { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import axios from 'axios';

interface EditBandwidthPlanProps {
  planId: number;
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Bandwidth',
    href: '/bandwidth',
  },
  {
    title: 'Plans',
    href: '/bandwidth/plans',
  },
  {
    title: 'Edit',
    href: '#',
  },
];

export default function EditBandwidthPlan({ planId }: EditBandwidthPlanProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    download_speed: 1, // Default to 1 Mbps
    upload_speed: 1,   // Default to 1 Mbps
    burst_download_speed: null as number | null,
    burst_upload_speed: null as number | null,
    burst_time: null as number | null,
    priority: 5,          // Default priority (1-8)
    active: true,         // Default to active
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [success, setSuccess] = useState(false);

  // Fetch plan data
  useEffect(() => {
    const fetchPlan = async () => {
      try {
        const response = await axios.get(`/api/bandwidth/plans/${planId}`);
        const plan = response.data;

        setFormData({
          name: plan.name,
          description: plan.description || '',
          download_speed: plan.download_speed,
          upload_speed: plan.upload_speed,
          burst_download_speed: plan.burst_download_speed,
          burst_upload_speed: plan.burst_upload_speed,
          burst_time: plan.burst_time,
          priority: plan.priority,
          active: plan.active,
        });
      } catch (error) {
        console.error('Error fetching bandwidth plan:', error);
        setErrors({ general: 'Failed to load bandwidth plan data.' });
      } finally {
        setFetchLoading(false);
      }
    };

    fetchPlan();
  }, [planId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'download_speed' || name === 'upload_speed' || name === 'burst_download_speed' ||
               name === 'burst_upload_speed' || name === 'burst_time' || name === 'priority') {
      // Convert to number and handle empty values for optional fields
      const numValue = value === '' ? null : Number(value);
      setFormData(prev => ({ ...prev, [name]: numValue }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});

    // Send speeds directly as Mbps (no conversion needed - database stores Mbps)
    const apiData = {
      ...formData,
      download_speed: formData.download_speed,
      upload_speed: formData.upload_speed,
      burst_download_speed: formData.burst_download_speed,
      burst_upload_speed: formData.burst_upload_speed,
    };

    try {
      await axios.put(`/api/bandwidth/plans/${planId}`, apiData);
      setSuccess(true);

      // Redirect to plans list after successful update
      setTimeout(() => {
        window.location.href = '/bandwidth/plans';
      }, 1500);
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.errors) {
        setErrors(error.response.data.errors);
      } else {
        setErrors({ general: 'An error occurred while updating the bandwidth plan.' });
      }
    } finally {
      setLoading(false);
    }
  };

  if (fetchLoading) {
    return (
      <AppLayout breadcrumbs={breadcrumbs}>
        <Head title="Edit Bandwidth Plan" />
        <div className="container mx-auto py-6 px-4">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Edit Bandwidth Plan" />
      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <h1>Edit Bandwidth Plan</h1>
            <p>Modify bandwidth limits and pricing</p>
          </div>
          <Button variant="outline" onClick={() => window.location.href = '/bandwidth/plans'}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Plans
          </Button>
        </div>

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            Bandwidth plan updated successfully! Redirecting...
          </div>
        )}

        {errors.general && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {errors.general}
          </div>
        )}

        <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4 md:col-span-2">
              <h2 className="text-xl font-semibold border-b pb-2">Basic Information</h2>

              <div>
                <label className="block text-gray-700 mb-2" htmlFor="name">
                  Plan Name <span className="text-red-500">*</span>
                </label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  className={`w-full px-4 py-2 border rounded-md ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
                {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-gray-700 mb-2" htmlFor="description">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                />
              </div>
            </div>

            {/* Speed Settings */}
            <div className="space-y-4 md:col-span-2">
              <h2 className="text-xl font-semibold border-b pb-2">Speed Settings</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-700 mb-2" htmlFor="download_speed">
                    Download Speed (Mbps) <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="download_speed"
                    name="download_speed"
                    type="number"
                    min="0.1"
                    step="0.1"
                    className={`w-full px-4 py-2 border rounded-md ${errors.download_speed ? 'border-red-500' : 'border-gray-300'}`}
                    value={formData.download_speed}
                    onChange={(e) => setFormData(prev => ({ ...prev, download_speed: Number(e.target.value) }))}
                    required
                  />
                  {errors.download_speed && <p className="text-red-500 text-sm mt-1">{errors.download_speed}</p>}
                </div>

                <div>
                  <label className="block text-gray-700 mb-2" htmlFor="upload_speed">
                    Upload Speed (Mbps) <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="upload_speed"
                    name="upload_speed"
                    type="number"
                    min="0.1"
                    step="0.1"
                    className={`w-full px-4 py-2 border rounded-md ${errors.upload_speed ? 'border-red-500' : 'border-gray-300'}`}
                    value={formData.upload_speed}
                    onChange={(e) => setFormData(prev => ({ ...prev, upload_speed: Number(e.target.value) }))}
                    required
                  />
                  {errors.upload_speed && <p className="text-red-500 text-sm mt-1">{errors.upload_speed}</p>}
                </div>
              </div>
            </div>

            {/* Burst Settings */}
            <div className="space-y-4 md:col-span-2">
              <h2 className="text-xl font-semibold border-b pb-2">Burst Settings (Optional)</h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-gray-700 mb-2" htmlFor="burst_download_speed">
                    Burst Download Speed (Mbps)
                  </label>
                  <input
                    id="burst_download_speed"
                    name="burst_download_speed"
                    type="number"
                    min="0.1"
                    step="0.1"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    value={formData.burst_download_speed || ''}
                    onChange={(e) => {
                      const value = e.target.value === '' ? null : Number(e.target.value);
                      setFormData(prev => ({ ...prev, burst_download_speed: value }));
                    }}
                  />
                </div>

                <div>
                  <label className="block text-gray-700 mb-2" htmlFor="burst_upload_speed">
                    Burst Upload Speed (Mbps)
                  </label>
                  <input
                    id="burst_upload_speed"
                    name="burst_upload_speed"
                    type="number"
                    min="0.1"
                    step="0.1"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    value={formData.burst_upload_speed || ''}
                    onChange={(e) => {
                      const value = e.target.value === '' ? null : Number(e.target.value);
                      setFormData(prev => ({ ...prev, burst_upload_speed: value }));
                    }}
                  />
                </div>

                <div>
                  <label className="block text-gray-700 mb-2" htmlFor="burst_time">
                    Burst Time (seconds)
                  </label>
                  <input
                    id="burst_time"
                    name="burst_time"
                    type="number"
                    min="1"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    value={formData.burst_time || ''}
                    onChange={(e) => {
                      const value = e.target.value === '' ? null : Number(e.target.value);
                      setFormData(prev => ({ ...prev, burst_time: value }));
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Additional Settings */}
            <div>
              <label className="block text-gray-700 mb-2" htmlFor="priority">
                Priority (1-8) <span className="text-red-500">*</span>
              </label>
              <select
                id="priority"
                name="priority"
                className={`w-full px-4 py-2 border rounded-md ${errors.priority ? 'border-red-500' : 'border-gray-300'}`}
                value={formData.priority}
                onChange={handleChange}
                required
              >
                <option value="1">1 - Highest</option>
                <option value="2">2</option>
                <option value="3">3</option>
                <option value="4">4</option>
                <option value="5">5 - Medium</option>
                <option value="6">6</option>
                <option value="7">7</option>
                <option value="8">8 - Lowest</option>
              </select>
              {errors.priority && <p className="text-red-500 text-sm mt-1">{errors.priority}</p>}
            </div>

            <div>
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  name="active"
                  checked={formData.active}
                  onChange={(e) => setFormData(prev => ({ ...prev, active: e.target.checked }))}
                  className="form-checkbox h-5 w-5 text-blue-600"
                />
                <span className="text-gray-700">Active</span>
              </label>
              <p className="text-gray-500 text-sm mt-1">
                Only active plans can be assigned to services
              </p>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <button
              type="button"
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 mr-2"
              onClick={() => window.location.href = '/bandwidth/plans'}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              disabled={loading}
            >
              {loading ? 'Updating...' : 'Update Plan'}
            </button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
