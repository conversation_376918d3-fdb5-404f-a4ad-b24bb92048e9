import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ModernTable, ModernTableBody, ModernTableCell, ModernTableHeader, ModernTableRow } from '@/components/ui/modern-table';
import {
  Gauge,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Download,
  Upload,
  DollarSign,
  ArrowUpDown,
  ArrowUp,
  ArrowDown
} from 'lucide-react';

// Define the BandwidthPlan type
interface BandwidthPlan {
  id: number;
  name: string;
  description: string | null;
  download_speed: number;
  upload_speed: number;
  burst_download_speed: number | null;
  burst_upload_speed: number | null;
  burst_time: number | null;
  priority: number;
  price: number | null;
  active: boolean;
  created_at: string;
  updated_at: string;
}

// Define the pagination type
interface Pagination {
  current_page: number;
  data: BandwidthPlan[];
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

interface BandwidthPlansProps {
  plans: Pagination;
  filters: {
    search?: string;
    active?: string;
    sort_field?: string;
    sort_direction?: string;
    per_page?: number;
  };
}

export default function BandwidthPlans({ plans: initialPlans, filters }: BandwidthPlansProps) {
  const [plans, setPlans] = useState<Pagination>(initialPlans);
  const [search, setSearch] = useState<string>(filters.search || '');
  const [active, setActive] = useState<string>(filters.active || 'all');
  const [sortField, setSortField] = useState<string>(filters.sort_field || 'name');
  const [sortDirection, setSortDirection] = useState<string>(filters.sort_direction || 'asc');
  const [perPage, setPerPage] = useState<number>(filters.per_page || 15);

  const fetchPlans = () => {
    router.get('/bandwidth/plans', {
      search: search || undefined,
      active: active === 'all' ? undefined : active,
      sort_field: sortField,
      sort_direction: sortDirection,
      per_page: perPage,
    }, {
      preserveState: true,
      preserveScroll: true,
      onSuccess: (page) => {
        setPlans(page.props.plans as Pagination);
      }
    });
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchPlans();
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [search, active, sortField, sortDirection, perPage]);

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const formatSpeed = (speed: number): string => {
    // Database stores speeds in Mbps, display as-is
    return speed.toFixed(2) + ' Mbps';
  };

  const formatPrice = (price: number | null): string => {
    if (!price) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getSortIcon = (field: string) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />;
    return sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  return (
    <AppLayout>
      <Head title="Bandwidth Plans" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                <Gauge className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1>Bandwidth Plans</h1>
                <p>Manage bandwidth plans and pricing</p>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/bandwidth/plans/create">
                <Plus className="h-4 w-4 mr-2" />
                Add Plan
              </Link>
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="card-modern">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="Search bandwidth plans..."
                    className="pl-10"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={active} onValueChange={setActive}>
                  <SelectTrigger className="w-[140px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="true">Active</SelectItem>
                    <SelectItem value="false">Inactive</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={perPage.toString()} onValueChange={(value) => setPerPage(parseInt(value))}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10 per page</SelectItem>
                    <SelectItem value="15">15 per page</SelectItem>
                    <SelectItem value="25">25 per page</SelectItem>
                    <SelectItem value="50">50 per page</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Plans Table */}
        <Card className="card-modern">
          <CardContent className="p-0">
            <ModernTable>
              <ModernTableHeader>
                <ModernTableRow>
                  <ModernTableCell
                    className="cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => handleSort('name')}
                  >
                    <div className="flex items-center gap-2">
                      Plan Name
                      {getSortIcon('name')}
                    </div>
                  </ModernTableCell>
                  <ModernTableCell>Description</ModernTableCell>
                  <ModernTableCell
                    className="cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => handleSort('download_speed')}
                  >
                    <div className="flex items-center gap-2">
                      <Download className="h-4 w-4" />
                      Download
                      {getSortIcon('download_speed')}
                    </div>
                  </ModernTableCell>
                  <ModernTableCell
                    className="cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => handleSort('upload_speed')}
                  >
                    <div className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      Upload
                      {getSortIcon('upload_speed')}
                    </div>
                  </ModernTableCell>
                  <ModernTableCell
                    className="cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => handleSort('price')}
                  >
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Price
                      {getSortIcon('price')}
                    </div>
                  </ModernTableCell>
                  <ModernTableCell
                    className="cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => handleSort('priority')}
                  >
                    <div className="flex items-center gap-2">
                      Priority
                      {getSortIcon('priority')}
                    </div>
                  </ModernTableCell>
                  <ModernTableCell
                    className="cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => handleSort('active')}
                  >
                    <div className="flex items-center gap-2">
                      Status
                      {getSortIcon('active')}
                    </div>
                  </ModernTableCell>
                  <ModernTableCell>Actions</ModernTableCell>
                </ModernTableRow>
              </ModernTableHeader>
              <ModernTableBody>
                {plans && plans.data.length > 0 ? (
                  plans.data.map((plan) => (
                    <ModernTableRow key={plan.id}>
                      <ModernTableCell>
                        <div className="font-medium">{plan.name}</div>
                      </ModernTableCell>
                      <ModernTableCell>
                        <div className="text-muted-foreground">
                          {plan.description || 'No description'}
                        </div>
                      </ModernTableCell>
                      <ModernTableCell>
                        <div className="flex items-center gap-2">
                          <Download className="h-4 w-4 text-green-600" />
                          <span className="font-medium">{formatSpeed(plan.download_speed)}</span>
                        </div>
                      </ModernTableCell>
                      <ModernTableCell>
                        <div className="flex items-center gap-2">
                          <Upload className="h-4 w-4 text-blue-600" />
                          <span className="font-medium">{formatSpeed(plan.upload_speed)}</span>
                        </div>
                      </ModernTableCell>
                      <ModernTableCell>
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-green-600" />
                          <span className="font-medium">{formatPrice(plan.price)}</span>
                        </div>
                      </ModernTableCell>
                      <ModernTableCell>
                        <Badge variant="outline" className="font-medium">
                          {plan.priority}
                        </Badge>
                      </ModernTableCell>
                      <ModernTableCell>
                        <Badge variant={plan.active ? "default" : "secondary"}>
                          {plan.active ? 'Active' : 'Inactive'}
                        </Badge>
                      </ModernTableCell>
                      <ModernTableCell>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/bandwidth/plans/${plan.id}`}>
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Link>
                          </Button>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/bandwidth/plans/${plan.id}/edit`}>
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Link>
                          </Button>
                        </div>
                      </ModernTableCell>
                    </ModernTableRow>
                  ))
                ) : (
                  <ModernTableRow>
                    <ModernTableCell colSpan={8} className="text-center py-8">
                      <div className="text-muted-foreground">
                        No bandwidth plans found
                      </div>
                    </ModernTableCell>
                  </ModernTableRow>
                )}
              </ModernTableBody>
            </ModernTable>
          </CardContent>
        </Card>

        {/* Pagination */}
        {plans && plans.last_page > 1 && (
          <Card className="card-modern">
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div className="text-sm text-muted-foreground">
                  Showing {plans.from} to {plans.to} of {plans.total} plans
                </div>
                <div className="flex gap-2">
                  {Array.from({ length: Math.min(plans.last_page, 10) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <Button
                        key={page}
                        variant={page === plans.current_page ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          router.get('/bandwidth/plans', {
                            search: search || undefined,
                            active: active === 'all' ? undefined : active,
                            sort_field: sortField,
                            sort_direction: sortDirection,
                            per_page: perPage,
                            page: page,
                          });
                        }}
                      >
                        {page}
                      </Button>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
