import { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import axios from 'axios';

interface ShowBandwidthPlanProps {
  planId: number;
}

interface BandwidthPlan {
  id: number;
  name: string;
  description: string | null;
  download_speed: number;
  upload_speed: number;
  burst_download_speed: number | null;
  burst_upload_speed: number | null;
  burst_time: number | null;
  priority: number;
  active: boolean;
  created_at: string;
  updated_at: string;
}

interface BandwidthAssignment {
  id: number;
  assignable_type: string;
  assignable_id: number;
  assignee_type: string;
  assignee_id: number;
  assignee: {
    id: number;
    name: string;
    [key: string]: any;
  };
  starts_at: string | null;
  ends_at: string | null;
  active: boolean;
  created_at: string;
  updated_at: string;
}

interface AssignmentsPagination {
  current_page: number;
  data: BandwidthAssignment[];
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Bandwidth',
    href: '/bandwidth',
  },
  {
    title: 'Plans',
    href: '/bandwidth/plans',
  },
  {
    title: 'Details',
    href: '#',
  },
];

export default function ShowBandwidthPlan({ planId }: ShowBandwidthPlanProps) {
  const [plan, setPlan] = useState<BandwidthPlan | null>(null);
  const [assignments, setAssignments] = useState<AssignmentsPagination | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPlan = async () => {
      try {
        const response = await axios.get(`/api/bandwidth/plans/${planId}`);
        setPlan(response.data);

        // Fetch assignments
        const assignmentsResponse = await axios.get(`/api/bandwidth/plans/${planId}/assignments`);
        setAssignments(assignmentsResponse.data);
      } catch (err) {
        console.error('Error fetching bandwidth plan:', err);
        setError('Failed to load bandwidth plan data.');
      } finally {
        setLoading(false);
      }
    };

    fetchPlan();
  }, [planId]);

  const formatSpeed = (speed: number): string => {
    // Database stores speeds in Mbps, display as-is
    return speed.toFixed(2) + ' Mbps';
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getAssigneeTypeLabel = (type: string): string => {
    const parts = type.split('\\');
    return parts[parts.length - 1];
  };

  if (loading) {
    return (
      <AppLayout breadcrumbs={breadcrumbs}>
        <Head title="Bandwidth Plan Details" />
        <div className="container mx-auto py-6 px-4">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error || !plan) {
    return (
      <AppLayout breadcrumbs={breadcrumbs}>
        <Head title="Bandwidth Plan Details" />
        <div className="container mx-auto py-6 px-4">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error || 'Failed to load bandwidth plan data.'}
          </div>
          <button
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
            onClick={() => window.location.href = '/bandwidth/plans'}
          >
            Back to Plans
          </button>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Bandwidth Plan: ${plan.name}`} />
      <div className="container mx-auto py-6 px-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Bandwidth Plan: {plan.name}</h1>
          <div className="flex space-x-2">
            <button
              className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
              onClick={() => window.location.href = `/bandwidth/plans/${plan.id}/edit`}
            >
              Edit
            </button>
            <button
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
              onClick={() => window.location.href = '/bandwidth/plans'}
            >
              Back to Plans
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Plan Details */}
          <div className="md:col-span-2">
            <div className="bg-white shadow-md rounded-lg p-6">
              <h2 className="text-xl font-semibold border-b pb-2 mb-4">Plan Details</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-gray-500 text-sm">Name</h3>
                  <p className="font-medium">{plan.name}</p>
                </div>

                <div>
                  <h3 className="text-gray-500 text-sm">Status</h3>
                  <p>
                    <span className={`px-2 py-1 rounded-full text-xs ${plan.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {plan.active ? 'Active' : 'Inactive'}
                    </span>
                  </p>
                </div>

                <div className="md:col-span-2">
                  <h3 className="text-gray-500 text-sm">Description</h3>
                  <p>{plan.description || 'No description provided.'}</p>
                </div>

                <div>
                  <h3 className="text-gray-500 text-sm">Download Speed</h3>
                  <p className="font-medium">{formatSpeed(plan.download_speed)}</p>
                </div>

                <div>
                  <h3 className="text-gray-500 text-sm">Upload Speed</h3>
                  <p className="font-medium">{formatSpeed(plan.upload_speed)}</p>
                </div>

                <div>
                  <h3 className="text-gray-500 text-sm">Burst Download Speed</h3>
                  <p>{plan.burst_download_speed ? formatSpeed(plan.burst_download_speed) : 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-gray-500 text-sm">Burst Upload Speed</h3>
                  <p>{plan.burst_upload_speed ? formatSpeed(plan.burst_upload_speed) : 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-gray-500 text-sm">Burst Time</h3>
                  <p>{plan.burst_time ? `${plan.burst_time} seconds` : 'N/A'}</p>
                </div>

                <div>
                  <h3 className="text-gray-500 text-sm">Priority</h3>
                  <p>{plan.priority} {plan.priority === 1 ? '(Highest)' : plan.priority === 8 ? '(Lowest)' : ''}</p>
                </div>

                <div>
                  <h3 className="text-gray-500 text-sm">Created</h3>
                  <p>{formatDate(plan.created_at)}</p>
                </div>

                <div>
                  <h3 className="text-gray-500 text-sm">Last Updated</h3>
                  <p>{formatDate(plan.updated_at)}</p>
                </div>
              </div>
            </div>

            {/* Assignments */}
            <div className="bg-white shadow-md rounded-lg p-6 mt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Assignments</h2>
                <button
                  className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
                  onClick={() => window.location.href = '/bandwidth/assignments/create'}
                >
                  Add Assignment
                </button>
              </div>

              {assignments && assignments.data.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="px-4 py-2 text-left">Assignee</th>
                        <th className="px-4 py-2 text-left">Type</th>
                        <th className="px-4 py-2 text-left">Start Date</th>
                        <th className="px-4 py-2 text-left">End Date</th>
                        <th className="px-4 py-2 text-left">Status</th>
                        <th className="px-4 py-2 text-left">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {assignments.data.map((assignment) => (
                        <tr key={assignment.id} className="border-t hover:bg-gray-50">
                          <td className="px-4 py-2">{assignment.assignee.name}</td>
                          <td className="px-4 py-2">{getAssigneeTypeLabel(assignment.assignee_type)}</td>
                          <td className="px-4 py-2">{formatDate(assignment.starts_at)}</td>
                          <td className="px-4 py-2">{formatDate(assignment.ends_at)}</td>
                          <td className="px-4 py-2">
                            <span className={`px-2 py-1 rounded-full text-xs ${assignment.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                              {assignment.active ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-4 py-2">
                            <button
                              className="px-2 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-xs"
                              onClick={() => window.location.href = `/bandwidth/assignments/${assignment.id}`}
                            >
                              View
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  No assignments found for this plan.
                </div>
              )}

              {assignments && assignments.last_page > 1 && (
                <div className="flex justify-center mt-4">
                  <div className="flex space-x-1">
                    {Array.from({ length: assignments.last_page }, (_, i) => i + 1).map((page) => (
                      <button
                        key={page}
                        className={`px-3 py-1 rounded-md ${
                          page === assignments.current_page
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-200 hover:bg-gray-300'
                        }`}
                        onClick={() => {
                          window.location.href = `/bandwidth/plans/${plan.id}?page=${page}`;
                        }}
                      >
                        {page}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div>
            <div className="bg-white shadow-md rounded-lg p-6">
              <h2 className="text-xl font-semibold border-b pb-2 mb-4">Actions</h2>

              <div className="space-y-2">
                <button
                  className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center justify-center"
                  onClick={() => window.location.href = `/bandwidth/plans/${plan.id}/edit`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                  </svg>
                  Edit Plan
                </button>

                <button
                  className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center justify-center"
                  onClick={() => window.location.href = '/bandwidth/assignments/create'}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  Assign to Service
                </button>

                <button
                  className="w-full px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 flex items-center justify-center"
                  onClick={() => {
                    // Clone plan functionality
                    window.location.href = `/bandwidth/plans/${plan.id}/clone`;
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z" />
                    <path d="M5 3a2 2 0 00-2 2v6a2 2 0 002 2V5h8a2 2 0 00-2-2H5z" />
                  </svg>
                  Clone Plan
                </button>

                <button
                  className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 flex items-center justify-center"
                  onClick={() => {
                    if (confirm('Are you sure you want to delete this bandwidth plan? This action cannot be undone.')) {
                      axios.delete(`/api/bandwidth/plans/${plan.id}`)
                        .then(() => {
                          window.location.href = '/bandwidth/plans';
                        })
                        .catch((err) => {
                          alert('Failed to delete plan: ' + (err.response?.data?.message || 'Unknown error'));
                        });
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  Delete Plan
                </button>
              </div>
            </div>

            <div className="bg-white shadow-md rounded-lg p-6 mt-6">
              <h2 className="text-xl font-semibold border-b pb-2 mb-4">Usage Statistics</h2>

              <div className="space-y-4">
                <div>
                  <h3 className="text-gray-500 text-sm">Total Assignments</h3>
                  <p className="text-2xl font-bold">{assignments?.total || 0}</p>
                </div>

                <div>
                  <h3 className="text-gray-500 text-sm">Active Assignments</h3>
                  <p className="text-2xl font-bold">
                    {assignments?.data.filter(a => a.active).length || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
