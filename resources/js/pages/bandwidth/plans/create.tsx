import { useState, useEffect } from 'react';
import { Head, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Gauge,
  ArrowLeft,
  Download,
  Upload,
  DollarSign,
  Zap,
  Settings,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import axios from 'axios';
import { getCurrencySymbol, getDefaultCurrency, initializeCurrency } from '@/lib/currency';

export default function CreateBandwidthPlan() {
  const { props } = usePage();
  const currencyConfig = (props as any).currencyConfig;

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    download_speed: 1, // Default to 1 Mbps
    upload_speed: 1,   // Default to 1 Mbps
    burst_download_speed: null as number | null,
    burst_upload_speed: null as number | null,
    burst_time: null as number | null,
    priority: 5,       // Default priority (1-8)
    price: null as number | null, // Monthly price
    active: true,      // Default to active
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [currentCurrency, setCurrentCurrency] = useState('KES');
  const [currencySymbol, setCurrencySymbol] = useState('KSh');

  // Initialize currency configuration
  useEffect(() => {
    if (currencyConfig) {
      initializeCurrency(currencyConfig);
      setCurrentCurrency(currencyConfig.default_currency);
      setCurrencySymbol(getCurrencySymbol(currencyConfig.default_currency));
    } else {
      // Fallback to default
      setCurrentCurrency(getDefaultCurrency());
      setCurrencySymbol(getCurrencySymbol());
    }
  }, [currencyConfig]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'download_speed' || name === 'upload_speed' || name === 'burst_download_speed' ||
               name === 'burst_upload_speed' || name === 'burst_time' || name === 'priority' || name === 'price') {
      // Convert to number and handle empty values for optional fields
      const numValue = value === '' ? null : Number(value);
      setFormData(prev => ({ ...prev, [name]: numValue }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});

    // Send speeds directly as Mbps (no conversion needed - database stores Mbps)
    const apiData = {
      ...formData,
      download_speed: formData.download_speed,
      upload_speed: formData.upload_speed,
      burst_download_speed: formData.burst_download_speed,
      burst_upload_speed: formData.burst_upload_speed,
      currency: currentCurrency, // Include currency in the request
    };

    try {
      await axios.post('/bandwidth/plans', apiData);
      setSuccess(true);

      // Redirect to plans list after successful creation
      setTimeout(() => {
        window.location.href = '/bandwidth/plans';
      }, 1500);
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.errors) {
        setErrors(error.response.data.errors);
      } else {
        setErrors({ general: 'An error occurred while creating the bandwidth plan.' });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <AppLayout>
      <Head title="Create Bandwidth Plan" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                <Gauge className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1>Create Bandwidth Plan</h1>
                <p>Define new bandwidth limits and pricing</p>
              </div>
            </div>
          </div>
          <Button variant="outline" asChild>
            <Link href="/bandwidth/plans">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Plans
            </Link>
          </Button>
        </div>

        {/* Success Alert */}
        {success && (
          <Alert className="border-green-200 bg-green-50 text-green-800">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Bandwidth plan created successfully! Redirecting...
            </AlertDescription>
          </Alert>
        )}

        {/* Error Alerts */}
        {errors.general && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {errors.general}
            </AlertDescription>
          </Alert>
        )}

        {(errors.name || errors.download_speed || errors.upload_speed || errors.price) && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              There are errors in the form. Please check the fields below.
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Enter the plan name and description
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">
                    Plan Name <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    value={formData.name}
                    onChange={handleChange}
                    className={errors.name ? 'border-destructive' : ''}
                    placeholder="e.g., Premium 100Mbps"
                    required
                  />
                  {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="price">
                    Monthly Price ({currentCurrency}) <span className="text-destructive">*</span>
                  </Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground font-medium">
                      {currencySymbol}
                    </span>
                    <Input
                      id="price"
                      name="price"
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.price || ''}
                      onChange={handleChange}
                      className={`pl-12 ${errors.price ? 'border-destructive' : ''}`}
                      placeholder="1000"
                      required
                    />
                  </div>
                  {errors.price && <p className="text-sm text-destructive">{errors.price}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  placeholder="Optional description of the bandwidth plan"
                />
              </div>
            </CardContent>
          </Card>

          {/* Speed Settings */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Speed Settings
              </CardTitle>
              <CardDescription>
                Configure download and upload speeds
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="download_speed">
                    Download Speed (Mbps) <span className="text-destructive">*</span>
                  </Label>
                  <div className="relative">
                    <Download className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="download_speed"
                      name="download_speed"
                      type="number"
                      min="0.1"
                      step="0.1"
                      value={formData.download_speed}
                      onChange={(e) => setFormData(prev => ({ ...prev, download_speed: Number(e.target.value) }))}
                      className={`pl-10 ${errors.download_speed ? 'border-destructive' : ''}`}
                      placeholder="100"
                      required
                    />
                  </div>
                  {errors.download_speed && <p className="text-sm text-destructive">{errors.download_speed}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="upload_speed">
                    Upload Speed (Mbps) <span className="text-destructive">*</span>
                  </Label>
                  <div className="relative">
                    <Upload className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="upload_speed"
                      name="upload_speed"
                      type="number"
                      min="0.1"
                      step="0.1"
                      value={formData.upload_speed}
                      onChange={(e) => setFormData(prev => ({ ...prev, upload_speed: Number(e.target.value) }))}
                      className={`pl-10 ${errors.upload_speed ? 'border-destructive' : ''}`}
                      placeholder="50"
                      required
                    />
                  </div>
                  {errors.upload_speed && <p className="text-sm text-destructive">{errors.upload_speed}</p>}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Burst Settings */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Burst Settings
              </CardTitle>
              <CardDescription>
                Configure burst speeds and timing (optional)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="burst_download_speed">
                    Burst Download Speed (Mbps)
                  </Label>
                  <Input
                    id="burst_download_speed"
                    name="burst_download_speed"
                    type="number"
                    min="0.1"
                    step="0.1"
                    value={formData.burst_download_speed || ''}
                    onChange={(e) => {
                      const value = e.target.value === '' ? null : Number(e.target.value);
                      setFormData(prev => ({ ...prev, burst_download_speed: value }));
                    }}
                    placeholder="200"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="burst_upload_speed">
                    Burst Upload Speed (Mbps)
                  </Label>
                  <Input
                    id="burst_upload_speed"
                    name="burst_upload_speed"
                    type="number"
                    min="0.1"
                    step="0.1"
                    value={formData.burst_upload_speed || ''}
                    onChange={(e) => {
                      const value = e.target.value === '' ? null : Number(e.target.value);
                      setFormData(prev => ({ ...prev, burst_upload_speed: value }));
                    }}
                    placeholder="100"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="burst_time">
                    Burst Time (seconds)
                  </Label>
                  <Input
                    id="burst_time"
                    name="burst_time"
                    type="number"
                    min="1"
                    value={formData.burst_time || ''}
                    onChange={(e) => {
                      const value = e.target.value === '' ? null : Number(e.target.value);
                      setFormData(prev => ({ ...prev, burst_time: value }));
                    }}
                    placeholder="30"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Settings */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Additional Settings
              </CardTitle>
              <CardDescription>
                Configure priority and activation status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="priority">
                  Priority (1-8) <span className="text-destructive">*</span>
                </Label>
                <Select value={formData.priority.toString()} onValueChange={(value) => setFormData(prev => ({ ...prev, priority: Number(value) }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 - Highest</SelectItem>
                    <SelectItem value="2">2</SelectItem>
                    <SelectItem value="3">3</SelectItem>
                    <SelectItem value="4">4</SelectItem>
                    <SelectItem value="5">5 - Medium</SelectItem>
                    <SelectItem value="6">6</SelectItem>
                    <SelectItem value="7">7</SelectItem>
                    <SelectItem value="8">8 - Lowest</SelectItem>
                  </SelectContent>
                </Select>
                {errors.priority && <p className="text-sm text-destructive">{errors.priority}</p>}
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="active"
                  checked={formData.active}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, active: !!checked }))}
                />
                <Label htmlFor="active">Active Plan</Label>
              </div>
              <p className="text-sm text-muted-foreground">
                Only active plans can be assigned to services
              </p>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-4">
            <Button variant="outline" asChild>
              <Link href="/bandwidth/plans">Cancel</Link>
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Plan'}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
