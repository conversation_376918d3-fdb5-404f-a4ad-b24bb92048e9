import { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Settings,
  ArrowLeft,
  Gauge,
  CheckCircle,
  AlertCircle,
  Download,
  Upload
} from 'lucide-react';
import axios from 'axios';

export default function CreateBandwidthPolicy() {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    policy_type: '',
    max_download_speed: null as number | null,
    max_upload_speed: null as number | null,
    burst_download_speed: null as number | null,
    burst_upload_speed: null as number | null,
    burst_time: null as number | null,
    priority: 5,
    queue_type: '',
    parent_policy_id: null as number | null,
    active: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'max_download_speed' || name === 'max_upload_speed' || 
               name === 'burst_download_speed' || name === 'burst_upload_speed' || 
               name === 'burst_time' || name === 'priority' || name === 'parent_policy_id') {
      const numValue = value === '' ? null : Number(value);
      setFormData(prev => ({ ...prev, [name]: numValue }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});

    try {
      await axios.post('/bandwidth/policies', formData);
      setSuccess(true);

      setTimeout(() => {
        window.location.href = '/bandwidth/policies';
      }, 1500);
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.errors) {
        setErrors(error.response.data.errors);
      } else {
        setErrors({ general: 'An error occurred while creating the bandwidth policy.' });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <AppLayout>
      <Head title="Create Bandwidth Policy" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/30">
                <Settings className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h1>Create Bandwidth Policy</h1>
                <p>Define new bandwidth policy and limits</p>
              </div>
            </div>
          </div>
          <Button variant="outline" asChild>
            <Link href="/bandwidth/policies">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Policies
            </Link>
          </Button>
        </div>

        {/* Success Alert */}
        {success && (
          <Alert className="border-green-200 bg-green-50 text-green-800">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Bandwidth policy created successfully! Redirecting...
            </AlertDescription>
          </Alert>
        )}

        {/* Error Alerts */}
        {errors.general && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {errors.general}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Enter the policy name and description
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">
                    Policy Name <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    value={formData.name}
                    onChange={handleChange}
                    className={errors.name ? 'border-destructive' : ''}
                    placeholder="e.g., Standard Business Policy"
                    required
                  />
                  {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="policy_type">
                    Policy Type <span className="text-destructive">*</span>
                  </Label>
                  <Select value={formData.policy_type} onValueChange={(value) => setFormData(prev => ({ ...prev, policy_type: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select policy type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="simple">Simple Queue</SelectItem>
                      <SelectItem value="tree">Queue Tree</SelectItem>
                      <SelectItem value="pcq">PCQ</SelectItem>
                      <SelectItem value="sfq">SFQ</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.policy_type && <p className="text-sm text-destructive">{errors.policy_type}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  placeholder="Optional description of the bandwidth policy"
                />
              </div>
            </CardContent>
          </Card>

          {/* Speed Limits */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gauge className="h-5 w-5" />
                Speed Limits
              </CardTitle>
              <CardDescription>
                Configure maximum speeds and burst settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="max_download_speed">
                    Max Download Speed (Mbps)
                  </Label>
                  <div className="relative">
                    <Download className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="max_download_speed"
                      name="max_download_speed"
                      type="number"
                      min="0.1"
                      step="0.1"
                      value={formData.max_download_speed || ''}
                      onChange={handleChange}
                      className="pl-10"
                      placeholder="100"
                    />
                  </div>
                  {errors.max_download_speed && <p className="text-sm text-destructive">{errors.max_download_speed}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max_upload_speed">
                    Max Upload Speed (Mbps)
                  </Label>
                  <div className="relative">
                    <Upload className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="max_upload_speed"
                      name="max_upload_speed"
                      type="number"
                      min="0.1"
                      step="0.1"
                      value={formData.max_upload_speed || ''}
                      onChange={handleChange}
                      className="pl-10"
                      placeholder="50"
                    />
                  </div>
                  {errors.max_upload_speed && <p className="text-sm text-destructive">{errors.max_upload_speed}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="burst_download_speed">
                    Burst Download Speed (Mbps)
                  </Label>
                  <Input
                    id="burst_download_speed"
                    name="burst_download_speed"
                    type="number"
                    min="0.1"
                    step="0.1"
                    value={formData.burst_download_speed || ''}
                    onChange={handleChange}
                    placeholder="200"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="burst_upload_speed">
                    Burst Upload Speed (Mbps)
                  </Label>
                  <Input
                    id="burst_upload_speed"
                    name="burst_upload_speed"
                    type="number"
                    min="0.1"
                    step="0.1"
                    value={formData.burst_upload_speed || ''}
                    onChange={handleChange}
                    placeholder="100"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="burst_time">
                    Burst Time (seconds)
                  </Label>
                  <Input
                    id="burst_time"
                    name="burst_time"
                    type="number"
                    min="1"
                    value={formData.burst_time || ''}
                    onChange={handleChange}
                    placeholder="30"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">
                    Priority
                  </Label>
                  <Select value={formData.priority.toString()} onValueChange={(value) => setFormData(prev => ({ ...prev, priority: Number(value) }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 - Highest</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5 - Medium</SelectItem>
                      <SelectItem value="6">6</SelectItem>
                      <SelectItem value="7">7</SelectItem>
                      <SelectItem value="8">8 - Lowest</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="active"
                  checked={formData.active}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, active: !!checked }))}
                />
                <Label htmlFor="active">Active</Label>
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-4">
            <Button variant="outline" asChild>
              <Link href="/bandwidth/policies">Cancel</Link>
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Policy'}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
