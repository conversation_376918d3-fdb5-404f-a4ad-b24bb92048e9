import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';
import axios from 'axios';

// Define the BandwidthPolicy type
interface BandwidthPolicy {
  id: number;
  name: string;
  description: string | null;
  type: 'limit' | 'shape' | 'prioritize' | 'quota';
  parameters: Record<string, unknown> | null;
  active: boolean;
  created_at: string;
  updated_at: string;
}

// Define the pagination type
interface Pagination {
  current_page: number;
  data: BandwidthPolicy[];
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
  {
    title: 'Bandwidth',
    href: '/bandwidth',
  },
  {
    title: 'Policies',
    href: '/bandwidth/policies',
  },
];

export default function BandwidthPolicies() {
  const [policies, setPolicies] = useState<Pagination | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [search, setSearch] = useState<string>('');
  const [type, setType] = useState<string>('');
  const [active, setActive] = useState<string>('');
  const [sortField, setSortField] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<string>('asc');
  const [perPage, setPerPage] = useState<number>(15);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const fetchPolicies = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/bandwidth/policies', {
        params: {
          search,
          type: type || undefined,
          active: active === '' ? undefined : active === 'true',
          sort_field: sortField,
          sort_direction: sortDirection,
          per_page: perPage,
          page: currentPage,
        },
      });
      setPolicies(response.data);
    } catch (error) {
      console.error('Error fetching bandwidth policies:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPolicies();
  }, [search, type, active, sortField, sortDirection, perPage, currentPage]);

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const formatPolicyType = (type: string): string => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Bandwidth Policies" />
      <div className="flex flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Bandwidth Policies</h1>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            onClick={() => window.location.href = '/bandwidth/policies/create'}
          >
            Add Policy
          </button>
        </div>

        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search policies..."
              className="w-full px-4 py-2 border rounded-md"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={type}
              onChange={(e) => setType(e.target.value)}
            >
              <option value="">All Types</option>
              <option value="limit">Limit</option>
              <option value="shape">Shape</option>
              <option value="prioritize">Prioritize</option>
              <option value="quota">Quota</option>
            </select>
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={active}
              onChange={(e) => setActive(e.target.value)}
            >
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>
          <div>
            <select
              className="w-full px-4 py-2 border rounded-md"
              value={perPage.toString()}
              onChange={(e) => setPerPage(parseInt(e.target.value))}
            >
              <option value="10">10 per page</option>
              <option value="15">15 per page</option>
              <option value="25">25 per page</option>
              <option value="50">50 per page</option>
            </select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border rounded-md">
                <thead className="bg-gray-100">
                  <tr>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('name')}
                    >
                      Name
                      {sortField === 'name' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-4 py-2 text-left">Description</th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('type')}
                    >
                      Type
                      {sortField === 'type' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-4 py-2 text-left">Parameters</th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('active')}
                    >
                      Status
                      {sortField === 'active' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-4 py-2 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {policies && policies.data.length > 0 ? (
                    policies.data.map((policy) => (
                      <tr key={policy.id} className="border-t hover:bg-gray-50">
                        <td className="px-4 py-2">{policy.name}</td>
                        <td className="px-4 py-2">{policy.description || '-'}</td>
                        <td className="px-4 py-2">{formatPolicyType(policy.type)}</td>
                        <td className="px-4 py-2">
                          {policy.parameters ? (
                            <button
                              className="px-2 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                              onClick={() => window.location.href = `/bandwidth/policies/${policy.id}/parameters`}
                            >
                              View Parameters
                            </button>
                          ) : (
                            '-'
                          )}
                        </td>
                        <td className="px-4 py-2">
                          <span className={`px-2 py-1 rounded-full text-xs ${policy.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {policy.active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-4 py-2">
                          <div className="flex space-x-2">
                            <button
                              className="px-2 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                              onClick={() => window.location.href = `/bandwidth/policies/${policy.id}`}
                            >
                              View
                            </button>
                            <button
                              className="px-2 py-1 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                              onClick={() => window.location.href = `/bandwidth/policies/${policy.id}/edit`}
                            >
                              Edit
                            </button>
                            <button
                              className="px-2 py-1 bg-purple-500 text-white rounded-md hover:bg-purple-600"
                              onClick={() => window.location.href = `/bandwidth/policies/${policy.id}/rules`}
                            >
                              Rules
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                        No bandwidth policies found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {policies && (
              <div className="flex justify-between items-center mt-4">
                <div>
                  Showing {policies.from} to {policies.to} of {policies.total} policies
                </div>
                <div className="flex space-x-2">
                  {Array.from({ length: policies.last_page }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      className={`px-3 py-1 rounded-md ${
                        page === policies.current_page
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 hover:bg-gray-300'
                      }`}
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </AppLayout>
  );
}
