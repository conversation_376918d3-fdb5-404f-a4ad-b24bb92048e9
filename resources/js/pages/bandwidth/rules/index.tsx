import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ModernTable, ModernTableBody, ModernTableCell, ModernTableHeader, ModernTableRow } from '@/components/ui/modern-table';
import {
  Zap,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Network,
  Globe
} from 'lucide-react';
import axios from 'axios';

// Define the BandwidthRule type
interface BandwidthRule {
  id: number;
  policy_id: number;
  name: string;
  description: string | null;
  source_type: string | null;
  source_value: string | null;
  destination_type: string | null;
  destination_value: string | null;
  protocol: string | null;
  port_range: string | null;
  time_range: string | null;
  priority: number;
  active: boolean;
  created_at: string;
  updated_at: string;
}

// Define the pagination type
interface Pagination {
  current_page: number;
  data: BandwidthRule[];
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

export default function BandwidthRules() {
  const [rules, setRules] = useState<Pagination | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [search, setSearch] = useState<string>('');
  const [sourceType, setSourceType] = useState<string>('all');
  const [destinationType, setDestinationType] = useState<string>('all');
  const [protocol, setProtocol] = useState<string>('all');
  const [active, setActive] = useState<string>('all');
  const [sortField, setSortField] = useState<string>('priority');
  const [sortDirection, setSortDirection] = useState<string>('asc');
  const [perPage, setPerPage] = useState<number>(15);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const fetchRules = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/bandwidth/rules', {
        params: {
          search,
          source_type: sourceType === 'all' ? undefined : sourceType,
          destination_type: destinationType === 'all' ? undefined : destinationType,
          protocol: protocol === 'all' ? undefined : protocol,
          active: active === 'all' ? undefined : active === 'true',
          sort_field: sortField,
          sort_direction: sortDirection,
          per_page: perPage,
          page: currentPage,
        },
      });
      setRules(response.data);
    } catch (error) {
      console.error('Error fetching bandwidth rules:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRules();
  }, [search, sourceType, destinationType, protocol, active, sortField, sortDirection, perPage, currentPage]);

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field: string) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />;
    return sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  return (
    <AppLayout>
      <Head title="Bandwidth Rules" />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/30">
                <Zap className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h1>Bandwidth Rules</h1>
                <p>Manage traffic shaping and QoS rules</p>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <Button asChild>
              <Link href="/bandwidth/rules/create">
                <Plus className="h-4 w-4 mr-2" />
                Add Rule
              </Link>
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="card-modern">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="Search bandwidth rules..."
                    className="pl-10"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                <Select value={sourceType} onValueChange={setSourceType}>
                  <SelectTrigger className="w-[140px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Source" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sources</SelectItem>
                    <SelectItem value="IP">IP Address</SelectItem>
                    <SelectItem value="network">Network</SelectItem>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="group">Group</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={destinationType} onValueChange={setDestinationType}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Destination" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Destinations</SelectItem>
                    <SelectItem value="IP">IP Address</SelectItem>
                    <SelectItem value="network">Network</SelectItem>
                    <SelectItem value="domain">Domain</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={protocol} onValueChange={setProtocol}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Protocol" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Protocols</SelectItem>
                    <SelectItem value="TCP">TCP</SelectItem>
                    <SelectItem value="UDP">UDP</SelectItem>
                    <SelectItem value="ICMP">ICMP</SelectItem>
                    <SelectItem value="ANY">ANY</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={active} onValueChange={setActive}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="true">Active</SelectItem>
                    <SelectItem value="false">Inactive</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={perPage.toString()} onValueChange={(value) => setPerPage(parseInt(value))}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10 per page</SelectItem>
                    <SelectItem value="15">15 per page</SelectItem>
                    <SelectItem value="25">25 per page</SelectItem>
                    <SelectItem value="50">50 per page</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border rounded-md">
                <thead className="bg-gray-100">
                  <tr>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('name')}
                    >
                      Name
                      {sortField === 'name' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('policy_id')}
                    >
                      Policy
                      {sortField === 'policy_id' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-4 py-2 text-left">Source</th>
                    <th className="px-4 py-2 text-left">Destination</th>
                    <th className="px-4 py-2 text-left">Protocol/Port</th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('priority')}
                    >
                      Priority
                      {sortField === 'priority' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th
                      className="px-4 py-2 text-left cursor-pointer"
                      onClick={() => handleSort('active')}
                    >
                      Status
                      {sortField === 'active' && (
                        <span className="ml-1">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-4 py-2 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {rules && rules.data.length > 0 ? (
                    rules.data.map((rule) => (
                      <tr key={rule.id} className="border-t hover:bg-gray-50">
                        <td className="px-4 py-2">{rule.name}</td>
                        <td className="px-4 py-2">
                          <a
                            href={`/bandwidth/policies/${rule.policy_id}`}
                            className="text-blue-500 hover:underline"
                          >
                            Policy #{rule.policy_id}
                          </a>
                        </td>
                        <td className="px-4 py-2">
                          {rule.source_type && rule.source_value ? (
                            <span>
                              {rule.source_type}: {rule.source_value}
                            </span>
                          ) : (
                            <span className="text-gray-400">Any</span>
                          )}
                        </td>
                        <td className="px-4 py-2">
                          {rule.destination_type && rule.destination_value ? (
                            <span>
                              {rule.destination_type}: {rule.destination_value}
                            </span>
                          ) : (
                            <span className="text-gray-400">Any</span>
                          )}
                        </td>
                        <td className="px-4 py-2">
                          {rule.protocol ? (
                            <span>
                              {rule.protocol}
                              {rule.port_range ? `: ${rule.port_range}` : ''}
                            </span>
                          ) : (
                            <span className="text-gray-400">Any</span>
                          )}
                        </td>
                        <td className="px-4 py-2">{rule.priority}</td>
                        <td className="px-4 py-2">
                          <span className={`px-2 py-1 rounded-full text-xs ${rule.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {rule.active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-4 py-2">
                          <div className="flex space-x-2">
                            <button
                              className="px-2 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                              onClick={() => window.location.href = `/bandwidth/rules/${rule.id}`}
                            >
                              View
                            </button>
                            <button
                              className="px-2 py-1 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                              onClick={() => window.location.href = `/bandwidth/rules/${rule.id}/edit`}
                            >
                              Edit
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={8} className="px-4 py-8 text-center text-gray-500">
                        No bandwidth rules found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {rules && (
              <div className="flex justify-between items-center mt-4">
                <div>
                  Showing {rules.from} to {rules.to} of {rules.total} rules
                </div>
                <div className="flex space-x-2">
                  {Array.from({ length: rules.last_page }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      className={`px-3 py-1 rounded-md ${
                        page === rules.current_page
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 hover:bg-gray-300'
                      }`}
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </AppLayout>
  );
}
