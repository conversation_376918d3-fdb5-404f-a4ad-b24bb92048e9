import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import AppLogoIcon from '@/components/app-logo-icon';
import {
    Server,
    Users,
    CreditCard,
    Wifi,
    Activity,
    DollarSign,
    CheckCircle,
    ArrowRight,
    Star,
    Shield,
    Zap,
    Globe,
    BarChart3,
    Clock,
    Monitor,
    Menu,
    X
} from 'lucide-react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;
    const [customerCount, setCustomerCount] = useState(1000);
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [activeSection, setActiveSection] = useState('hero');

    // Pricing calculation - Progressive tiered pricing
    const calculatePrice = (customers: number) => {
        let total = 0;

        if (customers <= 1000) {
            // Starter tier: $0.30 per customer
            total = customers * 0.30;
        } else if (customers <= 3000) {
            // Growth tier: First 1000 at $0.30, remainder at $0.25
            total = (1000 * 0.30) + ((customers - 1000) * 0.25);
        } else {
            // Enterprise tier: First 1000 at $0.30, next 2000 at $0.25, remainder at $0.20
            total = (1000 * 0.30) + (2000 * 0.25) + ((customers - 3000) * 0.20);
        }

        return total;
    };

    const getPricePerCustomer = (customers: number) => {
        if (customers <= 1000) return '0.30';
        if (customers <= 3000) return '0.25';
        return '0.20';
    };

    // Smooth scroll to section
    const scrollToSection = (sectionId: string) => {
        const element = document.getElementById(sectionId);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
            setActiveSection(sectionId);
        }
        setIsMenuOpen(false);
    };

    // Handle scroll for active section detection
    useEffect(() => {
        const handleScroll = () => {
            const sections = ['hero', 'features', 'pricing', 'benefits', 'technology', 'testimonials', 'cta'];
            const scrollPosition = window.scrollY + 100;

            for (const section of sections) {
                const element = document.getElementById(section);
                if (element) {
                    const { offsetTop, offsetHeight } = element;
                    if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
                        setActiveSection(section);
                        break;
                    }
                }
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    return (
        <>
            <Head title="ISP Management System - Automate Your Internet Service Provider Operations">
                <meta name="description" content="Comprehensive ISP management platform with MikroTik integration, automated billing, customer management, and network monitoring. Scale your ISP business efficiently." />
                <meta name="keywords" content="ISP management, MikroTik integration, internet service provider, billing automation, network monitoring" />
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>

            {/* Navigation Header */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-border/50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-16">
                        {/* Logo */}
                        <div className="flex items-center gap-2">
                            <AppLogoIcon className="size-8 fill-current text-primary" />
                            <span className="text-xl font-semibold text-foreground">ISP Manager</span>
                        </div>

                        {/* Desktop Navigation */}
                        <nav className="hidden md:flex items-center space-x-8">
                            <button
                                onClick={() => scrollToSection('features')}
                                className={`text-sm font-medium transition-colors hover:text-primary ${activeSection === 'features' ? 'text-primary' : 'text-muted-foreground'}`}
                            >
                                Features
                            </button>
                            <button
                                onClick={() => scrollToSection('pricing')}
                                className={`text-sm font-medium transition-colors hover:text-primary ${activeSection === 'pricing' ? 'text-primary' : 'text-muted-foreground'}`}
                            >
                                Pricing
                            </button>
                            <button
                                onClick={() => scrollToSection('benefits')}
                                className={`text-sm font-medium transition-colors hover:text-primary ${activeSection === 'benefits' ? 'text-primary' : 'text-muted-foreground'}`}
                            >
                                Benefits
                            </button>
                            <button
                                onClick={() => scrollToSection('technology')}
                                className={`text-sm font-medium transition-colors hover:text-primary ${activeSection === 'technology' ? 'text-primary' : 'text-muted-foreground'}`}
                            >
                                Technology
                            </button>
                        </nav>

                        {/* Auth Buttons */}
                        <div className="hidden md:flex items-center gap-4">
                            {auth.user ? (
                                <Button asChild>
                                    <Link href={route('dashboard')}>
                                        Dashboard
                                    </Link>
                                </Button>
                            ) : (
                                <>
                                    <Button variant="ghost" asChild>
                                        <Link href={route('login')}>
                                            Log in
                                        </Link>
                                    </Button>
                                    <Button asChild>
                                        <Link href={route('register')}>
                                            Get Started
                                        </Link>
                                    </Button>
                                </>
                            )}
                        </div>

                        {/* Mobile Menu Button */}
                        <button
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                            className="md:hidden p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent"
                        >
                            {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
                        </button>
                    </div>

                    {/* Mobile Navigation */}
                    {isMenuOpen && (
                        <div className="md:hidden border-t border-border/50 bg-white/95 backdrop-blur-sm">
                            <div className="px-2 pt-2 pb-3 space-y-1">
                                <button
                                    onClick={() => scrollToSection('features')}
                                    className="block px-3 py-2 text-base font-medium text-muted-foreground hover:text-primary hover:bg-accent rounded-md w-full text-left"
                                >
                                    Features
                                </button>
                                <button
                                    onClick={() => scrollToSection('pricing')}
                                    className="block px-3 py-2 text-base font-medium text-muted-foreground hover:text-primary hover:bg-accent rounded-md w-full text-left"
                                >
                                    Pricing
                                </button>
                                <button
                                    onClick={() => scrollToSection('benefits')}
                                    className="block px-3 py-2 text-base font-medium text-muted-foreground hover:text-primary hover:bg-accent rounded-md w-full text-left"
                                >
                                    Benefits
                                </button>
                                <button
                                    onClick={() => scrollToSection('technology')}
                                    className="block px-3 py-2 text-base font-medium text-muted-foreground hover:text-primary hover:bg-accent rounded-md w-full text-left"
                                >
                                    Technology
                                </button>
                                <div className="border-t border-border/50 pt-4 mt-4">
                                    {auth.user ? (
                                        <Button asChild className="w-full">
                                            <Link href={route('dashboard')}>
                                                Dashboard
                                            </Link>
                                        </Button>
                                    ) : (
                                        <div className="space-y-2">
                                            <Button variant="ghost" asChild className="w-full">
                                                <Link href={route('login')}>
                                                    Log in
                                                </Link>
                                            </Button>
                                            <Button asChild className="w-full">
                                                <Link href={route('register')}>
                                                    Get Started
                                                </Link>
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </header>

            <main>
                {/* Hero Section */}
                <section id="hero" className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/30">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div className="space-y-8">
                                <div className="space-y-4">
                                    <Badge className="bg-primary/10 text-primary border-primary/20 hover:bg-primary/20">
                                        <Zap className="h-3 w-3 mr-1" />
                                        Automate Your ISP Operations
                                    </Badge>
                                    <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                                        Complete ISP Management
                                        <span className="text-primary"> Platform</span>
                                    </h1>
                                    <p className="text-xl text-muted-foreground leading-relaxed">
                                        Streamline your Internet Service Provider operations with automated billing,
                                        MikroTik integration, customer management, and real-time network monitoring.
                                        Scale from hundreds to thousands of customers effortlessly.
                                    </p>
                                </div>

                                <div className="flex flex-col sm:flex-row gap-4">
                                    <Button size="lg" className="btn-gradient" asChild>
                                        <Link href={route('register')}>
                                            Start Free Trial
                                            <ArrowRight className="h-4 w-4 ml-2" />
                                        </Link>
                                    </Button>
                                    <Button size="lg" variant="outline" onClick={() => scrollToSection('features')}>
                                        <Monitor className="h-4 w-4 mr-2" />
                                        View Demo
                                    </Button>
                                </div>

                                <div className="flex items-center gap-8 pt-4">
                                    <div className="flex items-center gap-2">
                                        <div className="flex -space-x-2">
                                            <div className="w-8 h-8 rounded-full bg-primary/20 border-2 border-background flex items-center justify-center">
                                                <Users className="h-4 w-4 text-primary" />
                                            </div>
                                            <div className="w-8 h-8 rounded-full bg-green-500/20 border-2 border-background flex items-center justify-center">
                                                <CheckCircle className="h-4 w-4 text-green-600" />
                                            </div>
                                            <div className="w-8 h-8 rounded-full bg-blue-500/20 border-2 border-background flex items-center justify-center">
                                                <Server className="h-4 w-4 text-blue-600" />
                                            </div>
                                        </div>
                                        <div className="text-sm">
                                            <div className="font-medium text-foreground">500+ ISPs</div>
                                            <div className="text-muted-foreground">Trust our platform</div>
                                        </div>
                                    </div>
                                    <div className="text-sm">
                                        <div className="font-medium text-foreground">99.9% Uptime</div>
                                        <div className="text-muted-foreground">Guaranteed SLA</div>
                                    </div>
                                </div>
                            </div>

                            <div className="relative">
                                <div className="relative z-10">
                                    <Card className="card-modern-hover p-6 bg-gradient-to-br from-card to-card/50">
                                        <CardHeader className="pb-4">
                                            <div className="flex items-center justify-between">
                                                <CardTitle className="text-lg">Network Overview</CardTitle>
                                                <Badge className="bg-green-500/10 text-green-600 border-green-500/20">
                                                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                                                    Live
                                                </Badge>
                                            </div>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="grid grid-cols-2 gap-4">
                                                <div className="space-y-2">
                                                    <div className="flex items-center gap-2">
                                                        <Users className="h-4 w-4 text-primary" />
                                                        <span className="text-sm font-medium">Active Customers</span>
                                                    </div>
                                                    <div className="text-2xl font-bold text-primary">2,847</div>
                                                    <div className="text-xs text-green-600">+12% this month</div>
                                                </div>
                                                <div className="space-y-2">
                                                    <div className="flex items-center gap-2">
                                                        <Activity className="h-4 w-4 text-blue-600" />
                                                        <span className="text-sm font-medium">Bandwidth Usage</span>
                                                    </div>
                                                    <div className="text-2xl font-bold text-blue-600">847 GB</div>
                                                    <div className="text-xs text-muted-foreground">Real-time</div>
                                                </div>
                                            </div>
                                            <div className="space-y-2">
                                                <div className="flex items-center justify-between text-sm">
                                                    <span>Network Health</span>
                                                    <span className="text-green-600 font-medium">Excellent</span>
                                                </div>
                                                <div className="w-full bg-muted rounded-full h-2">
                                                    <div className="bg-gradient-to-r from-green-500 to-green-400 h-2 rounded-full w-[94%]"></div>
                                                </div>
                                            </div>
                                            <div className="grid grid-cols-3 gap-2 pt-2">
                                                <div className="text-center p-2 bg-muted/50 rounded-lg">
                                                    <Server className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
                                                    <div className="text-xs font-medium">18 Devices</div>
                                                </div>
                                                <div className="text-center p-2 bg-muted/50 rounded-lg">
                                                    <Wifi className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
                                                    <div className="text-xs font-medium">5 Sites</div>
                                                </div>
                                                <div className="text-center p-2 bg-muted/50 rounded-lg">
                                                    <Globe className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
                                                    <div className="text-xs font-medium">99.9% Up</div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>

                                {/* Background decoration */}
                                <div className="absolute -top-4 -right-4 w-72 h-72 bg-primary/5 rounded-full blur-3xl"></div>
                                <div className="absolute -bottom-8 -left-8 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section id="features" className="py-20 lg:py-32 bg-muted/30">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center space-y-4 mb-16">
                            <Badge className="bg-primary/10 text-primary border-primary/20">
                                <Shield className="h-3 w-3 mr-1" />
                                Enterprise Features
                            </Badge>
                            <h2 className="text-3xl lg:text-5xl font-bold text-foreground">
                                Everything You Need to
                                <span className="text-primary"> Scale Your ISP</span>
                            </h2>
                            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                                From customer onboarding to network monitoring, our comprehensive platform
                                handles every aspect of ISP operations with enterprise-grade reliability.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                            {/* Customer Management */}
                            <Card className="card-modern-hover group">
                                <CardHeader>
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                                            <Users className="h-6 w-6 text-primary" />
                                        </div>
                                        <CardTitle>Customer Management</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground mb-4">
                                        Complete CRM with customer profiles, service history, and automated communication.
                                    </p>
                                    <ul className="space-y-2 text-sm">
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Customer portal & self-service
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Service history tracking
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Document management
                                        </li>
                                    </ul>
                                </CardContent>
                            </Card>

                            {/* Billing Automation */}
                            <Card className="card-modern-hover group">
                                <CardHeader>
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-green-500/10 rounded-lg group-hover:bg-green-500/20 transition-colors">
                                            <CreditCard className="h-6 w-6 text-green-600" />
                                        </div>
                                        <CardTitle>Billing Automation</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground mb-4">
                                        Automated invoicing, payment processing, and revenue management with pro-rated billing.
                                    </p>
                                    <ul className="space-y-2 text-sm">
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Monthly automated invoicing
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            M-Pesa & cash payments
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Pro-rated billing support
                                        </li>
                                    </ul>
                                </CardContent>
                            </Card>

                            {/* Network Monitoring */}
                            <Card className="card-modern-hover group">
                                <CardHeader>
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-blue-500/10 rounded-lg group-hover:bg-blue-500/20 transition-colors">
                                            <Activity className="h-6 w-6 text-blue-600" />
                                        </div>
                                        <CardTitle>Network Monitoring</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground mb-4">
                                        Real-time bandwidth monitoring and network health tracking across all devices.
                                    </p>
                                    <ul className="space-y-2 text-sm">
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            15-minute data collection
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            SNMP + API monitoring
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Performance dashboards
                                        </li>
                                    </ul>
                                </CardContent>
                            </Card>

                            {/* MikroTik Integration */}
                            <Card className="card-modern-hover group">
                                <CardHeader>
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-orange-500/10 rounded-lg group-hover:bg-orange-500/20 transition-colors">
                                            <Server className="h-6 w-6 text-orange-600" />
                                        </div>
                                        <CardTitle>MikroTik Integration</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground mb-4">
                                        Native MikroTik API integration for automated service provisioning and management.
                                    </p>
                                    <ul className="space-y-2 text-sm">
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            PPPoE & Static IP services
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Queue-based operations
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Automatic suspension/activation
                                        </li>
                                    </ul>
                                </CardContent>
                            </Card>

                            {/* Service Management */}
                            <Card className="card-modern-hover group">
                                <CardHeader>
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-purple-500/10 rounded-lg group-hover:bg-purple-500/20 transition-colors">
                                            <Wifi className="h-6 w-6 text-purple-600" />
                                        </div>
                                        <CardTitle>Service Management</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground mb-4">
                                        Streamlined service creation, bandwidth plans, and IP pool management.
                                    </p>
                                    <ul className="space-y-2 text-sm">
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Bandwidth plan templates
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            IP pool automation
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Service lifecycle management
                                        </li>
                                    </ul>
                                </CardContent>
                            </Card>

                            {/* Analytics & Reporting */}
                            <Card className="card-modern-hover group">
                                <CardHeader>
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-indigo-500/10 rounded-lg group-hover:bg-indigo-500/20 transition-colors">
                                            <BarChart3 className="h-6 w-6 text-indigo-600" />
                                        </div>
                                        <CardTitle>Analytics & Reporting</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground mb-4">
                                        Comprehensive analytics with financial reports and performance insights.
                                    </p>
                                    <ul className="space-y-2 text-sm">
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Revenue analytics
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Customer growth metrics
                                        </li>
                                        <li className="flex items-center gap-2">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            Network utilization reports
                                        </li>
                                    </ul>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>

                {/* Pricing Section */}
                <section id="pricing" className="py-20 lg:py-32 bg-background">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center space-y-4 mb-16">
                            <Badge className="bg-green-500/10 text-green-600 border-green-500/20">
                                <DollarSign className="h-3 w-3 mr-1" />
                                Transparent Pricing
                            </Badge>
                            <h2 className="text-3xl lg:text-5xl font-bold text-foreground">
                                Simple, Scalable
                                <span className="text-primary"> Pricing</span>
                            </h2>
                            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                                Pay only for what you use. Our tiered pricing scales with your business,
                                offering better rates as you grow your customer base.
                            </p>
                        </div>

                        <div className="grid lg:grid-cols-2 gap-12 items-start">
                            {/* Pricing Calculator */}
                            <Card className="card-modern p-8">
                                <CardHeader className="pb-6">
                                    <CardTitle className="text-2xl">Pricing Calculator</CardTitle>
                                    <p className="text-muted-foreground">
                                        Calculate your monthly cost based on customer count
                                    </p>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="space-y-4">
                                        <label className="text-sm font-medium text-foreground">
                                            Number of Customers
                                        </label>
                                        <div className="space-y-2">
                                            <Input
                                                type="number"
                                                value={customerCount}
                                                onChange={(e) => setCustomerCount(Number(e.target.value))}
                                                className="input-modern text-lg"
                                                min="1"
                                                max="10000"
                                            />
                                            <div className="flex gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => setCustomerCount(750)}
                                                >
                                                    750
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => setCustomerCount(2000)}
                                                >
                                                    2,000
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => setCustomerCount(5000)}
                                                >
                                                    5,000
                                                </Button>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="p-6 bg-muted/50 rounded-xl space-y-4">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm text-muted-foreground">Price per customer</span>
                                            <span className="text-lg font-semibold text-primary">
                                                ${getPricePerCustomer(customerCount)}
                                            </span>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm text-muted-foreground">Total customers</span>
                                            <span className="text-lg font-semibold">
                                                {customerCount.toLocaleString()}
                                            </span>
                                        </div>
                                        <div className="border-t border-border pt-4">
                                            <div className="flex items-center justify-between">
                                                <span className="text-lg font-medium">Monthly Total</span>
                                                <span className="text-3xl font-bold text-primary">
                                                    ${calculatePrice(customerCount).toLocaleString()}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <Button size="lg" className="w-full btn-gradient" asChild>
                                        <Link href={route('register')}>
                                            Start Free Trial
                                            <ArrowRight className="h-4 w-4 ml-2" />
                                        </Link>
                                    </Button>
                                </CardContent>
                            </Card>

                            {/* Pricing Tiers */}
                            <div className="space-y-6">
                                <div className="space-y-4">
                                    <h3 className="text-2xl font-bold text-foreground">Pricing Tiers</h3>
                                    <p className="text-muted-foreground">
                                        Our pricing automatically adjusts based on your customer count,
                                        giving you better rates as you scale.
                                    </p>
                                </div>

                                <div className="space-y-4">
                                    {/* Starter Tier */}
                                    <Card className={`card-modern-hover ${customerCount <= 1000 ? 'ring-2 ring-primary' : ''}`}>
                                        <CardContent className="p-6">
                                            <div className="flex items-center justify-between mb-4">
                                                <div>
                                                    <h4 className="text-lg font-semibold text-foreground">Starter</h4>
                                                    <p className="text-sm text-muted-foreground">Perfect for small ISPs</p>
                                                </div>
                                                <div className="text-right">
                                                    <div className="text-2xl font-bold text-primary">$0.30</div>
                                                    <div className="text-sm text-muted-foreground">per customer</div>
                                                </div>
                                            </div>
                                            <div className="text-sm text-muted-foreground">
                                                1 - 1,000 customers
                                            </div>
                                            <div className="text-xs text-muted-foreground mt-2">
                                                Example: 500 customers = $150/month
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Growth Tier */}
                                    <Card className={`card-modern-hover ${customerCount > 1000 && customerCount <= 3000 ? 'ring-2 ring-primary' : ''}`}>
                                        <CardContent className="p-6">
                                            <div className="flex items-center justify-between mb-4">
                                                <div>
                                                    <h4 className="text-lg font-semibold text-foreground">Growth</h4>
                                                    <p className="text-sm text-muted-foreground">For expanding ISPs</p>
                                                </div>
                                                <div className="text-right">
                                                    <div className="text-2xl font-bold text-primary">$0.25</div>
                                                    <div className="text-sm text-muted-foreground">per customer*</div>
                                                </div>
                                            </div>
                                            <div className="text-sm text-muted-foreground">
                                                1,001 - 3,000 customers
                                            </div>
                                            <div className="text-xs text-muted-foreground mt-1">
                                                *Progressive pricing: First 1,000 at $0.30, additional at $0.25
                                            </div>
                                            <Badge className="mt-2 bg-green-500/10 text-green-600 border-green-500/20">
                                                17% savings on additional customers
                                            </Badge>
                                        </CardContent>
                                    </Card>

                                    {/* Enterprise Tier */}
                                    <Card className={`card-modern-hover ${customerCount > 3000 ? 'ring-2 ring-primary' : ''}`}>
                                        <CardContent className="p-6">
                                            <div className="flex items-center justify-between mb-4">
                                                <div>
                                                    <h4 className="text-lg font-semibold text-foreground">Enterprise</h4>
                                                    <p className="text-sm text-muted-foreground">For large ISPs</p>
                                                </div>
                                                <div className="text-right">
                                                    <div className="text-2xl font-bold text-primary">$0.20</div>
                                                    <div className="text-sm text-muted-foreground">per customer*</div>
                                                </div>
                                            </div>
                                            <div className="text-sm text-muted-foreground">
                                                3,001+ customers
                                            </div>
                                            <div className="text-xs text-muted-foreground mt-1">
                                                *Progressive pricing: First 1,000 at $0.30, next 2,000 at $0.25, additional at $0.20
                                            </div>
                                            <Badge className="mt-2 bg-green-500/10 text-green-600 border-green-500/20">
                                                33% savings on additional customers
                                            </Badge>
                                        </CardContent>
                                    </Card>
                                </div>

                                <div className="p-4 bg-muted/30 rounded-lg">
                                    <div className="flex items-start gap-3">
                                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                                        <div className="text-sm">
                                            <div className="font-medium text-foreground">All features included</div>
                                            <div className="text-muted-foreground">
                                                Every tier includes all platform features, MikroTik integration,
                                                and 24/7 support. No feature restrictions.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Benefits Section */}
                <section id="benefits" className="py-20 lg:py-32 bg-muted/30">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center space-y-4 mb-16">
                            <Badge className="bg-blue-500/10 text-blue-600 border-blue-500/20">
                                <Star className="h-3 w-3 mr-1" />
                                Why Choose Us
                            </Badge>
                            <h2 className="text-3xl lg:text-5xl font-bold text-foreground">
                                Built for ISPs,
                                <span className="text-primary"> By ISP Experts</span>
                            </h2>
                            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                                We understand the unique challenges of running an ISP. Our platform is designed
                                to solve real problems with proven solutions that scale.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                            {/* Reduce Operational Costs */}
                            <Card className="card-modern-hover text-center">
                                <CardContent className="p-8">
                                    <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                        <DollarSign className="h-8 w-8 text-green-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-foreground mb-4">
                                        Reduce Operational Costs
                                    </h3>
                                    <p className="text-muted-foreground mb-6">
                                        Automate billing, customer management, and network operations to reduce
                                        manual work by up to 80% and cut operational expenses.
                                    </p>
                                    <div className="text-3xl font-bold text-green-600 mb-2">80%</div>
                                    <div className="text-sm text-muted-foreground">Cost reduction</div>
                                </CardContent>
                            </Card>

                            {/* Faster Customer Onboarding */}
                            <Card className="card-modern-hover text-center">
                                <CardContent className="p-8">
                                    <div className="w-16 h-16 bg-blue-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                        <Clock className="h-8 w-8 text-blue-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-foreground mb-4">
                                        Faster Customer Onboarding
                                    </h3>
                                    <p className="text-muted-foreground mb-6">
                                        Streamlined service provisioning and automated MikroTik configuration
                                        gets new customers online in minutes, not hours.
                                    </p>
                                    <div className="text-3xl font-bold text-blue-600 mb-2">5 min</div>
                                    <div className="text-sm text-muted-foreground">Average setup time</div>
                                </CardContent>
                            </Card>

                            {/* Improved Customer Satisfaction */}
                            <Card className="card-modern-hover text-center">
                                <CardContent className="p-8">
                                    <div className="w-16 h-16 bg-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                        <Star className="h-8 w-8 text-purple-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-foreground mb-4">
                                        Improved Customer Satisfaction
                                    </h3>
                                    <p className="text-muted-foreground mb-6">
                                        Self-service portal, automated billing, and proactive monitoring
                                        lead to happier customers and reduced support tickets.
                                    </p>
                                    <div className="text-3xl font-bold text-purple-600 mb-2">95%</div>
                                    <div className="text-sm text-muted-foreground">Customer satisfaction</div>
                                </CardContent>
                            </Card>

                            {/* Scalable Infrastructure */}
                            <Card className="card-modern-hover text-center">
                                <CardContent className="p-8">
                                    <div className="w-16 h-16 bg-orange-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                        <BarChart3 className="h-8 w-8 text-orange-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-foreground mb-4">
                                        Scalable Infrastructure
                                    </h3>
                                    <p className="text-muted-foreground mb-6">
                                        Built to handle thousands of customers across multiple sites with
                                        enterprise-grade performance and reliability.
                                    </p>
                                    <div className="text-3xl font-bold text-orange-600 mb-2">10K+</div>
                                    <div className="text-sm text-muted-foreground">Customers supported</div>
                                </CardContent>
                            </Card>

                            {/* 24/7 Expert Support */}
                            <Card className="card-modern-hover text-center">
                                <CardContent className="p-8">
                                    <div className="w-16 h-16 bg-indigo-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                        <Shield className="h-8 w-8 text-indigo-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-foreground mb-4">
                                        24/7 Expert Support
                                    </h3>
                                    <p className="text-muted-foreground mb-6">
                                        Get help from ISP industry experts who understand your business.
                                        Priority support with guaranteed response times.
                                    </p>
                                    <div className="text-3xl font-bold text-indigo-600 mb-2">24/7</div>
                                    <div className="text-sm text-muted-foreground">Expert support</div>
                                </CardContent>
                            </Card>

                            {/* Proven ROI */}
                            <Card className="card-modern-hover text-center">
                                <CardContent className="p-8">
                                    <div className="w-16 h-16 bg-red-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                        <Zap className="h-8 w-8 text-red-600" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-foreground mb-4">
                                        Proven ROI
                                    </h3>
                                    <p className="text-muted-foreground mb-6">
                                        Our customers typically see full ROI within 6 months through
                                        reduced costs, increased efficiency, and faster growth.
                                    </p>
                                    <div className="text-3xl font-bold text-red-600 mb-2">6 mo</div>
                                    <div className="text-sm text-muted-foreground">Payback period</div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>

                {/* Technology Section */}
                <section id="technology" className="py-20 lg:py-32 bg-background">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center space-y-4 mb-16">
                            <Badge className="bg-indigo-500/10 text-indigo-600 border-indigo-500/20">
                                <Globe className="h-3 w-3 mr-1" />
                                Modern Technology
                            </Badge>
                            <h2 className="text-3xl lg:text-5xl font-bold text-foreground">
                                Built with
                                <span className="text-primary"> Modern Tech Stack</span>
                            </h2>
                            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                                Our platform leverages cutting-edge technologies to deliver exceptional
                                performance, security, and scalability for your ISP operations.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                            {/* Laravel */}
                            <Card className="card-modern-hover text-center">
                                <CardContent className="p-6">
                                    <div className="w-12 h-12 bg-red-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                                        <Server className="h-6 w-6 text-red-600" />
                                    </div>
                                    <h3 className="font-semibold text-foreground mb-2">Laravel</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Robust PHP framework for secure, scalable backend operations
                                    </p>
                                </CardContent>
                            </Card>

                            {/* React */}
                            <Card className="card-modern-hover text-center">
                                <CardContent className="p-6">
                                    <div className="w-12 h-12 bg-blue-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                                        <Monitor className="h-6 w-6 text-blue-600" />
                                    </div>
                                    <h3 className="font-semibold text-foreground mb-2">React</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Modern UI framework for responsive, interactive interfaces
                                    </p>
                                </CardContent>
                            </Card>

                            {/* MikroTik API */}
                            <Card className="card-modern-hover text-center">
                                <CardContent className="p-6">
                                    <div className="w-12 h-12 bg-green-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                                        <Wifi className="h-6 w-6 text-green-600" />
                                    </div>
                                    <h3 className="font-semibold text-foreground mb-2">MikroTik API</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Native integration for seamless network device management
                                    </p>
                                </CardContent>
                            </Card>

                            {/* Cloud Infrastructure */}
                            <Card className="card-modern-hover text-center">
                                <CardContent className="p-6">
                                    <div className="w-12 h-12 bg-purple-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                                        <Globe className="h-6 w-6 text-purple-600" />
                                    </div>
                                    <h3 className="font-semibold text-foreground mb-2">Cloud Ready</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Scalable cloud infrastructure with 99.9% uptime guarantee
                                    </p>
                                </CardContent>
                            </Card>
                        </div>

                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div className="space-y-6">
                                <h3 className="text-2xl font-bold text-foreground">
                                    Enterprise-Grade Security & Performance
                                </h3>
                                <div className="space-y-4">
                                    <div className="flex items-start gap-3">
                                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                                        <div>
                                            <div className="font-medium text-foreground">End-to-End Encryption</div>
                                            <div className="text-sm text-muted-foreground">
                                                All data transmission secured with industry-standard encryption
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                                        <div>
                                            <div className="font-medium text-foreground">Real-time Monitoring</div>
                                            <div className="text-sm text-muted-foreground">
                                                24/7 system monitoring with instant alerts and notifications
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                                        <div>
                                            <div className="font-medium text-foreground">Automated Backups</div>
                                            <div className="text-sm text-muted-foreground">
                                                Daily automated backups with point-in-time recovery
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                                        <div>
                                            <div className="font-medium text-foreground">API-First Architecture</div>
                                            <div className="text-sm text-muted-foreground">
                                                Extensible platform with comprehensive API for integrations
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <Card className="card-modern p-8">
                                <CardContent className="space-y-6">
                                    <div className="text-center">
                                        <h4 className="text-lg font-semibold text-foreground mb-2">
                                            Performance Metrics
                                        </h4>
                                        <p className="text-sm text-muted-foreground">
                                            Real-world performance data from our platform
                                        </p>
                                    </div>

                                    <div className="grid grid-cols-2 gap-6">
                                        <div className="text-center">
                                            <div className="text-2xl font-bold text-primary mb-1">99.9%</div>
                                            <div className="text-sm text-muted-foreground">Uptime SLA</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="text-2xl font-bold text-primary mb-1">&lt;100ms</div>
                                            <div className="text-sm text-muted-foreground">Response Time</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="text-2xl font-bold text-primary mb-1">10K+</div>
                                            <div className="text-sm text-muted-foreground">Concurrent Users</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="text-2xl font-bold text-primary mb-1">24/7</div>
                                            <div className="text-sm text-muted-foreground">Support</div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section id="cta" className="py-20 lg:py-32 bg-gradient-to-br from-primary via-primary to-primary/80">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                        <div className="space-y-8">
                            <div className="space-y-4">
                                <h2 className="text-3xl lg:text-5xl font-bold text-primary-foreground">
                                    Ready to Transform Your ISP?
                                </h2>
                                <p className="text-xl text-primary-foreground/80 max-w-3xl mx-auto">
                                    Join hundreds of ISPs who have already automated their operations and
                                    accelerated their growth with our platform.
                                </p>
                            </div>

                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Button size="lg" variant="secondary" asChild>
                                    <Link href={route('register')}>
                                        Start Free Trial
                                        <ArrowRight className="h-4 w-4 ml-2" />
                                    </Link>
                                </Button>
                                <Button size="lg" variant="outline" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10">
                                    Schedule Demo
                                    <Monitor className="h-4 w-4 ml-2" />
                                </Button>
                            </div>

                            <div className="flex items-center justify-center gap-8 pt-8">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-primary-foreground">14 Days</div>
                                    <div className="text-sm text-primary-foreground/70">Free Trial</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-primary-foreground">No Setup</div>
                                    <div className="text-sm text-primary-foreground/70">Fees</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-primary-foreground">Cancel</div>
                                    <div className="text-sm text-primary-foreground/70">Anytime</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-muted/30 border-t border-border/50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                        <div className="grid md:grid-cols-4 gap-8">
                            <div className="space-y-4">
                                <div className="flex items-center gap-2">
                                    <AppLogoIcon className="size-6 fill-current text-primary" />
                                    <span className="font-semibold text-foreground">ISP Manager</span>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                    Complete ISP management platform with MikroTik integration,
                                    automated billing, and network monitoring.
                                </p>
                            </div>

                            <div className="space-y-4">
                                <h4 className="font-semibold text-foreground">Product</h4>
                                <div className="space-y-2 text-sm">
                                    <button onClick={() => scrollToSection('features')} className="block text-muted-foreground hover:text-foreground transition-colors">
                                        Features
                                    </button>
                                    <button onClick={() => scrollToSection('pricing')} className="block text-muted-foreground hover:text-foreground transition-colors">
                                        Pricing
                                    </button>
                                    <button onClick={() => scrollToSection('technology')} className="block text-muted-foreground hover:text-foreground transition-colors">
                                        Technology
                                    </button>
                                </div>
                            </div>

                            <div className="space-y-4">
                                <h4 className="font-semibold text-foreground">Support</h4>
                                <div className="space-y-2 text-sm">
                                    <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">
                                        Documentation
                                    </a>
                                    <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">
                                        Help Center
                                    </a>
                                    <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">
                                        Contact Us
                                    </a>
                                </div>
                            </div>

                            <div className="space-y-4">
                                <h4 className="font-semibold text-foreground">Company</h4>
                                <div className="space-y-2 text-sm">
                                    <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">
                                        About Us
                                    </a>
                                    <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">
                                        Privacy Policy
                                    </a>
                                    <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">
                                        Terms of Service
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div className="border-t border-border/50 mt-8 pt-8 text-center">
                            <p className="text-sm text-muted-foreground">
                                © 2024 ISP Manager. All rights reserved.
                            </p>
                        </div>
                    </div>
                </footer>
            </main>
        </>
    );
}
