import { Head, Link, useForm } from '@inertiajs/react';
import { LoaderCircle, User, Mail, Lock, Eye, EyeOff, CheckCircle } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import AuthLayout from '@/layouts/auth-layout';

type RegisterForm = {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
};

export default function Register() {
    const { data, setData, post, processing, errors, reset } = useForm<Required<RegisterForm>>({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
    });

    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('register'), {
            onFinish: () => reset('password', 'password_confirmation'),
        });
    };

    // Password strength validation
    const getPasswordStrength = (password: string) => {
        let strength = 0;
        if (password.length >= 8) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        return strength;
    };

    const passwordStrength = getPasswordStrength(data.password);
    const passwordsMatch = data.password && data.password_confirmation && data.password === data.password_confirmation;

    return (
        <AuthLayout title="Create your account" description="Start managing your ISP operations with our comprehensive platform">
            <Head title="Register" />

            {/* Benefits badge */}
            <div className="mb-6">
                <Badge className="w-full justify-center bg-primary/10 text-primary border-primary/20 py-2">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Free 30-day trial • No credit card required
                </Badge>
            </div>

            <form className="space-y-6" onSubmit={submit}>
                {/* Name field */}
                <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-medium text-foreground">
                        Full name
                    </Label>
                    <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            id="name"
                            type="text"
                            required
                            autoFocus
                            tabIndex={1}
                            autoComplete="name"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            disabled={processing}
                            placeholder="Enter your full name"
                            className="pl-10 input-modern"
                            aria-invalid={errors.name ? 'true' : 'false'}
                        />
                    </div>
                    <InputError message={errors.name} />
                </div>

                {/* Email field */}
                <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium text-foreground">
                        Email address
                    </Label>
                    <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            id="email"
                            type="email"
                            required
                            tabIndex={2}
                            autoComplete="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            disabled={processing}
                            placeholder="Enter your email address"
                            className="pl-10 input-modern"
                            aria-invalid={errors.email ? 'true' : 'false'}
                        />
                    </div>
                    <InputError message={errors.email} />
                </div>

                {/* Password field */}
                <div className="space-y-2">
                    <Label htmlFor="password" className="text-sm font-medium text-foreground">
                        Password
                    </Label>
                    <div className="relative">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            id="password"
                            type={showPassword ? "text" : "password"}
                            required
                            tabIndex={3}
                            autoComplete="new-password"
                            value={data.password}
                            onChange={(e) => setData('password', e.target.value)}
                            disabled={processing}
                            placeholder="Create a strong password"
                            className="pl-10 pr-10 input-modern"
                            aria-invalid={errors.password ? 'true' : 'false'}
                        />
                        <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                            tabIndex={-1}
                        >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                    </div>

                    {/* Password strength indicator */}
                    {data.password && (
                        <div className="space-y-2">
                            <div className="flex space-x-1">
                                {[1, 2, 3, 4, 5].map((level) => (
                                    <div
                                        key={level}
                                        className={`h-1 flex-1 rounded-full transition-colors ${
                                            passwordStrength >= level
                                                ? passwordStrength <= 2
                                                    ? 'bg-red-500'
                                                    : passwordStrength <= 3
                                                    ? 'bg-yellow-500'
                                                    : 'bg-green-500'
                                                : 'bg-muted'
                                        }`}
                                    />
                                ))}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Password strength: {
                                    passwordStrength <= 2 ? 'Weak' :
                                    passwordStrength <= 3 ? 'Medium' : 'Strong'
                                }
                            </p>
                        </div>
                    )}

                    <InputError message={errors.password} />
                </div>

                {/* Confirm password field */}
                <div className="space-y-2">
                    <Label htmlFor="password_confirmation" className="text-sm font-medium text-foreground">
                        Confirm password
                    </Label>
                    <div className="relative">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            id="password_confirmation"
                            type={showConfirmPassword ? "text" : "password"}
                            required
                            tabIndex={4}
                            autoComplete="new-password"
                            value={data.password_confirmation}
                            onChange={(e) => setData('password_confirmation', e.target.value)}
                            disabled={processing}
                            placeholder="Confirm your password"
                            className="pl-10 pr-10 input-modern"
                            aria-invalid={errors.password_confirmation ? 'true' : 'false'}
                        />
                        <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                            tabIndex={-1}
                        >
                            {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                    </div>

                    {/* Password match indicator */}
                    {data.password_confirmation && (
                        <div className="flex items-center gap-2">
                            {passwordsMatch ? (
                                <>
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                    <span className="text-xs text-green-600">Passwords match</span>
                                </>
                            ) : (
                                <>
                                    <div className="h-4 w-4 rounded-full border-2 border-red-500" />
                                    <span className="text-xs text-red-600">Passwords don't match</span>
                                </>
                            )}
                        </div>
                    )}

                    <InputError message={errors.password_confirmation} />
                </div>

                {/* Submit button */}
                <Button
                    type="submit"
                    className="w-full btn-gradient"
                    size="lg"
                    tabIndex={5}
                    disabled={processing}
                >
                    {processing ? (
                        <>
                            <LoaderCircle className="h-4 w-4 animate-spin mr-2" />
                            Creating account...
                        </>
                    ) : (
                        'Create your account'
                    )}
                </Button>

                {/* Terms notice */}
                <p className="text-xs text-muted-foreground text-center">
                    By creating an account, you agree to our{' '}
                    <Link href="#" className="text-primary hover:text-primary/80 transition-colors">
                        Terms of Service
                    </Link>{' '}
                    and{' '}
                    <Link href="#" className="text-primary hover:text-primary/80 transition-colors">
                        Privacy Policy
                    </Link>
                </p>

                {/* Divider */}
                <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                        <span className="w-full border-t border-border/50" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-card px-2 text-muted-foreground">Already have an account?</span>
                    </div>
                </div>

                {/* Sign in link */}
                <div className="text-center">
                    <p className="text-sm text-muted-foreground">
                        Ready to sign in?{' '}
                        <Link
                            href={route('login')}
                            className="font-medium text-primary hover:text-primary/80 transition-colors"
                            tabIndex={6}
                        >
                            Sign in to your account
                        </Link>
                    </p>
                </div>
            </form>
        </AuthLayout>
    );
}
