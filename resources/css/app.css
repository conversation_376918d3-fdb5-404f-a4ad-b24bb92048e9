@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
    --background: oklch(0.99 0.002 247.858);
    --foreground: oklch(0.145 0.012 247.858);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0.012 247.858);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0.012 247.858);
    --primary: oklch(0.445 0.194 254.139);
    --primary-foreground: oklch(0.985 0.002 247.858);
    --secondary: oklch(0.96 0.006 247.858);
    --secondary-foreground: oklch(0.205 0.012 247.858);
    --muted: oklch(0.96 0.006 247.858);
    --muted-foreground: oklch(0.556 0.012 247.858);
    --accent: oklch(0.96 0.006 247.858);
    --accent-foreground: oklch(0.205 0.012 247.858);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.985 0.002 247.858);
    --border: oklch(0.922 0.006 247.858);
    --input: oklch(0.922 0.006 247.858);
    --ring: oklch(0.445 0.194 254.139);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --success: oklch(0.577 0.245 142.495);
    --success-foreground: oklch(0.985 0.002 247.858);
    --warning: oklch(0.577 0.245 60.325);
    --warning-foreground: oklch(0.985 0.002 247.858);
    --info: oklch(0.577 0.245 254.139);
    --info-foreground: oklch(0.985 0.002 247.858);
    --gradient-primary: linear-gradient(135deg, oklch(0.445 0.194 254.139), oklch(0.545 0.194 274.139));
    --gradient-secondary: linear-gradient(135deg, oklch(0.96 0.006 247.858), oklch(0.98 0.006 267.858));
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --radius: 0.625rem;
    --sidebar: oklch(0.99 0.002 247.858);
    --sidebar-foreground: oklch(0.145 0.012 247.858);
    --sidebar-primary: oklch(0.445 0.194 254.139);
    --sidebar-primary-foreground: oklch(0.985 0.002 247.858);
    --sidebar-accent: oklch(0.96 0.006 247.858);
    --sidebar-accent-foreground: oklch(0.205 0.012 247.858);
    --sidebar-border: oklch(0.922 0.006 247.858);
    --sidebar-ring: oklch(0.445 0.194 254.139);
}

.dark {
    --background: oklch(0.09 0.012 247.858);
    --foreground: oklch(0.985 0.002 247.858);
    --card: oklch(0.12 0.012 247.858);
    --card-foreground: oklch(0.985 0.002 247.858);
    --popover: oklch(0.12 0.012 247.858);
    --popover-foreground: oklch(0.985 0.002 247.858);
    --primary: oklch(0.545 0.194 254.139);
    --primary-foreground: oklch(0.985 0.002 247.858);
    --secondary: oklch(0.18 0.012 247.858);
    --secondary-foreground: oklch(0.985 0.002 247.858);
    --muted: oklch(0.18 0.012 247.858);
    --muted-foreground: oklch(0.708 0.012 247.858);
    --accent: oklch(0.18 0.012 247.858);
    --accent-foreground: oklch(0.985 0.002 247.858);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.985 0.002 247.858);
    --border: oklch(0.18 0.012 247.858);
    --input: oklch(0.18 0.012 247.858);
    --ring: oklch(0.545 0.194 254.139);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --success: oklch(0.577 0.245 142.495);
    --success-foreground: oklch(0.985 0.002 247.858);
    --warning: oklch(0.577 0.245 60.325);
    --warning-foreground: oklch(0.985 0.002 247.858);
    --info: oklch(0.577 0.245 254.139);
    --info-foreground: oklch(0.985 0.002 247.858);
    --gradient-primary: linear-gradient(135deg, oklch(0.545 0.194 254.139), oklch(0.645 0.194 274.139));
    --gradient-secondary: linear-gradient(135deg, oklch(0.18 0.012 247.858), oklch(0.22 0.012 267.858));
    --sidebar: oklch(0.12 0.012 247.858);
    --sidebar-foreground: oklch(0.985 0.002 247.858);
    --sidebar-primary: oklch(0.545 0.194 254.139);
    --sidebar-primary-foreground: oklch(0.985 0.002 247.858);
    --sidebar-accent: oklch(0.18 0.012 247.858);
    --sidebar-accent-foreground: oklch(0.985 0.002 247.858);
    --sidebar-border: oklch(0.18 0.012 247.858);
    --sidebar-ring: oklch(0.545 0.194 254.139);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground font-sans antialiased;
    }

    h1, h2, h3, h4, h5, h6 {
        @apply font-semibold tracking-tight;
    }

    h1 {
        @apply text-3xl lg:text-4xl;
    }

    h2 {
        @apply text-2xl lg:text-3xl;
    }

    h3 {
        @apply text-xl lg:text-2xl;
    }

    h4 {
        @apply text-lg lg:text-xl;
    }
}

/* Custom utility classes for Tailwind CSS v4 */
@utility gradient-primary {
    background: var(--gradient-primary);
}

@utility gradient-secondary {
    background: var(--gradient-secondary);
}

@utility shadow-modern {
    box-shadow: var(--shadow);
}

@utility shadow-modern-md {
    box-shadow: var(--shadow-md);
}

@utility shadow-modern-lg {
    box-shadow: var(--shadow-lg);
}

@layer components {
    .card-modern {
        @apply bg-card border border-border/50 rounded-xl;
        box-shadow: var(--shadow);
    }

    .card-modern-hover {
        @apply bg-card border border-border/50 rounded-xl transition-all duration-200 hover:border-border;
        box-shadow: var(--shadow);
    }

    .card-modern-hover:hover {
        box-shadow: var(--shadow-md);
    }

    .btn-gradient {
        @apply text-primary-foreground border-0 transition-all duration-200;
        background: var(--gradient-primary);
        box-shadow: var(--shadow);
    }

    .btn-gradient:hover {
        box-shadow: var(--shadow-md);
    }

    .input-modern {
        @apply bg-background border-border/60 focus:border-primary/60 focus:ring-2 focus:ring-primary/20 transition-all duration-200;
    }

    .table-modern {
        @apply bg-card border border-border/50 rounded-xl overflow-hidden;
        box-shadow: var(--shadow);
    }

    .table-modern th {
        @apply bg-muted/50 font-semibold text-muted-foreground border-b border-border/50;
    }

    .table-modern td {
        @apply border-b border-border/30 last:border-b-0;
    }

    .table-modern tr:hover td {
        @apply bg-muted/30;
    }

    .status-badge-active {
        @apply bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400;
    }

    .status-badge-inactive {
        @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400;
    }

    .status-badge-suspended {
        @apply bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400;
    }

    .stats-card {
        @apply bg-card border border-border/50 rounded-xl p-6 space-y-2;
        box-shadow: var(--shadow);
    }

    .stats-card-value {
        @apply text-2xl font-bold text-foreground;
    }

    .stats-card-label {
        @apply text-sm text-muted-foreground;
    }

    .stats-card-change {
        @apply text-xs font-medium;
    }

    .stats-card-change.positive {
        @apply text-green-600 dark:text-green-400;
    }

    .stats-card-change.negative {
        @apply text-red-600 dark:text-red-400;
    }

    /* Modern sidebar enhancements */
    .sidebar-modern {
        @apply bg-sidebar border-r border-sidebar-border/50;
        box-shadow: var(--shadow);
    }

    .sidebar-nav-item {
        @apply relative flex items-center gap-3 px-3 py-2 rounded-lg text-sidebar-foreground transition-all duration-200;
    }

    .sidebar-nav-item:hover {
        @apply bg-sidebar-accent text-sidebar-accent-foreground;
    }

    .sidebar-nav-item[data-active="true"] {
        @apply bg-sidebar-primary text-sidebar-primary-foreground shadow-sm;
    }

    .sidebar-nav-item[data-active="true"]::before {
        content: '';
        @apply absolute left-0 top-1/2 -translate-y-1/2 w-1 h-6 bg-sidebar-primary-foreground rounded-r-full;
    }

    /* Enhanced layout spacing */
    .layout-content {
        @apply min-h-screen bg-background;
    }

    .page-container {
        @apply flex h-full flex-1 flex-col gap-6 p-6;
    }

    .page-header {
        @apply flex items-center justify-between;
    }

    .page-title {
        @apply space-y-1;
    }

    .page-title h1 {
        @apply text-3xl font-bold tracking-tight;
    }

    .page-title p {
        @apply text-muted-foreground;
    }

    /* Modern form styling */
    .form-modern {
        @apply space-y-6;
    }

    .form-group {
        @apply space-y-2;
    }

    .form-label {
        @apply text-sm font-medium text-foreground;
    }

    .form-description {
        @apply text-sm text-muted-foreground;
    }

    /* Enhanced responsive design */
    @media (max-width: 768px) {
        .page-container {
            @apply p-4 gap-4;
        }

        .page-header {
            @apply flex-col items-start gap-4;
        }
    }
}
