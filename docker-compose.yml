version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: isp_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./storage/app/public:/var/www/public/storage
    environment:
      - APP_ENV=production
      - CONTAINER_ROLE=app
    depends_on:
      - postgres
      - redis
    networks:
      - isp_network

  nginx:
    image: nginx:alpine
    container_name: isp_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/nginx/sites/:/etc/nginx/sites-available/
      - ./deployment/nginx/ssl/:/etc/nginx/ssl/
    depends_on:
      - app
    networks:
      - isp_network

  postgres:
    image: postgres:15-alpine
    container_name: isp_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: isp_management
      POSTGRES_USER: isp_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./deployment/postgres/init:/docker-entrypoint-initdb.d
      - ./backups:/backups
    ports:
      - "5432:5432"
    networks:
      - isp_network

  redis:
    image: redis:7-alpine
    container_name: isp_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - isp_network

  queue:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: isp_queue
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
    environment:
      - APP_ENV=production
      - CONTAINER_ROLE=queue
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600
    depends_on:
      - postgres
      - redis
    networks:
      - isp_network

  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: isp_scheduler
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
    environment:
      - APP_ENV=production
      - CONTAINER_ROLE=scheduler
    command: php artisan schedule:work
    depends_on:
      - postgres
      - redis
    networks:
      - isp_network

  backup:
    image: postgres:15-alpine
    container_name: isp_backup
    restart: "no"
    volumes:
      - ./backups:/backups
      - ./scripts:/scripts
    environment:
      POSTGRES_DB: isp_management
      POSTGRES_USER: isp_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_HOST: postgres
    command: /scripts/backup.sh
    depends_on:
      - postgres
    networks:
      - isp_network
    profiles:
      - backup

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  isp_network:
    driver: bridge
