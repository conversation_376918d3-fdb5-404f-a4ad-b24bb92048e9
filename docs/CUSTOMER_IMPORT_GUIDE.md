# Customer Import Template Guide

## Overview

This guide explains how to use the standardized Excel template for importing customer data into the ISP Management Platform.

## Quick Start

1. **Access Documentation**: Visit `/admin/import/documentation` in your browser
2. **Download Template**: Click "Download Template" to get the latest Excel file
3. **Prepare Data**: Fill in your customer data following the validation rules
4. **Import**: Upload the completed file through the import interface

## Template Structure

### Required Fields (marked with *)
- `customer_name` - Full customer name
- `customer_email` - Unique email address
- `customer_phone` - Phone with country code (+254XXXXXXXXX)
- `network_site` - Physical service location
- `service_type` - Either "static_ip" or "pppoe"
- `bandwidth_plan` - Must match existing plans
- `monthly_fee` - Service cost in KES (decimal format)
- `service_start_date` - Date in YYYY-MM-DD format

### Optional Fields
- `customer_address`, `customer_city`, `customer_state`, `customer_postal_code`, `customer_country`
- `ip_address` - Required only for static_ip service type
- `last_payment_date`, `last_payment_amount` - Historical payment data
- `mpesa_id` - Behavior depends on assignment mode
- `customer_status`, `service_status` - Default to "active"
- `notes` - Additional information

## M-Pesa ID Assignment Modes

### Manual Mode
- M-Pesa ID field is **REQUIRED**
- Must provide unique M-Pesa ID for each customer
- No automatic generation occurs

### Auto Mode
- M-Pesa ID field is **IGNORED**
- System automatically generates unique IDs
- Leave column empty or values will be ignored

### Hybrid Mode
- M-Pesa ID field is **OPTIONAL**
- Use provided ID if valid, otherwise auto-generate
- Best of both worlds approach

## Data Validation Rules

### Email Addresses
- Must be valid email format
- Must be unique across all customers
- Used for customer login and communication

### Phone Numbers
- Must include country code
- Format: +254XXXXXXXXX (for Kenya)
- Used for SMS notifications and contact

### IP Addresses
- Required only for static_ip service type
- Must be valid IPv4 format
- Must be available in configured IP pools
- Leave empty for pppoe service type

### Dates
- Must use YYYY-MM-DD format
- service_start_date cannot be more than 1 year in past/future
- last_payment_date must be before or equal to service_start_date

### Service Types
- **static_ip**: Customer gets fixed IP address
- **pppoe**: Customer connects via PPPoE authentication

## Common Mistakes to Avoid

1. **Modifying Column Headers**: Never change the column names in the template
2. **Wrong Date Format**: Always use YYYY-MM-DD, not DD/MM/YYYY or MM/DD/YYYY
3. **Missing Country Code**: Phone numbers must include +254 for Kenya
4. **Duplicate Emails**: Each customer must have a unique email address
5. **IP for PPPoE**: Don't provide IP addresses for pppoe service type
6. **Invalid Service Types**: Only use "static_ip" or "pppoe"

## Example Data

### Static IP Customer
```
customer_name: John Doe Smith
customer_email: <EMAIL>
customer_phone: +254712345678
network_site: Downtown_Site_A
service_type: static_ip
ip_address: *************
bandwidth_plan: 10Mbps_Unlimited
monthly_fee: 1500.00
service_start_date: 2025-01-15
```

### PPPoE Customer
```
customer_name: Jane Wilson
customer_email: <EMAIL>
customer_phone: +254723456789
network_site: Coastal_Site_B
service_type: pppoe
ip_address: (leave empty)
bandwidth_plan: 5Mbps_Unlimited
monthly_fee: 1000.00
service_start_date: 2025-02-01
```

## Import Process

1. **Template Download**: Get latest template with current M-Pesa ID mode settings
2. **Data Preparation**: Remove example rows and add your customer data
3. **Validation**: Check data against rules before import
4. **File Upload**: Use the import interface to upload your Excel file
5. **Processing**: System validates and imports data in batches
6. **Verification**: Review import results and handle any errors

## Error Handling

### Format Errors
- Invalid column headers → Entire import rejected
- Wrong data types → Specific rows rejected
- Invalid dates/emails → Specific rows rejected

### Business Rule Violations
- Duplicate emails → Conflicting rows rejected
- IP address conflicts → Conflicting rows rejected
- Invalid bandwidth plans → Specific rows rejected

### Missing Data
- Required fields empty → Specific rows rejected
- Conditional requirements not met → Specific rows rejected

## Support

- **Technical Issues**: Contact <EMAIL>
- **Data Preparation**: Our team can assist with formatting
- **Template Updates**: Check documentation page for latest version

## File Specifications

- **Format**: Excel (.xlsx) only
- **Max File Size**: 10MB
- **Max Records**: 1000 customers per import
- **Template Version**: Updates automatically based on system settings

## Integration Notes

- **Network Sites**: New sites will be created automatically
- **IP Pools**: Must be configured before importing static IP customers
- **Bandwidth Plans**: Must exist in system before import
- **MikroTik**: Services will be automatically provisioned
- **Billing**: Subscriptions and invoices created automatically
