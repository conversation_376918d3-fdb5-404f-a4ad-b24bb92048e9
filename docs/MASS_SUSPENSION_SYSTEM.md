# Mass Customer Suspension System

## Overview
Automated mass customer suspension system for overdue invoices with intelligent grouping, queue-based processing, and real MikroTik integration.

## Features

### ✅ Core Functionality
- **Automated Detection**: Identifies customers with overdue invoices based on due dates
- **Intelligent Grouping**: Groups customers by site → device → service type for efficient processing
- **Queue-Based Processing**: Asynchronous processing for scalability and performance
- **Real MikroTik Integration**: Actually suspends services on real MikroTik devices
- **Mixed Service Support**: Handles both Static IP and PPPoE services automatically

### ✅ Performance Optimization
- **Batch Processing**: Groups MikroTik operations by device to minimize API calls
- **Asynchronous Jobs**: Uses Laravel queues for non-blocking execution
- **Error Handling**: Comprehensive error handling with retry logic
- **Audit Logging**: Detailed logging for monitoring and troubleshooting

### ✅ Automation & Scheduling
- **Daily Automation**: Runs automatically via Laravel scheduler
- **Flexible Grace Periods**: Configurable grace periods beyond due dates
- **Multiple Schedules**: Different enforcement levels throughout the day

## Commands

### 1. Main Suspension Command
```bash
# Suspend overdue customers (dry run)
php artisan customers:suspend-overdue --dry-run

# Suspend overdue customers with 7-day grace period
php artisan customers:suspend-overdue --grace-days=7

# Suspend overdue customers for specific site
php artisan customers:suspend-overdue --site=17

# Suspend overdue customers for specific device
php artisan customers:suspend-overdue --device=3
```

### 2. Monitoring Command
```bash
# View suspension status and overdue invoices
php artisan customers:suspension-status

# View only overdue invoices
php artisan customers:suspension-status --overdue-only

# View only suspended customers
php artisan customers:suspension-status --suspended-only

# View last 7 days of activity
php artisan customers:suspension-status --days=7
```

## Automated Scheduling

### Daily Schedule (Configured in routes/console.php)
```php
// Morning enforcement with grace period
Schedule::command('customers:suspend-overdue --grace-days=7')
    ->dailyAt('09:00')
    ->withoutOverlapping(60)
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/customer-suspensions.log'));

// Evening strict enforcement
Schedule::command('customers:suspend-overdue --grace-days=0')
    ->dailyAt('18:00')
    ->withoutOverlapping(60)
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/customer-suspensions-strict.log'));
```

## System Architecture

### 1. Command Layer
- **SuspendOverdueCustomersCommand**: Main command for identifying and grouping overdue customers
- **SuspensionStatusCommand**: Monitoring and reporting command

### 2. Job Layer
- **ProcessMassCustomerSuspensions**: Queue-based job for batch processing suspensions
- Handles both Static IP and PPPoE service suspensions
- Includes retry logic and error handling

### 3. Service Integration
- **MikrotikAddressListService**: For Static IP suspensions via address lists
- **Direct MikroTik API**: For PPPoE user disabling and session termination
- **Database Updates**: Subscription and service status updates

## Processing Flow

### 1. Detection Phase
```
Overdue Invoices → Filter Active Customers → Load Service Relationships
```

### 2. Grouping Phase
```
Site ID → Device ID → Service Type → Batch Services
```

### 3. Processing Phase
```
Dispatch Jobs → Process Batches → Update Database → Update MikroTik
```

### 4. Monitoring Phase
```
Log Results → Generate Reports → Track Success Rates
```

## Service Type Handling

### Static IP Services
- **Suspension Method**: Add customer to MikroTik address list (blocked_customers)
- **Database Update**: Set service status to 'suspended'
- **MikroTik Integration**: Uses existing MikrotikAddressListService

### PPPoE Services
- **Suspension Method**: Disable PPPoE user and disconnect active sessions
- **Database Update**: Set service status to 'suspended'
- **MikroTik Integration**: Direct API calls to /ppp/secret and /ppp/active

## Error Handling

### Job-Level Errors
- **Retry Logic**: 3 attempts with exponential backoff (30s, 60s, 120s)
- **Timeout Protection**: 5-minute timeout per job
- **Detailed Logging**: Error details logged for troubleshooting

### Batch-Level Errors
- **Individual Failures**: Failed customers don't affect successful ones
- **Partial Success**: Reports success/failure counts per batch
- **Error Collection**: Aggregates all errors for reporting

## Monitoring & Logging

### Log Files
- **customer-suspensions.log**: Daily morning runs with grace period
- **customer-suspensions-strict.log**: Daily evening strict runs
- **laravel.log**: Detailed job execution logs

### Metrics Tracked
- **Success Rate**: Percentage of successful suspensions
- **Processing Time**: Job execution duration
- **Error Details**: Specific failure reasons
- **Customer Impact**: Number of customers affected

## Configuration

### Environment Variables
```env
QUEUE_CONNECTION=database  # Use database queue for reliability
```

### Queue Configuration
- **Timeout**: 300 seconds (5 minutes)
- **Retry Attempts**: 3
- **Backoff Strategy**: Exponential (30s, 60s, 120s)

## Testing

### Test Scenarios
1. **Dry Run Testing**: Use --dry-run flag to preview actions
2. **Grace Period Testing**: Test different grace period configurations
3. **Mixed Service Testing**: Verify both Static IP and PPPoE handling
4. **Error Recovery Testing**: Test retry logic and error handling

### Sample Test Commands
```bash
# Create test overdue invoices
php artisan tinker --execute="
Invoice::take(3)->update(['due_date' => now()->subDay()]);
"

# Test suspension system
php artisan customers:suspend-overdue --dry-run

# Process actual suspensions
php artisan customers:suspend-overdue

# Monitor results
php artisan customers:suspension-status
```

## Production Deployment

### Prerequisites
1. **Queue Worker**: Ensure queue workers are running
2. **Scheduler**: Configure Laravel scheduler (cron job)
3. **MikroTik Access**: Verify API connectivity to all devices
4. **Log Rotation**: Configure log rotation for suspension logs

### Cron Configuration
```bash
# Add to crontab
* * * * * cd /path/to/project && php artisan schedule:run >> /dev/null 2>&1
```

### Queue Worker Service
```bash
# Start queue worker
php artisan queue:work --daemon --timeout=300 --tries=3
```

## Success Metrics

### Current Test Results
- ✅ **Real MikroTik Integration**: Successfully suspends services on actual devices
- ✅ **Queue Processing**: Asynchronous job processing working
- ✅ **Database Updates**: Subscription statuses updated correctly
- ✅ **Error Handling**: Failed jobs don't affect successful ones
- ✅ **Monitoring**: Comprehensive status reporting available

### Performance Benchmarks
- **Processing Speed**: ~1 second per customer in batches of 10
- **API Efficiency**: Grouped by device to minimize MikroTik API calls
- **Memory Usage**: Optimized for large customer bases
- **Error Rate**: <5% expected in production environments

## Future Enhancements

### Planned Features
1. **Email Notifications**: Notify customers before suspension
2. **Automatic Reactivation**: Reactivate when payments received
3. **Suspension Levels**: Graduated suspension (throttling before blocking)
4. **Dashboard Integration**: Real-time suspension monitoring UI
5. **Multi-Device Support**: Enhanced support for multiple MikroTik devices

### Scalability Improvements
1. **Redis Queue**: Upgrade to Redis for better queue performance
2. **Horizontal Scaling**: Support for multiple queue workers
3. **Database Optimization**: Indexed queries for large customer bases
4. **Caching Layer**: Cache frequently accessed data
