# Role-Based Permission System Implementation

## Overview

This document outlines the comprehensive role-based permission system implemented for the ISP Management System using Spatie Laravel Permission package.

## Features Implemented

### 1. **<PERSON>tie <PERSON>vel Permission Integration**
- Installed and configured <PERSON><PERSON> Laravel Permission package
- Created permission tables and models
- Integrated with existing User model

### 2. **Default Roles and Permissions**
- **Super Admin**: Full system access including user/role management
- **Admin**: Most permissions except user/role management
- **Manager**: Customer and service management
- **Technician**: Network and technical management
- **Operator**: Basic operations
- **Viewer**: Read-only access

### 3. **Granular Permissions**
- Dashboard access
- User management (view, create, edit, delete)
- Role management (view, create, edit, delete)
- Customer management (view, create, edit, delete)
- Network management (view devices, sites, IP pools)
- Bandwidth management (plans, policies, quotas)
- Service management (view, create, edit, delete, manage types)
- Subscription management (view, create, edit, delete)
- Invoice management (view, create, edit, delete)
- Payment management (view, process, refund)
- Settings management (view admin settings, manage system/mpesa settings)
- Reports (view, export)

### 4. **User Management Interface**
- **User List**: View all users with roles and status
- **Create User**: Add new users with role assignment
- **Edit User**: Update user details and roles
- **View User**: Display user information and permissions
- Permission-based access control

### 5. **Role Management Interface**
- **Role List**: View all roles with permission counts
- **Create Role**: Create new roles with permission assignment
- **Edit Role**: Update role permissions
- **View Role**: Display role details and assigned users
- Grouped permission selection for easier management

### 6. **Security Features**
- Route-level permission middleware (Laravel 12 compatible)
- Permission-based sidebar navigation
- Prevent self-deletion of user accounts
- Prevent deletion of roles with assigned users
- Comprehensive permission checking

## File Structure

### Backend Files
```
app/
├── Http/
│   ├── Controllers/
│   │   └── Admin/
│   │       ├── UserController.php
│   │       ├── RoleController.php
│   │       └── SettingsController.php
│   └── Middleware/
│       └── CheckPermission.php
├── Models/
│   └── User.php (updated with HasRoles trait)
├── Console/
│   └── Commands/
│       └── AssignSuperAdminRole.php
database/
├── migrations/
│   └── 2025_05_28_063339_create_permission_tables.php
└── seeders/
    └── RolePermissionSeeder.php
```

### Frontend Files
```
resources/js/
├── pages/
│   └── admin/
│       └── settings/
│           ├── users/
│           │   ├── index.tsx
│           │   ├── create.tsx
│           │   ├── edit.tsx
│           │   └── show.tsx
│           └── roles/
│               ├── index.tsx
│               └── create.tsx
├── components/
│   ├── app-sidebar.tsx (updated with permission-based navigation)
│   └── ui/
│       └── badge.tsx
```

### Route Files
```
routes/
└── settings.php (updated with permission middleware)
```

## Setup Instructions

### 1. Install Dependencies
```bash
composer require spatie/laravel-permission
```

### 2. Run Migrations
```bash
php artisan migrate
```

### 3. Seed Default Roles and Permissions
```bash
php artisan db:seed --class=RolePermissionSeeder
```

### 4. Assign Super Admin Role
```bash
php artisan user:make-super-admin <EMAIL>
```

## Usage

### Accessing User Management
1. Navigate to Admin Settings
2. Click on "User Management"
3. Create, edit, or view users
4. Assign roles to users

### Accessing Role Management
1. Navigate to Admin Settings
2. Click on "Role Management"
3. Create custom roles
4. Assign specific permissions to roles

### Permission Checking
- Sidebar navigation automatically hides/shows based on user permissions
- Routes are protected with permission middleware
- Controllers validate permissions before executing actions

## Permission List

### User Management
- `view users`
- `create users`
- `edit users`
- `delete users`

### Role Management
- `view roles`
- `create roles`
- `edit roles`
- `delete roles`

### Customer Management
- `view customers`
- `create customers`
- `edit customers`
- `delete customers`

### Network Management
- `view network`
- `manage network devices`
- `manage network sites`
- `manage ip pools`

### Bandwidth Management
- `view bandwidth`
- `manage bandwidth plans`
- `manage bandwidth policies`
- `manage bandwidth quotas`

### Service Management
- `view services`
- `create services`
- `edit services`
- `delete services`
- `manage service types`

### Subscription Management
- `view subscriptions`
- `create subscriptions`
- `edit subscriptions`
- `delete subscriptions`

### Invoice Management
- `view invoices`
- `create invoices`
- `edit invoices`
- `delete invoices`

### Payment Management
- `view payments`
- `process payments`
- `refund payments`

### Settings Management
- `view admin settings`
- `manage system settings`
- `manage mpesa settings`

### Reports
- `view reports`
- `export reports`

## Security Considerations

1. **Route Protection**: All sensitive routes are protected with permission middleware
2. **UI Security**: Navigation and buttons are hidden based on permissions
3. **Data Isolation**: Users can only access data they have permissions for
4. **Role Hierarchy**: Clear role hierarchy with appropriate permission distribution
5. **Self-Protection**: Users cannot delete their own accounts

## Customization

### Adding New Permissions
1. Add permission to `RolePermissionSeeder.php`
2. Assign to appropriate roles
3. Add route middleware protection
4. Update UI components to check permission

### Creating Custom Roles
1. Use the Role Management interface
2. Select appropriate permissions
3. Assign to users as needed

### Modifying Existing Roles
1. Edit roles through the Role Management interface
2. Add or remove permissions as needed
3. Changes apply immediately to all users with that role

## Commands

### Assign Super Admin Role
```bash
php artisan user:make-super-admin {email}
```

This command assigns the Super Admin role to a user by email address.
