<?php

namespace App\Services;

use App\Jobs\MikroTik\ReactivatePppoeServiceJob;
use App\Jobs\MikroTik\ReactivateStaticIpServiceJob;
use App\Jobs\MikroTik\SuspendPppoeServiceJob;
use App\Jobs\MikroTik\SuspendStaticIpServiceJob;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Models\Subscription;
use Illuminate\Support\Facades\Log;

class ServiceSuspensionService
{
    /**
     * Suspend services for a subscription due to non-payment.
     */
    public function suspendServices(Subscription $subscription): array
    {
        $result = [
            'success' => true,
            'message' => 'Services suspended successfully',
            'services' => [],
        ];

        try {
            // Get all PPPoE services for this subscription
            $pppoeServices = PppoeService::where('subscription_id', $subscription->id)
                ->where('status', 'active')
                ->get();

            // Get all Static IP services for this subscription
            $staticIpServices = StaticIpService::where('subscription_id', $subscription->id)
                ->where('status', 'active')
                ->get();

            // Queue PPPoE service suspension jobs
            foreach ($pppoeServices as $service) {
                SuspendPppoeServiceJob::dispatch($service);
                $result['services']['pppoe'][] = $service->id;

                Log::info('PPPoE service suspension job queued', [
                    'service_id' => $service->id,
                    'subscription_id' => $subscription->id,
                ]);
            }

            // Queue Static IP service suspension jobs
            foreach ($staticIpServices as $service) {
                SuspendStaticIpServiceJob::dispatch($service);
                $result['services']['static_ip'][] = $service->id;

                Log::info('Static IP service suspension job queued', [
                    'service_id' => $service->id,
                    'subscription_id' => $subscription->id,
                ]);
            }

            // Update subscription status
            $subscription->status = 'suspended';
            $subscription->save();

            Log::info('Services suspended for subscription', [
                'subscription_id' => $subscription->id,
                'customer_id' => $subscription->customer_id,
                'pppoe_count' => count($pppoeServices),
                'static_ip_count' => count($staticIpServices),
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to suspend services', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to suspend services: '.$e->getMessage(),
                'services' => [],
            ];
        }
    }

    /**
     * Reactivate services for a subscription after payment.
     */
    public function reactivateServices(Subscription $subscription): array
    {
        $result = [
            'success' => true,
            'message' => 'Services reactivated successfully',
            'services' => [],
        ];

        try {
            // Get all PPPoE services for this subscription
            $pppoeServices = PppoeService::where('subscription_id', $subscription->id)
                ->where('status', 'suspended')
                ->get();

            // Get all Static IP services for this subscription
            $staticIpServices = StaticIpService::where('subscription_id', $subscription->id)
                ->where('status', 'suspended')
                ->get();

            // Queue PPPoE service reactivation jobs
            foreach ($pppoeServices as $service) {
                ReactivatePppoeServiceJob::dispatch($service);
                $result['services']['pppoe'][] = $service->id;

                Log::info('PPPoE service reactivation job queued', [
                    'service_id' => $service->id,
                    'subscription_id' => $subscription->id,
                ]);
            }

            // Queue Static IP service reactivation jobs
            foreach ($staticIpServices as $service) {
                ReactivateStaticIpServiceJob::dispatch($service);
                $result['services']['static_ip'][] = $service->id;

                Log::info('Static IP service reactivation job queued', [
                    'service_id' => $service->id,
                    'subscription_id' => $subscription->id,
                ]);
            }

            // Update subscription status
            $subscription->status = 'active';
            $subscription->save();

            Log::info('Services reactivated for subscription', [
                'subscription_id' => $subscription->id,
                'customer_id' => $subscription->customer_id,
                'pppoe_count' => count($pppoeServices),
                'static_ip_count' => count($staticIpServices),
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to reactivate services', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to reactivate services: '.$e->getMessage(),
                'services' => [],
            ];
        }
    }
}
