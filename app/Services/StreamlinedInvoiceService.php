<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StreamlinedInvoiceService
{
    /**
     * Create an initial invoice for a new subscription.
     */
    public function createInitialInvoice(Subscription $subscription): ?Invoice
    {
        try {
            DB::beginTransaction();

            // Calculate billing period for the first invoice
            $billingPeriod = $this->calculateInitialBillingPeriod($subscription);

            // Calculate due date based on billing cycle
            $dueDate = $this->calculateDueDate($subscription->start_date, $subscription->billing_cycle);

            // Generate unique invoice number
            $invoiceNumber = $this->generateInvoiceNumber();

            // Create the invoice
            $invoice = Invoice::create([
                'customer_id' => $subscription->customer_id,
                'subscription_id' => $subscription->id,
                'invoice_number' => $invoiceNumber,
                'issue_date' => $subscription->start_date,
                'due_date' => $dueDate,
                'billing_period_start' => $billingPeriod['start'],
                'billing_period_end' => $billingPeriod['end'],
                'is_prorated' => false, // Initial invoices are typically not prorated
                'active_days' => $billingPeriod['total_days'],
                'total_days' => $billingPeriod['total_days'],
                'auto_generated' => false, // This is manually triggered by subscription creation
                'amount' => $subscription->price,
                'tax_amount' => 0, // TODO: Add tax calculation if needed
                'total_amount' => $subscription->price,
                'status' => 'pending',
                'notes' => 'Initial invoice for subscription: '.$subscription->name,
            ]);

            // Create invoice item
            $this->createInvoiceItem($invoice, $subscription, $billingPeriod);

            DB::commit();

            Log::info('Initial invoice created for subscription', [
                'invoice_id' => $invoice->id,
                'subscription_id' => $subscription->id,
                'customer_id' => $subscription->customer_id,
                'amount' => $subscription->price,
            ]);

            return $invoice;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create initial invoice for subscription', [
                'subscription_id' => $subscription->id,
                'customer_id' => $subscription->customer_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Don't throw the exception - we don't want to fail subscription creation
            // if invoice creation fails
            return null;
        }
    }

    /**
     * Calculate the initial billing period for a new subscription.
     */
    protected function calculateInitialBillingPeriod(Subscription $subscription): array
    {
        $startDate = Carbon::parse($subscription->start_date);
        $endDate = $this->calculatePeriodEnd($startDate, $subscription->billing_cycle);
        $totalDays = $startDate->diffInDays($endDate) + 1;

        return [
            'start' => $startDate,
            'end' => $endDate,
            'total_days' => $totalDays,
        ];
    }

    /**
     * Calculate period end based on billing cycle.
     */
    protected function calculatePeriodEnd(Carbon $startDate, string $billingCycle): Carbon
    {
        switch ($billingCycle) {
            case 'monthly':
                return $startDate->copy()->addMonth()->subDay();
            case 'quarterly':
                return $startDate->copy()->addMonths(3)->subDay();
            case 'biannually':
                return $startDate->copy()->addMonths(6)->subDay();
            case 'annually':
                return $startDate->copy()->addYear()->subDay();
            default:
                return $startDate->copy()->addMonth()->subDay();
        }
    }

    /**
     * Calculate due date based on start date and billing cycle.
     */
    protected function calculateDueDate(Carbon $startDate, string $billingCycle): Carbon
    {
        // Standard grace period of 15 days for initial invoices
        $gracePeriod = 15;

        switch ($billingCycle) {
            case 'monthly':
                return $startDate->copy()->addDays($gracePeriod);
            case 'quarterly':
                return $startDate->copy()->addDays($gracePeriod);
            case 'biannually':
                return $startDate->copy()->addDays($gracePeriod);
            case 'annually':
                return $startDate->copy()->addDays($gracePeriod);
            default:
                return $startDate->copy()->addDays($gracePeriod);
        }
    }

    /**
     * Generate unique invoice number.
     */
    protected function generateInvoiceNumber(): string
    {
        $prefix = 'INV-';
        $date = now()->format('Ymd');
        $lastInvoice = Invoice::where('invoice_number', 'like', $prefix.$date.'%')
            ->orderBy('invoice_number', 'desc')
            ->first();

        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoice_number, strlen($prefix.$date));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix.$date.str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Create invoice item for the subscription.
     */
    protected function createInvoiceItem(Invoice $invoice, Subscription $subscription, array $billingPeriod): InvoiceItem
    {
        $description = $this->generateInvoiceItemDescription($subscription, $billingPeriod);

        return InvoiceItem::create([
            'invoice_id' => $invoice->id,
            'description' => $description,
            'quantity' => 1,
            'unit_price' => $subscription->price,
            'subtotal' => $subscription->price,
            'tax_rate' => 0,
            'tax_amount' => 0,
            'total' => $subscription->price,
        ]);
    }

    /**
     * Generate description for the invoice item.
     */
    protected function generateInvoiceItemDescription(Subscription $subscription, array $billingPeriod): string
    {
        $description = $subscription->name;

        // Add billing period information
        $periodStart = $billingPeriod['start']->format('M j, Y');
        $periodEnd = $billingPeriod['end']->format('M j, Y');
        $description .= " (Billing Period: {$periodStart} - {$periodEnd})";

        // Add bandwidth plan information if available
        if ($subscription->bandwidthPlan) {
            $plan = $subscription->bandwidthPlan;
            $description .= " - {$plan->download_speed}/{$plan->upload_speed} Mbps";
        }

        return $description;
    }

    /**
     * Get billing cycle display name.
     */
    protected function getBillingCycleDisplayName(string $billingCycle): string
    {
        return match ($billingCycle) {
            'monthly' => 'Monthly',
            'quarterly' => 'Quarterly',
            'biannually' => 'Biannually',
            'annually' => 'Annually',
            default => 'Monthly'
        };
    }
}
