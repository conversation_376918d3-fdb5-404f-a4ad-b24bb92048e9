<?php

namespace App\Services;

use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Network\NetworkDevice;
use App\Models\Services\IpPool;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Models\Subscription;
use Illuminate\Support\Facades\Log;

class ServiceProvisioningService
{
    /**
     * Provision services for a subscription based on its plan.
     */
    public function provisionServices(Subscription $subscription): array
    {
        $result = [
            'success' => true,
            'message' => 'Services provisioned successfully',
            'services' => [],
        ];

        try {
            // Get the subscription plan details
            $plan = $subscription->plan;

            if (! $plan) {
                throw new \Exception('Subscription has no associated plan');
            }

            // Get the customer
            $customer = $subscription->customer;

            if (! $customer) {
                throw new \Exception('Subscription has no associated customer');
            }

            // Check if the plan includes PPPoE service
            if ($plan->includes_pppoe) {
                $pppoeService = $this->provisionPppoeService($subscription, $customer, $plan);
                $result['services']['pppoe'] = $pppoeService->id;
            }

            // Check if the plan includes Static IP service
            if ($plan->includes_static_ip) {
                $staticIpService = $this->provisionStaticIpService($subscription, $customer, $plan);
                $result['services']['static_ip'] = $staticIpService->id;
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to provision services', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to provision services: '.$e->getMessage(),
                'services' => [],
            ];
        }
    }

    /**
     * Provision a PPPoE service for a subscription.
     *
     * @param  mixed  $plan
     */
    protected function provisionPppoeService(Subscription $subscription, Customer $customer, $plan): PppoeService
    {
        // Find a suitable router device
        $device = $this->findSuitableDevice($customer, 'pppoe');

        if (! $device) {
            throw new \Exception('No suitable router device found for PPPoE service');
        }

        // Find the bandwidth plan if specified in the subscription plan
        $bandwidthPlan = null;
        if ($plan->bandwidth_plan_id) {
            $bandwidthPlan = BandwidthPlan::find($plan->bandwidth_plan_id);
        }

        // Generate username and password
        $username = PppoeService::generateUsername($customer);
        $password = PppoeService::generatePassword();

        // Create the PPPoE service
        $service = PppoeService::create([
            'subscription_id' => $subscription->id,
            'customer_id' => $customer->id,
            'device_id' => $device->id,
            'username' => $username,
            'password' => $password,
            'service_profile' => $plan->pppoe_profile ?? 'default',
            'bandwidth_plan_id' => $bandwidthPlan ? $bandwidthPlan->id : null,
            'status' => 'active',
            'comment' => "Auto-provisioned for subscription {$subscription->id}",
        ]);

        // Provision the service on the MikroTik router
        try {
            $this->provisionPppoeOnMikrotik($service);

            Log::info('PPPoE service provisioned successfully', [
                'service_id' => $service->id,
                'subscription_id' => $subscription->id,
                'customer_id' => $customer->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to provision PPPoE service on MikroTik', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            // We'll still return the service, but it may need manual intervention
        }

        return $service;
    }

    /**
     * Provision a Static IP service for a subscription.
     *
     * @param  mixed  $plan
     */
    protected function provisionStaticIpService(Subscription $subscription, Customer $customer, $plan): StaticIpService
    {
        // Find a suitable router device
        $device = $this->findSuitableDevice($customer, 'static_ip');

        if (! $device) {
            throw new \Exception('No suitable router device found for Static IP service');
        }

        // Find a suitable IP pool
        $ipPool = $this->findSuitableIpPool($device);

        if (! $ipPool) {
            throw new \Exception('No suitable IP pool found for Static IP service');
        }

        // Get the next available IP from the pool
        $ipAddress = $ipPool->getNextAvailableIp();

        if (! $ipAddress) {
            throw new \Exception('No available IP addresses in the pool');
        }

        // Find the bandwidth plan if specified in the subscription plan
        $bandwidthPlan = null;
        if ($plan->bandwidth_plan_id) {
            $bandwidthPlan = BandwidthPlan::find($plan->bandwidth_plan_id);
        }

        // Create the Static IP service
        $service = StaticIpService::create([
            'subscription_id' => $subscription->id,
            'customer_id' => $customer->id,
            'device_id' => $device->id,
            'ip_address' => $ipAddress,
            'subnet_mask' => $ipPool->subnet_mask,
            'gateway' => $ipPool->gateway,
            'dns_servers' => $ipPool->dns_servers,
            'bandwidth_plan_id' => $bandwidthPlan ? $bandwidthPlan->id : null,
            'ip_pool_id' => $ipPool->id,
            'status' => 'active',
            'comment' => "Auto-provisioned for subscription {$subscription->id}",
        ]);

        // Provision the service on the MikroTik router
        try {
            $this->provisionStaticIpOnMikrotik($service);

            Log::info('Static IP service provisioned successfully', [
                'service_id' => $service->id,
                'subscription_id' => $subscription->id,
                'customer_id' => $customer->id,
                'ip_address' => $ipAddress,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to provision Static IP service on MikroTik', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            // We'll still return the service, but it may need manual intervention
        }

        return $service;
    }

    /**
     * Find a suitable device for the service based on customer location.
     */
    protected function findSuitableDevice(Customer $customer, string $serviceType): ?NetworkDevice
    {
        // If the customer has a network site, use the devices at that site
        if ($customer->networkSite) {
            $devices = $customer->networkSite->devices()
                ->where('type', 'mikrotik')
                ->where('status', 'active')
                ->get();

            if ($devices->isNotEmpty()) {
                // Return the first suitable device
                return $devices->first();
            }
        }

        // Otherwise, find a default device
        return NetworkDevice::where('type', 'mikrotik')
            ->where('status', 'active')
            ->where('is_default', true)
            ->first();
    }

    /**
     * Find a suitable IP pool for the service.
     */
    protected function findSuitableIpPool(NetworkDevice $device): ?IpPool
    {
        // Find an active IP pool with available IPs
        $pools = IpPool::where('device_id', $device->id)
            ->where('status', 'active')
            ->get();

        foreach ($pools as $pool) {
            if ($pool->available_ips > 0) {
                return $pool;
            }
        }

        return null;
    }

    /**
     * Provision a PPPoE service on a MikroTik router.
     *
     * @return mixed
     */
    protected function provisionPppoeOnMikrotik(PppoeService $service)
    {
        $device = $service->device;

        // Test connection to the device
        if (! $device->testConnection()) {
            throw new \Exception('Cannot connect to MikroTik device');
        }

        // Prepare command to add PPPoE secret
        $command = '/ppp/secret/add';
        $params = [
            'name' => $service->username,
            'password' => $service->password,
            'service' => 'pppoe',
            'profile' => $service->service_profile ?? 'default',
            'comment' => "Customer: {$service->customer->name}, ID: {$service->customer->id}",
        ];

        // Add bandwidth limits if a plan is specified
        if ($service->bandwidthPlan) {
            $plan = $service->bandwidthPlan;

            // If the service profile doesn't exist, create it
            $profileName = "plan-{$plan->id}";
            $profileExists = false;

            try {
                $profiles = $device->executeMikrotikCommand('/ppp/profile/print');
                foreach ($profiles as $profile) {
                    if ($profile['name'] === $profileName) {
                        $profileExists = true;
                        break;
                    }
                }

                if (! $profileExists) {
                    // Create a new profile with bandwidth limits
                    $device->executeMikrotikCommand('/ppp/profile/add', [
                        'name' => $profileName,
                        'rate-limit' => "{$plan->download_speed}k/{$plan->upload_speed}k",
                        'comment' => "Bandwidth Plan: {$plan->name}",
                    ]);
                }

                // Use the profile for this PPPoE secret
                $params['profile'] = $profileName;
            } catch (\Exception $e) {
                Log::warning('Failed to create PPP profile for bandwidth plan', [
                    'plan_id' => $plan->id,
                    'error' => $e->getMessage(),
                ]);
                // Continue with default profile
            }
        }

        // Execute the command to add the PPPoE secret
        $result = $device->executeMikrotikCommand($command, $params);

        // Store the MikroTik ID for future reference
        if (isset($result['.id'])) {
            $service->mikrotik_id = $result['.id'];
            $service->save();
        }

        return $result;
    }

    /**
     * Provision a Static IP service on a MikroTik router.
     */
    protected function provisionStaticIpOnMikrotik(StaticIpService $service): bool
    {
        $device = $service->device;

        // Test connection to the device
        if (! $device->testConnection()) {
            throw new \Exception('Cannot connect to MikroTik device');
        }

        // 1. Add static route
        $routeCommand = '/ip/route/add';
        $routeParams = [
            'dst-address' => $service->cidr,
            'gateway' => $service->gateway,
            'comment' => "Customer: {$service->customer->name}, ID: {$service->customer->id}",
        ];

        $routeResult = $device->executeMikrotikCommand($routeCommand, $routeParams);

        // Store the MikroTik route ID for future reference
        if (isset($routeResult['.id'])) {
            $service->mikrotik_route_id = $routeResult['.id'];
            $service->save();
        }

        // 2. Add firewall rule to allow traffic
        $firewallCommand = '/ip/firewall/filter/add';
        $firewallParams = [
            'chain' => 'forward',
            'src-address' => $service->cidr,
            'action' => 'accept',
            'comment' => "Allow traffic from customer {$service->customer->name}",
        ];

        $firewallResult = $device->executeMikrotikCommand($firewallCommand, $firewallParams);

        // Store the MikroTik firewall ID for future reference
        if (isset($firewallResult['.id'])) {
            $service->mikrotik_firewall_id = $firewallResult['.id'];
            $service->save();
        }

        // 3. Add NAT rule for outgoing traffic
        $natCommand = '/ip/firewall/nat/add';
        $natParams = [
            'chain' => 'srcnat',
            'src-address' => $service->cidr,
            'action' => 'masquerade',
            'comment' => "NAT for customer {$service->customer->name}",
        ];

        $natResult = $device->executeMikrotikCommand($natCommand, $natParams);

        // Store the MikroTik NAT ID for future reference
        if (isset($natResult['.id'])) {
            $service->mikrotik_nat_id = $natResult['.id'];
            $service->save();
        }

        // 4. Add bandwidth limitation if a plan is specified
        if ($service->bandwidthPlan) {
            $this->addBandwidthLimitation($service);
        }

        return true;
    }

    /**
     * Add bandwidth limitation for a Static IP service.
     */
    protected function addBandwidthLimitation(StaticIpService $service): bool
    {
        $device = $service->device;
        $plan = $service->bandwidthPlan;

        if (! $plan) {
            return false;
        }

        try {
            // Add simple queue for bandwidth limitation
            $queueCommand = '/queue/simple/add';
            $queueParams = [
                'name' => "customer-{$service->customer_id}-{$service->id}",
                'target' => $service->cidr,
                'max-limit' => "{$plan->download_speed}M/{$plan->upload_speed}M", // Use M for Mbps
                'comment' => "Bandwidth limit for customer {$service->customer->name}",
            ];

            $device->executeMikrotikCommand($queueCommand, $queueParams);

            return true;
        } catch (\Exception $e) {
            Log::warning('Failed to add bandwidth limitation', [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
