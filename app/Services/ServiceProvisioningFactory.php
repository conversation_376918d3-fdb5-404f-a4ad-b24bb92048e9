<?php

namespace App\Services;

use App\Http\Controllers\Services\StaticIpServiceController;
use App\Jobs\MikroTik\ProvisionPppoeServiceJob;
use App\Jobs\MikroTik\ProvisionStaticIpServiceJob;
use App\Models\Customer;
use App\Models\Services\IpPool;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Models\Subscription;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ServiceProvisioningFactory
{
    /**
     * Create a static IP service with unified logic
     */
    public function createStaticIpService(Customer $customer, Subscription $subscription, array $config, ?string $sessionId = null): ServiceResult
    {
        try {
            DB::beginTransaction();

            // Validate required configuration
            $validation = $this->validateStaticIpConfig($config);
            if (! $validation->isSuccess()) {
                return $validation;
            }

            // Get or assign IP address
            $ipAssignment = $this->assignIpAddress($config);
            if (! $ipAssignment->isSuccess()) {
                return $ipAssignment;
            }

            $ipData = $ipAssignment->getData();

            // Create static IP service
            $service = StaticIpService::create([
                'subscription_id' => $subscription->id,
                'customer_id' => $customer->id,
                'device_id' => $config['device_id'],
                'ip_address' => $ipData['ip_address'],
                'subnet_mask' => $ipData['subnet_mask'],
                'gateway' => $ipData['gateway'],
                'dns_servers' => [$ipData['dns_primary'], $ipData['dns_secondary']],
                'bandwidth_plan_id' => $config['bandwidth_plan_id'],
                'ip_pool_id' => $config['ip_pool_id'],
                'status' => 'active',
                'comment' => $config['comment'] ?? "Service for {$customer->name}",
            ]);

            // Provision MikroTik synchronously
            $this->provisionMikroTikSynchronously($service, 'static_ip', $sessionId);

            DB::commit();

            Log::info('Static IP service created successfully', [
                'service_id' => $service->id,
                'customer_id' => $customer->id,
                'ip_address' => $service->ip_address,
            ]);

            return ServiceResult::success([
                'service' => $service,
                'ip_assigned' => $ipData['ip_address'],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create static IP service', [
                'customer_id' => $customer->id,
                'error' => $e->getMessage(),
                'config' => $config,
            ]);

            return ServiceResult::error('Failed to create static IP service: '.$e->getMessage());
        }
    }

    /**
     * Create a PPPoE service with unified logic
     */
    public function createPppoeService(Customer $customer, Subscription $subscription, array $config): ServiceResult
    {
        try {
            DB::beginTransaction();

            // Validate required configuration
            $validation = $this->validatePppoeConfig($config);
            if (! $validation->isSuccess()) {
                return $validation;
            }

            // Generate or use provided credentials
            $username = $config['username'] ?? $this->generatePppoeUsername($customer);
            $password = $config['password'] ?? $this->generatePppoePassword();

            // Create PPPoE service
            $service = PppoeService::create([
                'subscription_id' => $subscription->id,
                'customer_id' => $customer->id,
                'device_id' => $config['device_id'],
                'username' => $username,
                'password' => $password,
                'bandwidth_plan_id' => $config['bandwidth_plan_id'],
                'status' => 'active',
                'comment' => $config['comment'] ?? "Service for {$customer->name}",
            ]);

            // Queue MikroTik provisioning
            $this->queueMikroTikProvisioning($service, 'pppoe');

            DB::commit();

            Log::info('PPPoE service created successfully', [
                'service_id' => $service->id,
                'customer_id' => $customer->id,
                'username' => $service->username,
            ]);

            return ServiceResult::success([
                'service' => $service,
                'username' => $username,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create PPPoE service', [
                'customer_id' => $customer->id,
                'error' => $e->getMessage(),
                'config' => $config,
            ]);

            return ServiceResult::error('Failed to create PPPoE service: '.$e->getMessage());
        }
    }

    /**
     * Validate static IP configuration
     */
    private function validateStaticIpConfig(array $config): ServiceResult
    {
        $required = ['device_id', 'ip_pool_id', 'bandwidth_plan_id'];

        foreach ($required as $field) {
            if (! isset($config[$field]) || empty($config[$field])) {
                return ServiceResult::error("Missing required field: {$field}");
            }
        }

        // Validate IP pool exists and is active
        $ipPool = IpPool::find($config['ip_pool_id']);
        if (! $ipPool || $ipPool->status !== 'active') {
            return ServiceResult::error('Invalid or inactive IP pool');
        }

        // Validate specific IP if provided
        if (isset($config['ip_address']) && ! filter_var($config['ip_address'], FILTER_VALIDATE_IP)) {
            return ServiceResult::error('Invalid IP address format');
        }

        return ServiceResult::success();
    }

    /**
     * Validate PPPoE configuration
     */
    private function validatePppoeConfig(array $config): ServiceResult
    {
        $required = ['device_id', 'bandwidth_plan_id'];

        foreach ($required as $field) {
            if (! isset($config[$field]) || empty($config[$field])) {
                return ServiceResult::error("Missing required field: {$field}");
            }
        }

        // Validate username uniqueness if provided
        if (isset($config['username'])) {
            $existing = PppoeService::where('username', $config['username'])->exists();
            if ($existing) {
                return ServiceResult::error('Username already exists');
            }
        }

        return ServiceResult::success();
    }

    /**
     * Unified IP assignment logic - uses pool's on-the-fly calculation
     */
    private function assignIpAddress(array $config): ServiceResult
    {
        $ipPool = IpPool::find($config['ip_pool_id']);

        if (! $ipPool) {
            return ServiceResult::error('IP pool not found');
        }

        if (isset($config['ip_address']) && $config['ip_address']) {
            // Use specific IP if provided - validate it's available using the pool's logic
            $availableIps = $ipPool->getAllAvailableIps();

            if (! in_array($config['ip_address'], $availableIps)) {
                return ServiceResult::error("IP address {$config['ip_address']} is not available");
            }

            $ipAddress = $config['ip_address'];
        } else {
            // Get next available IP from pool using the pool's logic
            $ipAddress = $ipPool->getNextAvailableIp();

            if (! $ipAddress) {
                return ServiceResult::error('No available IP addresses in pool');
            }
        }

        return ServiceResult::success([
            'ip_address' => $ipAddress,
            'subnet_mask' => $ipPool->subnet_mask,
            'gateway' => $ipPool->gateway,
            'dns_primary' => $ipPool->dns_servers[0] ?? '*******',
            'dns_secondary' => $ipPool->dns_servers[1] ?? '*******',
        ]);
    }

    /**
     * Provision MikroTik service synchronously
     */
    private function provisionMikroTikSynchronously($service, string $type, ?string $sessionId = null): void
    {
        try {
            if ($type === 'static_ip') {
                $sessionId = $sessionId ?: \Illuminate\Support\Str::random(32);
                $controller = new StaticIpServiceController();
                $controller->provisionStaticIpServiceSynchronously($service, $sessionId);
            } else {
                // For other service types, still use the job queue
                ProvisionPppoeServiceJob::dispatch($service);
            }

            Log::info("{$type} service provisioning completed synchronously", [
                'service_id' => $service->id,
                'type' => $type,
                'session_id' => $sessionId,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to provision {$type} service synchronously", [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);
            throw $e; // Re-throw to trigger rollback
        }
    }

    /**
     * Queue MikroTik provisioning job (kept for backward compatibility and other services)
     */
    private function queueMikroTikProvisioning($service, string $type, ?string $sessionId = null): void
    {
        try {
            if ($type === 'static_ip') {
                $sessionId = $sessionId ?: \Illuminate\Support\Str::random(32);
                ProvisionStaticIpServiceJob::dispatch($service, $sessionId);
            } else {
                ProvisionPppoeServiceJob::dispatch($service);
            }

            Log::info("{$type} service provisioning job dispatched", [
                'service_id' => $service->id,
                'type' => $type,
                'session_id' => $sessionId,
            ]);
        } catch (\Exception $e) {
            Log::warning("Failed to dispatch {$type} provisioning job", [
                'service_id' => $service->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Generate unique PPPoE username
     */
    private function generatePppoeUsername(Customer $customer): string
    {
        $baseName = Str::slug(strtolower($customer->name), '');
        $baseName = substr($baseName, 0, 8);

        // Ensure uniqueness
        $counter = 1;
        $username = $baseName.$customer->id;

        while (PppoeService::where('username', $username)->exists()) {
            $username = $baseName.$customer->id.'_'.$counter;
            $counter++;
        }

        return $username;
    }

    /**
     * Generate secure PPPoE password
     */
    private function generatePppoePassword(): string
    {
        return Str::random(12);
    }
}
