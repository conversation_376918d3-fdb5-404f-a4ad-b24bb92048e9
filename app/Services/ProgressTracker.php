<?php

namespace App\Services;

use Illuminate\Support\Str;

class ProgressTracker
{
    private string $sessionId;

    private static array $progressData = [];

    public function __construct(?string $sessionId = null)
    {
        $this->sessionId = $sessionId ?? Str::random(16);
    }

    /**
     * Start tracking progress for an operation
     */
    public function start(string $operation, int $totalSteps = 100): string
    {
        $progress = [
            'operation' => $operation,
            'current_step' => 0,
            'total_steps' => $totalSteps,
            'percentage' => 0,
            'status' => 'started',
            'message' => "Starting {$operation}...",
            'started_at' => now(),
            'updated_at' => now(),
            'errors' => [],
        ];

        // Store progress in memory instead of cache
        self::$progressData[$this->sessionId] = $progress;

        return $this->sessionId;
    }

    /**
     * Update progress
     */
    public function update(int $currentStep, string $message = '', string $status = 'in_progress'): void
    {
        $progress = self::$progressData[$this->sessionId] ?? null;

        if (! $progress) {
            return;
        }

        $progress['current_step'] = $currentStep;
        $progress['percentage'] = $progress['total_steps'] > 0
            ? round(($currentStep / $progress['total_steps']) * 100, 2)
            : 0;
        $progress['status'] = $status;
        $progress['message'] = $message ?: $progress['message'];
        $progress['updated_at'] = now();

        // Update progress in memory
        self::$progressData[$this->sessionId] = $progress;
    }

    /**
     * Add an error to the progress
     */
    public function addError(string $error): void
    {
        $progress = self::$progressData[$this->sessionId] ?? null;

        if (! $progress) {
            return;
        }

        $progress['errors'][] = [
            'message' => $error,
            'timestamp' => now(),
        ];
        $progress['status'] = 'error';
        $progress['updated_at'] = now();

        self::$progressData[$this->sessionId] = $progress;
    }

    /**
     * Mark operation as completed
     */
    public function complete(string $message = 'Operation completed successfully'): void
    {
        $progress = self::$progressData[$this->sessionId] ?? null;

        if (! $progress) {
            return;
        }

        $progress['current_step'] = $progress['total_steps'];
        $progress['percentage'] = 100;
        $progress['status'] = 'completed';
        $progress['message'] = $message;
        $progress['completed_at'] = now();
        $progress['updated_at'] = now();

        self::$progressData[$this->sessionId] = $progress;
    }

    /**
     * Get current progress
     */
    public function getProgress(): ?array
    {
        return self::$progressData[$this->sessionId] ?? null;
    }

    /**
     * Clear progress data
     */
    public function clear(): void
    {
        unset(self::$progressData[$this->sessionId]);
    }

    /**
     * Get session ID
     */
    public function getSessionId(): string
    {
        return $this->sessionId;
    }

    /**
     * Create a new progress tracker for PPPoE service creation
     */
    public static function forPppoeCreation(string $sessionId): self
    {
        $steps = [
            'Validating input data',
            'Connecting to MikroTik device',
            'Checking existing profiles',
            'Creating bandwidth profile',
            'Adding PPPoE secret',
            'Finalizing service',
        ];

        return new self($sessionId);
    }

    /**
     * Create a new progress tracker for Static IP service creation
     */
    public static function forStaticIpCreation(string $sessionId): self
    {
        $steps = [
            'Validating input data',
            'Connecting to MikroTik device',
            'Checking IP availability',
            'Creating bandwidth profile',
            'Configuring static IP',
            'Finalizing service',
        ];

        return new self($sessionId);
    }

    /**
     * Create a new progress tracker for service update
     */
    public static function forServiceUpdate(string $sessionId): self
    {
        $steps = [
            'Validating changes',
            'Connecting to MikroTik device',
            'Updating configuration',
            'Applying changes',
            'Finalizing update',
        ];

        return new self($sessionId);
    }
}
