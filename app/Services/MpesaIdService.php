<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\SystemSetting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MpesaIdService
{
    /**
     * Generate M-Pesa ID for a customer based on system settings.
     */
    public function generateMpesaId(Customer $customer, ?string $manualId = null): string
    {
        // If manual ID is provided and manual assignment is enabled, use it
        if ($manualId && $this->isManualAssignmentEnabled()) {
            $this->validateMpesaId($manualId, $customer->id);

            return $manualId;
        }

        // Get generation method from settings
        $method = SystemSetting::get('mpesa_id_generation_method', 'sequential');

        switch ($method) {
            case 'customer_id':
                return $this->generateFromCustomerId($customer);
            case 'phone_number':
                return $this->generateFromPhoneNumber($customer);
            case 'custom_format':
                return $this->generateCustomFormat($customer);
            case 'sequential':
            default:
                return $this->generateSequential();
        }
    }

    /**
     * Generate sequential M-Pesa ID.
     */
    protected function generateSequential(): string
    {
        $prefix = SystemSetting::get('mpesa_id_prefix', 'MP');
        $suffix = SystemSetting::get('mpesa_id_suffix', '');
        $startingNumber = SystemSetting::get('mpesa_id_starting_number', 1000);
        $padding = SystemSetting::get('mpesa_id_padding', 4);

        // Get the highest existing sequential number (database-agnostic approach)
        $lastCustomer = Customer::whereNotNull('mpesa_id')
            ->where('mpesa_id', 'LIKE', $prefix.'%')
            ->orderBy('id', 'desc') // Use ID as fallback ordering
            ->get()
            ->filter(function ($customer) use ($prefix) {
                // Filter to only include customers with numeric IDs after prefix
                $idPart = substr($customer->mpesa_id, strlen($prefix));

                return is_numeric(preg_replace('/[^0-9]/', '', $idPart));
            })
            ->sortByDesc(function ($customer) use ($prefix) {
                // Sort by the numeric part of the M-Pesa ID
                $idPart = substr($customer->mpesa_id, strlen($prefix));

                return (int) preg_replace('/[^0-9]/', '', $idPart);
            })
            ->first();

        if ($lastCustomer) {
            // Extract number from last ID
            $lastNumber = (int) preg_replace('/[^0-9]/', '', substr($lastCustomer->mpesa_id, strlen($prefix)));
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = $startingNumber;
        }

        $paddedNumber = str_pad($nextNumber, $padding, '0', STR_PAD_LEFT);

        return $prefix.$paddedNumber.$suffix;
    }

    /**
     * Generate M-Pesa ID from customer ID.
     */
    protected function generateFromCustomerId(Customer $customer): string
    {
        $prefix = SystemSetting::get('mpesa_id_prefix', 'CU');
        $suffix = SystemSetting::get('mpesa_id_suffix', '');
        $padding = SystemSetting::get('mpesa_id_padding', 4);

        $paddedId = str_pad($customer->id, $padding, '0', STR_PAD_LEFT);

        return $prefix.$paddedId.$suffix;
    }

    /**
     * Generate M-Pesa ID from phone number.
     */
    protected function generateFromPhoneNumber(Customer $customer): string
    {
        $prefix = SystemSetting::get('mpesa_id_prefix', 'PH');
        $suffix = SystemSetting::get('mpesa_id_suffix', '');

        // Extract last 6 digits of phone number
        $phoneDigits = preg_replace('/[^0-9]/', '', $customer->phone);
        $lastDigits = substr($phoneDigits, -6);

        return $prefix.$lastDigits.$suffix;
    }

    /**
     * Generate custom format M-Pesa ID.
     */
    protected function generateCustomFormat(Customer $customer): string
    {
        $format = SystemSetting::get('mpesa_id_custom_format', '{prefix}{customer_id}{suffix}');

        $replacements = [
            '{prefix}' => SystemSetting::get('mpesa_id_prefix', 'MP'),
            '{suffix}' => SystemSetting::get('mpesa_id_suffix', ''),
            '{customer_id}' => str_pad($customer->id, SystemSetting::get('mpesa_id_padding', 4), '0', STR_PAD_LEFT),
            '{phone_last_4}' => substr(preg_replace('/[^0-9]/', '', $customer->phone), -4),
            '{phone_last_6}' => substr(preg_replace('/[^0-9]/', '', $customer->phone), -6),
            '{sequential}' => $this->getNextSequentialNumber(),
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $format);
    }

    /**
     * Get next sequential number for custom format.
     */
    protected function getNextSequentialNumber(): string
    {
        $startingNumber = SystemSetting::get('mpesa_id_starting_number', 1000);
        $padding = SystemSetting::get('mpesa_id_padding', 4);

        $lastCustomer = Customer::whereNotNull('mpesa_id')
            ->orderBy('id', 'desc')
            ->first();

        $nextNumber = $lastCustomer ? $lastCustomer->id + 1 : $startingNumber;

        return str_pad($nextNumber, $padding, '0', STR_PAD_LEFT);
    }

    /**
     * Validate M-Pesa ID uniqueness and format.
     */
    public function validateMpesaId(string $mpesaId, ?int $excludeCustomerId = null): void
    {
        // Check uniqueness
        $query = Customer::where('mpesa_id', $mpesaId);
        if ($excludeCustomerId) {
            $query->where('id', '!=', $excludeCustomerId);
        }

        if ($query->exists()) {
            throw new \InvalidArgumentException("M-Pesa ID '{$mpesaId}' is already in use.");
        }

        // Validate format
        $minLength = SystemSetting::get('mpesa_id_min_length', 3);
        $maxLength = SystemSetting::get('mpesa_id_max_length', 20);

        if (strlen($mpesaId) < $minLength || strlen($mpesaId) > $maxLength) {
            throw new \InvalidArgumentException("M-Pesa ID must be between {$minLength} and {$maxLength} characters.");
        }

        // Check allowed characters
        $allowedPattern = SystemSetting::get('mpesa_id_allowed_pattern', '/^[A-Z0-9]+$/');
        if ($allowedPattern && ! preg_match($allowedPattern, $mpesaId)) {
            throw new \InvalidArgumentException('M-Pesa ID contains invalid characters. Only alphanumeric characters are allowed.');
        }
    }

    /**
     * Check if manual assignment is enabled.
     */
    public function isManualAssignmentEnabled(): bool
    {
        $mode = SystemSetting::get('mpesa_id_assignment_mode', 'auto');

        return in_array($mode, ['manual', 'hybrid']);
    }

    /**
     * Check if auto-generation is enabled.
     */
    public function isAutoGenerationEnabled(): bool
    {
        $mode = SystemSetting::get('mpesa_id_assignment_mode', 'auto');

        return in_array($mode, ['auto', 'hybrid']);
    }

    /**
     * Bulk generate M-Pesa IDs for existing customers.
     */
    public function bulkGenerateForExistingCustomers(): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        $customers = Customer::whereNull('mpesa_id')->get();

        foreach ($customers as $customer) {
            try {
                DB::beginTransaction();

                $mpesaId = $this->generateMpesaId($customer);
                $customer->mpesa_id = $mpesaId;
                $customer->save();

                DB::commit();
                $results['success']++;

                Log::info('Generated M-Pesa ID for existing customer', [
                    'customer_id' => $customer->id,
                    'customer_name' => $customer->name,
                    'mpesa_id' => $mpesaId,
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                $results['failed']++;
                $results['errors'][] = [
                    'customer_id' => $customer->id,
                    'customer_name' => $customer->name,
                    'error' => $e->getMessage(),
                ];

                Log::error('Failed to generate M-Pesa ID for customer', [
                    'customer_id' => $customer->id,
                    'customer_name' => $customer->name,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $results;
    }
}
