<?php

namespace App\Services;

use Illuminate\Support\Facades\Validator;

class ValidationService
{
    /**
     * Validate customer creation data
     */
    public static function validateCustomerData(array $data): ServiceResult
    {
        $rules = [
            'name' => 'required|string|max:255|min:2',
            'email' => 'required|email|max:255|unique:customers,email',
            'phone' => 'nullable|string|max:20|regex:/^\+[1-9]\d{1,14}$/',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'status' => 'in:active,inactive,suspended',
        ];

        $messages = [
            'name.required' => 'Customer name is required',
            'name.min' => 'Customer name must be at least 2 characters',
            'email.required' => 'Email address is required',
            'email.email' => 'Email address must be valid',
            'email.unique' => 'Email address is already registered',
            'phone.regex' => 'Phone number must be in international format (e.g., +254712345678)',
        ];

        return self::performValidation($data, $rules, $messages);
    }

    /**
     * Validate customer update data (email uniqueness ignored for same customer)
     */
    public static function validateCustomerUpdateData(array $data, int $customerId): ServiceResult
    {
        $rules = [
            'name' => 'required|string|max:255|min:2',
            'email' => "required|email|max:255|unique:customers,email,{$customerId}",
            'phone' => 'nullable|string|max:20|regex:/^\+[1-9]\d{1,14}$/',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'status' => 'in:active,inactive,suspended',
        ];

        return self::performValidation($data, $rules);
    }

    /**
     * Validate static IP service configuration
     */
    public static function validateStaticIpServiceData(array $data): ServiceResult
    {
        $rules = [
            'customer_id' => 'required|integer|exists:customers,id',
            'device_id' => 'required|integer|exists:network_devices,id',
            'ip_pool_id' => 'required|integer|exists:ip_pools,id',
            'ip_address' => 'nullable|ip',
            'bandwidth_plan_id' => 'nullable|integer|exists:bandwidth_plans,id',
            'comment' => 'nullable|string|max:1000',
        ];

        $messages = [
            'customer_id.required' => 'Customer selection is required',
            'customer_id.exists' => 'Selected customer does not exist',
            'device_id.required' => 'Network device selection is required',
            'device_id.exists' => 'Selected network device does not exist',
            'ip_pool_id.required' => 'IP pool selection is required',
            'ip_pool_id.exists' => 'Selected IP pool does not exist',
            'ip_address.ip' => 'IP address must be a valid IPv4 address',
        ];

        return self::performValidation($data, $rules, $messages);
    }

    /**
     * Validate PPPoE service configuration
     */
    public static function validatePppoeServiceData(array $data): ServiceResult
    {
        $rules = [
            'customer_id' => 'required|integer|exists:customers,id',
            'device_id' => 'required|integer|exists:network_devices,id',
            'username' => 'nullable|string|max:50|unique:pppoe_services,username',
            'password' => 'nullable|string|min:6|max:50',
            'bandwidth_plan_id' => 'nullable|integer|exists:bandwidth_plans,id',
            'comment' => 'nullable|string|max:1000',
        ];

        $messages = [
            'customer_id.required' => 'Customer selection is required',
            'customer_id.exists' => 'Selected customer does not exist',
            'device_id.required' => 'Network device selection is required',
            'device_id.exists' => 'Selected network device does not exist',
            'username.unique' => 'Username is already taken',
            'password.min' => 'Password must be at least 6 characters',
        ];

        return self::performValidation($data, $rules, $messages);
    }

    /**
     * Validate IP pool creation data
     */
    public static function validateIpPoolData(array $data): ServiceResult
    {
        $rules = [
            'name' => 'required|string|max:255|unique:ip_pools,name',
            'description' => 'nullable|string|max:1000',
            'device_id' => 'required|integer|exists:network_devices,id',
            'network_address' => 'required|ip',
            'subnet_mask' => 'required|ip',
            'gateway' => 'nullable|ip',
            'dns_servers' => 'nullable|array',
            'dns_servers.*' => 'ip',
            'interface' => 'required|string|max:50',
            'status' => 'in:active,inactive',
        ];

        $messages = [
            'name.required' => 'Pool name is required',
            'name.unique' => 'Pool name must be unique',
            'device_id.required' => 'Network device selection is required',
            'device_id.exists' => 'Selected network device does not exist',
            'network_address.required' => 'Network address is required',
            'network_address.ip' => 'Network address must be a valid IPv4 address',
            'subnet_mask.required' => 'Subnet mask is required',
            'subnet_mask.ip' => 'Subnet mask must be a valid IPv4 address',
            'gateway.ip' => 'Gateway must be a valid IPv4 address',
            'dns_servers.*.ip' => 'All DNS servers must be valid IPv4 addresses',
            'interface.required' => 'Interface name is required',
        ];

        return self::performValidation($data, $rules, $messages);
    }

    /**
     * Validate CSV import data structure
     */
    public static function validateImportData(array $data): ServiceResult
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'service_type' => 'required|in:static_ip,pppoe',
            'bandwidth_plan_id' => 'required|integer|exists:bandwidth_plans,id',
            'device_id' => 'required|integer|exists:network_devices,id',
            'network_site_id' => 'nullable|integer|exists:network_sites,id',
            'subscription_price' => 'nullable|numeric|min:0',
            'billing_cycle' => 'in:monthly,quarterly,yearly',
            'start_date' => 'required|date',
            'next_billing_date' => 'required|date|after_or_equal:start_date',
        ];

        // Add service-specific validation
        if (isset($data['service_type'])) {
            if ($data['service_type'] === 'static_ip') {
                $rules['ip_pool_id'] = 'required|integer|exists:ip_pools,id';
                $rules['ip_address'] = 'nullable|ip';
            } elseif ($data['service_type'] === 'pppoe') {
                $rules['username'] = 'nullable|string|max:50';
                $rules['password'] = 'nullable|string|min:6';
            }
        }

        return self::performValidation($data, $rules);
    }

    /**
     * Sanitize input data
     */
    public static function sanitizeData(array $data): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Trim whitespace
                $value = trim($value);

                // Remove null bytes
                $value = str_replace("\0", '', $value);

                // Handle specific field types
                switch ($key) {
                    case 'email':
                        $value = strtolower($value);
                        break;
                    case 'phone':
                        // Remove non-numeric except + and spaces
                        $value = preg_replace('/[^\d\+\s]/', '', $value);
                        // Remove spaces
                        $value = str_replace(' ', '', $value);
                        break;
                    case 'name':
                        // Remove extra spaces and capitalize properly
                        $value = preg_replace('/\s+/', ' ', $value);
                        $value = ucwords(strtolower($value));
                        break;
                    case 'ip_address':
                    case 'gateway':
                    case 'network_address':
                        // Validate IP format
                        if (! filter_var($value, FILTER_VALIDATE_IP)) {
                            $value = null;
                        }
                        break;
                }
            } elseif (is_array($value)) {
                $value = self::sanitizeData($value);
            }

            $sanitized[$key] = $value;
        }

        return $sanitized;
    }

    /**
     * Validate network-specific data
     */
    public static function validateNetworkData(array $data): ServiceResult
    {
        // Validate IP address is within pool range
        if (isset($data['ip_address']) && isset($data['ip_pool_id'])) {
            $ipPool = \App\Models\Services\IpPool::find($data['ip_pool_id']);
            if ($ipPool && ! self::isIpInNetwork($data['ip_address'], $ipPool->network_address, $ipPool->subnet_mask)) {
                return ServiceResult::validationError([
                    'ip_address' => 'IP address is not within the selected pool network range',
                ]);
            }
        }

        // Validate subnet mask format
        if (isset($data['subnet_mask'])) {
            if (! self::isValidSubnetMask($data['subnet_mask'])) {
                return ServiceResult::validationError([
                    'subnet_mask' => 'Invalid subnet mask format',
                ]);
            }
        }

        return ServiceResult::success();
    }

    /**
     * Check if IP is within network range
     */
    private static function isIpInNetwork(string $ip, string $network, string $subnetMask): bool
    {
        $ipLong = ip2long($ip);
        $networkLong = ip2long($network);
        $maskLong = ip2long($subnetMask);

        return ($ipLong & $maskLong) === ($networkLong & $maskLong);
    }

    /**
     * Validate subnet mask
     */
    private static function isValidSubnetMask(string $mask): bool
    {
        $maskLong = ip2long($mask);
        if ($maskLong === false) {
            return false;
        }

        // Check if it's a valid subnet mask (contiguous 1s followed by 0s)
        $binary = decbin($maskLong);

        return preg_match('/^1*0*$/', $binary) === 1;
    }

    /**
     * Perform validation using Laravel validator
     */
    private static function performValidation(array $data, array $rules, array $messages = []): ServiceResult
    {
        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            return ServiceResult::validationError(
                $validator->errors()->toArray(),
                'Validation failed'
            );
        }

        return ServiceResult::success($validator->validated());
    }
}
