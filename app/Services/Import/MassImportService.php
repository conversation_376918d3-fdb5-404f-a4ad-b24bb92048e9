<?php

namespace App\Services\Import;

use App\Jobs\Import\ProcessCustomerImportJob;
use App\Jobs\Import\ProcessIpPoolImportJob;
use App\Services\Import\Contracts\ImportableDataInterface;
use App\Services\Import\Contracts\MassImportServiceInterface;
use App\Services\Import\Data\CustomerImportData;
use App\Services\Import\Data\IpPoolImportData;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * Main mass import service for ISP customer migration
 * Handles queue-based processing of large datasets
 */
class MassImportService implements MassImportServiceInterface
{
    protected array $importStatus = [];

    /**
     * Process import with queue-based jobs
     */
    public function processImport(ImportableDataInterface $data, array $options = []): string
    {
        $batchId = $this->generateBatchId();
        $chunkSize = $options['chunk_size'] ?? $this->getDefaultChunkSize($data);

        Log::info('Starting mass import', [
            'batch_id' => $batchId,
            'data_type' => get_class($data),
            'record_count' => $data->getRecordCount(),
            'chunk_size' => $chunkSize,
        ]);

        // Validate data before processing
        if (! $data->validate()) {
            $errors = $data->getValidationErrors();
            Log::error('Import data validation failed', [
                'batch_id' => $batchId,
                'errors' => $errors,
            ]);

            throw new \InvalidArgumentException('Import data validation failed');
        }

        // Initialize import status
        $this->importStatus[$batchId] = [
            'status' => 'processing',
            'total_records' => $data->getRecordCount(),
            'processed_records' => 0,
            'successful_records' => 0,
            'failed_records' => 0,
            'started_at' => now(),
            'completed_at' => null,
            'errors' => [],
        ];

        // Dispatch jobs for each chunk
        $jobCount = 0;
        foreach ($data->getDataInChunks($chunkSize) as $chunk) {
            $this->dispatchImportJob($data, $chunk, $batchId);
            $jobCount++;
        }

        Log::info('Import jobs dispatched', [
            'batch_id' => $batchId,
            'job_count' => $jobCount,
        ]);

        return $batchId;
    }

    /**
     * Dispatch appropriate job based on data type
     */
    protected function dispatchImportJob(ImportableDataInterface $data, array $chunk, string $batchId): void
    {
        if ($data instanceof CustomerImportData) {
            ProcessCustomerImportJob::dispatch($chunk, $batchId);
        } elseif ($data instanceof IpPoolImportData) {
            ProcessIpPoolImportJob::dispatch($chunk, $batchId);
        } else {
            throw new \InvalidArgumentException('Unsupported import data type: '.get_class($data));
        }
    }

    /**
     * Get default chunk size based on data type
     */
    protected function getDefaultChunkSize(ImportableDataInterface $data): int
    {
        if ($data instanceof CustomerImportData) {
            return 50; // Customers are more complex, smaller chunks
        } elseif ($data instanceof IpPoolImportData) {
            return 5; // IP pools involve MikroTik operations, very small chunks
        }

        return 100; // Default chunk size
    }

    /**
     * Generate unique batch ID
     */
    protected function generateBatchId(): string
    {
        return 'import_'.now()->format('Ymd_His').'_'.Str::random(8);
    }

    /**
     * Get import status
     */
    public function getImportStatus(string $batchId): array
    {
        return $this->importStatus[$batchId] ?? [
            'status' => 'not_found',
            'message' => 'Import batch not found',
        ];
    }

    /**
     * Cancel ongoing import
     */
    public function cancelImport(string $batchId): bool
    {
        // Implementation would depend on queue system
        // For now, just mark as cancelled
        if (isset($this->importStatus[$batchId])) {
            $this->importStatus[$batchId]['status'] = 'cancelled';
            $this->importStatus[$batchId]['completed_at'] = now();

            return true;
        }

        return false;
    }

    /**
     * Clean up after import completion
     */
    public function cleanup(string $batchId): bool
    {
        unset($this->importStatus[$batchId]);

        return true;
    }

    /**
     * Import IP pools
     */
    public function importIpPools(IpPoolImportData $data, array $options = []): string
    {
        return $this->processImport($data, $options);
    }

    /**
     * Import customers
     */
    public function importCustomers(CustomerImportData $data, array $options = []): string
    {
        return $this->processImport($data, $options);
    }

    /**
     * Full migration process
     */
    public function processMigration(array $migrationData, array $options = []): array
    {
        $results = [];

        Log::info('Starting full migration process', [
            'components' => array_keys($migrationData),
        ]);

        // Step 1: Import IP pools first
        if (isset($migrationData['ip_pools'])) {
            $ipPoolData = $migrationData['ip_pools'];
            if ($ipPoolData instanceof IpPoolImportData) {
                $results['ip_pools'] = $this->importIpPools($ipPoolData, $options);
                Log::info('IP pools import initiated', ['batch_id' => $results['ip_pools']]);
            }
        }

        // Step 2: Import customers (after IP pools are created)
        if (isset($migrationData['customers'])) {
            $customerData = $migrationData['customers'];
            if ($customerData instanceof CustomerImportData) {
                // For now, skip the delay and process immediately
                // In production, you might want to add a delay or check if IP pools are ready
                $results['customers'] = $this->importCustomers($customerData, $options);
                Log::info('Customers import initiated', ['batch_id' => $results['customers']]);
            }
        }

        return $results;
    }

    /**
     * Create sample migration data for testing
     */
    public static function createSampleMigrationData(int $customerCount = 2000): array
    {
        // This will be used by the seeder
        return [
            'ip_pools' => IpPoolImportData::generateStandardPools(17, 3), // Use existing site and device
            'customers' => self::generateSampleCustomers($customerCount),
        ];
    }

    /**
     * Generate sample customer data for migration testing
     */
    protected static function generateSampleCustomers(int $count): CustomerImportData
    {
        $customers = [];
        $serviceTypes = ['static_ip', 'pppoe'];

        // Get actual bandwidth plan IDs from database
        $bandwidthPlans = \App\Models\Bandwidth\BandwidthPlan::pluck('id')->toArray();
        if (empty($bandwidthPlans)) {
            $bandwidthPlans = [28, 29, 30]; // Fallback to known IDs
        }

        $ipPools = [1, 2, 3, 4]; // Will be created by IP pool import

        for ($i = 1; $i <= $count; $i++) {
            $serviceType = $serviceTypes[array_rand($serviceTypes)];
            $isStaticIp = $serviceType === 'static_ip';

            $customers[] = [
                'name' => fake()->name(),
                'email' => fake()->unique()->safeEmail(),
                'phone' => fake()->phoneNumber(),
                'address' => fake()->streetAddress(),
                'city' => fake()->city(),
                'state' => fake()->state(),
                'postal_code' => fake()->postcode(),
                'country' => 'Kenya',
                'status' => 'active',
                'service_type' => $serviceType,
                'bandwidth_plan_id' => $bandwidthPlans[array_rand($bandwidthPlans)],
                'network_site_id' => 17, // Use existing site
                'device_id' => 3, // Use existing device
                'ip_pool_id' => $isStaticIp ? $ipPools[array_rand($ipPools)] : null,
                'subscription_price' => null, // Will use bandwidth plan price
                'billing_cycle' => 'monthly',
                'start_date' => now()->subDays(rand(1, 365))->toDateString(),
                'next_billing_date' => now()->addDays(rand(1, 30))->toDateString(),
                'external_platform' => 'migration_seeder',
                'import_batch' => 'migration_2024',
                'notes' => 'Imported customer from migration seeder',
            ];
        }

        return new CustomerImportData($customers);
    }
}
