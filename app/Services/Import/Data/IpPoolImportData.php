<?php

namespace App\Services\Import\Data;

use App\Services\Import\Contracts\ImportableDataInterface;
use Illuminate\Support\Facades\Validator;

/**
 * IP Pool import data structure
 * Handles mass IP pool creation for customer migration
 */
class IpPoolImportData implements ImportableDataInterface
{
    protected array $ipPools = [];

    protected array $validationErrors = [];

    protected bool $validated = false;

    public function __construct(array $ipPools = [])
    {
        $this->ipPools = $ipPools;
    }

    /**
     * Add an IP pool to the import data
     */
    public function addIpPool(array $poolData): self
    {
        $this->ipPools[] = $this->normalizeIpPoolData($poolData);
        $this->validated = false;

        return $this;
    }

    /**
     * Add multiple IP pools
     */
    public function addIpPools(array $pools): self
    {
        foreach ($pools as $pool) {
            $this->addIpPool($pool);
        }

        return $this;
    }

    /**
     * Normalize IP pool data to standard format
     */
    protected function normalizeIpPoolData(array $data): array
    {
        return [
            'name' => $data['name'] ?? $data['pool_name'] ?? '',
            'description' => $data['description'] ?? $data['pool_description'] ?? '',
            'network_site_id' => $data['network_site_id'] ?? $data['site_id'] ?? null,
            'device_id' => $data['device_id'] ?? null,
            'subnet' => $data['subnet'] ?? $data['network'] ?? '',
            'subnet_mask' => $data['subnet_mask'] ?? '*************',
            'gateway' => $data['gateway'] ?? null, // Will be auto-calculated if null
            'dns_primary' => $data['dns_primary'] ?? $data['dns1'] ?? '*******',
            'dns_secondary' => $data['dns_secondary'] ?? $data['dns2'] ?? '*******',
            'vlan_id' => $data['vlan_id'] ?? null,
            'interface_name' => $data['interface_name'] ?? $data['interface'] ?? null,
            'pool_type' => $data['pool_type'] ?? 'static', // static, dhcp, pppoe
            'status' => $data['status'] ?? 'active',

            // IP range configuration
            'start_ip' => $data['start_ip'] ?? null,
            'end_ip' => $data['end_ip'] ?? null,
            'exclude_ips' => $data['exclude_ips'] ?? [], // IPs to exclude from assignment

            // External platform mapping
            'external_id' => $data['external_id'] ?? $data['splynx_id'] ?? null,
            'external_platform' => $data['external_platform'] ?? 'manual',

            // Additional metadata
            'notes' => $data['notes'] ?? '',
            'import_batch' => $data['import_batch'] ?? null,
        ];
    }

    /**
     * Generate standard IP pools for ISP migration
     */
    public static function generateStandardPools(int $siteId, int $deviceId): self
    {
        $pools = [
            [
                'name' => 'Residential Pool A',
                'description' => 'Primary residential customer pool',
                'network_site_id' => $siteId,
                'device_id' => $deviceId,
                'subnet' => '*************/24',
                'gateway' => '*************',
                'interface_name' => 'ether11',
                'start_ip' => '*************0',
                'end_ip' => '*************00',
                'exclude_ips' => ['*************'], // Exclude gateway
            ],
            [
                'name' => 'Residential Pool B',
                'description' => 'Secondary residential customer pool',
                'network_site_id' => $siteId,
                'device_id' => $deviceId,
                'subnet' => '*************/24',
                'gateway' => '*************',
                'interface_name' => 'ether11',
                'start_ip' => '*************0',
                'end_ip' => '*************00',
                'exclude_ips' => ['*************'],
            ],
            [
                'name' => 'Business Pool A',
                'description' => 'Primary business customer pool',
                'network_site_id' => $siteId,
                'device_id' => $deviceId,
                'subnet' => '*************/24',
                'gateway' => '*************',
                'interface_name' => 'ether11',
                'start_ip' => '*************0',
                'end_ip' => '**************',
                'exclude_ips' => ['*************'],
            ],
            [
                'name' => 'Premium Pool',
                'description' => 'Premium customer pool with dedicated IPs',
                'network_site_id' => $siteId,
                'device_id' => $deviceId,
                'subnet' => '*************/24',
                'gateway' => '*************',
                'interface_name' => 'ether11',
                'start_ip' => '*************0',
                'end_ip' => '**************',
                'exclude_ips' => ['*************'],
            ],
        ];

        $instance = new self;
        foreach ($pools as $pool) {
            $instance->addIpPool($pool);
        }

        return $instance;
    }

    public function getData(): array
    {
        return $this->ipPools;
    }

    public function validate(): bool
    {
        if ($this->validated) {
            return empty($this->validationErrors);
        }

        $this->validationErrors = [];

        foreach ($this->ipPools as $index => $pool) {
            $validator = Validator::make($pool, [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:500',
                'network_site_id' => 'required|integer|exists:network_sites,id',
                'device_id' => 'required|integer|exists:network_devices,id',
                'subnet' => 'required|string',
                'subnet_mask' => 'required|ip',
                'gateway' => 'nullable|ip',
                'dns_primary' => 'required|ip',
                'dns_secondary' => 'required|ip',
                'vlan_id' => 'nullable|integer|min:1|max:4094',
                'interface_name' => 'nullable|string|max:50',
                'pool_type' => 'required|in:static,dhcp,pppoe',
                'status' => 'required|in:active,inactive',
                'start_ip' => 'nullable|ip',
                'end_ip' => 'nullable|ip',
                'exclude_ips' => 'array',
                'exclude_ips.*' => 'ip',
            ]);

            if ($validator->fails()) {
                $this->validationErrors["pool_{$index}"] = $validator->errors()->toArray();
            }
        }

        $this->validated = true;

        return empty($this->validationErrors);
    }

    public function getValidationErrors(): array
    {
        return $this->validationErrors;
    }

    public function getRecordCount(): int
    {
        return count($this->ipPools);
    }

    public function getDataInChunks(int $chunkSize = 10): \Generator
    {
        $chunks = array_chunk($this->ipPools, $chunkSize);
        foreach ($chunks as $chunk) {
            yield $chunk;
        }
    }

    /**
     * Create from external platform data
     */
    public static function fromExternalPlatform(array $data, string $platform = 'splynx'): self
    {
        $pools = [];

        foreach ($data as $record) {
            $pools[] = array_merge($record, [
                'external_platform' => $platform,
                'external_id' => $record['id'] ?? null,
            ]);
        }

        return new self($pools);
    }
}
