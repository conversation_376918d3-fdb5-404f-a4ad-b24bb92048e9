<?php

namespace App\Services\Import;

use App\Models\SystemSetting;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ExcelTemplateService
{
    /**
     * Generate standardized customer import template.
     */
    public function generateTemplate(): string
    {
        $spreadsheet = new Spreadsheet;
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Customer Import Template');

        // Define template structure
        $headers = $this->getTemplateHeaders();
        $examples = $this->getExampleData();
        $validationRules = $this->getValidationRules();

        // Set up headers
        $this->setupHeaders($sheet, $headers);

        // Add example data
        $this->addExampleData($sheet, $examples);

        // Add validation rules sheet
        $this->addValidationSheet($spreadsheet, $validationRules);

        // Add instructions sheet
        $this->addInstructionsSheet($spreadsheet);

        // Save template
        $filename = 'customer_import_template_v'.date('Y.m.d').'.xlsx';
        $filepath = storage_path('app/public/templates/'.$filename);

        // Ensure directory exists
        if (! file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        $writer = new Xlsx($spreadsheet);

        // Set compatibility options for better Google Sheets support
        $writer->setPreCalculateFormulas(false);
        $writer->setUseDiskCaching(false); // Disable disk caching for better compatibility
        $writer->setOffice2003Compatibility(true);

        $writer->save($filepath);

        return $filepath;
    }

    /**
     * Generate CSV template for better Google Sheets compatibility.
     */
    public function generateCsvTemplate(): string
    {
        $headers = $this->getTemplateHeaders();
        $examples = $this->getExampleData();

        $filename = 'customer_import_template_v'.date('Y.m.d').'.csv';
        $filepath = storage_path('app/public/templates/'.$filename);

        // Ensure directory exists
        if (! file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        $file = fopen($filepath, 'w');

        // Write headers
        fputcsv($file, array_values($headers), ',', '"', '\\');

        // Write example data
        foreach ($examples as $example) {
            fputcsv($file, array_values($example), ',', '"', '\\');
        }

        fclose($file);

        return $filepath;
    }

    /**
     * Get template headers based on M-Pesa assignment mode.
     */
    protected function getTemplateHeaders(): array
    {
        try {
            $assignmentMode = SystemSetting::get('mpesa_id_assignment_mode', 'hybrid');
        } catch (\Exception $e) {
            // Fallback if database is not available
            $assignmentMode = 'hybrid';
        }

        // Headers matching your existing CustomerImportData structure
        $baseHeaders = [
            'name' => 'Customer Name *',
            'email' => 'Customer Email *',
            'phone' => 'Customer Phone',
            'address' => 'Customer Address',
            'city' => 'Customer City',
            'state' => 'Customer State',
            'postal_code' => 'Postal Code',
            'country' => 'Country',
            'service_type' => 'Service Type *',
            'ip_address' => 'IP Address (Static Only)',
            'bandwidth_plan_id' => 'Bandwidth Plan ID *',
            'network_site_id' => 'Network Site ID',
            'device_id' => 'Device ID',
            'ip_pool_id' => 'IP Pool ID (Static Only)',
            'subscription_price' => 'Monthly Fee (KES) *',
            'billing_cycle' => 'Billing Cycle',
            'start_date' => 'Service Start Date *',
            'next_billing_date' => 'Next Billing Date',
            'username' => 'PPPoE Username',
            'password' => 'PPPoE Password',
            'external_id' => 'External ID',
            'notes' => 'Notes',
        ];

        // Add M-Pesa ID column based on assignment mode
        switch ($assignmentMode) {
            case 'manual':
                $baseHeaders['mpesa_id'] = 'M-Pesa ID *';
                break;
            case 'auto':
                $baseHeaders['mpesa_id'] = 'M-Pesa ID (Auto-Generated)';
                break;
            case 'hybrid':
                $baseHeaders['mpesa_id'] = 'M-Pesa ID (Optional)';
                break;
        }

        $baseHeaders['customer_status'] = 'Customer Status';
        $baseHeaders['service_status'] = 'Service Status';
        $baseHeaders['notes'] = 'Notes';

        return $baseHeaders;
    }

    /**
     * Get example data for the template.
     */
    protected function getExampleData(): array
    {
        try {
            $assignmentMode = SystemSetting::get('mpesa_id_assignment_mode', 'hybrid');
        } catch (\Exception $e) {
            // Fallback if database is not available
            $assignmentMode = 'hybrid';
        }

        // Example data using real IDs from your system
        return [
            [
                'name' => 'John Doe Smith',
                'email' => '<EMAIL>',
                'phone' => '+254712345678',
                'address' => '123 Main Street',
                'city' => 'Nairobi',
                'state' => 'Nairobi County',
                'postal_code' => '00100',
                'country' => 'Kenya',
                'service_type' => 'static_ip',
                'ip_address' => '**************',
                'bandwidth_plan_id' => '28', // Basic 10 Mbps
                'network_site_id' => '17', // Main Site
                'device_id' => '2', // Main Router (correct ID)
                'ip_pool_id' => '1', // Residential Pool A
                'subscription_price' => '1500.00',
                'billing_cycle' => 'monthly',
                'start_date' => '2025-01-15',
                'next_billing_date' => '2025-02-15',
                'username' => '',
                'password' => '',
                'external_id' => 'EXT001',
                'mpesa_id' => $assignmentMode === 'manual' ? 'MP001234' : ($assignmentMode === 'auto' ? '' : 'MP001234'),
                'notes' => 'VIP Customer',
            ],
            [
                'name' => 'Jane Wilson',
                'email' => '<EMAIL>',
                'phone' => '+254723456789',
                'address' => '456 Oak Avenue',
                'city' => 'Mombasa',
                'state' => 'Mombasa County',
                'postal_code' => '80100',
                'country' => 'Kenya',
                'service_type' => 'pppoe',
                'ip_address' => '',
                'bandwidth_plan_id' => '29', // Standard 25 Mbps
                'network_site_id' => '17', // Main Site
                'device_id' => '2', // Main Router (correct ID)
                'ip_pool_id' => '', // Not needed for PPPoE
                'subscription_price' => '2500.00',
                'billing_cycle' => 'monthly',
                'start_date' => '2025-02-01',
                'next_billing_date' => '2025-03-01',
                'username' => 'jane.wilson',
                'password' => 'secure123',
                'external_id' => 'EXT002',
                'mpesa_id' => $assignmentMode === 'manual' ? 'MP001235' : '',
                'notes' => '',
            ],
        ];
    }

    /**
     * Set up header row with styling.
     */
    protected function setupHeaders($sheet, array $headers): void
    {
        $column = 'A';
        foreach ($headers as $key => $header) {
            $sheet->setCellValue($column.'1', $header);

            // Style headers (simplified for better compatibility)
            $sheet->getStyle($column.'1')->applyFromArray([
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E6E6E6'],
                ],
            ]);

            // Auto-size columns
            $sheet->getColumnDimension($column)->setAutoSize(true);
            $column++;
        }

        // Set header row height
        $sheet->getRowDimension('1')->setRowHeight(25);
    }

    /**
     * Add example data to the template.
     */
    protected function addExampleData($sheet, array $examples): void
    {
        $row = 2;
        foreach ($examples as $example) {
            $column = 'A';
            foreach ($example as $value) {
                $sheet->setCellValue($column.$row, $value);

                // Style example rows (simplified)
                $sheet->getStyle($column.$row)->applyFromArray([
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => 'F8F8F8'],
                    ],
                ]);

                $column++;
            }
            $row++;
        }
    }

    /**
     * Add validation rules sheet.
     */
    protected function addValidationSheet($spreadsheet, array $rules): void
    {
        $validationSheet = $spreadsheet->createSheet();
        $validationSheet->setTitle('Validation Rules');

        // Headers
        $validationSheet->setCellValue('A1', 'Column Name');
        $validationSheet->setCellValue('B1', 'Required');
        $validationSheet->setCellValue('C1', 'Data Type');
        $validationSheet->setCellValue('D1', 'Format/Rules');
        $validationSheet->setCellValue('E1', 'Example');

        // Style headers
        $validationSheet->getStyle('A1:E1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '70AD47'],
            ],
        ]);

        // Add rules
        $row = 2;
        foreach ($rules as $rule) {
            $validationSheet->setCellValue('A'.$row, $rule['column']);
            $validationSheet->setCellValue('B'.$row, $rule['required']);
            $validationSheet->setCellValue('C'.$row, $rule['type']);
            $validationSheet->setCellValue('D'.$row, $rule['format']);
            $validationSheet->setCellValue('E'.$row, $rule['example']);
            $row++;
        }

        // Auto-size columns
        foreach (range('A', 'E') as $column) {
            $validationSheet->getColumnDimension($column)->setAutoSize(true);
        }
    }

    /**
     * Add instructions sheet.
     */
    protected function addInstructionsSheet($spreadsheet): void
    {
        $instructionsSheet = $spreadsheet->createSheet();
        $instructionsSheet->setTitle('Instructions');

        $instructions = $this->getInstructions();

        $row = 1;
        foreach ($instructions as $instruction) {
            $instructionsSheet->setCellValue('A'.$row, $instruction);
            $instructionsSheet->getStyle('A'.$row)->getAlignment()->setWrapText(true);
            $row++;
        }

        $instructionsSheet->getColumnDimension('A')->setWidth(100);
    }

    /**
     * Get validation rules.
     */
    protected function getValidationRules(): array
    {
        return [
            [
                'column' => 'customer_name',
                'required' => 'Yes',
                'type' => 'Text',
                'format' => '2-100 characters, letters/spaces only',
                'example' => 'John Doe Smith',
            ],
            [
                'column' => 'customer_email',
                'required' => 'Yes',
                'type' => 'Email',
                'format' => 'Valid email format, must be unique',
                'example' => '<EMAIL>',
            ],
            [
                'column' => 'customer_phone',
                'required' => 'Yes',
                'type' => 'Phone',
                'format' => 'International format with country code',
                'example' => '+254712345678',
            ],
            [
                'column' => 'service_type',
                'required' => 'Yes',
                'type' => 'Text',
                'format' => 'static_ip or pppoe',
                'example' => 'static_ip',
            ],
            [
                'column' => 'ip_address',
                'required' => 'If Static IP',
                'type' => 'IP Address',
                'format' => 'Valid IPv4 address',
                'example' => '*************',
            ],
            [
                'column' => 'monthly_fee',
                'required' => 'Yes',
                'type' => 'Number',
                'format' => 'Decimal format (XXXX.XX)',
                'example' => '1500.00',
            ],
            [
                'column' => 'service_start_date',
                'required' => 'Yes',
                'type' => 'Date',
                'format' => 'YYYY-MM-DD format',
                'example' => '2025-01-15',
            ],
        ];
    }

    /**
     * Get instructions text.
     */
    protected function getInstructions(): array
    {
        try {
            $assignmentMode = SystemSetting::get('mpesa_id_assignment_mode', 'hybrid');
        } catch (\Exception $e) {
            // Fallback if database is not available
            $assignmentMode = 'hybrid';
        }

        $instructions = [
            'CUSTOMER IMPORT TEMPLATE - INSTRUCTIONS',
            '',
            '1. OVERVIEW',
            'This template is designed for importing customer data into the ISP Management Platform.',
            'All columns marked with * are required fields.',
            '',
            '2. BEFORE YOU START',
            '- Download the latest version of this template',
            '- Do not modify column headers',
            '- Remove example rows before importing your data',
            '- Save file as .xlsx format',
            '',
            '3. DATA REQUIREMENTS',
            '- Customer emails must be unique across all customers',
            '- Phone numbers must include country code (+254 for Kenya)',
            '- Dates must be in YYYY-MM-DD format',
            '- IP addresses are required only for static_ip service type',
            '- Monthly fees should be in decimal format (1500.00)',
            '',
            '4. M-PESA ID SETTINGS',
        ];

        switch ($assignmentMode) {
            case 'manual':
                $instructions[] = 'Current Mode: MANUAL - M-Pesa ID is REQUIRED and must be unique';
                $instructions[] = 'Provide a unique M-Pesa ID for each customer';
                break;
            case 'auto':
                $instructions[] = 'Current Mode: AUTO - M-Pesa ID will be automatically generated';
                $instructions[] = 'Leave M-Pesa ID column empty (values will be ignored)';
                break;
            case 'hybrid':
                $instructions[] = 'Current Mode: HYBRID - M-Pesa ID is optional';
                $instructions[] = 'Provide M-Pesa ID if available, otherwise it will be auto-generated';
                break;
        }

        $instructions = array_merge($instructions, [
            '',
            '5. SERVICE TYPES',
            '- static_ip: Customer gets a fixed IP address (ip_address required)',
            '- pppoe: Customer connects via PPPoE (leave ip_address empty)',
            '',
            '6. VALIDATION',
            'See "Validation Rules" sheet for detailed field requirements',
            '',
            '7. SUPPORT',
            'For questions or issues, contact: <EMAIL>',
        ]);

        return $instructions;
    }
}
