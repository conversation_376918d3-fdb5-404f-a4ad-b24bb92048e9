<?php

namespace App\Services\Import\Contracts;

/**
 * Interface for importable data structures
 * Designed to support multiple data sources (seeders, Excel, CSV, API)
 */
interface ImportableDataInterface
{
    /**
     * Get the data in a standardized format for import processing
     */
    public function getData(): array;

    /**
     * Validate the data structure before import
     */
    public function validate(): bool;

    /**
     * Get validation errors if any
     */
    public function getValidationErrors(): array;

    /**
     * Get the total number of records to import
     */
    public function getRecordCount(): int;

    /**
     * Get data in chunks for batch processing
     */
    public function getDataInChunks(int $chunkSize = 100): \Generator;
}
