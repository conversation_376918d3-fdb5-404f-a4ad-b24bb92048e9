<?php

namespace App\Services\Import\Contracts;

/**
 * Interface for mass import services
 * Supports different import types (customers, services, etc.)
 */
interface MassImportServiceInterface
{
    /**
     * Process the import with queue-based jobs
     */
    public function processImport(ImportableDataInterface $data, array $options = []): string;

    /**
     * Get import progress/status
     */
    public function getImportStatus(string $batchId): array;

    /**
     * Cancel an ongoing import
     */
    public function cancelImport(string $batchId): bool;

    /**
     * Clean up after import completion
     */
    public function cleanup(string $batchId): bool;
}
