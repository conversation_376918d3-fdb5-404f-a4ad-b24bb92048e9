<?php

namespace App\Services;

use App\Models\Network\IpAddress;
use App\Models\Network\NetworkDevice;
use App\Models\Network\NetworkInterface;
use App\Models\Services\IpPool;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IpPoolService
{
    /**
     * Create IP pool and all IP addresses in MikroTik.
     */
    public function createIpPool(array $data): array
    {
        $device = NetworkDevice::findOrFail($data['device_id']);

        try {
            DB::beginTransaction();

            // Step 1: Sync interfaces from MikroTik first
            $interfaceSync = NetworkInterface::syncFromDevice($device);
            if (! $interfaceSync['success']) {
                throw new \Exception('Failed to sync interfaces: '.$interfaceSync['error']);
            }

            // Step 2: Validate interface exists
            $interface = NetworkInterface::where('device_id', $device->id)
                ->where('name', $data['interface'])
                ->first();

            if (! $interface) {
                throw new \Exception("Interface '{$data['interface']}' not found on device");
            }

            // Step 3: Create IP pool in database
            $pool = IpPool::create([
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'network_cidr' => $data['network_cidr'] ?? null,
                'cidr_range' => $data['cidr_range'] ?? $data['network_cidr'], // Store original CIDR for range calculation
                'network_address' => $data['network_address'],
                'subnet_mask' => $data['subnet_mask'], // Always ************* for private networks
                'gateway' => $data['gateway'] ?? null,
                'dns_servers' => $data['dns_servers'] ?? [],
                'device_id' => $device->id,
                'interface' => $data['interface'],
                'status' => 'active',
                'addresses_created' => false,
                'excluded_ips' => $data['excluded_ips'] ?? [],
            ]);

            // Step 4: Generate and create IP addresses
            $result = $this->createIpAddressesInPool($pool);

            if (! $result['success']) {
                throw new \Exception('Failed to create IP addresses: '.$result['error']);
            }

            // Step 5: Update pool statistics
            $pool->update([
                'addresses_created' => true,
                'total_addresses' => $result['total_created'],
                'available_addresses' => $result['available'],
                'assigned_addresses' => 0,
                'creation_log' => $result['log'],
            ]);

            DB::commit();

            Log::info('IP pool created successfully', [
                'pool_id' => $pool->id,
                'pool_name' => $pool->name,
                'device_id' => $device->id,
                'interface' => $data['interface'],
                'total_addresses' => $result['total_created'],
                'available_addresses' => $result['available'],
            ]);

            return [
                'success' => true,
                'pool' => $pool,
                'addresses_created' => $result['total_created'],
                'available_addresses' => $result['available'],
                'interface_sync' => $interfaceSync,
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create IP pool', [
                'device_id' => $device->id,
                'interface' => $data['interface'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create IP address for a pool (single subnet IP, not individual IPs).
     */
    public function createIpAddressesInPool(IpPool $pool): array
    {
        try {
            $device = $pool->device;
            // Use the user's specified CIDR prefix length for MikroTik configuration
            $subnetMask = $this->getSubnetMaskBits($pool->subnet_mask);

            // Use gateway IP or first usable IP for the subnet
            $gatewayIp = $pool->gateway ?: $this->getFirstUsableIp($pool->network_address, $subnetMask);
            $fullAddress = "{$gatewayIp}/{$subnetMask}";

            // Get existing IP addresses from MikroTik
            $existingIps = $this->getExistingIpsFromMikroTik($device);

            $log = [];
            $created = 0;
            $skipped = 0;
            $errors = 0;

            // Check if this subnet already exists in MikroTik
            $existsInMikroTik = $this->checkSubnetExistsInMikroTik($existingIps, $gatewayIp, $subnetMask);

            if ($existsInMikroTik) {
                $skipped = 1;
                $log[] = "Skipped {$fullAddress} (subnet already exists in MikroTik)";
            } else {
                // Create the single IP address in MikroTik
                $result = $this->createIpInMikroTik($device, $fullAddress, $pool->interface);

                if ($result['success']) {
                    $created = 1;
                    $log[] = "Created {$fullAddress} in MikroTik";
                } else {
                    $errors = 1;
                    $log[] = "Failed to create {$fullAddress}: ".$result['error'];
                }
            }

            // Create database records for tracking (but only create the main IP in MikroTik)
            $this->createDatabaseRecordsForPool($pool, $subnetMask, $existsInMikroTik || $created > 0);

            // Count available addresses based on the subnet mask (which now correctly reflects the CIDR)
            $totalIps = pow(2, (32 - $subnetMask));
            $available = $totalIps - 3; // Subtract network, gateway, and broadcast

            return [
                'success' => true,
                'total_created' => $created,
                'skipped' => $skipped,
                'errors' => $errors,
                'available' => $available,
                'log' => $log,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'total_created' => 0,
                'available' => 0,
                'log' => [],
            ];
        }
    }

    /**
     * Get existing IP addresses from MikroTik.
     */
    private function getExistingIpsFromMikroTik(NetworkDevice $device): array
    {
        try {
            $addresses = $device->executeMikrotikCommand('/ip/address/print');
            $existingIps = [];

            foreach ($addresses as $address) {
                if (isset($address['address'])) {
                    $existingIps[] = $address['address'];
                }
            }

            return $existingIps;

        } catch (\Exception $e) {
            Log::warning('Failed to get existing IPs from MikroTik', [
                'device_id' => $device->id,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Create IP address in MikroTik.
     */
    private function createIpInMikroTik(NetworkDevice $device, string $address, string $interface): array
    {
        try {
            $response = $device->executeMikrotikCommand('/ip/address/add', [
                'address' => $address,
                'interface' => $interface,
            ]);

            return [
                'success' => true,
                'mikrotik_id' => $response[0]['.id'] ?? null,
                'response' => $response,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Determine IP address type.
     */
    private function getIpType(int $index, int $totalIps, IpPool $pool): string
    {
        if ($index === 0) {
            return 'network';
        }

        if ($index === 1 && $pool->gateway) {
            return 'gateway';
        }

        if ($index === $totalIps - 1) {
            return 'broadcast';
        }

        return 'usable';
    }

    /**
     * Get CIDR notation from pool.
     */
    private function getCidrFromPool(IpPool $pool): string
    {
        $bits = $this->getSubnetMaskBits($pool->subnet_mask);

        return "{$pool->network_address}/{$bits}";
    }

    /**
     * Convert subnet mask to bits.
     */
    private function getSubnetMaskBits(string $subnetMask): int
    {
        $binary = '';
        foreach (explode('.', $subnetMask) as $octet) {
            $binary .= str_pad(decbin((int) $octet), 8, '0', STR_PAD_LEFT);
        }

        return substr_count($binary, '1');
    }

    /**
     * Get first usable IP in a network.
     */
    private function getFirstUsableIp(string $networkAddress, int $subnetMask): string
    {
        $networkLong = ip2long($networkAddress);
        $firstUsableLong = $networkLong + 1; // Skip network address

        return long2ip($firstUsableLong);
    }

    /**
     * Check if subnet exists in MikroTik.
     */
    private function checkSubnetExistsInMikroTik(array $existingIps, string $gatewayIp, int $subnetMask): bool
    {
        $targetAddress = "{$gatewayIp}/{$subnetMask}";

        foreach ($existingIps as $existingIp) {
            if ($existingIp === $targetAddress) {
                return true;
            }

            // Also check if any IP in the same subnet exists
            if (strpos($existingIp, '/') !== false) {
                [$ip, $mask] = explode('/', $existingIp);
                if ($mask == $subnetMask && $this->ipInSameSubnet($ip, $gatewayIp, $subnetMask)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if two IPs are in the same subnet.
     */
    private function ipInSameSubnet(string $ip1, string $ip2, int $subnetMask): bool
    {
        $ip1Long = ip2long($ip1);
        $ip2Long = ip2long($ip2);
        $maskLong = -1 << (32 - $subnetMask);

        return ($ip1Long & $maskLong) === ($ip2Long & $maskLong);
    }

    /**
     * Create database records for IP pool tracking.
     */
    private function createDatabaseRecordsForPool(IpPool $pool, int $subnetMask, bool $existsInMikroTik): void
    {
        $networkLong = ip2long($pool->network_address);
        $totalIps = pow(2, (32 - $subnetMask));

        // Create a few key records for tracking
        $records = [
            [
                'ip' => $pool->network_address,
                'type' => 'network',
                'status' => 'reserved',
            ],
            [
                'ip' => $pool->gateway ?: long2ip($networkLong + 1),
                'type' => 'gateway',
                'status' => 'reserved',
            ],
            [
                'ip' => long2ip($networkLong + $totalIps - 1),
                'type' => 'broadcast',
                'status' => 'reserved',
            ],
        ];

        foreach ($records as $record) {
            IpAddress::create([
                'ip_pool_id' => $pool->id,
                'ip_address' => $record['ip'],
                'subnet_mask' => (string) $subnetMask,
                'full_address' => "{$record['ip']}/{$subnetMask}",
                'status' => $record['status'],
                'type' => $record['type'],
                'exists_in_mikrotik' => $existsInMikroTik && $record['type'] === 'gateway',
            ]);
        }
    }
}
