<?php

namespace App\Services;

class ServiceResult
{
    private bool $success;

    private string $message;

    private mixed $data;

    private array $errors;

    private int $code;

    public function __construct(
        bool $success = true,
        string $message = '',
        mixed $data = null,
        array $errors = [],
        int $code = 200
    ) {
        $this->success = $success;
        $this->message = $message;
        $this->data = $data;
        $this->errors = $errors;
        $this->code = $code;
    }

    /**
     * Create a successful result
     */
    public static function success(mixed $data = null, string $message = 'Operation completed successfully'): self
    {
        return new self(true, $message, $data);
    }

    /**
     * Create an error result
     */
    public static function error(string $message = 'Operation failed', array $errors = [], int $code = 400): self
    {
        return new self(false, $message, null, $errors, $code);
    }

    /**
     * Create a validation error result
     */
    public static function validationError(array $errors, string $message = 'Validation failed'): self
    {
        return new self(false, $message, null, $errors, 422);
    }

    /**
     * Create a not found result
     */
    public static function notFound(string $message = 'Resource not found'): self
    {
        return new self(false, $message, null, [], 404);
    }

    /**
     * Create an unauthorized result
     */
    public static function unauthorized(string $message = 'Unauthorized access'): self
    {
        return new self(false, $message, null, [], 401);
    }

    /**
     * Check if the operation was successful
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * Check if the operation failed
     */
    public function isError(): bool
    {
        return ! $this->success;
    }

    /**
     * Get the result message
     */
    public function getMessage(): string
    {
        return $this->message;
    }

    /**
     * Get the result data
     */
    public function getData(): mixed
    {
        return $this->data;
    }

    /**
     * Get validation errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get the HTTP status code
     */
    public function getCode(): int
    {
        return $this->code;
    }

    /**
     * Set additional data
     */
    public function setData(mixed $data): self
    {
        $this->data = $data;

        return $this;
    }

    /**
     * Add an error
     */
    public function addError(string $field, string $message): self
    {
        $this->errors[$field] = $message;

        return $this;
    }

    /**
     * Convert to array format
     */
    public function toArray(): array
    {
        $result = [
            'success' => $this->success,
            'message' => $this->message,
        ];

        if ($this->data !== null) {
            $result['data'] = $this->data;
        }

        if (! empty($this->errors)) {
            $result['errors'] = $this->errors;
        }

        return $result;
    }

    /**
     * Convert to JSON response format
     */
    public function toJsonResponse(): \Illuminate\Http\JsonResponse
    {
        return response()->json($this->toArray(), $this->code);
    }

    /**
     * Convert to redirect response with flash messages
     */
    public function toRedirectResponse(string $route, array $parameters = []): \Illuminate\Http\RedirectResponse
    {
        $redirect = redirect()->route($route, $parameters);

        if ($this->success) {
            $redirect->with('success', $this->message);
            if ($this->data) {
                $redirect->with('data', $this->data);
            }
        } else {
            $redirect->with('error', $this->message);
            if (! empty($this->errors)) {
                $redirect->withErrors($this->errors);
            }
        }

        return $redirect;
    }

    /**
     * Throw exception if operation failed
     */
    public function throwIfError(): self
    {
        if ($this->isError()) {
            throw new \Exception($this->message, $this->code);
        }

        return $this;
    }

    /**
     * Chain multiple operations
     */
    public function then(callable $callback): self
    {
        if ($this->isSuccess()) {
            return $callback($this);
        }

        return $this;
    }

    /**
     * Handle error case
     */
    public function catch(callable $callback): self
    {
        if ($this->isError()) {
            return $callback($this);
        }

        return $this;
    }
}
