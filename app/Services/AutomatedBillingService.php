<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AutomatedBillingService
{
    /**
     * Generate invoice for a subscription with pro-rated billing support.
     */
    public function generateInvoice(Subscription $subscription, ?Carbon $billingDate = null): ?Invoice
    {
        $billingDate = $billingDate ?? now();

        try {
            DB::beginTransaction();

            // Check if customer has already been billed for this period
            if ($this->hasExistingInvoiceForPeriod($subscription, $billingDate)) {
                Log::info('Skipping invoice generation - customer already billed for this period', [
                    'subscription_id' => $subscription->id,
                    'billing_date' => $billingDate->toDateString(),
                ]);
                DB::rollBack();

                return null;
            }

            // Calculate billing period
            $billingPeriod = $this->calculateBillingPeriod($subscription, $billingDate);

            // Calculate pro-rated amount
            $billingCalculation = $this->calculateProRatedAmount($subscription, $billingPeriod);

            if ($billingCalculation['amount'] <= 0) {
                Log::info('Skipping invoice generation - no billable amount', [
                    'subscription_id' => $subscription->id,
                    'billing_period' => $billingPeriod,
                    'calculation' => $billingCalculation,
                ]);
                DB::rollBack();

                return null;
            }

            // Generate invoice number
            $invoiceNumber = $this->generateInvoiceNumber($billingDate);

            // Create invoice
            $invoice = Invoice::create([
                'customer_id' => $subscription->customer_id,
                'subscription_id' => $subscription->id,
                'invoice_number' => $invoiceNumber,
                'issue_date' => $billingDate,
                'due_date' => $billingDate->copy()->addDays($subscription->grace_period_days ?? 7),
                'billing_period_start' => $billingPeriod['start'],
                'billing_period_end' => $billingPeriod['end'],
                'is_prorated' => $billingCalculation['is_prorated'],
                'active_days' => $billingCalculation['active_days'],
                'total_days' => $billingCalculation['total_days'],
                'auto_generated' => true,
                'billing_details' => json_encode($billingCalculation['details']),
                'amount' => $billingCalculation['amount'],
                'tax_amount' => 0, // TODO: Add tax calculation if needed
                'total_amount' => $billingCalculation['amount'],
                'status' => 'pending',
            ]);

            // Create invoice item
            InvoiceItem::create([
                'invoice_id' => $invoice->id,
                'description' => $this->generateInvoiceDescription($subscription, $billingCalculation),
                'quantity' => 1,
                'unit_price' => $billingCalculation['amount'],
                'subtotal' => $billingCalculation['amount'],
                'total' => $billingCalculation['amount'],
            ]);

            // Update only last billing date - next billing date will be updated when payment is received
            $subscription->update([
                'last_billing_date' => $billingDate,
            ]);

            // Record billing history
            $this->recordBillingHistory($subscription, $invoice, 'billed', $billingDate, $billingPeriod, $billingCalculation);

            DB::commit();

            Log::info('Invoice generated successfully', [
                'invoice_id' => $invoice->id,
                'subscription_id' => $subscription->id,
                'amount' => $billingCalculation['amount'],
                'is_prorated' => $billingCalculation['is_prorated'],
            ]);

            return $invoice;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to generate invoice', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Calculate billing period for subscription.
     */
    protected function calculateBillingPeriod(Subscription $subscription, Carbon $billingDate): array
    {
        $periodStart = $subscription->last_billing_date
            ? $subscription->last_billing_date->copy()->addDay()
            : $subscription->start_date;

        $periodEnd = $this->calculatePeriodEnd($periodStart, $subscription->billing_cycle);

        return [
            'start' => $periodStart,
            'end' => $periodEnd,
        ];
    }

    /**
     * Calculate pro-rated amount based on active days.
     */
    protected function calculateProRatedAmount(Subscription $subscription, array $billingPeriod): array
    {
        $totalDays = $billingPeriod['start']->diffInDays($billingPeriod['end']) + 1;

        // Calculate suspension days within this billing period
        $suspensionDaysInPeriod = 0;

        // If subscription has suspension tracking, calculate days suspended in this period
        if ($subscription->suspension_date && $subscription->reactivation_date) {
            $suspensionStart = max($subscription->suspension_date, $billingPeriod['start']);
            $suspensionEnd = min($subscription->reactivation_date, $billingPeriod['end']);

            if ($suspensionStart <= $suspensionEnd) {
                $suspensionDaysInPeriod = $suspensionStart->diffInDays($suspensionEnd) + 1;
            }
        } elseif ($subscription->suspension_date && ! $subscription->reactivation_date) {
            // Still suspended - calculate from suspension date to end of period
            $suspensionStart = max($subscription->suspension_date, $billingPeriod['start']);
            $suspensionDaysInPeriod = $suspensionStart->diffInDays($billingPeriod['end']) + 1;
        } else {
            // Use the accumulated suspension days if no specific dates
            $suspensionDaysInPeriod = min($subscription->suspension_days ?? 0, $totalDays);
        }

        $activeDays = max(0, $totalDays - $suspensionDaysInPeriod);

        $fullAmount = $subscription->price;
        $proRatedAmount = $activeDays > 0 ? ($fullAmount * $activeDays) / $totalDays : 0;

        $isProrated = $suspensionDaysInPeriod > 0;

        return [
            'amount' => round($proRatedAmount, 2),
            'full_amount' => $fullAmount,
            'active_days' => $activeDays,
            'total_days' => $totalDays,
            'suspension_days' => $suspensionDaysInPeriod,
            'is_prorated' => $isProrated,
            'details' => [
                'billing_period' => $billingPeriod,
                'suspension_tracking' => [
                    'suspension_date' => $subscription->suspension_date?->toDateString(),
                    'reactivation_date' => $subscription->reactivation_date?->toDateString(),
                    'accumulated_suspension_days' => $subscription->suspension_days ?? 0,
                ],
                'calculation' => [
                    'full_price' => $fullAmount,
                    'total_days' => $totalDays,
                    'active_days' => $activeDays,
                    'suspension_days_in_period' => $suspensionDaysInPeriod,
                    'daily_rate' => round($fullAmount / $totalDays, 4),
                    'prorated_amount' => $proRatedAmount,
                ],
            ],
        ];
    }

    /**
     * Calculate period end based on billing cycle.
     */
    protected function calculatePeriodEnd(Carbon $periodStart, string $billingCycle): Carbon
    {
        switch ($billingCycle) {
            case 'monthly':
                return $periodStart->copy()->addMonth()->subDay();
            case 'quarterly':
                return $periodStart->copy()->addMonths(3)->subDay();
            case 'yearly':
                return $periodStart->copy()->addYear()->subDay();
            default:
                return $periodStart->copy()->addMonth()->subDay();
        }
    }

    /**
     * Calculate next billing date.
     */
    protected function calculateNextBillingDate(Subscription $subscription, Carbon $periodEnd): Carbon
    {
        return $periodEnd->copy()->addDay();
    }

    /**
     * Check if subscription already has an invoice for the current billing period.
     */
    protected function hasExistingInvoiceForPeriod(Subscription $subscription, Carbon $billingDate): bool
    {
        $billingPeriod = $this->calculateBillingPeriod($subscription, $billingDate);

        // Check for existing invoices that overlap with this billing period
        $existingInvoice = Invoice::where('subscription_id', $subscription->id)
            ->where(function ($query) use ($billingPeriod) {
                $query->where(function ($q) use ($billingPeriod) {
                    // Invoice period starts within our billing period
                    $q->whereBetween('billing_period_start', [$billingPeriod['start'], $billingPeriod['end']]);
                })->orWhere(function ($q) use ($billingPeriod) {
                    // Invoice period ends within our billing period
                    $q->whereBetween('billing_period_end', [$billingPeriod['start'], $billingPeriod['end']]);
                })->orWhere(function ($q) use ($billingPeriod) {
                    // Invoice period completely encompasses our billing period
                    $q->where('billing_period_start', '<=', $billingPeriod['start'])
                        ->where('billing_period_end', '>=', $billingPeriod['end']);
                });
            })
            ->exists();

        if ($existingInvoice) {
            Log::info('Found existing invoice for billing period', [
                'subscription_id' => $subscription->id,
                'billing_period_start' => $billingPeriod['start']->toDateString(),
                'billing_period_end' => $billingPeriod['end']->toDateString(),
            ]);
        }

        return $existingInvoice;
    }

    /**
     * Update subscription's next billing date when payment is received.
     * This should be called when an invoice is marked as paid.
     */
    public function updateNextBillingDateOnPayment(Invoice $invoice): void
    {
        if (! $invoice->subscription_id) {
            Log::warning('Cannot update billing date - invoice has no subscription', [
                'invoice_id' => $invoice->id,
            ]);

            return;
        }

        $subscription = $invoice->subscription;
        if (! $subscription) {
            Log::warning('Cannot update billing date - subscription not found', [
                'invoice_id' => $invoice->id,
                'subscription_id' => $invoice->subscription_id,
            ]);

            return;
        }

        // Calculate next billing date based on the invoice's billing period
        $billingPeriodEnd = Carbon::parse($invoice->billing_period_end);
        $nextBillingDate = $this->calculateNextBillingDate($subscription, $billingPeriodEnd);

        $subscription->update([
            'next_billing_date' => $nextBillingDate,
        ]);

        Log::info('Updated next billing date after payment', [
            'subscription_id' => $subscription->id,
            'invoice_id' => $invoice->id,
            'previous_next_billing_date' => $subscription->getOriginal('next_billing_date'),
            'new_next_billing_date' => $nextBillingDate->toDateString(),
        ]);

        // Record billing history for the payment
        $this->recordBillingHistory(
            $subscription,
            $invoice,
            'paid',
            now(),
            [
                'start' => Carbon::parse($invoice->billing_period_start),
                'end' => $billingPeriodEnd,
            ],
            [
                'amount' => $invoice->total_amount,
                'active_days' => $invoice->active_days ?? 0,
                'total_days' => $invoice->total_days ?? 0,
                'details' => [
                    'payment_date' => now(),
                    'next_billing_date_updated' => $nextBillingDate->toDateString(),
                ],
            ]
        );
    }

    /**
     * Generate unique invoice number.
     */
    protected function generateInvoiceNumber(Carbon $date): string
    {
        $prefix = 'INV';
        $dateStr = $date->format('Ymd');

        // Find the highest existing sequence number for today
        $lastInvoice = Invoice::where('invoice_number', 'like', "{$prefix}-{$dateStr}-%")
            ->orderByRaw('CAST(SUBSTR(invoice_number, -4) AS INTEGER) DESC')
            ->first();

        $sequence = 1;
        if ($lastInvoice) {
            $lastSequence = (int) substr($lastInvoice->invoice_number, -4);
            $sequence = $lastSequence + 1;
        }

        return sprintf('%s-%s-%04d', $prefix, $dateStr, $sequence);
    }

    /**
     * Generate invoice description.
     */
    protected function generateInvoiceDescription(Subscription $subscription, array $calculation): string
    {
        $description = $subscription->name;

        if ($calculation['is_prorated']) {
            $description .= sprintf(
                ' (Pro-rated: %d/%d days)',
                $calculation['active_days'],
                $calculation['total_days']
            );
        }

        return $description;
    }

    /**
     * Record billing history.
     */
    protected function recordBillingHistory(
        Subscription $subscription,
        Invoice $invoice,
        string $action,
        Carbon $actionDate,
        array $billingPeriod,
        array $calculation
    ): void {
        DB::table('subscription_billing_history')->insert([
            'subscription_id' => $subscription->id,
            'invoice_id' => $invoice->id,
            'action' => $action,
            'action_date' => $actionDate,
            'period_start' => $billingPeriod['start'],
            'period_end' => $billingPeriod['end'],
            'amount' => $calculation['amount'],
            'active_days' => $calculation['active_days'],
            'total_days' => $calculation['total_days'],
            'metadata' => json_encode($calculation['details']),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Update subscription suspension tracking.
     */
    public function recordSuspension(Subscription $subscription, Carbon $suspensionDate): void
    {
        $subscription->update([
            'suspension_date' => $suspensionDate,
            'reactivation_date' => null,
        ]);

        $this->recordBillingHistory(
            $subscription,
            null,
            'suspended',
            $suspensionDate,
            ['start' => $suspensionDate, 'end' => null],
            ['amount' => 0, 'active_days' => 0, 'total_days' => 0, 'details' => ['suspension_date' => $suspensionDate]]
        );
    }

    /**
     * Update subscription reactivation tracking and calculate suspension days.
     */
    public function recordReactivation(Subscription $subscription, Carbon $reactivationDate): void
    {
        $suspensionDays = 0;

        if ($subscription->suspension_date) {
            $suspensionDays = $subscription->suspension_date->diffInDays($reactivationDate);
        }

        $subscription->update([
            'reactivation_date' => $reactivationDate,
            'suspension_days' => ($subscription->suspension_days ?? 0) + $suspensionDays,
            'next_billing_date' => $reactivationDate->copy()->addDays(30), // Extend by 30 days from reactivation
        ]);

        $this->recordBillingHistory(
            $subscription,
            null,
            'reactivated',
            $reactivationDate,
            ['start' => $subscription->suspension_date, 'end' => $reactivationDate],
            [
                'amount' => 0,
                'active_days' => 0,
                'total_days' => $suspensionDays,
                'details' => [
                    'suspension_date' => $subscription->suspension_date,
                    'reactivation_date' => $reactivationDate,
                    'suspension_days' => $suspensionDays,
                ],
            ]
        );
    }
}
