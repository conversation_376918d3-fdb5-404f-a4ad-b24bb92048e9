<?php

namespace App\Services;

use App\Models\Network\NetworkDevice;
use Illuminate\Support\Facades\Log;

/**
 * Simplified MikroTik Address List Service for Static IP Suspension
 *
 * This service provides streamlined suspension/reactivation operations:
 * - 1 API call per operation (add for suspension, remove for reactivation)
 * - Uses stored MikroTik IDs for reliable removal
 * - No unnecessary checks or complex logic
 */
class MikrotikAddressListService
{
    /**
     * Standard address list name for suspended static IP customers.
     */
    const SUSPENDED_STATIC_CUSTOMERS_LIST = 'suspended_static_customers';

    /**
     * Suspend a customer by adding their IP to the address list.
     * Returns the MikroTik entry ID for later removal.
     */
    public function suspendCustomer(NetworkDevice $device, string $ipAddress, string $customerName = ''): ?string
    {
        try {
            $result = $device->executeMikrotikCommand('/ip/firewall/address-list/add', [
                'list' => self::SUSPENDED_STATIC_CUSTOMERS_LIST,
                'address' => $ipAddress.'/32',
                'comment' => $customerName ? "SUSPENDED: {$customerName}" : "SUSPENDED: {$ipAddress}",
            ]);

            // Extract the MikroTik ID from the response
            // MikroTik add operations return: {"after": {"ret": "*ID"}}
            $mikrotikId = $result['after']['ret'] ?? null;

            if (! $mikrotikId) {
                throw new \Exception('MikroTik did not return an entry ID');
            }

            Log::info('Customer suspended successfully', [
                'device_id' => $device->id,
                'ip_address' => $ipAddress,
                'customer_name' => $customerName,
                'mikrotik_id' => $mikrotikId,
            ]);

            return $mikrotikId;
        } catch (\Exception $e) {
            Log::error('Failed to suspend customer', [
                'device_id' => $device->id,
                'ip_address' => $ipAddress,
                'customer_name' => $customerName,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Reactivate a customer by removing their IP from the address list using stored MikroTik ID.
     */
    public function reactivateCustomer(NetworkDevice $device, string $mikrotikId, string $customerName = ''): bool
    {
        try {
            $device->executeMikrotikCommand('/ip/firewall/address-list/remove', [
                '.id' => $mikrotikId,
            ]);

            Log::info('Customer reactivated successfully', [
                'device_id' => $device->id,
                'mikrotik_id' => $mikrotikId,
                'customer_name' => $customerName,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to reactivate customer', [
                'device_id' => $device->id,
                'mikrotik_id' => $mikrotikId,
                'customer_name' => $customerName,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get all suspended IP addresses from the address list (utility method).
     */
    public function getSuspendedIpAddresses(NetworkDevice $device): array
    {
        try {
            $entries = $device->executeMikrotikCommand('/ip/firewall/address-list/print', [
                '?list' => self::SUSPENDED_STATIC_CUSTOMERS_LIST,
            ]);

            $suspendedIps = [];
            foreach ($entries as $entry) {
                $suspendedIps[] = [
                    'id' => $entry['.id'],
                    'address' => $entry['address'] ?? '',
                    'comment' => $entry['comment'] ?? '',
                ];
            }

            return $suspendedIps;
        } catch (\Exception $e) {
            Log::error('Failed to get suspended IP addresses', [
                'device_id' => $device->id,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }
}
