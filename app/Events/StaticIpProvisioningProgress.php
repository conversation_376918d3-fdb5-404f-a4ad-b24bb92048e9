<?php

namespace App\Events;

use App\Models\Services\StaticIpService;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StaticIpProvisioningProgress implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public StaticIpService $service,
        public string $sessionId,
        public string $step,
        public string $message,
        public int $currentStep,
        public int $totalSteps = 4
    ) {}

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("static-ip-provisioning.{$this->sessionId}"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        $progress = $this->totalSteps > 0 ? round(($this->currentStep / $this->totalSteps) * 100, 2) : 0;

        return [
            'service_id' => $this->service->id,
            'session_id' => $this->sessionId,
            'step' => $this->step,
            'message' => $this->message,
            'current_step' => $this->currentStep,
            'total_steps' => $this->totalSteps,
            'progress' => $progress,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'provisioning.progress';
    }
}
