<?php

namespace App\Events;

use App\Models\Services\StaticIpService;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StaticIpProvisioningCompleted implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public StaticIpService $service,
        public string $sessionId,
        public bool $success = true,
        public ?string $errorMessage = null
    ) {}

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("static-ip-provisioning.{$this->sessionId}"),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'service_id' => $this->service->id,
            'session_id' => $this->sessionId,
            'step' => $this->success ? 'completed' : 'failed',
            'message' => $this->success
                ? 'Provisioning completed successfully!'
                : "Provisioning failed: {$this->errorMessage}",
            'progress' => $this->success ? 100 : 0,
            'success' => $this->success,
            'error_message' => $this->errorMessage,
            'timestamp' => now()->toISOString(),
            'service' => [
                'id' => $this->service->id,
                'ip_address' => $this->service->ip_address,
                'status' => $this->service->status,
            ],
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return $this->success ? 'provisioning.completed' : 'provisioning.failed';
    }
}
