<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'invoice_id',
        'description',
        'quantity',
        'unit_price',
        'tax_rate',
        'tax_amount',
        'subtotal',
        'total',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'total' => 'decimal:2',
    ];

    /**
     * Get the invoice that owns the item.
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Calculate the subtotal for this item.
     */
    public function calculateSubtotal(): void
    {
        $this->subtotal = $this->quantity * $this->unit_price;
        $this->save();
    }

    /**
     * Calculate the tax amount for this item.
     */
    public function calculateTaxAmount(): void
    {
        $this->tax_amount = $this->subtotal * ($this->tax_rate / 100);
        $this->save();
    }

    /**
     * Calculate the total for this item.
     */
    public function calculateTotal(): void
    {
        $this->total = $this->subtotal + $this->tax_amount;
        $this->save();
    }

    /**
     * Calculate all amounts for this item.
     */
    public function calculateAmounts(): void
    {
        $this->calculateSubtotal();
        $this->calculateTaxAmount();
        $this->calculateTotal();
    }
}
