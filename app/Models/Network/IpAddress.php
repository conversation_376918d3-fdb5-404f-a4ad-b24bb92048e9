<?php

namespace App\Models\Network;

use App\Models\Services\IpPool;
use App\Models\Services\StaticIpService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IpAddress extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'ip_pool_id',
        'ip_address',
        'subnet_mask',
        'full_address',
        'status',
        'type',
        'exists_in_mikrotik',
        'mikrotik_id',
        'assigned_to_service_id',
        'assigned_at',
        'mikrotik_response',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'exists_in_mikrotik' => 'boolean',
        'assigned_at' => 'datetime',
        'mikrotik_response' => 'array',
    ];

    /**
     * Get the IP pool that owns this address.
     */
    public function ipPool()
    {
        return $this->belongsTo(IpPool::class);
    }

    /**
     * Get the static IP service this address is assigned to.
     */
    public function staticIpService()
    {
        return $this->belongsTo(StaticIpService::class, 'assigned_to_service_id');
    }

    /**
     * Scope for available addresses.
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    /**
     * Scope for assigned addresses.
     */
    public function scopeAssigned($query)
    {
        return $query->where('status', 'assigned');
    }

    /**
     * Scope for usable addresses (not network, gateway, or broadcast).
     */
    public function scopeUsable($query)
    {
        return $query->where('type', 'usable');
    }

    /**
     * Scope for addresses that exist in MikroTik.
     */
    public function scopeExistsInMikrotik($query)
    {
        return $query->where('exists_in_mikrotik', true);
    }

    /**
     * Mark address as assigned to a service.
     */
    public function assignTo(StaticIpService $service)
    {
        $this->update([
            'status' => 'assigned',
            'assigned_to_service_id' => $service->id,
            'assigned_at' => now(),
        ]);

        return $this;
    }

    /**
     * Mark address as available.
     */
    public function markAsAvailable()
    {
        $this->update([
            'status' => 'available',
            'assigned_to_service_id' => null,
            'assigned_at' => null,
        ]);

        return $this;
    }

    /**
     * Mark as existing in MikroTik.
     */
    public function markAsExistsInMikrotik($mikrotikId = null, $response = null)
    {
        $this->update([
            'exists_in_mikrotik' => true,
            'mikrotik_id' => $mikrotikId,
            'mikrotik_response' => $response,
        ]);

        return $this;
    }

    /**
     * Get the next available IP address in a pool.
     */
    public static function getNextAvailable(IpPool $pool)
    {
        return static::where('ip_pool_id', $pool->id)
            ->available()
            ->usable()
            ->orderBy('ip_address')
            ->first();
    }

    /**
     * Check if IP address is valid format.
     */
    public static function isValidIpAddress($ip)
    {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) !== false;
    }
}
