<?php

namespace App\Models\Network;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class NetworkMap extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'site_id',
        'background_image',
        'width',
        'height',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'width' => 'integer',
        'height' => 'integer',
        'settings' => 'json',
    ];

    /**
     * Get the site that owns the map.
     */
    public function site(): BelongsTo
    {
        return $this->belongsTo(NetworkSite::class, 'site_id');
    }

    /**
     * Get the items on the map.
     */
    public function items(): HasMany
    {
        return $this->hasMany(NetworkMapItem::class, 'map_id');
    }

    /**
     * Get the devices on the map.
     */
    public function devices()
    {
        return $this->items()->where('itemable_type', NetworkDevice::class)
            ->with('itemable');
    }

    /**
     * Add a device to the map.
     */
    public function addDevice(NetworkDevice $device, $x, $y, $scale = 1.0, $rotation = 0, $settings = null)
    {
        return $this->items()->create([
            'itemable_type' => get_class($device),
            'itemable_id' => $device->id,
            'x_position' => $x,
            'y_position' => $y,
            'scale' => $scale,
            'rotation' => $rotation,
            'settings' => $settings,
        ]);
    }

    /**
     * Get the setting value for a specific key.
     */
    public function getSettingValue($key, $default = null)
    {
        $settings = $this->settings ?? [];

        return $settings[$key] ?? $default;
    }

    /**
     * Set a setting value.
     */
    public function setSettingValue($key, $value)
    {
        $settings = $this->settings ?? [];
        $settings[$key] = $value;
        $this->settings = $settings;

        return $this;
    }

    /**
     * Get the background image URL.
     */
    public function getBackgroundImageUrlAttribute()
    {
        if (! $this->background_image) {
            return null;
        }

        return asset('storage/'.$this->background_image);
    }

    /**
     * Get all connections between devices on this map.
     */
    public function getConnectionsAttribute()
    {
        $deviceIds = $this->devices()->pluck('itemable_id')->toArray();

        if (empty($deviceIds)) {
            return collect();
        }

        $interfaceIds = NetworkInterface::whereIn('device_id', $deviceIds)->pluck('id')->toArray();

        return NetworkConnection::where(function ($query) use ($interfaceIds) {
            $query->whereIn('source_interface_id', $interfaceIds)
                ->whereIn('target_interface_id', $interfaceIds);
        })->get();
    }
}
