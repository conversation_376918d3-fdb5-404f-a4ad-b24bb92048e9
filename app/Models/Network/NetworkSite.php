<?php

namespace App\Models\Network;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class NetworkSite extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'address',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        // No special casting needed for simplified fields
    ];

    // Removed parent/child site relationships for simplicity

    /**
     * Get all devices at this site.
     */
    public function devices(): HasMany
    {
        return $this->hasMany(NetworkDevice::class, 'site_id');
    }

    /**
     * Get all maps for this site.
     */
    public function maps(): HasMany
    {
        return $this->hasMany(NetworkMap::class, 'site_id');
    }

    /**
     * Scope a query to only include active sites.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Removed location-based scopes and hierarchical methods since those fields were removed
}
