<?php

namespace App\Models\Network;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class NetworkInterface extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'device_id',
        'name',
        'type',
        'status',
        'ip_address',
        'subnet_mask',
        'mac_address',
        'speed',
        'is_management',
        'running',
        'enabled',
        'comment',
        'mikrotik_id',
        'mikrotik_data',
        'last_synced_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'speed' => 'integer',
        'is_management' => 'boolean',
        'running' => 'boolean',
        'enabled' => 'boolean',
        'mikrotik_data' => 'array',
        'last_synced_at' => 'datetime',
    ];

    /**
     * Get the device that owns the interface.
     */
    public function device(): BelongsTo
    {
        return $this->belongsTo(NetworkDevice::class, 'device_id');
    }

    /**
     * Get the outgoing connections from this interface.
     */
    public function outgoingConnections(): HasMany
    {
        return $this->hasMany(NetworkConnection::class, 'source_interface_id');
    }

    /**
     * Get the incoming connections to this interface.
     */
    public function incomingConnections(): HasMany
    {
        return $this->hasMany(NetworkConnection::class, 'target_interface_id');
    }

    /**
     * Scope a query to only include interfaces with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include interfaces of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to only include management interfaces.
     */
    public function scopeManagement($query)
    {
        return $query->where('is_management', true);
    }

    /**
     * Scope a query to only include up interfaces.
     */
    public function scopeUp($query)
    {
        return $query->where('status', 'up');
    }

    /**
     * Scope a query to only include down interfaces.
     */
    public function scopeDown($query)
    {
        return $query->where('status', 'down');
    }

    /**
     * Get all connections (both incoming and outgoing) for this interface.
     */
    public function getAllConnectionsAttribute()
    {
        return $this->outgoingConnections->merge($this->incomingConnections);
    }

    /**
     * Get the CIDR notation for the IP address and subnet mask.
     */
    public function getCidrAttribute()
    {
        if (! $this->ip_address || ! $this->subnet_mask) {
            return null;
        }

        $mask = $this->subnetMaskToCidr($this->subnet_mask);

        return "{$this->ip_address}/{$mask}";
    }

    /**
     * Convert a subnet mask to CIDR notation.
     */
    protected function subnetMaskToCidr($subnetMask)
    {
        $binary = '';
        foreach (explode('.', $subnetMask) as $octet) {
            $binary .= str_pad(decbin($octet), 8, '0', STR_PAD_LEFT);
        }

        return substr_count($binary, '1');
    }

    /**
     * Scope for enabled interfaces.
     */
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    /**
     * Scope for running interfaces.
     */
    public function scopeRunning($query)
    {
        return $query->where('running', true);
    }

    /**
     * Scope for usable interfaces (enabled and running).
     */
    public function scopeUsable($query)
    {
        return $query->where('enabled', true)->where('running', true);
    }

    /**
     * Get display name for interface.
     */
    public function getDisplayNameAttribute()
    {
        $status = [];
        if (! $this->enabled) {
            $status[] = 'disabled';
        }
        if (! $this->running) {
            $status[] = 'down';
        }

        $statusText = empty($status) ? '' : ' ('.implode(', ', $status).')';

        return $this->name.$statusText;
    }

    /**
     * Sync interfaces from MikroTik device.
     */
    public static function syncFromDevice(NetworkDevice $device)
    {
        try {
            // Get interfaces from MikroTik
            $interfaces = $device->executeMikrotikCommand('/interface/print');

            $synced = 0;
            $created = 0;
            $updated = 0;

            foreach ($interfaces as $interface) {
                $data = [
                    'device_id' => $device->id,
                    'name' => $interface['name'],
                    'type' => $interface['type'] ?? null,
                    'running' => isset($interface['running']) && $interface['running'] === 'true',
                    'enabled' => ! isset($interface['disabled']) || $interface['disabled'] !== 'true',
                    'comment' => $interface['comment'] ?? null,
                    'mikrotik_id' => $interface['.id'] ?? null,
                    'mikrotik_data' => $interface,
                    'last_synced_at' => now(),
                ];

                $existing = static::where('device_id', $device->id)
                    ->where('name', $interface['name'])
                    ->first();

                if ($existing) {
                    $existing->update($data);
                    $updated++;
                } else {
                    static::create($data);
                    $created++;
                }

                $synced++;
            }

            return [
                'success' => true,
                'synced' => $synced,
                'created' => $created,
                'updated' => $updated,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'synced' => 0,
                'created' => 0,
                'updated' => 0,
            ];
        }
    }

    /**
     * Get usable interfaces for a device.
     */
    public static function getUsableForDevice(NetworkDevice $device)
    {
        return static::where('device_id', $device->id)
            ->usable()
            ->orderBy('name')
            ->get();
    }
}
