<?php

namespace App\Models\Network;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use RouterOS\Client;
use RouterOS\Query;

class NetworkDevice extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'ip_address',
        'status',
        'site_id',
        'api_username',
        'api_password',
        'api_port',
        'detected_model',
        'detected_version',
        'last_connected_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'api_port' => 'integer',
        'last_connected_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'api_password',
    ];

    /**
     * Get the site that owns the device.
     */
    public function site(): BelongsTo
    {
        return $this->belongsTo(NetworkSite::class, 'site_id');
    }

    /**
     * Get the interfaces for the device.
     */
    public function interfaces(): HasMany
    {
        return $this->hasMany(NetworkInterface::class, 'device_id');
    }

    /**
     * Get the map items for the device.
     */
    public function mapItems(): MorphMany
    {
        return $this->morphMany(NetworkMapItem::class, 'itemable');
    }

    /**
     * Get the static IP services for this device.
     */
    public function staticIpServices(): HasMany
    {
        return $this->hasMany(\App\Models\Services\StaticIpService::class, 'device_id');
    }

    /**
     * Get the PPPoE services for this device.
     */
    public function pppoeServices(): HasMany
    {
        return $this->hasMany(\App\Models\Services\PppoeService::class, 'device_id');
    }

    /**
     * Scope a query to only include active devices.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include devices with a detected model.
     */
    public function scopeByDetectedModel($query, $model)
    {
        return $query->where('detected_model', 'like', "%{$model}%");
    }

    /**
     * Scope a query to only include MikroTik devices (all devices are MikroTik now).
     */
    public function scopeMikroTik($query)
    {
        // Since all devices are now MikroTik, this just returns the query as-is
        // but provides a semantic method for clarity
        return $query;
    }

    /**
     * Get the management interface for the device.
     */
    public function getManagementInterfaceAttribute()
    {
        return $this->interfaces()->where('is_management', true)->first();
    }

    /**
     * Get the configuration value for a specific key.
     */
    public function getConfigValue($key, $default = null)
    {
        $config = $this->configuration ?? [];

        return $config[$key] ?? $default;
    }

    /**
     * Set a configuration value.
     */
    public function setConfigValue($key, $value)
    {
        $config = $this->configuration ?? [];
        $config[$key] = $value;
        $this->configuration = $config;

        return $this;
    }

    /**
     * Test SNMP connectivity to the device.
     */
    public function testSnmpConnection(): bool
    {
        // Check if SNMP extension is available
        if (! function_exists('snmp2_get') && ! function_exists('snmpget')) {
            \Log::info('SNMP extension not available', [
                'device_id' => $this->id,
                'ip_address' => $this->ip_address,
            ]);

            return false;
        }

        try {
            $community = $this->getConfigValue('snmp_community', 'public');
            $version = $this->getConfigValue('snmp_version', '2c');

            if ($version === '1' && function_exists('snmpget')) {
                $result = snmpget($this->ip_address, $community, '*******.*******.0');
            } elseif (function_exists('snmp2_get')) {
                $result = snmp2_get($this->ip_address, $community, '*******.*******.0');
            } else {
                return false;
            }

            return $result !== false;
        } catch (\Exception $e) {
            \Log::warning('SNMP connection test failed', [
                'device_id' => $this->id,
                'ip_address' => $this->ip_address,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get SNMP system information.
     */
    public function getSnmpSystemInfo(): array
    {
        try {
            $community = $this->getConfigValue('snmp_community', 'public');
            $version = $this->getConfigValue('snmp_version', '2c');

            $info = [];

            if ($version === '1') {
                $info['sysDescr'] = snmpget($this->ip_address, $community, '*******.*******.0');
                $info['sysUpTime'] = snmpget($this->ip_address, $community, '*******.*******.0');
                $info['sysName'] = snmpget($this->ip_address, $community, '*******.*******.0');
            } else {
                $info['sysDescr'] = snmp2_get($this->ip_address, $community, '*******.*******.0');
                $info['sysUpTime'] = snmp2_get($this->ip_address, $community, '*******.*******.0');
                $info['sysName'] = snmp2_get($this->ip_address, $community, '*******.*******.0');
            }

            // Clean up the values
            foreach ($info as $key => $value) {
                $info[$key] = trim($value, '"');
            }

            return $info;
        } catch (\Exception $e) {
            \Log::warning('Failed to get SNMP system info', [
                'device_id' => $this->id,
                'ip_address' => $this->ip_address,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Get all connections associated with this device.
     */
    public function connections()
    {
        $interfaceIds = $this->interfaces()->pluck('id')->toArray();

        return NetworkConnection::where(function ($query) use ($interfaceIds) {
            $query->whereIn('source_interface_id', $interfaceIds)
                ->orWhereIn('target_interface_id', $interfaceIds);
        });
    }

    /**
     * Test connection to the device and fetch its capabilities.
     *
     * @return array|null The device capabilities or null if connection failed
     */
    public function testConnection()
    {
        if (! $this->ip_address) {
            // Return null if no IP address is available
            return null;
        }

        try {
            // Initialize capabilities array
            $capabilities = [
                'status' => 'success',
                'system_capabilities' => [
                    'hardware' => [],
                    'software' => [],
                    'memory' => [],
                    'storage' => [],
                ],
                'timestamp' => now()->toIso8601String(),
            ];

            // Since we're MikroTik-focused, always use RouterOS API
            $capabilities = $this->fetchMikrotikCapabilities();

            return $capabilities;
        } catch (\Exception $e) {
            \Log::error('Error connecting to device: '.$e->getMessage(), [
                'device_id' => $this->id,
                'ip_address' => $this->ip_address,
                'exception' => $e,
            ]);

            return [
                'status' => 'error',
                'message' => 'Failed to connect to device: '.$e->getMessage(),
                'timestamp' => now()->toIso8601String(),
            ];
        }
    }

    /**
     * Fetch capabilities from a MikroTik device using RouterOS API.
     *
     * @return array The device capabilities
     */
    private function fetchMikrotikCapabilities()
    {
        try {
            // Fetch data from the device using RouterOS API
            $cpuInfo = $this->executeMikrotikCommand('/system/resource/print');
            $identity = $this->executeMikrotikCommand('/system/identity/print');
            $interfaces = $this->executeMikrotikCommand('/interface/print');

            // Process the results
            // If multiple results are returned, use the first one
            $cpuInfoItem = is_array($cpuInfo) && ! empty($cpuInfo) ? $cpuInfo[0] : $cpuInfo;
            $identityItem = is_array($identity) && ! empty($identity) ? $identity[0] : $identity;

            return [
                'status' => 'success',
                'system_capabilities' => [
                    'hardware' => [
                        'CPU' => $cpuInfoItem['cpu'] ?? 'ARM',
                        'CPU Count' => $cpuInfoItem['cpu-count'] ?? 4,
                        'Architecture' => $cpuInfoItem['architecture-name'] ?? 'arm',
                    ],
                    'software' => [
                        'Identity' => $identityItem['name'] ?? $this->name ?: 'MikroTik',
                        'OS' => 'RouterOS',
                        'Version' => $cpuInfoItem['version'] ?? '6.48.6',
                    ],
                    'memory' => [
                        'Total' => $cpuInfoItem['total-memory'] ?? '1024 MB',
                        'Free' => $cpuInfoItem['free-memory'] ?? '512 MB',
                    ],
                    'storage' => [
                        'Total' => $cpuInfoItem['total-hdd-space'] ?? '16 GB',
                        'Free' => $cpuInfoItem['free-hdd-space'] ?? '8 GB',
                    ],
                    'interfaces' => $interfaces ?? [],
                ],
                'timestamp' => now()->toIso8601String(),
            ];
        } catch (\Exception $e) {
            \Log::error('Error fetching capabilities from Mikrotik device: '.$e->getMessage(), [
                'device_id' => $this->id,
                'ip_address' => $this->ip_address,
                'exception' => $e,
            ]);

            // Re-throw the exception to show the real error
            throw new \Exception('Failed to connect to MikroTik device: '.$e->getMessage());
        }
    }

    /**
     * Fetch interfaces directly from the device using RouterOS API.
     *
     * @return array The interfaces from the device
     *
     * @throws \Exception If connection to the device fails
     */
    public function fetchInterfacesFromDevice()
    {
        if (! $this->ip_address) {
            throw new \Exception('Device IP address is not set');
        }

        try {
            // Get credentials from simplified device fields
            $username = $this->api_username ?? config('services.mikrotik.username');
            $password = $this->api_password ? decrypt($this->api_password) : config('services.mikrotik.password');
            $port = $this->api_port ?? intval(config('services.mikrotik.port', 8728));

            // Create RouterOS API client with increased timeout for slow connections
            $client = new \RouterOS\Client([
                'host' => $this->ip_address,
                'user' => $username,
                'pass' => $password,
                'port' => $port,
                'timeout' => 30, // Increase timeout to 30 seconds for slow connections
            ]);

            // Create query for getting interfaces
            $query = new \RouterOS\Query('/interface/print');

            // Send query to RouterOS
            $interfaces = $client->query($query)->read();

            return $interfaces;
        } catch (\Exception $e) {
            \Log::error('Error connecting to Mikrotik device: '.$e->getMessage(), [
                'device_id' => $this->id,
                'ip_address' => $this->ip_address,
                'exception' => $e,
            ]);

            throw new \Exception('Failed to connect to device: '.$e->getMessage());
        }
    }

    /**
     * Execute a MikroTik RouterOS command using the RouterOS API.
     *
     * @param  string  $command  The command to execute (e.g., '/interface/print')
     * @param  array  $params  Optional parameters for the command
     * @return array The command result
     *
     * @throws \Exception If connection to the device fails
     */
    public function executeMikrotikCommand($command, $params = [])
    {
        if (! $this->ip_address) {
            throw new \Exception('Device IP address is not set');
        }

        try {
            // Get credentials from simplified device fields
            $username = $this->api_username ?? config('services.mikrotik.username');
            $password = $this->api_password ? decrypt($this->api_password) : config('services.mikrotik.password');
            $port = $this->api_port ?? intval(config('services.mikrotik.port', 8728));

            // Create RouterOS API client with increased timeout for slow connections
            $client = new \RouterOS\Client([
                'host' => $this->ip_address,
                'user' => $username,
                'pass' => $password,
                'port' => $port,
                'timeout' => 30, // Increase timeout to 30 seconds for slow connections
            ]);

            // Create query for the command
            $query = new \RouterOS\Query($command);

            // Add parameters to the query
            if (! empty($params)) {
                foreach ($params as $key => $value) {
                    if (strpos($key, '?') === 0) {
                        // This is a filter parameter (e.g., '?name' => 'username')
                        $query->where(substr($key, 1), $value);
                    } elseif ($key === '.id') {
                        // Special handling for .id parameter
                        $query->equal('.id', $value);
                    } else {
                        // Regular parameter
                        $query->equal($key, $value);
                    }
                }
            }

            // Send query to RouterOS
            $result = $client->query($query)->read();

            return $result;
        } catch (\Exception $e) {
            \Log::error('Error executing command on Mikrotik device: '.$e->getMessage(), [
                'device_id' => $this->id,
                'ip_address' => $this->ip_address,
                'command' => $command,
                'params' => $params,
                'exception' => $e,
            ]);

            // Re-throw the exception instead of falling back to simulation
            throw new \Exception('Failed to execute MikroTik command: '.$e->getMessage());
        }
    }

    /**
     * Simulate a command result for fallback when connection fails.
     *
     * @param  string  $command  The command to simulate
     * @param  array  $params  The parameters for the command
     * @return array The simulated command result
     */
    private function simulateCommandResult($command, $params = [])
    {
        if ($command === '/system/resource/print') {
            return [
                'cpu' => 'ARM',
                'cpu-count' => rand(1, 8),
                'cpu-frequency' => rand(600, 1200).' MHz',
                'cpu-load' => rand(0, 100),
                'free-hdd-space' => rand(5, 15).' GB',
                'total-hdd-space' => '16 GB',
                'free-memory' => rand(256, 768).' MB',
                'total-memory' => '1024 MB',
                'architecture-name' => 'arm',
                'board-name' => $this->model ?: 'RB4011',
                'version' => '6.'.rand(40, 49).'.'.rand(1, 9),
            ];
        } elseif ($command === '/system/identity/print') {
            return [
                'name' => $this->name ?: 'MikroTik Router',
            ];
        } elseif ($command === '/interface/print') {
            $interfaces = [];
            $interfaceTypes = ['ether', 'wlan', 'bridge', 'vlan'];

            for ($i = 1; $i <= rand(4, 8); $i++) {
                $type = $interfaceTypes[array_rand($interfaceTypes)];
                $interfaces[] = [
                    'name' => $type.$i,
                    'type' => $type,
                    'mtu' => 1500,
                    'actual-mtu' => 1500,
                    'mac-address' => $this->generateRandomMac(),
                    'running' => (rand(0, 10) > 2) ? 'true' : 'false', // 80% chance of being up
                ];
            }

            return $interfaces;
        }

        return [];
    }

    /**
     * Generate a random MAC address for simulation.
     *
     * @return string A random MAC address
     */
    private function generateRandomMac()
    {
        $mac = [];
        for ($i = 0; $i < 6; $i++) {
            $mac[] = sprintf('%02X', rand(0, 255));
        }

        return implode(':', $mac);
    }

    /**
     * Fetch capabilities from a Cisco device using SSH or SNMP.
     *
     * @return array The device capabilities
     */
    private function fetchCiscoCapabilities()
    {
        // Simulate network latency
        usleep(rand(100000, 500000)); // 100-500ms delay

        return [
            'status' => 'success',
            'system_capabilities' => [
                'hardware' => [
                    'CPU' => 'x86',
                    'CPU Count' => rand(1, 4),
                    'Architecture' => 'x86_64',
                ],
                'software' => [
                    'Identity' => $this->name ?: 'Cisco Device',
                    'OS' => 'IOS',
                    'Version' => '15.'.rand(0, 9).'('.rand(1, 9).')',
                ],
                'memory' => [
                    'Total' => rand(2, 8).' GB',
                    'Free' => rand(1, 4).' GB',
                ],
                'storage' => [
                    'Total' => rand(8, 32).' GB',
                    'Free' => rand(4, 16).' GB',
                ],
            ],
            'timestamp' => now()->toIso8601String(),
        ];
    }

    /**
     * Fetch capabilities from a Ubiquiti device.
     *
     * @return array The device capabilities
     */
    private function fetchUbiquitiCapabilities()
    {
        // Simulate network latency
        usleep(rand(100000, 500000)); // 100-500ms delay

        return [
            'status' => 'success',
            'system_capabilities' => [
                'hardware' => [
                    'CPU' => 'MIPS',
                    'CPU Count' => rand(1, 2),
                    'Architecture' => 'mips',
                ],
                'software' => [
                    'Identity' => $this->name ?: 'Ubiquiti Device',
                    'OS' => 'EdgeOS',
                    'Version' => '2.'.rand(0, 9).'.'.rand(1, 9),
                ],
                'memory' => [
                    'Total' => rand(512, 1024).' MB',
                    'Free' => rand(256, 512).' MB',
                ],
                'storage' => [
                    'Total' => rand(4, 8).' GB',
                    'Free' => rand(2, 4).' GB',
                ],
            ],
            'timestamp' => now()->toIso8601String(),
        ];
    }

    /**
     * Fetch capabilities from a device using SNMP.
     *
     * @return array The device capabilities
     */
    private function fetchSnmpCapabilities()
    {
        // Simulate network latency
        usleep(rand(100000, 500000)); // 100-500ms delay

        return [
            'status' => 'success',
            'system_capabilities' => [
                'hardware' => [
                    'CPU' => 'Generic',
                    'CPU Count' => rand(1, 4),
                    'Architecture' => 'unknown',
                ],
                'software' => [
                    'Identity' => $this->name ?: 'MikroTik Device',
                    'OS' => $this->getConfigValue('os', 'Generic OS'),
                    'Version' => rand(1, 9).'.'.rand(0, 9).'.'.rand(0, 9),
                ],
                'memory' => [
                    'Total' => rand(512, 4096).' MB',
                    'Free' => rand(256, 2048).' MB',
                ],
                'storage' => [
                    'Total' => rand(4, 16).' GB',
                    'Free' => rand(2, 8).' GB',
                ],
            ],
            'timestamp' => now()->toIso8601String(),
        ];
    }

    /**
     * Get the number of connected clients for a specific interface by ARP table.
     *
     * @param  string  $interfaceName
     * @return int
     */
    public function getArpClientsCountForInterface($interfaceName)
    {
        try {
            // Use MikroTik API filtering to only get ARP entries for the specific interface
            $arpEntries = $this->executeMikrotikCommand("/ip/arp/print interface={$interfaceName}");
            if (! is_array($arpEntries)) {
                return 0;
            }
            // Optionally, filter for only complete entries
            $clients = collect($arpEntries)->filter(function ($entry) {
                return ! isset($entry['invalid']) && (! isset($entry['complete']) || $entry['complete'] === 'true');
            });

            return $clients->count();
        } catch (\Exception $e) {
            \Log::error('Error fetching ARP clients for interface: '.$e->getMessage(), [
                'device_id' => $this->id,
                'interface' => $interfaceName,
                'exception' => $e,
            ]);

            return 0;
        }
    }
}
