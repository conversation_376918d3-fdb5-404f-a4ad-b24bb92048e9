<?php

namespace App\Models\Network;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NetworkConnection extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'source_interface_id',
        'target_interface_id',
        'type',
        'status',
        'bandwidth',
        'latency',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'bandwidth' => 'integer',
        'latency' => 'integer',
    ];

    /**
     * Get the source interface for the connection.
     */
    public function sourceInterface(): BelongsTo
    {
        return $this->belongsTo(NetworkInterface::class, 'source_interface_id');
    }

    /**
     * Get the target interface for the connection.
     */
    public function targetInterface(): BelongsTo
    {
        return $this->belongsTo(NetworkInterface::class, 'target_interface_id');
    }

    /**
     * Get the source device for the connection.
     */
    public function getSourceDeviceAttribute()
    {
        return $this->sourceInterface->device;
    }

    /**
     * Get the target device for the connection.
     */
    public function getTargetDeviceAttribute()
    {
        return $this->targetInterface->device;
    }

    /**
     * Scope a query to only include active connections.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include connections of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to only include connections with a minimum bandwidth.
     */
    public function scopeMinBandwidth($query, $bandwidth)
    {
        return $query->where('bandwidth', '>=', $bandwidth);
    }

    /**
     * Scope a query to only include connections with a maximum latency.
     */
    public function scopeMaxLatency($query, $latency)
    {
        return $query->where('latency', '<=', $latency);
    }

    /**
     * Scope a query to only include connections involving a specific device.
     */
    public function scopeInvolvingDevice($query, $deviceId)
    {
        $interfaceIds = NetworkInterface::where('device_id', $deviceId)->pluck('id')->toArray();

        return $query->where(function ($query) use ($interfaceIds) {
            $query->whereIn('source_interface_id', $interfaceIds)
                ->orWhereIn('target_interface_id', $interfaceIds);
        });
    }

    /**
     * Scope a query to only include connections between two specific devices.
     */
    public function scopeBetweenDevices($query, $sourceDeviceId, $targetDeviceId)
    {
        $sourceInterfaceIds = NetworkInterface::where('device_id', $sourceDeviceId)->pluck('id')->toArray();
        $targetInterfaceIds = NetworkInterface::where('device_id', $targetDeviceId)->pluck('id')->toArray();

        return $query->where(function ($query) use ($sourceInterfaceIds, $targetInterfaceIds) {
            $query->where(function ($query) use ($sourceInterfaceIds, $targetInterfaceIds) {
                $query->whereIn('source_interface_id', $sourceInterfaceIds)
                    ->whereIn('target_interface_id', $targetInterfaceIds);
            })->orWhere(function ($query) use ($sourceInterfaceIds, $targetInterfaceIds) {
                $query->whereIn('source_interface_id', $targetInterfaceIds)
                    ->whereIn('target_interface_id', $sourceInterfaceIds);
            });
        });
    }
}
