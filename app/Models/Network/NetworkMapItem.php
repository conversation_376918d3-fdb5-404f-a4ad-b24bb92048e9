<?php

namespace App\Models\Network;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class NetworkMapItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'map_id',
        'itemable_type',
        'itemable_id',
        'x_position',
        'y_position',
        'scale',
        'rotation',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'x_position' => 'float',
        'y_position' => 'float',
        'scale' => 'float',
        'rotation' => 'integer',
        'settings' => 'json',
    ];

    /**
     * Get the map that owns the item.
     */
    public function map(): BelongsTo
    {
        return $this->belongsTo(NetworkMap::class, 'map_id');
    }

    /**
     * Get the parent itemable model (device, etc.).
     */
    public function itemable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the setting value for a specific key.
     */
    public function getSettingValue($key, $default = null)
    {
        $settings = $this->settings ?? [];

        return $settings[$key] ?? $default;
    }

    /**
     * Set a setting value.
     */
    public function setSettingValue($key, $value)
    {
        $settings = $this->settings ?? [];
        $settings[$key] = $value;
        $this->settings = $settings;

        return $this;
    }

    /**
     * Move the item to a new position.
     */
    public function moveTo($x, $y)
    {
        $this->x_position = $x;
        $this->y_position = $y;

        return $this;
    }

    /**
     * Resize the item.
     */
    public function resize($scale)
    {
        $this->scale = $scale;

        return $this;
    }

    /**
     * Rotate the item.
     */
    public function rotate($rotation)
    {
        $this->rotation = $rotation;

        return $this;
    }

    /**
     * Get the item's label.
     */
    public function getLabelAttribute()
    {
        // Try to get a custom label from settings
        $label = $this->getSettingValue('label');

        if ($label) {
            return $label;
        }

        // Fall back to the itemable's name if available
        if ($this->itemable && method_exists($this->itemable, 'getAttribute') && $this->itemable->getAttribute('name')) {
            return $this->itemable->name;
        }

        // Last resort: use the class name and ID
        $className = class_basename($this->itemable_type);

        return "{$className} #{$this->itemable_id}";
    }
}
