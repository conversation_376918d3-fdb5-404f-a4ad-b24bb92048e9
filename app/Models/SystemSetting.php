<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'group',
        'is_public',
    ];

    protected $casts = [
        'is_public' => 'boolean',
    ];

    /**
     * Get a system setting value with optional default
     */
    public static function get(string $key, $default = null)
    {
        // Get setting directly from database without caching
        $setting = self::where('key', $key)->first();

        if (! $setting) {
            return $default;
        }

        // Cast the value to the appropriate type
        return match ($setting->type) {
            'boolean' => (bool) $setting->value,
            'integer' => (int) $setting->value,
            'float' => (float) $setting->value,
            'array', 'json' => json_decode($setting->value, true),
            default => $setting->value,
        };
    }

    /**
     * Set a system setting value
     */
    public static function set(string $key, $value, string $type = 'string', ?string $group = null): self
    {
        // Convert value to string for storage
        $stringValue = match ($type) {
            'boolean' => $value ? '1' : '0',
            'array', 'json' => json_encode($value),
            default => (string) $value,
        };

        $setting = self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $stringValue,
                'type' => $type,
                'group' => $group ?? 'general',
            ]
        );

        // No cache clearing needed since we don't cache
        return $setting;
    }

    /**
     * Get all settings for a specific group
     */
    public static function getGroup(string $group): array
    {
        // Get group settings directly from database without caching
        $settings = self::where('group', $group)->get();

        $result = [];
        foreach ($settings as $setting) {
            $result[$setting->key] = match ($setting->type) {
                'boolean' => (bool) $setting->value,
                'integer' => (int) $setting->value,
                'float' => (float) $setting->value,
                'array', 'json' => json_decode($setting->value, true),
                default => $setting->value,
            };
        }

        return $result;
    }

    /**
     * Clear cache when settings are updated
     */
    public static function clearCache(): void
    {
        // No cache to clear since we removed caching
    }

    /**
     * Boot method to clear cache on model events
     */
    protected static function boot()
    {
        parent::boot();

        // Remove cache clearing since we don't cache anymore
        static::saved(function ($setting) {
            // No cache clearing needed
        });

        static::deleted(function ($setting) {
            // No cache clearing needed
        });
    }
}
