<?php

namespace App\Models\Bandwidth;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class BandwidthPolicy extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'type',
        'parameters',
        'active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'parameters' => 'json',
        'active' => 'boolean',
    ];

    /**
     * Get the rules for the policy.
     */
    public function rules(): HasMany
    {
        return $this->hasMany(BandwidthRule::class, 'policy_id');
    }

    /**
     * Get the assignments for the policy.
     */
    public function assignments(): MorphMany
    {
        return $this->morphMany(BandwidthAssignment::class, 'assignable');
    }

    /**
     * Scope a query to only include active policies.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope a query to only include policies of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the parameter value for a specific key.
     */
    public function getParameterValue($key, $default = null)
    {
        $parameters = $this->parameters ?? [];

        return $parameters[$key] ?? $default;
    }

    /**
     * Set a parameter value.
     */
    public function setParameterValue($key, $value)
    {
        $parameters = $this->parameters ?? [];
        $parameters[$key] = $value;
        $this->parameters = $parameters;

        return $this;
    }

    /**
     * Add a rule to the policy.
     */
    public function addRule($attributes)
    {
        return $this->rules()->create($attributes);
    }

    /**
     * Assign this policy to a model.
     */
    public function assignTo($model, $startsAt = null, $endsAt = null)
    {
        return $this->assignments()->create([
            'assignee_type' => get_class($model),
            'assignee_id' => $model->id,
            'starts_at' => $startsAt,
            'ends_at' => $endsAt,
            'active' => true,
        ]);
    }
}
