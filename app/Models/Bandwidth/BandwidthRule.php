<?php

namespace App\Models\Bandwidth;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BandwidthRule extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'policy_id',
        'name',
        'description',
        'source_type',
        'source_value',
        'destination_type',
        'destination_value',
        'protocol',
        'port_range',
        'time_range',
        'priority',
        'active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'priority' => 'integer',
        'active' => 'boolean',
    ];

    /**
     * Get the policy that owns the rule.
     */
    public function policy(): BelongsTo
    {
        return $this->belongsTo(BandwidthPolicy::class, 'policy_id');
    }

    /**
     * Scope a query to only include active rules.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope a query to only include rules with a specific source type.
     */
    public function scopeWithSourceType($query, $type)
    {
        return $query->where('source_type', $type);
    }

    /**
     * Scope a query to only include rules with a specific destination type.
     */
    public function scopeWithDestinationType($query, $type)
    {
        return $query->where('destination_type', $type);
    }

    /**
     * Scope a query to only include rules with a specific protocol.
     */
    public function scopeWithProtocol($query, $protocol)
    {
        return $query->where('protocol', $protocol);
    }

    /**
     * Scope a query to only include rules with a specific priority.
     */
    public function scopeWithPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope a query to only include rules ordered by priority.
     */
    public function scopeOrderByPriority($query, $direction = 'asc')
    {
        return $query->orderBy('priority', $direction);
    }

    /**
     * Check if the rule matches the given parameters.
     */
    public function matches($source, $destination, $protocol = null, $port = null, $time = null)
    {
        // Check source
        if ($this->source_type && $this->source_value) {
            if (! $this->matchesValue($source, $this->source_type, $this->source_value)) {
                return false;
            }
        }

        // Check destination
        if ($this->destination_type && $this->destination_value) {
            if (! $this->matchesValue($destination, $this->destination_type, $this->destination_value)) {
                return false;
            }
        }

        // Check protocol
        if ($this->protocol && $protocol) {
            if ($this->protocol !== '*' && $this->protocol !== $protocol) {
                return false;
            }
        }

        // Check port range
        if ($this->port_range && $port) {
            if (! $this->matchesPortRange($port, $this->port_range)) {
                return false;
            }
        }

        // Check time range
        if ($this->time_range && $time) {
            if (! $this->matchesTimeRange($time, $this->time_range)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if a value matches a rule value based on type.
     */
    protected function matchesValue($value, $type, $ruleValue)
    {
        // Implementation would depend on the types of values we support
        // For example, IP addresses, networks, user groups, etc.
        return true; // Placeholder
    }

    /**
     * Check if a port matches a port range.
     */
    protected function matchesPortRange($port, $portRange)
    {
        // Handle single port
        if (is_numeric($portRange)) {
            return (int) $port === (int) $portRange;
        }

        // Handle port range (e.g., "80-100")
        if (strpos($portRange, '-') !== false) {
            [$min, $max] = explode('-', $portRange);

            return (int) $port >= (int) $min && (int) $port <= (int) $max;
        }

        // Handle comma-separated ports (e.g., "80,443")
        if (strpos($portRange, ',') !== false) {
            $ports = explode(',', $portRange);

            return in_array((int) $port, array_map('intval', $ports));
        }

        return false;
    }

    /**
     * Check if a time matches a time range.
     */
    protected function matchesTimeRange($time, $timeRange)
    {
        // Implementation would depend on the format of time ranges we support
        return true; // Placeholder
    }
}
