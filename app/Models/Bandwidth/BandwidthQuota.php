<?php

namespace App\Models\Bandwidth;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class BandwidthQuota extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'download_limit',
        'upload_limit',
        'total_limit',
        'period',
        'custom_period',
        'action_on_exceed',
        'action_parameters',
        'active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'download_limit' => 'integer',
        'upload_limit' => 'integer',
        'total_limit' => 'integer',
        'custom_period' => 'integer',
        'action_parameters' => 'json',
        'active' => 'boolean',
    ];

    /**
     * Get the assignments for the quota.
     */
    public function assignments(): MorphMany
    {
        return $this->morphMany(BandwidthAssignment::class, 'assignable');
    }

    /**
     * Scope a query to only include active quotas.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope a query to only include quotas with a specific period.
     */
    public function scopeWithPeriod($query, $period)
    {
        return $query->where('period', $period);
    }

    /**
     * Scope a query to only include quotas with a specific action on exceed.
     */
    public function scopeWithActionOnExceed($query, $action)
    {
        return $query->where('action_on_exceed', $action);
    }

    /**
     * Get the download limit in GB.
     */
    public function getDownloadLimitGbAttribute()
    {
        return $this->download_limit / 1024;
    }

    /**
     * Get the upload limit in GB.
     */
    public function getUploadLimitGbAttribute()
    {
        return $this->upload_limit / 1024;
    }

    /**
     * Get the total limit in GB.
     */
    public function getTotalLimitGbAttribute()
    {
        return $this->total_limit / 1024;
    }

    /**
     * Get the action parameter value for a specific key.
     */
    public function getActionParameterValue($key, $default = null)
    {
        $parameters = $this->action_parameters ?? [];

        return $parameters[$key] ?? $default;
    }

    /**
     * Set an action parameter value.
     */
    public function setActionParameterValue($key, $value)
    {
        $parameters = $this->action_parameters ?? [];
        $parameters[$key] = $value;
        $this->action_parameters = $parameters;

        return $this;
    }

    /**
     * Calculate the period start and end dates for a given date.
     */
    public function getPeriodDates($date = null)
    {
        $date = $date ?? now();

        switch ($this->period) {
            case 'daily':
                $start = $date->copy()->startOfDay();
                $end = $date->copy()->endOfDay();
                break;
            case 'weekly':
                $start = $date->copy()->startOfWeek();
                $end = $date->copy()->endOfWeek();
                break;
            case 'monthly':
                $start = $date->copy()->startOfMonth();
                $end = $date->copy()->endOfMonth();
                break;
            case 'custom':
                $start = $date->copy()->startOfDay();
                $end = $date->copy()->addDays($this->custom_period - 1)->endOfDay();
                break;
            default:
                $start = $date->copy()->startOfMonth();
                $end = $date->copy()->endOfMonth();
        }

        return [
            'start' => $start,
            'end' => $end,
        ];
    }

    /**
     * Assign this quota to a model.
     */
    public function assignTo($model, $startsAt = null, $endsAt = null)
    {
        return $this->assignments()->create([
            'assignee_type' => get_class($model),
            'assignee_id' => $model->id,
            'starts_at' => $startsAt,
            'ends_at' => $endsAt,
            'active' => true,
        ]);
    }
}
