<?php

namespace App\Models\Bandwidth;

use App\Models\Network\NetworkDevice;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QueueUsage extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'queue_usage';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'device_id',
        'queue_name',
        'target_ip',
        'customer_id',
        'service_id',
        'service_type',
        'download_bytes',
        'upload_bytes',
        'total_bytes',
        'raw_download_bytes',
        'raw_upload_bytes',
        'period_start',
        'period_end',
        'is_mapped',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'download_bytes' => 'integer',
        'upload_bytes' => 'integer',
        'total_bytes' => 'integer',
        'raw_download_bytes' => 'integer',
        'raw_upload_bytes' => 'integer',
        'period_start' => 'datetime',
        'period_end' => 'datetime',
        'is_mapped' => 'boolean',
    ];

    /**
     * Get the device that owns this queue usage.
     */
    public function device()
    {
        return $this->belongsTo(NetworkDevice::class);
    }

    /**
     * Get the customer if mapped.
     */
    public function customer()
    {
        return $this->belongsTo(\App\Models\Customer::class);
    }

    /**
     * Get the download usage in MB.
     */
    public function getDownloadMbAttribute()
    {
        return $this->download_bytes / (1024 * 1024);
    }

    /**
     * Get the upload usage in MB.
     */
    public function getUploadMbAttribute()
    {
        return $this->upload_bytes / (1024 * 1024);
    }

    /**
     * Get the total usage in MB.
     */
    public function getTotalMbAttribute()
    {
        return $this->total_bytes / (1024 * 1024);
    }

    /**
     * Get the download usage in GB.
     */
    public function getDownloadGbAttribute()
    {
        return $this->download_bytes / (1024 * 1024 * 1024);
    }

    /**
     * Get the upload usage in GB.
     */
    public function getUploadGbAttribute()
    {
        return $this->upload_bytes / (1024 * 1024 * 1024);
    }

    /**
     * Get the total usage in GB.
     */
    public function getTotalGbAttribute()
    {
        return $this->total_bytes / (1024 * 1024 * 1024);
    }

    /**
     * Scope for mapped customers only.
     */
    public function scopeMapped($query)
    {
        return $query->where('is_mapped', true);
    }

    /**
     * Scope for unmapped queues only.
     */
    public function scopeUnmapped($query)
    {
        return $query->where('is_mapped', false);
    }

    /**
     * Scope for a specific period.
     */
    public function scopeForPeriod($query, $start, $end)
    {
        return $query->where('period_start', '>=', $start)
            ->where('period_end', '<=', $end);
    }

    /**
     * Record queue usage with difference calculation.
     */
    public static function recordQueueUsage(array $queueData, $periodStart, $periodEnd)
    {
        $deviceId = $queueData['device_id'];
        $queueName = $queueData['queue_name'];
        $targetIp = $queueData['identifier'];
        $currentDownload = $queueData['download_bytes'];
        $currentUpload = $queueData['upload_bytes'];

        // Get the last recorded usage for this queue to calculate difference
        $lastUsage = static::where('device_id', $deviceId)
            ->where('queue_name', $queueName)
            ->where('target_ip', $targetIp)
            ->orderBy('period_end', 'desc')
            ->first();

        // Calculate the difference (usage for this period only)
        $downloadDiff = $currentDownload;
        $uploadDiff = $currentUpload;

        if ($lastUsage) {
            // Get the last cumulative totals from the raw_download_bytes and raw_upload_bytes
            // If we don't have raw bytes stored, use the current values as baseline
            $lastCumulativeDownload = $lastUsage->raw_download_bytes ?? 0;
            $lastCumulativeUpload = $lastUsage->raw_upload_bytes ?? 0;

            // Calculate difference only if current values are greater (handle counter resets)
            if ($currentDownload >= $lastCumulativeDownload) {
                $downloadDiff = $currentDownload - $lastCumulativeDownload;
            }

            if ($currentUpload >= $lastCumulativeUpload) {
                $uploadDiff = $currentUpload - $lastCumulativeUpload;
            }
        }

        return static::updateOrCreate([
            'device_id' => $deviceId,
            'queue_name' => $queueName,
            'target_ip' => $targetIp,
            'period_start' => $periodStart,
            'period_end' => $periodEnd,
        ], [
            'customer_id' => $queueData['customer_id'],
            'service_id' => $queueData['service_id'],
            'service_type' => $queueData['service_type'],
            'download_bytes' => $downloadDiff,  // Store period difference, not cumulative
            'upload_bytes' => $uploadDiff,      // Store period difference, not cumulative
            'total_bytes' => $downloadDiff + $uploadDiff,
            'raw_download_bytes' => $currentDownload,  // Store raw cumulative for next calculation
            'raw_upload_bytes' => $currentUpload,      // Store raw cumulative for next calculation
            'is_mapped' => $queueData['is_mapped'],
        ]);
    }
}
