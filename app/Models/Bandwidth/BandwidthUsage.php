<?php

namespace App\Models\Bandwidth;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class BandwidthUsage extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'bandwidth_usage';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'usageable_type',
        'usageable_id',
        'download',
        'upload',
        'total',
        'period_start',
        'period_end',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'download' => 'integer',
        'upload' => 'integer',
        'total' => 'integer',
        'period_start' => 'datetime',
        'period_end' => 'datetime',
    ];

    /**
     * Get the parent usageable model (user, device, etc.).
     */
    public function usageable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to only include usage for a specific period.
     */
    public function scopeForPeriod($query, $start, $end)
    {
        return $query->where('period_start', '>=', $start)
            ->where('period_end', '<=', $end);
    }

    /**
     * Scope a query to only include usage for the current day.
     */
    public function scopeForToday($query)
    {
        return $query->where('period_start', '>=', now()->startOfDay())
            ->where('period_end', '<=', now()->endOfDay());
    }

    /**
     * Scope a query to only include usage for the current week.
     */
    public function scopeForThisWeek($query)
    {
        return $query->where('period_start', '>=', now()->startOfWeek())
            ->where('period_end', '<=', now()->endOfWeek());
    }

    /**
     * Scope a query to only include usage for the current month.
     */
    public function scopeForThisMonth($query)
    {
        return $query->where('period_start', '>=', now()->startOfMonth())
            ->where('period_end', '<=', now()->endOfMonth());
    }

    /**
     * Get the download usage in MB.
     */
    public function getDownloadMbAttribute()
    {
        return $this->download / (1024 * 1024);
    }

    /**
     * Get the upload usage in MB.
     */
    public function getUploadMbAttribute()
    {
        return $this->upload / (1024 * 1024);
    }

    /**
     * Get the total usage in MB.
     */
    public function getTotalMbAttribute()
    {
        return $this->total / (1024 * 1024);
    }

    /**
     * Get the download usage in GB.
     */
    public function getDownloadGbAttribute()
    {
        return $this->download / (1024 * 1024 * 1024);
    }

    /**
     * Get the upload usage in GB.
     */
    public function getUploadGbAttribute()
    {
        return $this->upload / (1024 * 1024 * 1024);
    }

    /**
     * Get the total usage in GB.
     */
    public function getTotalGbAttribute()
    {
        return $this->total / (1024 * 1024 * 1024);
    }

    /**
     * Add usage to the record.
     */
    public function addUsage($download, $upload)
    {
        $this->download += $download;
        $this->upload += $upload;
        $this->total = $this->download + $this->upload;

        return $this;
    }

    /**
     * Create or update usage for a model.
     */
    public static function recordUsage($model, $download, $upload, $periodStart = null, $periodEnd = null)
    {
        $periodStart = $periodStart ?? now()->startOfDay();
        $periodEnd = $periodEnd ?? now()->endOfDay();

        $usage = static::firstOrNew([
            'usageable_type' => get_class($model),
            'usageable_id' => $model->id,
            'period_start' => $periodStart,
            'period_end' => $periodEnd,
        ]);

        if (! $usage->exists) {
            $usage->download = 0;
            $usage->upload = 0;
            $usage->total = 0;
        }

        $usage->addUsage($download, $upload);
        $usage->save();

        return $usage;
    }
}
