<?php

namespace App\Models\Bandwidth;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class BandwidthAssignment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'assignable_type',
        'assignable_id',
        'assignee_type',
        'assignee_id',
        'starts_at',
        'ends_at',
        'active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'active' => 'boolean',
    ];

    /**
     * Get the parent assignable model (plan, policy, quota).
     */
    public function assignable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the parent assignee model (user, device, etc.).
     */
    public function assignee(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to only include active assignments.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope a query to only include assignments that are currently in effect.
     */
    public function scopeInEffect($query)
    {
        return $query->where('active', true)
            ->where(function ($query) {
                $query->whereNull('starts_at')
                    ->orWhere('starts_at', '<=', now());
            })
            ->where(function ($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>=', now());
            });
    }

    /**
     * Scope a query to only include assignments of a specific assignable type.
     */
    public function scopeOfAssignableType($query, $type)
    {
        return $query->where('assignable_type', $type);
    }

    /**
     * Scope a query to only include assignments to a specific assignee type.
     */
    public function scopeToAssigneeType($query, $type)
    {
        return $query->where('assignee_type', $type);
    }

    /**
     * Scope a query to only include assignments for a specific assignee.
     */
    public function scopeForAssignee($query, $model)
    {
        return $query->where('assignee_type', get_class($model))
            ->where('assignee_id', $model->id);
    }

    /**
     * Check if the assignment is currently in effect.
     */
    public function isInEffect()
    {
        if (! $this->active) {
            return false;
        }

        $now = now();

        if ($this->starts_at && $this->starts_at > $now) {
            return false;
        }

        if ($this->ends_at && $this->ends_at < $now) {
            return false;
        }

        return true;
    }

    /**
     * Activate the assignment.
     */
    public function activate()
    {
        $this->active = true;

        return $this;
    }

    /**
     * Deactivate the assignment.
     */
    public function deactivate()
    {
        $this->active = false;

        return $this;
    }

    /**
     * Set the start date of the assignment.
     */
    public function setStartDate($date)
    {
        $this->starts_at = $date;

        return $this;
    }

    /**
     * Set the end date of the assignment.
     */
    public function setEndDate($date)
    {
        $this->ends_at = $date;

        return $this;
    }
}
