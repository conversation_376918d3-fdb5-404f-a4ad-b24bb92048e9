<?php

namespace App\Models\Services;

use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Network\NetworkDevice;
use App\Models\Subscription;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PppoeService extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'subscription_id',
        'customer_id',
        'device_id',
        'username',
        'password',
        'service_profile',
        'bandwidth_plan_id',
        'ip_address',
        'remote_address',
        'status',
        'last_connected',
        'last_disconnected',
        'comment',
        'mikrotik_id',
        'cached_session_data',
        'session_data_cached_at',
        'is_currently_connected',
        'current_session_ip',
        'current_session_uptime',
        'last_session_check',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'last_connected' => 'datetime',
        'last_disconnected' => 'datetime',
        'cached_session_data' => 'array',
        'session_data_cached_at' => 'datetime',
        'is_currently_connected' => 'boolean',
        'last_session_check' => 'datetime',
    ];

    /**
     * Get the customer that owns the PPPoE service.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the subscription associated with the PPPoE service.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get the device (router) that hosts this PPPoE service.
     */
    public function device(): BelongsTo
    {
        return $this->belongsTo(NetworkDevice::class, 'device_id');
    }

    /**
     * Get the bandwidth plan associated with the PPPoE service.
     */
    public function bandwidthPlan(): BelongsTo
    {
        return $this->belongsTo(BandwidthPlan::class, 'bandwidth_plan_id');
    }

    /**
     * Check if cached session data is still valid (within 60 seconds).
     */
    public function hasFreshSessionData(): bool
    {
        if (! $this->session_data_cached_at) {
            return false;
        }

        return $this->session_data_cached_at->diffInSeconds(now()) < 60;
    }

    /**
     * Get cached session data or null if not available/expired.
     */
    public function getCachedSessionData(): ?array
    {
        if (! $this->hasFreshSessionData()) {
            return null;
        }

        return $this->cached_session_data;
    }

    /**
     * Update cached session data.
     */
    public function updateSessionCache(?array $sessionData): void
    {
        $this->update([
            'cached_session_data' => $sessionData,
            'session_data_cached_at' => now(),
            'is_currently_connected' => $sessionData !== null,
            'current_session_ip' => $sessionData['address'] ?? null,
            'current_session_uptime' => $sessionData['uptime'] ?? null,
            'last_session_check' => now(),
        ]);
    }

    /**
     * Scope a query to only include active PPPoE services.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include suspended PPPoE services.
     */
    public function scopeSuspended($query)
    {
        return $query->where('status', 'suspended');
    }

    /**
     * Scope a query to only include services for a specific customer.
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope a query to only include services on a specific device.
     */
    public function scopeOnDevice($query, $deviceId)
    {
        return $query->where('device_id', $deviceId);
    }

    /**
     * Check if the service is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the service is suspended.
     */
    public function isSuspended(): bool
    {
        return $this->status === 'suspended';
    }

    /**
     * Activate the PPPoE service.
     */
    public function activate(): bool
    {
        if ($this->status !== 'active') {
            $this->status = 'active';

            return $this->save();
        }

        return true;
    }

    /**
     * Suspend the PPPoE service.
     */
    public function suspend(): bool
    {
        if ($this->status !== 'suspended') {
            $this->status = 'suspended';

            return $this->save();
        }

        return true;
    }

    /**
     * Generate a random password for PPPoE service.
     */
    public static function generatePassword($length = 8): string
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $password = '';

        for ($i = 0; $i < $length; $i++) {
            $password .= $chars[rand(0, strlen($chars) - 1)];
        }

        return $password;
    }

    /**
     * Generate a username based on customer information.
     */
    public static function generateUsername(Customer $customer): string
    {
        // Remove spaces and special characters from customer name
        $name = preg_replace('/[^a-zA-Z0-9]/', '', $customer->name);

        // Convert to lowercase and take first 8 characters
        $username = strtolower(substr($name, 0, 8));

        // Add a random number to ensure uniqueness
        $username .= rand(100, 999);

        // Check if username already exists
        $count = self::where('username', $username)->count();
        if ($count > 0) {
            // If exists, add another random number
            $username .= rand(10, 99);
        }

        return $username;
    }
}
