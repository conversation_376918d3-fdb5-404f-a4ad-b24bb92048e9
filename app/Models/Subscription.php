<?php

namespace App\Models;

use App\Models\Network\NetworkSite;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subscription extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_id',
        'bandwidth_plan_id',
        'network_site_id',
        'name',
        'description',
        'customer_notes',
        'allow_overrides',
        'status',
        'price',
        'billing_cycle',
        'start_date',
        'end_date',
        'next_billing_date',
        'last_billing_date',
        'suspension_date',
        'reactivation_date',
        'suspension_days',
        'auto_billing_enabled',
        'grace_period_days',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'next_billing_date' => 'date',
        'last_billing_date' => 'date',
        'suspension_date' => 'date',
        'reactivation_date' => 'date',
        'auto_billing_enabled' => 'boolean',
        'allow_overrides' => 'boolean',
    ];

    /**
     * Get the customer that owns the subscription.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the bandwidth plan associated with the subscription.
     */
    public function bandwidthPlan(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Bandwidth\BandwidthPlan::class);
    }

    /**
     * Get the network site associated with the subscription.
     */
    public function networkSite(): BelongsTo
    {
        return $this->belongsTo(NetworkSite::class);
    }

    /**
     * Get the invoices for the subscription.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the static IP service for the subscription.
     */
    public function staticIpService(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(\App\Models\Services\StaticIpService::class);
    }

    /**
     * Get the PPPoE service for the subscription.
     */
    public function pppoeService(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(\App\Models\Services\PppoeService::class);
    }

    /**
     * Scope a query to only include active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include subscriptions that need billing.
     */
    public function scopeNeedsBilling($query)
    {
        return $query->where('status', 'active')
            ->where('next_billing_date', '<=', now());
    }

    /**
     * Check if the subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the subscription has ended.
     */
    public function hasEnded(): bool
    {
        return $this->end_date !== null && $this->end_date <= now();
    }

    /**
     * Calculate the next billing date based on the billing cycle.
     */
    public function calculateNextBillingDate(): \Carbon\Carbon
    {
        $date = \Carbon\Carbon::parse($this->next_billing_date);

        switch ($this->billing_cycle) {
            case 'monthly':
                return $date->addMonth();
            case 'quarterly':
                return $date->addMonths(3);
            case 'yearly':
                return $date->addYear();
            default:
                return $date->addMonth();
        }
    }

    /**
     * Update the next billing date.
     */
    public function updateNextBillingDate(): void
    {
        $this->next_billing_date = $this->calculateNextBillingDate();
        $this->save();
    }

    /**
     * Auto-populate subscription details from bandwidth plan.
     */
    public function populateFromBandwidthPlan(): void
    {
        if ($this->bandwidth_plan_id && $this->bandwidthPlan) {
            $plan = $this->bandwidthPlan;

            // Only populate if not overridden or if allow_overrides is false
            if (! $this->allow_overrides || empty($this->name)) {
                $this->name = $plan->name;
            }

            if (! $this->allow_overrides || empty($this->description)) {
                $this->description = $plan->description;
            }

            if (! $this->allow_overrides || empty($this->price)) {
                $this->price = $plan->price;
            }
        }
    }

    /**
     * Create a subscription from a bandwidth plan.
     */
    public static function createFromBandwidthPlan(
        int $customerId,
        int $bandwidthPlanId,
        array $overrides = []
    ): self {
        $plan = \App\Models\Bandwidth\BandwidthPlan::findOrFail($bandwidthPlanId);

        $subscription = new self([
            'customer_id' => $customerId,
            'bandwidth_plan_id' => $bandwidthPlanId,
            'name' => $overrides['name'] ?? $plan->name,
            'description' => $overrides['description'] ?? $plan->description,
            'price' => $overrides['price'] ?? $plan->price,
            'billing_cycle' => $overrides['billing_cycle'] ?? 'monthly',
            'start_date' => $overrides['start_date'] ?? now()->toDateString(),
            'end_date' => $overrides['end_date'] ?? null,
            'status' => $overrides['status'] ?? 'active',
            'customer_notes' => $overrides['customer_notes'] ?? null,
            'allow_overrides' => $overrides['allow_overrides'] ?? false,
        ]);

        // Set next billing date
        $startDate = \Carbon\Carbon::parse($subscription->start_date);
        $subscription->next_billing_date = $startDate->toDateString();

        return $subscription;
    }
}
