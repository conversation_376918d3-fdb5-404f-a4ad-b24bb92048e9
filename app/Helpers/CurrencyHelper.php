<?php

if (! function_exists('formatCurrency')) {
    /**
     * Format currency amount
     */
    function formatCurrency(float $amount, ?string $currency = null, array $options = []): string
    {
        return \App\Services\CurrencyService::format($amount, $currency, $options);
    }
}

if (! function_exists('formatCurrencyCompact')) {
    /**
     * Format currency amount in compact format
     */
    function formatCurrencyCompact(float $amount, ?string $currency = null): string
    {
        return \App\Services\CurrencyService::formatCompact($amount, $currency);
    }
}

if (! function_exists('getCurrencySymbol')) {
    /**
     * Get currency symbol
     */
    function getCurrencySymbol(?string $currency = null): string
    {
        return \App\Services\CurrencyService::getSymbol($currency);
    }
}

if (! function_exists('getDefaultCurrency')) {
    /**
     * Get default currency
     */
    function getDefaultCurrency(): string
    {
        return \App\Services\CurrencyService::getDefaultCurrency();
    }
}

if (! function_exists('getSupportedCurrencies')) {
    /**
     * Get all supported currencies
     */
    function getSupportedCurrencies(): array
    {
        return \App\Services\CurrencyService::getSupportedCurrencies();
    }
}
