<?php

namespace App\Observers;

use App\Jobs\MikroTik\ReactivatePppoeServiceJob;
use App\Jobs\MikroTik\ReactivateStaticIpServiceJob;
use App\Models\Invoice;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Services\AutomatedBillingService;
use Illuminate\Support\Facades\Log;

class InvoiceObserver
{
    /**
     * Handle the Invoice "updated" event.
     * Automatically reactivate suspended services when invoice is marked as paid.
     */
    public function updated(Invoice $invoice): void
    {
        // Check if the invoice status changed from 'pending' to 'paid'
        if ($invoice->isDirty('status') &&
            $invoice->getOriginal('status') === 'pending' &&
            $invoice->status === 'paid') {

            Log::info('Invoice marked as paid - processing payment actions', [
                'invoice_id' => $invoice->id,
                'customer_id' => $invoice->customer_id,
                'invoice_number' => $invoice->invoice_number,
                'amount' => $invoice->total_amount,
            ]);

            // Update subscription's next billing date
            $this->updateSubscriptionBillingDate($invoice);

            // Reactivate suspended services
            $this->reactivateCustomerServices($invoice);
        }
    }

    /**
     * Update subscription's next billing date when payment is received.
     */
    protected function updateSubscriptionBillingDate(Invoice $invoice): void
    {
        if (! $invoice->subscription_id) {
            Log::info('Invoice has no subscription - skipping billing date update', [
                'invoice_id' => $invoice->id,
            ]);

            return;
        }

        try {
            $billingService = app(AutomatedBillingService::class);
            $billingService->updateNextBillingDateOnPayment($invoice);
        } catch (\Exception $e) {
            Log::error('Failed to update next billing date after payment', [
                'invoice_id' => $invoice->id,
                'subscription_id' => $invoice->subscription_id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Reactivate suspended services for the customer associated with the paid invoice.
     */
    protected function reactivateCustomerServices(Invoice $invoice): void
    {
        $customer = $invoice->customer;

        if (! $customer) {
            Log::warning('Invoice has no associated customer', [
                'invoice_id' => $invoice->id,
            ]);

            return;
        }

        // Reactivate Static IP services
        $this->reactivateStaticIpServices($customer, $invoice);

        // Reactivate PPPoE services
        $this->reactivatePppoeServices($customer, $invoice);

        // Update subscription status to active if it was suspended
        $this->reactivateCustomerSubscriptions($customer, $invoice);
    }

    /**
     * Reactivate suspended Static IP services for the customer.
     */
    protected function reactivateStaticIpServices($customer, Invoice $invoice): void
    {
        // Get all suspended Static IP services for this customer
        $suspendedStaticIpServices = StaticIpService::where('customer_id', $customer->id)
            ->where('status', 'suspended')
            ->whereNotNull('mikrotik_id') // Only services that were suspended via MikroTik
            ->get();

        if ($suspendedStaticIpServices->isEmpty()) {
            Log::info('No suspended Static IP services found for customer', [
                'invoice_id' => $invoice->id,
                'customer_id' => $customer->id,
                'customer_name' => $customer->name,
            ]);

            return;
        }

        Log::info('Found suspended Static IP services to reactivate', [
            'invoice_id' => $invoice->id,
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'services_count' => $suspendedStaticIpServices->count(),
            'service_ids' => $suspendedStaticIpServices->pluck('id')->toArray(),
        ]);

        // Queue reactivation jobs for each suspended service
        $queuedCount = 0;
        foreach ($suspendedStaticIpServices as $service) {
            try {
                // Dispatch reactivation job
                ReactivateStaticIpServiceJob::dispatch($service, $invoice->id);
                $queuedCount++;

                Log::info('Static IP service reactivation job queued', [
                    'invoice_id' => $invoice->id,
                    'service_id' => $service->id,
                    'customer_name' => $customer->name,
                    'ip_address' => $service->ip_address,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to queue Static IP service reactivation job', [
                    'invoice_id' => $invoice->id,
                    'service_id' => $service->id,
                    'customer_name' => $customer->name,
                    'ip_address' => $service->ip_address,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Log summary
        Log::info('Static IP service reactivation jobs queued', [
            'invoice_id' => $invoice->id,
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'total_services' => $suspendedStaticIpServices->count(),
            'queued_count' => $queuedCount,
        ]);
    }

    /**
     * Reactivate suspended PPPoE services for the customer.
     */
    protected function reactivatePppoeServices($customer, Invoice $invoice): void
    {
        // Get all suspended PPPoE services for this customer (both subscription and non-subscription)
        $suspendedPppoeServices = PppoeService::where('customer_id', $customer->id)
            ->where('status', 'suspended')
            ->get();

        if ($suspendedPppoeServices->isEmpty()) {
            Log::info('No suspended PPPoE services found for customer', [
                'invoice_id' => $invoice->id,
                'customer_id' => $customer->id,
                'customer_name' => $customer->name,
            ]);

            return;
        }

        Log::info('Found suspended PPPoE services to reactivate', [
            'invoice_id' => $invoice->id,
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'services_count' => $suspendedPppoeServices->count(),
            'service_ids' => $suspendedPppoeServices->pluck('id')->toArray(),
        ]);

        // Queue reactivation jobs for each suspended service
        $queuedCount = 0;
        foreach ($suspendedPppoeServices as $service) {
            try {
                // Dispatch reactivation job
                ReactivatePppoeServiceJob::dispatch($service, $invoice->id);
                $queuedCount++;

                Log::info('PPPoE service reactivation job queued', [
                    'invoice_id' => $invoice->id,
                    'service_id' => $service->id,
                    'customer_name' => $customer->name,
                    'username' => $service->username,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to queue PPPoE service reactivation job', [
                    'invoice_id' => $invoice->id,
                    'service_id' => $service->id,
                    'customer_name' => $customer->name,
                    'username' => $service->username,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Log summary
        Log::info('PPPoE service reactivation jobs queued', [
            'invoice_id' => $invoice->id,
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'total_services' => $suspendedPppoeServices->count(),
            'queued_count' => $queuedCount,
        ]);
    }

    /**
     * Reactivate suspended subscriptions for the customer after payment.
     */
    protected function reactivateCustomerSubscriptions($customer, Invoice $invoice): void
    {
        // Get all suspended subscriptions for this customer
        $suspendedSubscriptions = $customer->subscriptions()
            ->where('status', 'suspended')
            ->get();

        if ($suspendedSubscriptions->isEmpty()) {
            Log::info('No suspended subscriptions found for customer', [
                'invoice_id' => $invoice->id,
                'customer_id' => $customer->id,
                'customer_name' => $customer->name,
            ]);

            return;
        }

        $reactivatedCount = 0;
        foreach ($suspendedSubscriptions as $subscription) {
            try {
                // Check if this subscription has any other overdue invoices
                $otherOverdueInvoices = $subscription->invoices()
                    ->where('id', '!=', $invoice->id)
                    ->where('status', 'overdue')
                    ->count();

                if ($otherOverdueInvoices > 0) {
                    Log::info('Subscription has other overdue invoices, keeping suspended', [
                        'subscription_id' => $subscription->id,
                        'overdue_count' => $otherOverdueInvoices,
                    ]);

                    continue;
                }

                // Reactivate the subscription
                $subscription->status = 'active';
                $subscription->reactivation_date = now();
                $subscription->save();

                $reactivatedCount++;

                Log::info('Subscription reactivated after payment', [
                    'invoice_id' => $invoice->id,
                    'subscription_id' => $subscription->id,
                    'customer_name' => $customer->name,
                ]);

            } catch (\Exception $e) {
                Log::error('Failed to reactivate subscription after payment', [
                    'invoice_id' => $invoice->id,
                    'subscription_id' => $subscription->id,
                    'customer_name' => $customer->name,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        Log::info('Subscription reactivation completed', [
            'invoice_id' => $invoice->id,
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'total_subscriptions' => $suspendedSubscriptions->count(),
            'reactivated_count' => $reactivatedCount,
        ]);
    }
}
