<?php

namespace App\Observers;

use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use Illuminate\Support\Facades\Log;

class StaticIpServiceObserver
{
    /**
     * Handle the StaticIpService "updated" event.
     */
    public function updated(StaticIpService $service): void
    {
        // Check if the status was changed
        if ($service->isDirty('status')) {
            $oldStatus = $service->getOriginal('status');
            $newStatus = $service->status;

            Log::info('Static IP service status changed', [
                'service_id' => $service->id,
                'customer_id' => $service->customer_id,
                'customer_name' => $service->customer->name,
                'ip_address' => $service->ip_address,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
            ]);

            // Update customer status based on all their services
            $this->updateCustomerStatusBasedOnServices($service);
        }
    }

    /**
     * Update customer status based on all their services.
     */
    protected function updateCustomerStatusBasedOnServices(StaticIpService $service): void
    {
        $customer = $service->customer;

        // Get all services for this customer
        $staticIpServices = StaticIpService::where('customer_id', $customer->id)->get();
        $pppoeServices = PppoeService::where('customer_id', $customer->id)->get();

        $allServices = $staticIpServices->concat($pppoeServices);

        if ($allServices->isEmpty()) {
            Log::info('Customer has no services, keeping current status', [
                'customer_id' => $customer->id,
                'current_status' => $customer->status,
            ]);

            return;
        }

        // Count service statuses
        $activeServices = $allServices->where('status', 'active')->count();
        $suspendedServices = $allServices->where('status', 'suspended')->count();
        $totalServices = $allServices->count();

        Log::info('Customer service status summary', [
            'customer_id' => $customer->id,
            'total_services' => $totalServices,
            'active_services' => $activeServices,
            'suspended_services' => $suspendedServices,
            'current_customer_status' => $customer->status,
        ]);

        $newCustomerStatus = null;

        // Determine new customer status based on service statuses
        if ($activeServices === $totalServices) {
            // All services are active
            if ($customer->status === 'suspended') {
                $newCustomerStatus = 'active';
            }
        } elseif ($suspendedServices === $totalServices) {
            // All services are suspended
            if ($customer->status === 'active') {
                $newCustomerStatus = 'suspended';
            }
        } elseif ($activeServices > 0 && $suspendedServices > 0) {
            // Mixed status - some active, some suspended
            // Keep customer active if they have any active services
            if ($customer->status === 'suspended') {
                $newCustomerStatus = 'active';
            }
        }

        // Update customer status if needed
        if ($newCustomerStatus && $newCustomerStatus !== $customer->status) {
            Log::info('Updating customer status based on service changes', [
                'customer_id' => $customer->id,
                'old_status' => $customer->status,
                'new_status' => $newCustomerStatus,
                'reason' => 'Service status change',
            ]);

            // Temporarily disable the observer to prevent infinite loop
            $customer->withoutEvents(function () use ($customer, $newCustomerStatus) {
                $customer->status = $newCustomerStatus;
                $customer->save();
            });

            Log::info('Customer status updated successfully', [
                'customer_id' => $customer->id,
                'new_status' => $newCustomerStatus,
            ]);
        } else {
            Log::info('No customer status change needed', [
                'customer_id' => $customer->id,
                'current_status' => $customer->status,
                'would_be_status' => $newCustomerStatus,
            ]);
        }
    }
}
