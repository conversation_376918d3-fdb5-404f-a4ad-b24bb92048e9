<?php

namespace App\Providers;

use App\Models\Customer;
use App\Models\Invoice;
use App\Observers\CustomerObserver;
use App\Observers\InvoiceObserver;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register model observers
        Customer::observe(CustomerObserver::class);
        Invoice::observe(InvoiceObserver::class);

        // enable lazy loading
        Model::preventLazyLoading();

        // always use https in production
        if ($this->app->environment('production')) {
            URL::forceScheme('https');
        }
    }
}
