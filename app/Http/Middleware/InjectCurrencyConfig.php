<?php

namespace App\Http\Middleware;

use App\Services\CurrencyService;
use Closure;
use Illuminate\Http\Request;
use Inertia\Inertia;

class InjectCurrencyConfig
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Inject currency configuration into all Inertia responses
        Inertia::share([
            'currencyConfig' => function () {
                return CurrencyService::getJavaScriptConfig();
            },
        ]);

        return $next($request);
    }
}
