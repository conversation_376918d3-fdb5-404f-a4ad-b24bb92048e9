<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\SystemSetting;
use App\Services\MpesaIdService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class CustomerController extends Controller
{
    /**
     * Display a listing of the customers.
     */
    public function index()
    {
        $customers = Customer::orderBy('name')->paginate(15);

        return response()->json([
            'status' => 'success',
            'data' => $customers,
        ]);
    }

    /**
     * Store a newly created customer in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:customers,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'user_id' => 'nullable|exists:users,id',
            'mpesa_id' => 'nullable|string|max:50|unique:customers,mpesa_id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Create customer without M-Pesa ID first
            $customerData = $request->except('mpesa_id');
            $customer = Customer::create($customerData);

            // Generate M-Pesa ID if auto-generation is enabled or manual ID provided
            $mpesaIdService = app(MpesaIdService::class);
            $autoGenerate = SystemSetting::get('mpesa_id_auto_generate_on_create', true);
            $manualId = $request->mpesa_id;

            if ($autoGenerate || $manualId) {
                try {
                    $mpesaId = $mpesaIdService->generateMpesaId($customer, $manualId);
                    $customer->mpesa_id = $mpesaId;
                    $customer->save();

                    Log::info('M-Pesa ID generated for new customer', [
                        'customer_id' => $customer->id,
                        'customer_name' => $customer->name,
                        'mpesa_id' => $mpesaId,
                        'method' => $manualId ? 'manual' : 'auto',
                    ]);
                } catch (\Exception $e) {
                    Log::warning('Failed to generate M-Pesa ID for new customer', [
                        'customer_id' => $customer->id,
                        'customer_name' => $customer->name,
                        'error' => $e->getMessage(),
                    ]);
                    // Continue without M-Pesa ID - it can be generated later
                }
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Customer created successfully',
                'data' => $customer->fresh(),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create customer', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create customer: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified customer.
     */
    public function show(Customer $customer)
    {
        $customer->load(['subscriptions', 'invoices']);

        return response()->json([
            'status' => 'success',
            'data' => $customer,
        ]);
    }

    /**
     * Update the specified customer in storage.
     */
    public function update(Request $request, Customer $customer)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:customers,email,'.$customer->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'status' => 'sometimes|required|in:active,inactive',
            'user_id' => 'nullable|exists:users,id',
            'mpesa_id' => 'nullable|string|max:50|unique:customers,mpesa_id,'.$customer->id,
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            // Validate M-Pesa ID if provided
            if ($request->has('mpesa_id') && $request->mpesa_id !== $customer->mpesa_id) {
                $mpesaIdService = app(MpesaIdService::class);

                if ($request->mpesa_id) {
                    $mpesaIdService->validateMpesaId($request->mpesa_id, $customer->id);
                }

                Log::info('M-Pesa ID updated for customer', [
                    'customer_id' => $customer->id,
                    'customer_name' => $customer->name,
                    'old_mpesa_id' => $customer->mpesa_id,
                    'new_mpesa_id' => $request->mpesa_id,
                ]);
            }

            $customer->update($request->all());

            return response()->json([
                'status' => 'success',
                'message' => 'Customer updated successfully',
                'data' => $customer->fresh(),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update customer', [
                'customer_id' => $customer->id,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update customer: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified customer from storage.
     */
    public function destroy(Customer $customer)
    {
        // Check if customer has active subscriptions
        if ($customer->subscriptions()->where('status', 'active')->exists()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Cannot delete customer with active subscriptions',
            ], 422);
        }

        $customer->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Customer deleted successfully',
        ]);
    }

    /**
     * Get all subscriptions for a customer.
     */
    public function subscriptions(Customer $customer)
    {
        $subscriptions = $customer->subscriptions()->with('networkSite')->get();

        return response()->json([
            'status' => 'success',
            'data' => $subscriptions,
        ]);
    }

    /**
     * Get all invoices for a customer.
     */
    public function invoices(Customer $customer)
    {
        $invoices = $customer->invoices()->with('subscription')->get();

        return response()->json([
            'status' => 'success',
            'data' => $invoices,
        ]);
    }

    /**
     * Generate M-Pesa ID for a customer.
     */
    public function generateMpesaId(Request $request, Customer $customer)
    {
        try {
            if ($customer->mpesa_id) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Customer already has an M-Pesa ID',
                ], 422);
            }

            $mpesaIdService = app(MpesaIdService::class);
            $manualId = $request->mpesa_id;

            $mpesaId = $mpesaIdService->generateMpesaId($customer, $manualId);
            $customer->mpesa_id = $mpesaId;
            $customer->save();

            Log::info('M-Pesa ID generated for existing customer', [
                'customer_id' => $customer->id,
                'customer_name' => $customer->name,
                'mpesa_id' => $mpesaId,
                'method' => $manualId ? 'manual' : 'auto',
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'M-Pesa ID generated successfully',
                'data' => [
                    'mpesa_id' => $mpesaId,
                    'customer' => $customer->fresh(),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to generate M-Pesa ID for customer', [
                'customer_id' => $customer->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to generate M-Pesa ID: '.$e->getMessage(),
            ], 500);
        }
    }
}
