<?php

namespace App\Http\Controllers;

use App\Services\ProgressTracker;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProgressController extends Controller
{
    /**
     * Get progress for a specific session
     */
    public function show(Request $request, string $sessionId): JsonResponse
    {
        $request->validate([
            'session_id' => 'required|string',
        ]);

        // Get progress from ProgressTracker (no cache)
        $tracker = new ProgressTracker($sessionId);
        $progress = $tracker->getProgress();

        if (! $progress) {
            return response()->json([
                'success' => false,
                'message' => 'Progress session not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'progress' => $progress,
        ]);
    }

    /**
     * Start a new progress tracking session
     */
    public function start(Request $request): JsonResponse
    {
        $request->validate([
            'operation' => 'required|string|max:255',
            'total_steps' => 'required|integer|min:1',
        ]);

        $tracker = new ProgressTracker;
        $sessionId = $tracker->start(
            $request->input('operation'),
            $request->input('total_steps')
        );

        return response()->json([
            'success' => true,
            'session_id' => $sessionId,
            'progress' => $tracker->getProgress(),
        ]);
    }

    /**
     * Update progress for a session
     */
    public function update(Request $request, string $sessionId): JsonResponse
    {
        $request->validate([
            'current_step' => 'required|integer|min:0',
            'message' => 'nullable|string|max:500',
            'status' => 'nullable|string|in:started,in_progress,completed,error',
        ]);

        $tracker = new ProgressTracker($sessionId);

        if (! $tracker->getProgress()) {
            return response()->json([
                'success' => false,
                'message' => 'Progress session not found',
            ], 404);
        }

        $tracker->update(
            $request->input('current_step'),
            $request->input('message', ''),
            $request->input('status', 'in_progress')
        );

        return response()->json([
            'success' => true,
            'progress' => $tracker->getProgress(),
        ]);
    }

    /**
     * Complete a progress session
     */
    public function complete(Request $request, string $sessionId): JsonResponse
    {
        $request->validate([
            'message' => 'nullable|string|max:500',
        ]);

        $tracker = new ProgressTracker($sessionId);

        if (! $tracker->getProgress()) {
            return response()->json([
                'success' => false,
                'message' => 'Progress session not found',
            ], 404);
        }

        $tracker->complete($request->input('message', 'Operation completed successfully'));

        return response()->json([
            'success' => true,
            'progress' => $tracker->getProgress(),
        ]);
    }

    /**
     * Clear/delete a progress session
     */
    public function destroy(string $sessionId): JsonResponse
    {
        $tracker = new ProgressTracker($sessionId);
        $tracker->clear();

        return response()->json([
            'success' => true,
            'message' => 'Progress session cleared',
        ]);
    }
}
