<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of the subscriptions.
     */
    public function index()
    {
        $subscriptions = Subscription::with(['customer', 'networkSite'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'status' => 'success',
            'data' => $subscriptions,
        ]);
    }

    /**
     * Store a newly created subscription in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'network_site_id' => 'nullable|exists:network_sites,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,quarterly,yearly',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Set the next billing date based on the start date
        $startDate = Carbon::parse($request->start_date);
        $nextBillingDate = $startDate->copy();

        $subscription = Subscription::create(array_merge(
            $request->all(),
            ['next_billing_date' => $nextBillingDate->toDateString()]
        ));

        // Generate the first invoice
        $this->generateInvoice($subscription);

        return response()->json([
            'status' => 'success',
            'message' => 'Subscription created successfully',
            'data' => $subscription,
        ], 201);
    }

    /**
     * Display the specified subscription.
     */
    public function show(Subscription $subscription)
    {
        $subscription->load(['customer', 'networkSite', 'invoices']);

        return response()->json([
            'status' => 'success',
            'data' => $subscription,
        ]);
    }

    /**
     * Update the specified subscription in storage.
     */
    public function update(Request $request, Subscription $subscription)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'sometimes|required|exists:customers,id',
            'network_site_id' => 'nullable|exists:network_sites,id',
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'sometimes|required|in:active,inactive,cancelled',
            'price' => 'sometimes|required|numeric|min:0',
            'billing_cycle' => 'sometimes|required|in:monthly,quarterly,yearly',
            'start_date' => 'sometimes|required|date',
            'end_date' => 'nullable|date|after:start_date',
            'next_billing_date' => 'sometimes|required|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $subscription->update($request->all());

        return response()->json([
            'status' => 'success',
            'message' => 'Subscription updated successfully',
            'data' => $subscription,
        ]);
    }

    /**
     * Remove the specified subscription from storage.
     */
    public function destroy(Subscription $subscription)
    {
        // Check if subscription has active invoices
        if ($subscription->invoices()->where('status', 'pending')->exists()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Cannot delete subscription with pending invoices',
            ], 422);
        }

        $subscription->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Subscription deleted successfully',
        ]);
    }

    /**
     * Get all invoices for a subscription.
     */
    public function invoices(Subscription $subscription)
    {
        $invoices = $subscription->invoices()->with('items')->get();

        return response()->json([
            'status' => 'success',
            'data' => $invoices,
        ]);
    }

    /**
     * Cancel a subscription.
     */
    public function cancel(Subscription $subscription)
    {
        $subscription->status = 'cancelled';
        $subscription->end_date = now();
        $subscription->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Subscription cancelled successfully',
            'data' => $subscription,
        ]);
    }

    /**
     * Renew a subscription.
     */
    public function renew(Subscription $subscription)
    {
        if ($subscription->status !== 'active') {
            $subscription->status = 'active';
            $subscription->end_date = null;
            $subscription->save();
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Subscription renewed successfully',
            'data' => $subscription,
        ]);
    }

    /**
     * Generate a new invoice for a subscription.
     */
    public function generateInvoice(Subscription $subscription)
    {
        // Create the invoice
        $invoice = new Invoice([
            'customer_id' => $subscription->customer_id,
            'subscription_id' => $subscription->id,
            'invoice_number' => Invoice::generateInvoiceNumber(),
            'issue_date' => now()->toDateString(),
            'due_date' => now()->addDays(15)->toDateString(),
            'amount' => 0,
            'tax_amount' => 0,
            'total_amount' => 0,
            'status' => 'pending',
        ]);

        $invoice->save();

        // Create the invoice item
        $invoiceItem = new InvoiceItem([
            'invoice_id' => $invoice->id,
            'description' => $subscription->name.' ('.$subscription->billing_cycle.')',
            'quantity' => 1,
            'unit_price' => $subscription->price,
            'tax_rate' => 0, // Adjust as needed
            'tax_amount' => 0,
            'subtotal' => $subscription->price,
            'total' => $subscription->price,
        ]);

        $invoiceItem->save();
        $invoiceItem->calculateAmounts();

        // Update the invoice totals
        $invoice->calculateTotal();

        // Update the subscription's next billing date
        $subscription->updateNextBillingDate();

        return $invoice;
    }

    /**
     * Process subscriptions that need billing.
     */
    public function processBilling()
    {
        $subscriptions = Subscription::needsBilling()->get();
        $count = 0;

        foreach ($subscriptions as $subscription) {
            $this->generateInvoice($subscription);
            $count++;
        }

        return response()->json([
            'status' => 'success',
            'message' => $count.' subscriptions processed for billing',
        ]);
    }
}
