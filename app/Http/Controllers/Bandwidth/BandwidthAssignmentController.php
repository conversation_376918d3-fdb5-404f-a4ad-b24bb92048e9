<?php

namespace App\Http\Controllers\Bandwidth;

use App\Http\Controllers\Controller;
use App\Models\Bandwidth\BandwidthAssignment;
use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Bandwidth\BandwidthPolicy;
use App\Models\Bandwidth\BandwidthQuota;
use Illuminate\Http\Request;

class BandwidthAssignmentController extends Controller
{
    /**
     * Display a listing of the assignments.
     */
    public function index(Request $request)
    {
        $query = BandwidthAssignment::query();

        // Apply filters
        if ($request->has('assignable_type')) {
            $query->where('assignable_type', $request->assignable_type);
        }

        if ($request->has('assignable_id')) {
            $query->where('assignable_id', $request->assignable_id);
        }

        if ($request->has('assignee_type')) {
            $query->where('assignee_type', $request->assignee_type);
        }

        if ($request->has('assignee_id')) {
            $query->where('assignee_id', $request->assignee_id);
        }

        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        if ($request->has('current')) {
            $query->inEffect();
        }

        if ($request->has('expired')) {
            $query->where('ends_at', '<', now());
        }

        if ($request->has('not_started')) {
            $query->where('starts_at', '>', now());
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $assignments = $query->with(['assignable', 'assignee'])->paginate($request->get('per_page', 15));

        return response()->json($assignments);
    }

    /**
     * Store a newly created assignment.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'assignable_type' => 'required|string',
            'assignable_id' => 'required|integer',
            'assignee_type' => 'required|string',
            'assignee_id' => 'required|integer',
            'starts_at' => 'nullable|date',
            'ends_at' => 'nullable|date|after:starts_at',
            'active' => 'boolean',
        ]);

        // Check if the assignable exists
        $assignableClass = $validated['assignable_type'];
        $assignableId = $validated['assignable_id'];

        if (! class_exists($assignableClass)) {
            return response()->json([
                'message' => 'Invalid assignable type',
            ], 422);
        }

        $assignable = $assignableClass::find($assignableId);
        if (! $assignable) {
            return response()->json([
                'message' => 'Assignable not found',
            ], 404);
        }

        // Check if the assignee exists
        $assigneeClass = $validated['assignee_type'];
        $assigneeId = $validated['assignee_id'];

        if (! class_exists($assigneeClass)) {
            return response()->json([
                'message' => 'Invalid assignee type',
            ], 422);
        }

        $assignee = $assigneeClass::find($assigneeId);
        if (! $assignee) {
            return response()->json([
                'message' => 'Assignee not found',
            ], 404);
        }

        // Check if an assignment already exists
        $existingAssignment = BandwidthAssignment::where('assignable_type', $assignableClass)
            ->where('assignable_id', $assignableId)
            ->where('assignee_type', $assigneeClass)
            ->where('assignee_id', $assigneeId)
            ->first();

        if ($existingAssignment) {
            return response()->json([
                'message' => 'An assignment already exists for this assignable and assignee',
                'assignment' => $existingAssignment,
            ], 422);
        }

        $assignment = BandwidthAssignment::create([
            'assignable_type' => $assignableClass,
            'assignable_id' => $assignableId,
            'assignee_type' => $assigneeClass,
            'assignee_id' => $assigneeId,
            'starts_at' => $validated['starts_at'] ?? null,
            'ends_at' => $validated['ends_at'] ?? null,
            'active' => $validated['active'] ?? true,
        ]);

        return response()->json($assignment, 201);
    }

    /**
     * Display the specified assignment.
     */
    public function show(BandwidthAssignment $assignment)
    {
        $assignment->load(['assignable', 'assignee']);

        return response()->json($assignment);
    }

    /**
     * Update the specified assignment.
     */
    public function update(Request $request, BandwidthAssignment $assignment)
    {
        $validated = $request->validate([
            'starts_at' => 'nullable|date',
            'ends_at' => 'nullable|date|after:starts_at',
            'active' => 'boolean',
        ]);

        $assignment->update($validated);

        return response()->json($assignment);
    }

    /**
     * Remove the specified assignment.
     */
    public function destroy(BandwidthAssignment $assignment)
    {
        $assignment->delete();

        return response()->json(null, 204);
    }

    /**
     * Get all assignments for a specific assignee.
     */
    public function forAssignee(Request $request)
    {
        $validated = $request->validate([
            'assignee_type' => 'required|string',
            'assignee_id' => 'required|integer',
        ]);

        // Check if the assignee exists
        $assigneeClass = $validated['assignee_type'];
        $assigneeId = $validated['assignee_id'];

        if (! class_exists($assigneeClass)) {
            return response()->json([
                'message' => 'Invalid assignee type',
            ], 422);
        }

        $assignee = $assigneeClass::find($assigneeId);
        if (! $assignee) {
            return response()->json([
                'message' => 'Assignee not found',
            ], 404);
        }

        $query = BandwidthAssignment::where('assignee_type', $assigneeClass)
            ->where('assignee_id', $assigneeId);

        // Apply filters
        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        if ($request->has('current')) {
            $query->inEffect();
        }

        if ($request->has('assignable_type')) {
            $query->where('assignable_type', $request->assignable_type);
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $assignments = $query->with('assignable')->paginate($request->get('per_page', 15));

        return response()->json($assignments);
    }

    /**
     * Get all assignments for a specific assignable.
     */
    public function forAssignable(Request $request)
    {
        $validated = $request->validate([
            'assignable_type' => 'required|string',
            'assignable_id' => 'required|integer',
        ]);

        // Check if the assignable exists
        $assignableClass = $validated['assignable_type'];
        $assignableId = $validated['assignable_id'];

        if (! class_exists($assignableClass)) {
            return response()->json([
                'message' => 'Invalid assignable type',
            ], 422);
        }

        $assignable = $assignableClass::find($assignableId);
        if (! $assignable) {
            return response()->json([
                'message' => 'Assignable not found',
            ], 404);
        }

        $query = BandwidthAssignment::where('assignable_type', $assignableClass)
            ->where('assignable_id', $assignableId);

        // Apply filters
        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        if ($request->has('current')) {
            $query->inEffect();
        }

        if ($request->has('assignee_type')) {
            $query->where('assignee_type', $request->assignee_type);
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $assignments = $query->with('assignee')->paginate($request->get('per_page', 15));

        return response()->json($assignments);
    }

    /**
     * Activate an assignment.
     */
    public function activate(BandwidthAssignment $assignment)
    {
        $assignment->activate();
        $assignment->save();

        return response()->json([
            'message' => 'Assignment activated successfully',
            'assignment' => $assignment,
        ]);
    }

    /**
     * Deactivate an assignment.
     */
    public function deactivate(BandwidthAssignment $assignment)
    {
        $assignment->deactivate();
        $assignment->save();

        return response()->json([
            'message' => 'Assignment deactivated successfully',
            'assignment' => $assignment,
        ]);
    }

    /**
     * Set the start date of an assignment.
     */
    public function setStartDate(Request $request, BandwidthAssignment $assignment)
    {
        $validated = $request->validate([
            'starts_at' => 'required|date',
        ]);

        $assignment->setStartDate($validated['starts_at']);
        $assignment->save();

        return response()->json([
            'message' => 'Assignment start date updated successfully',
            'assignment' => $assignment,
        ]);
    }

    /**
     * Set the end date of an assignment.
     */
    public function setEndDate(Request $request, BandwidthAssignment $assignment)
    {
        $validated = $request->validate([
            'ends_at' => 'required|date|after:'.$assignment->starts_at,
        ]);

        $assignment->setEndDate($validated['ends_at']);
        $assignment->save();

        return response()->json([
            'message' => 'Assignment end date updated successfully',
            'assignment' => $assignment,
        ]);
    }

    /**
     * Check if an assignment is currently in effect.
     */
    public function isInEffect(BandwidthAssignment $assignment)
    {
        $inEffect = $assignment->isInEffect();

        return response()->json([
            'in_effect' => $inEffect,
            'assignment' => $assignment,
        ]);
    }

    /**
     * Get all current assignments.
     */
    public function currentAssignments(Request $request)
    {
        $query = BandwidthAssignment::inEffect();

        // Apply filters
        if ($request->has('assignable_type')) {
            $query->where('assignable_type', $request->assignable_type);
        }

        if ($request->has('assignee_type')) {
            $query->where('assignee_type', $request->assignee_type);
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $assignments = $query->with(['assignable', 'assignee'])->paginate($request->get('per_page', 15));

        return response()->json($assignments);
    }

    /**
     * Get assignment statistics.
     */
    public function statistics()
    {
        $totalAssignments = BandwidthAssignment::count();
        $activeAssignments = BandwidthAssignment::where('active', true)->count();
        $currentAssignments = BandwidthAssignment::inEffect()->count();
        $expiredAssignments = BandwidthAssignment::where('ends_at', '<', now())->count();
        $futureAssignments = BandwidthAssignment::where('starts_at', '>', now())->count();

        $assignmentsByAssignableType = BandwidthAssignment::select('assignable_type')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('assignable_type')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $assignmentsByAssigneeType = BandwidthAssignment::select('assignee_type')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('assignee_type')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $planAssignments = BandwidthAssignment::where('assignable_type', BandwidthPlan::class)->count();
        $policyAssignments = BandwidthAssignment::where('assignable_type', BandwidthPolicy::class)->count();
        $quotaAssignments = BandwidthAssignment::where('assignable_type', BandwidthQuota::class)->count();

        return response()->json([
            'total_assignments' => $totalAssignments,
            'active_assignments' => $activeAssignments,
            'current_assignments' => $currentAssignments,
            'expired_assignments' => $expiredAssignments,
            'future_assignments' => $futureAssignments,
            'assignments_by_assignable_type' => $assignmentsByAssignableType,
            'assignments_by_assignee_type' => $assignmentsByAssigneeType,
            'plan_assignments' => $planAssignments,
            'policy_assignments' => $policyAssignments,
            'quota_assignments' => $quotaAssignments,
        ]);
    }
}
