<?php

namespace App\Http\Controllers\Bandwidth;

use App\Http\Controllers\Controller;
use App\Models\Bandwidth\BandwidthAssignment;
use App\Models\Bandwidth\BandwidthPolicy;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class BandwidthPolicyController extends Controller
{
    /**
     * Display a listing of the policies.
     */
    public function index(Request $request)
    {
        $query = BandwidthPolicy::query();

        // Apply filters
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->search.'%')
                    ->orWhere('description', 'like', '%'.$request->search.'%');
            });
        }

        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $policies = $query->paginate($request->get('per_page', 15));

        return response()->json($policies);
    }

    /**
     * Store a newly created policy.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:bandwidth_policies',
            'description' => 'nullable|string',
            'type' => 'required|string|in:limit,shape,prioritize,quota',
            'parameters' => 'nullable|json',
            'active' => 'boolean',
        ]);

        // Parse JSON parameters if provided as string
        if (isset($validated['parameters']) && is_string($validated['parameters'])) {
            $validated['parameters'] = json_decode($validated['parameters'], true);
        }

        $policy = BandwidthPolicy::create($validated);

        return response()->json($policy, 201);
    }

    /**
     * Display the specified policy.
     */
    public function show(BandwidthPolicy $policy)
    {
        $policy->load('rules');

        return response()->json($policy);
    }

    /**
     * Update the specified policy.
     */
    public function update(Request $request, BandwidthPolicy $policy)
    {
        $validated = $request->validate([
            'name' => [
                'string',
                'max:255',
                Rule::unique('bandwidth_policies')->ignore($policy->id),
            ],
            'description' => 'nullable|string',
            'type' => 'string|in:limit,shape,prioritize,quota',
            'parameters' => 'nullable|json',
            'active' => 'boolean',
        ]);

        // Parse JSON parameters if provided as string
        if (isset($validated['parameters']) && is_string($validated['parameters'])) {
            $validated['parameters'] = json_decode($validated['parameters'], true);
        }

        $policy->update($validated);

        return response()->json($policy);
    }

    /**
     * Remove the specified policy.
     */
    public function destroy(BandwidthPolicy $policy)
    {
        // Check if the policy has rules
        $rulesCount = $policy->rules()->count();
        if ($rulesCount > 0) {
            return response()->json([
                'message' => 'Cannot delete policy with rules. There are '.$rulesCount.' rules using this policy.',
            ], 422);
        }

        // Check if the policy has assignments
        $assignmentsCount = $policy->assignments()->count();
        if ($assignmentsCount > 0) {
            return response()->json([
                'message' => 'Cannot delete policy with assignments. There are '.$assignmentsCount.' assignments using this policy.',
            ], 422);
        }

        $policy->delete();

        return response()->json(null, 204);
    }

    /**
     * Get the rules for the specified policy.
     */
    public function rules(BandwidthPolicy $policy, Request $request)
    {
        $query = $policy->rules();

        // Apply filters
        if ($request->has('source_type')) {
            $query->where('source_type', $request->source_type);
        }

        if ($request->has('destination_type')) {
            $query->where('destination_type', $request->destination_type);
        }

        if ($request->has('protocol')) {
            $query->where('protocol', $request->protocol);
        }

        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'priority');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $rules = $query->paginate($request->get('per_page', 15));

        return response()->json($rules);
    }

    /**
     * Add a rule to the policy.
     */
    public function addRule(Request $request, BandwidthPolicy $policy)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'source_type' => 'nullable|string|max:255',
            'source_value' => 'nullable|string|max:255',
            'destination_type' => 'nullable|string|max:255',
            'destination_value' => 'nullable|string|max:255',
            'protocol' => 'nullable|string|max:255',
            'port_range' => 'nullable|string|max:255',
            'time_range' => 'nullable|string|max:255',
            'priority' => 'integer|min:1',
            'active' => 'boolean',
        ]);

        $rule = $policy->addRule($validated);

        return response()->json($rule, 201);
    }

    /**
     * Get the assignments for the specified policy.
     */
    public function assignments(BandwidthPolicy $policy, Request $request)
    {
        $query = $policy->assignments();

        // Apply filters
        if ($request->has('assignee_type')) {
            $query->where('assignee_type', $request->assignee_type);
        }

        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        if ($request->has('current')) {
            $query->inEffect();
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $assignments = $query->with('assignee')->paginate($request->get('per_page', 15));

        return response()->json($assignments);
    }

    /**
     * Assign the policy to a model.
     */
    public function assign(Request $request, BandwidthPolicy $policy)
    {
        $validated = $request->validate([
            'assignee_type' => 'required|string',
            'assignee_id' => 'required|integer',
            'starts_at' => 'nullable|date',
            'ends_at' => 'nullable|date|after:starts_at',
            'active' => 'boolean',
        ]);

        // Check if the assignee exists
        $assigneeClass = $validated['assignee_type'];
        $assigneeId = $validated['assignee_id'];

        if (! class_exists($assigneeClass)) {
            return response()->json([
                'message' => 'Invalid assignee type',
            ], 422);
        }

        $assignee = $assigneeClass::find($assigneeId);
        if (! $assignee) {
            return response()->json([
                'message' => 'Assignee not found',
            ], 404);
        }

        // Check if an assignment already exists
        $existingAssignment = BandwidthAssignment::where('assignable_type', get_class($policy))
            ->where('assignable_id', $policy->id)
            ->where('assignee_type', $assigneeClass)
            ->where('assignee_id', $assigneeId)
            ->first();

        if ($existingAssignment) {
            return response()->json([
                'message' => 'An assignment already exists for this policy and assignee',
                'assignment' => $existingAssignment,
            ], 422);
        }

        $assignment = $policy->assignTo(
            $assignee,
            $validated['starts_at'] ?? null,
            $validated['ends_at'] ?? null
        );

        if (isset($validated['active'])) {
            $assignment->active = $validated['active'];
            $assignment->save();
        }

        return response()->json($assignment, 201);
    }

    /**
     * Remove an assignment.
     */
    public function removeAssignment(Request $request, BandwidthPolicy $policy)
    {
        $validated = $request->validate([
            'assignment_id' => 'required|exists:bandwidth_assignments,id',
        ]);

        $assignment = BandwidthAssignment::findOrFail($validated['assignment_id']);

        // Check if the assignment belongs to this policy
        if ($assignment->assignable_type !== get_class($policy) || $assignment->assignable_id !== $policy->id) {
            return response()->json([
                'message' => 'Assignment does not belong to this policy',
            ], 403);
        }

        $assignment->delete();

        return response()->json([
            'message' => 'Assignment removed successfully',
        ]);
    }

    /**
     * Get a parameter value for the specified policy.
     */
    public function getParameterValue(Request $request, BandwidthPolicy $policy)
    {
        $validated = $request->validate([
            'key' => 'required|string',
        ]);

        $value = $policy->getParameterValue($validated['key']);

        if ($value === null) {
            return response()->json([
                'message' => 'Parameter key not found',
            ], 404);
        }

        return response()->json([
            'key' => $validated['key'],
            'value' => $value,
        ]);
    }

    /**
     * Set a parameter value for the specified policy.
     */
    public function setParameterValue(Request $request, BandwidthPolicy $policy)
    {
        $validated = $request->validate([
            'key' => 'required|string',
            'value' => 'required',
        ]);

        $policy->setParameterValue($validated['key'], $validated['value']);
        $policy->save();

        return response()->json([
            'message' => 'Parameter value updated successfully',
            'policy' => $policy,
        ]);
    }

    /**
     * Clone an existing policy.
     */
    public function clone(Request $request, BandwidthPolicy $policy)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:bandwidth_policies',
            'description' => 'nullable|string',
            'include_rules' => 'boolean',
        ]);

        $newPolicy = new BandwidthPolicy;
        $newPolicy->name = $validated['name'];
        $newPolicy->description = $validated['description'] ?? $policy->description;
        $newPolicy->type = $policy->type;
        $newPolicy->parameters = $policy->parameters;
        $newPolicy->active = true;
        $newPolicy->save();

        // Clone rules if requested
        if (isset($validated['include_rules']) && $validated['include_rules']) {
            foreach ($policy->rules as $rule) {
                $newRule = $rule->replicate();
                $newRule->policy_id = $newPolicy->id;
                $newRule->save();
            }
        }

        return response()->json([
            'message' => 'Policy cloned successfully',
            'policy' => $newPolicy->load('rules'),
        ], 201);
    }

    /**
     * Get policy types.
     */
    public function types()
    {
        return response()->json([
            'types' => [
                'limit',
                'shape',
                'prioritize',
                'quota',
            ],
        ]);
    }

    /**
     * Get policy statistics.
     */
    public function statistics()
    {
        $totalPolicies = BandwidthPolicy::count();
        $activePolicies = BandwidthPolicy::where('active', true)->count();

        $policiesByType = BandwidthPolicy::select('type')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('type')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $policiesWithMostRules = BandwidthPolicy::withCount('rules')
            ->orderBy('rules_count', 'desc')
            ->limit(10)
            ->get();

        $policiesWithMostAssignments = BandwidthPolicy::withCount('assignments')
            ->orderBy('assignments_count', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'total_policies' => $totalPolicies,
            'active_policies' => $activePolicies,
            'policies_by_type' => $policiesByType,
            'policies_with_most_rules' => $policiesWithMostRules,
            'policies_with_most_assignments' => $policiesWithMostAssignments,
        ]);
    }
}
