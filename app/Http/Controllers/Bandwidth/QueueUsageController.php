<?php

namespace App\Http\Controllers\Bandwidth;

use App\Http\Controllers\Controller;
use App\Models\Bandwidth\QueueUsage;
use App\Models\Network\NetworkDevice;
use Illuminate\Http\Request;
use Inertia\Inertia;

class QueueUsageController extends Controller
{
    /**
     * Display queue usage data.
     */
    public function index(Request $request)
    {
        $validated = $request->validate([
            'search' => 'nullable|string|max:255',
            'device_id' => 'nullable|integer|exists:network_devices,id',
            'show_mapped' => 'nullable|string|in:all,mapped,unmapped',
            'period_start' => 'nullable|date',
            'period_end' => 'nullable|date|after_or_equal:period_start',
            'sort_field' => 'nullable|string|in:queue_name,target_ip,download_bytes,upload_bytes,total_bytes,period_start',
            'sort_direction' => 'nullable|string|in:asc,desc',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = QueueUsage::with(['device', 'customer']);

        // Search filter
        if (! empty($validated['search'])) {
            $search = $validated['search'];
            $query->where(function ($q) use ($search) {
                $q->where('queue_name', 'like', "%{$search}%")
                    ->orWhere('target_ip', 'like', "%{$search}%");
            });
        }

        // Device filter
        if (! empty($validated['device_id'])) {
            $query->where('device_id', $validated['device_id']);
        }

        // Mapping filter
        $showMapped = $validated['show_mapped'] ?? 'all';
        if ($showMapped === 'mapped') {
            $query->mapped();
        } elseif ($showMapped === 'unmapped') {
            $query->unmapped();
        }

        // Date range filter
        if (! empty($validated['period_start']) && ! empty($validated['period_end'])) {
            $query->forPeriod($validated['period_start'], $validated['period_end']);
        } else {
            // Default to last 24 hours
            $query->forPeriod(now()->subDay(), now());
        }

        // Sorting
        $sortField = $validated['sort_field'] ?? 'total_bytes';
        $sortDirection = $validated['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        // Pagination
        $perPage = $validated['per_page'] ?? 15;
        $queueUsage = $query->paginate($perPage);

        // Get devices for filter dropdown
        $devices = NetworkDevice::where('type', 'mikrotik')
            ->where('status', 'active')
            ->select('id', 'name', 'ip_address')
            ->get();

        return Inertia::render('bandwidth/queue-usage/index', [
            'queueUsage' => $queueUsage,
            'devices' => $devices,
            'filters' => $validated,
        ]);
    }

    /**
     * Get queue usage statistics.
     */
    public function statistics(Request $request)
    {
        $validated = $request->validate([
            'device_id' => 'nullable|integer|exists:network_devices,id',
            'period_start' => 'nullable|date',
            'period_end' => 'nullable|date|after_or_equal:period_start',
        ]);

        $query = QueueUsage::query();

        // Apply filters
        if (! empty($validated['device_id'])) {
            $query->where('device_id', $validated['device_id']);
        }

        if (! empty($validated['period_start']) && ! empty($validated['period_end'])) {
            $query->forPeriod($validated['period_start'], $validated['period_end']);
        } else {
            $query->forPeriod(now()->subDay(), now());
        }

        // Get statistics
        $stats = $query->selectRaw('
            COUNT(*) as total_queues,
            COUNT(CASE WHEN is_mapped = 1 THEN 1 END) as mapped_queues,
            COUNT(CASE WHEN is_mapped = 0 THEN 1 END) as unmapped_queues,
            SUM(download_bytes) as total_download,
            SUM(upload_bytes) as total_upload,
            SUM(total_bytes) as total_bandwidth,
            AVG(total_bytes) as avg_usage
        ')->first();

        // Get top queues by usage
        $topQueues = $query->select('queue_name', 'target_ip', 'device_id', 'is_mapped')
            ->selectRaw('SUM(total_bytes) as total_usage')
            ->groupBy('queue_name', 'target_ip', 'device_id', 'is_mapped')
            ->orderBy('total_usage', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'statistics' => $stats,
            'top_queues' => $topQueues,
        ]);
    }

    /**
     * Export queue usage data.
     */
    public function export(Request $request)
    {
        $validated = $request->validate([
            'device_id' => 'nullable|integer|exists:network_devices,id',
            'show_mapped' => 'nullable|string|in:all,mapped,unmapped',
            'period_start' => 'nullable|date',
            'period_end' => 'nullable|date|after_or_equal:period_start',
            'format' => 'nullable|string|in:csv,xlsx',
        ]);

        $query = QueueUsage::with(['device', 'customer']);

        // Apply same filters as index
        if (! empty($validated['device_id'])) {
            $query->where('device_id', $validated['device_id']);
        }

        $showMapped = $validated['show_mapped'] ?? 'all';
        if ($showMapped === 'mapped') {
            $query->mapped();
        } elseif ($showMapped === 'unmapped') {
            $query->unmapped();
        }

        if (! empty($validated['period_start']) && ! empty($validated['period_end'])) {
            $query->forPeriod($validated['period_start'], $validated['period_end']);
        } else {
            $query->forPeriod(now()->subDay(), now());
        }

        $data = $query->orderBy('total_bytes', 'desc')->get();

        $format = $validated['format'] ?? 'csv';
        $filename = 'queue_usage_'.now()->format('Y-m-d_H-i-s').'.'.$format;

        if ($format === 'csv') {
            return $this->exportCsv($data, $filename);
        }

        // Add Excel export if needed
        return response()->json(['error' => 'Format not supported'], 400);
    }

    /**
     * Export data as CSV.
     */
    private function exportCsv($data, $filename)
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($data) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Queue Name',
                'Target IP',
                'Device',
                'Customer',
                'Download (GB)',
                'Upload (GB)',
                'Total (GB)',
                'Period Start',
                'Period End',
                'Mapped',
            ]);

            // CSV data
            foreach ($data as $usage) {
                fputcsv($file, [
                    $usage->queue_name,
                    $usage->target_ip,
                    $usage->device->name ?? 'Unknown',
                    $usage->customer->name ?? 'Unmapped',
                    round($usage->download_gb, 3),
                    round($usage->upload_gb, 3),
                    round($usage->total_gb, 3),
                    $usage->period_start->format('Y-m-d H:i:s'),
                    $usage->period_end->format('Y-m-d H:i:s'),
                    $usage->is_mapped ? 'Yes' : 'No',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
