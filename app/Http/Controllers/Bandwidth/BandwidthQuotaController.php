<?php

namespace App\Http\Controllers\Bandwidth;

use App\Http\Controllers\Controller;
use App\Models\Bandwidth\BandwidthAssignment;
use App\Models\Bandwidth\BandwidthQuota;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class BandwidthQuotaController extends Controller
{
    /**
     * Display a listing of the quotas.
     */
    public function index(Request $request)
    {
        $query = BandwidthQuota::query();

        // Apply filters
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->search.'%')
                    ->orWhere('description', 'like', '%'.$request->search.'%');
            });
        }

        if ($request->has('period')) {
            $query->where('period', $request->period);
        }

        if ($request->has('action_on_exceed')) {
            $query->where('action_on_exceed', $request->action_on_exceed);
        }

        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        if ($request->has('min_download_limit')) {
            $query->where('download_limit', '>=', $request->min_download_limit);
        }

        if ($request->has('min_upload_limit')) {
            $query->where('upload_limit', '>=', $request->min_upload_limit);
        }

        if ($request->has('min_total_limit')) {
            $query->where('total_limit', '>=', $request->min_total_limit);
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $quotas = $query->paginate($request->get('per_page', 15));

        return response()->json($quotas);
    }

    /**
     * Store a newly created quota.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:bandwidth_quotas',
            'description' => 'nullable|string',
            'download_limit' => 'required|integer|min:1',
            'upload_limit' => 'required|integer|min:1',
            'total_limit' => 'required|integer|min:1',
            'period' => 'required|string|in:daily,weekly,monthly,custom',
            'custom_period' => 'nullable|required_if:period,custom|integer|min:1',
            'action_on_exceed' => 'required|string|in:notify,throttle,block',
            'action_parameters' => 'nullable|json',
            'active' => 'boolean',
        ]);

        // Parse JSON action parameters if provided as string
        if (isset($validated['action_parameters']) && is_string($validated['action_parameters'])) {
            $validated['action_parameters'] = json_decode($validated['action_parameters'], true);
        }

        $quota = BandwidthQuota::create($validated);

        return response()->json($quota, 201);
    }

    /**
     * Display the specified quota.
     */
    public function show(BandwidthQuota $quota)
    {
        return response()->json($quota);
    }

    /**
     * Update the specified quota.
     */
    public function update(Request $request, BandwidthQuota $quota)
    {
        $validated = $request->validate([
            'name' => [
                'string',
                'max:255',
                Rule::unique('bandwidth_quotas')->ignore($quota->id),
            ],
            'description' => 'nullable|string',
            'download_limit' => 'integer|min:1',
            'upload_limit' => 'integer|min:1',
            'total_limit' => 'integer|min:1',
            'period' => 'string|in:daily,weekly,monthly,custom',
            'custom_period' => 'nullable|required_if:period,custom|integer|min:1',
            'action_on_exceed' => 'string|in:notify,throttle,block',
            'action_parameters' => 'nullable|json',
            'active' => 'boolean',
        ]);

        // Parse JSON action parameters if provided as string
        if (isset($validated['action_parameters']) && is_string($validated['action_parameters'])) {
            $validated['action_parameters'] = json_decode($validated['action_parameters'], true);
        }

        $quota->update($validated);

        return response()->json($quota);
    }

    /**
     * Remove the specified quota.
     */
    public function destroy(BandwidthQuota $quota)
    {
        // Check if the quota has assignments
        $assignmentsCount = $quota->assignments()->count();
        if ($assignmentsCount > 0) {
            return response()->json([
                'message' => 'Cannot delete quota with assignments. There are '.$assignmentsCount.' assignments using this quota.',
            ], 422);
        }

        $quota->delete();

        return response()->json(null, 204);
    }

    /**
     * Get the assignments for the specified quota.
     */
    public function assignments(BandwidthQuota $quota, Request $request)
    {
        $query = $quota->assignments();

        // Apply filters
        if ($request->has('assignee_type')) {
            $query->where('assignee_type', $request->assignee_type);
        }

        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        if ($request->has('current')) {
            $query->inEffect();
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $assignments = $query->with('assignee')->paginate($request->get('per_page', 15));

        return response()->json($assignments);
    }

    /**
     * Assign the quota to a model.
     */
    public function assign(Request $request, BandwidthQuota $quota)
    {
        $validated = $request->validate([
            'assignee_type' => 'required|string',
            'assignee_id' => 'required|integer',
            'starts_at' => 'nullable|date',
            'ends_at' => 'nullable|date|after:starts_at',
            'active' => 'boolean',
        ]);

        // Check if the assignee exists
        $assigneeClass = $validated['assignee_type'];
        $assigneeId = $validated['assignee_id'];

        if (! class_exists($assigneeClass)) {
            return response()->json([
                'message' => 'Invalid assignee type',
            ], 422);
        }

        $assignee = $assigneeClass::find($assigneeId);
        if (! $assignee) {
            return response()->json([
                'message' => 'Assignee not found',
            ], 404);
        }

        // Check if an assignment already exists
        $existingAssignment = BandwidthAssignment::where('assignable_type', get_class($quota))
            ->where('assignable_id', $quota->id)
            ->where('assignee_type', $assigneeClass)
            ->where('assignee_id', $assigneeId)
            ->first();

        if ($existingAssignment) {
            return response()->json([
                'message' => 'An assignment already exists for this quota and assignee',
                'assignment' => $existingAssignment,
            ], 422);
        }

        $assignment = $quota->assignTo(
            $assignee,
            $validated['starts_at'] ?? null,
            $validated['ends_at'] ?? null
        );

        if (isset($validated['active'])) {
            $assignment->active = $validated['active'];
            $assignment->save();
        }

        return response()->json($assignment, 201);
    }

    /**
     * Remove an assignment.
     */
    public function removeAssignment(Request $request, BandwidthQuota $quota)
    {
        $validated = $request->validate([
            'assignment_id' => 'required|exists:bandwidth_assignments,id',
        ]);

        $assignment = BandwidthAssignment::findOrFail($validated['assignment_id']);

        // Check if the assignment belongs to this quota
        if ($assignment->assignable_type !== get_class($quota) || $assignment->assignable_id !== $quota->id) {
            return response()->json([
                'message' => 'Assignment does not belong to this quota',
            ], 403);
        }

        $assignment->delete();

        return response()->json([
            'message' => 'Assignment removed successfully',
        ]);
    }

    /**
     * Get quotas with limits in GB.
     */
    public function quotasInGb()
    {
        $quotas = BandwidthQuota::active()->get()->map(function ($quota) {
            return [
                'id' => $quota->id,
                'name' => $quota->name,
                'description' => $quota->description,
                'download_limit_gb' => $quota->getDownloadLimitGbAttribute(),
                'upload_limit_gb' => $quota->getUploadLimitGbAttribute(),
                'total_limit_gb' => $quota->getTotalLimitGbAttribute(),
                'period' => $quota->period,
                'custom_period' => $quota->custom_period,
                'action_on_exceed' => $quota->action_on_exceed,
                'action_parameters' => $quota->action_parameters,
                'active' => $quota->active,
            ];
        });

        return response()->json($quotas);
    }

    /**
     * Get the period dates for a quota.
     */
    public function periodDates(Request $request, BandwidthQuota $quota)
    {
        $validated = $request->validate([
            'date' => 'nullable|date',
        ]);

        $date = isset($validated['date']) ? new \DateTime($validated['date']) : null;
        $periodDates = $quota->getPeriodDates($date);

        return response()->json([
            'period' => $quota->period,
            'custom_period' => $quota->custom_period,
            'start_date' => $periodDates['start'],
            'end_date' => $periodDates['end'],
        ]);
    }

    /**
     * Get an action parameter value for the specified quota.
     */
    public function getActionParameterValue(Request $request, BandwidthQuota $quota)
    {
        $validated = $request->validate([
            'key' => 'required|string',
        ]);

        $value = $quota->getActionParameterValue($validated['key']);

        if ($value === null) {
            return response()->json([
                'message' => 'Action parameter key not found',
            ], 404);
        }

        return response()->json([
            'key' => $validated['key'],
            'value' => $value,
        ]);
    }

    /**
     * Set an action parameter value for the specified quota.
     */
    public function setActionParameterValue(Request $request, BandwidthQuota $quota)
    {
        $validated = $request->validate([
            'key' => 'required|string',
            'value' => 'required',
        ]);

        $quota->setActionParameterValue($validated['key'], $validated['value']);
        $quota->save();

        return response()->json([
            'message' => 'Action parameter value updated successfully',
            'quota' => $quota,
        ]);
    }

    /**
     * Clone an existing quota.
     */
    public function clone(Request $request, BandwidthQuota $quota)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:bandwidth_quotas',
            'description' => 'nullable|string',
        ]);

        $newQuota = new BandwidthQuota;
        $newQuota->name = $validated['name'];
        $newQuota->description = $validated['description'] ?? $quota->description;
        $newQuota->download_limit = $quota->download_limit;
        $newQuota->upload_limit = $quota->upload_limit;
        $newQuota->total_limit = $quota->total_limit;
        $newQuota->period = $quota->period;
        $newQuota->custom_period = $quota->custom_period;
        $newQuota->action_on_exceed = $quota->action_on_exceed;
        $newQuota->action_parameters = $quota->action_parameters;
        $newQuota->active = true;
        $newQuota->save();

        return response()->json([
            'message' => 'Quota cloned successfully',
            'quota' => $newQuota,
        ], 201);
    }

    /**
     * Get quota periods.
     */
    public function periods()
    {
        return response()->json([
            'periods' => [
                'daily',
                'weekly',
                'monthly',
                'custom',
            ],
        ]);
    }

    /**
     * Get quota actions on exceed.
     */
    public function actionsOnExceed()
    {
        return response()->json([
            'actions_on_exceed' => [
                'notify',
                'throttle',
                'block',
            ],
        ]);
    }

    /**
     * Get quota statistics.
     */
    public function statistics()
    {
        $totalQuotas = BandwidthQuota::count();
        $activeQuotas = BandwidthQuota::where('active', true)->count();

        $quotasByPeriod = BandwidthQuota::select('period')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('period')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $quotasByActionOnExceed = BandwidthQuota::select('action_on_exceed')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('action_on_exceed')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $quotasWithMostAssignments = BandwidthQuota::withCount('assignments')
            ->orderBy('assignments_count', 'desc')
            ->limit(10)
            ->get();

        $avgDownloadLimit = BandwidthQuota::avg('download_limit');
        $avgUploadLimit = BandwidthQuota::avg('upload_limit');
        $avgTotalLimit = BandwidthQuota::avg('total_limit');

        return response()->json([
            'total_quotas' => $totalQuotas,
            'active_quotas' => $activeQuotas,
            'quotas_by_period' => $quotasByPeriod,
            'quotas_by_action_on_exceed' => $quotasByActionOnExceed,
            'quotas_with_most_assignments' => $quotasWithMostAssignments,
            'average_download_limit' => $avgDownloadLimit,
            'average_upload_limit' => $avgUploadLimit,
            'average_total_limit' => $avgTotalLimit,
        ]);
    }
}
