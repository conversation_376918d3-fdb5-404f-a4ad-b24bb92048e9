<?php

namespace App\Http\Controllers\Bandwidth;

use App\Http\Controllers\Controller;
use App\Models\Bandwidth\BandwidthUsage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BandwidthUsageController extends Controller
{
    /**
     * Display a listing of the usage records.
     */
    public function index(Request $request)
    {
        $query = BandwidthUsage::query();

        // Apply filters
        if ($request->has('usageable_type')) {
            $query->where('usageable_type', $request->usageable_type);
        }

        if ($request->has('usageable_id')) {
            $query->where('usageable_id', $request->usageable_id);
        }

        // Filter by customer ID (across all service types)
        if ($request->has('customer_id')) {
            $customerId = $request->customer_id;
            $query->where(function ($q) use ($customerId) {
                $q->whereHasMorph('usageable', ['App\Models\Services\StaticIpService'], function ($sq) use ($customerId) {
                    $sq->where('customer_id', $customerId);
                })->orWhereHasMorph('usageable', ['App\Models\Services\PppoeService'], function ($sq) use ($customerId) {
                    $sq->where('customer_id', $customerId);
                });
            });
        }

        if ($request->has('start_date')) {
            $query->where('period_start', '>=', $request->start_date);
        }

        if ($request->has('end_date')) {
            $query->where('period_end', '<=', $request->end_date);
        }

        if ($request->has('min_download')) {
            $query->where('download', '>=', $request->min_download);
        }

        if ($request->has('min_upload')) {
            $query->where('upload', '>=', $request->min_upload);
        }

        if ($request->has('min_total')) {
            $query->where('total', '>=', $request->min_total);
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'period_start');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $usage = $query->with('usageable')->paginate($request->get('per_page', 15));

        return response()->json($usage);
    }

    /**
     * Store a newly created usage record.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'usageable_type' => 'required|string',
            'usageable_id' => 'required|integer',
            'download' => 'required|integer|min:0',
            'upload' => 'required|integer|min:0',
            'period_start' => 'required|date',
            'period_end' => 'required|date|after_or_equal:period_start',
        ]);

        // Check if the usageable exists
        $usageableClass = $validated['usageable_type'];
        $usageableId = $validated['usageable_id'];

        if (! class_exists($usageableClass)) {
            return response()->json([
                'message' => 'Invalid usageable type',
            ], 422);
        }

        $usageable = $usageableClass::find($usageableId);
        if (! $usageable) {
            return response()->json([
                'message' => 'Usageable not found',
            ], 404);
        }

        // Calculate total
        $total = $validated['download'] + $validated['upload'];

        // Check if a usage record already exists for this period
        $existingUsage = BandwidthUsage::where('usageable_type', $usageableClass)
            ->where('usageable_id', $usageableId)
            ->where('period_start', $validated['period_start'])
            ->where('period_end', $validated['period_end'])
            ->first();

        if ($existingUsage) {
            // Update existing record
            $existingUsage->download += $validated['download'];
            $existingUsage->upload += $validated['upload'];
            $existingUsage->total = $existingUsage->download + $existingUsage->upload;
            $existingUsage->save();

            return response()->json([
                'message' => 'Usage record updated successfully',
                'usage' => $existingUsage,
            ]);
        }

        // Create new record
        $usage = BandwidthUsage::create([
            'usageable_type' => $usageableClass,
            'usageable_id' => $usageableId,
            'download' => $validated['download'],
            'upload' => $validated['upload'],
            'total' => $total,
            'period_start' => $validated['period_start'],
            'period_end' => $validated['period_end'],
        ]);

        return response()->json($usage, 201);
    }

    /**
     * Display the specified usage record.
     */
    public function show(BandwidthUsage $usage)
    {
        $usage->load('usageable');

        return response()->json($usage);
    }

    /**
     * Update the specified usage record.
     */
    public function update(Request $request, BandwidthUsage $usage)
    {
        $validated = $request->validate([
            'download' => 'integer|min:0',
            'upload' => 'integer|min:0',
            'period_start' => 'date',
            'period_end' => 'date|after_or_equal:period_start',
        ]);

        // Update download and upload
        if (isset($validated['download'])) {
            $usage->download = $validated['download'];
        }

        if (isset($validated['upload'])) {
            $usage->upload = $validated['upload'];
        }

        // Recalculate total
        $usage->total = $usage->download + $usage->upload;

        // Update period dates
        if (isset($validated['period_start'])) {
            $usage->period_start = $validated['period_start'];
        }

        if (isset($validated['period_end'])) {
            $usage->period_end = $validated['period_end'];
        }

        $usage->save();

        return response()->json($usage);
    }

    /**
     * Remove the specified usage record.
     */
    public function destroy(BandwidthUsage $usage)
    {
        $usage->delete();

        return response()->json(null, 204);
    }

    /**
     * Record usage for a model.
     */
    public function recordUsage(Request $request)
    {
        $validated = $request->validate([
            'usageable_type' => 'required|string',
            'usageable_id' => 'required|integer',
            'download' => 'required|integer|min:0',
            'upload' => 'required|integer|min:0',
            'period_start' => 'nullable|date',
            'period_end' => 'nullable|date|after_or_equal:period_start',
        ]);

        // Check if the usageable exists
        $usageableClass = $validated['usageable_type'];
        $usageableId = $validated['usageable_id'];

        if (! class_exists($usageableClass)) {
            return response()->json([
                'message' => 'Invalid usageable type',
            ], 422);
        }

        $usageable = $usageableClass::find($usageableId);
        if (! $usageable) {
            return response()->json([
                'message' => 'Usageable not found',
            ], 404);
        }

        // Use default period if not provided
        $periodStart = $validated['period_start'] ?? now()->startOfDay();
        $periodEnd = $validated['period_end'] ?? now()->endOfDay();

        // Record usage
        $usage = BandwidthUsage::recordUsage(
            $usageable,
            $validated['download'],
            $validated['upload'],
            $periodStart,
            $periodEnd
        );

        return response()->json([
            'message' => 'Usage recorded successfully',
            'usage' => $usage,
        ]);
    }

    /**
     * Get usage for a specific model.
     */
    public function getUsage(Request $request)
    {
        $validated = $request->validate([
            'usageable_type' => 'nullable|string',
            'usageable_id' => 'nullable|integer',
            'customer_id' => 'nullable|integer|exists:customers,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'period' => 'nullable|string|in:daily,weekly,monthly,all,day,week,month',
        ]);

        // Normalize period values
        $period = $validated['period'] ?? 'all';
        $periodMap = [
            'day' => 'daily',
            'week' => 'weekly',
            'month' => 'monthly',
        ];
        $period = $periodMap[$period] ?? $period;

        // Set default dates if not provided
        $startDate = $validated['start_date'] ?? now()->subMonth()->startOfDay();
        $endDate = $validated['end_date'] ?? now()->endOfDay();

        // Build query based on filtering criteria
        $query = BandwidthUsage::where('period_start', '>=', $startDate)
            ->where('period_end', '<=', $endDate);

        // Filter by specific usageable if provided
        if (isset($validated['usageable_type']) && isset($validated['usageable_id'])) {
            $usageableClass = $validated['usageable_type'];
            $usageableId = $validated['usageable_id'];

            if (! class_exists($usageableClass)) {
                return response()->json([
                    'message' => 'Invalid usageable type',
                ], 422);
            }

            $usageable = $usageableClass::find($usageableId);
            if (! $usageable) {
                return response()->json([
                    'message' => 'Usageable not found',
                ], 404);
            }

            $query->where('usageable_type', $usageableClass)
                ->where('usageable_id', $usageableId);
        }
        // Filter by customer ID if provided
        elseif (isset($validated['customer_id'])) {
            $customerId = $validated['customer_id'];

            // Check what types of services this customer has
            $hasStaticIp = \App\Models\Services\StaticIpService::where('customer_id', $customerId)->exists();
            $hasPppoe = \App\Models\Services\PppoeService::where('customer_id', $customerId)->exists();

            $combinedUsage = collect();

            // For PPPoE customers, get data from BandwidthUsage table
            if ($hasPppoe) {
                $query->where(function ($q) use ($customerId) {
                    $q->whereHasMorph('usageable', ['App\Models\Services\PppoeService'], function ($sq) use ($customerId) {
                        $sq->where('customer_id', $customerId);
                    });
                });
                $bandwidthUsage = $query->get();
                $combinedUsage = $this->combineUsageData($bandwidthUsage, collect());
            }

            // For Static IP customers, get data from QueueUsage table
            if ($hasStaticIp) {
                $queueUsage = $this->getQueueUsageForCustomer($customerId, $startDate, $endDate);
                $staticUsage = $this->combineUsageData(collect(), $queueUsage);
                $combinedUsage = $combinedUsage->merge($staticUsage);
            }

            return response()->json([
                'usage' => $combinedUsage->sortByDesc('period_start')->values(),
                'summary' => $this->calculateUsageSummary($combinedUsage),
            ]);
        } else {
            return response()->json([
                'message' => 'Either usageable_type and usageable_id, or customer_id must be provided',
            ], 422);
        }

        // Apply grouping by period if specified
        // (period is already normalized above)

        if ($period === 'daily') {
            $usage = $query->select(
                DB::raw('DATE(period_start) as date'),
                DB::raw('SUM(download) as download'),
                DB::raw('SUM(upload) as upload'),
                DB::raw('SUM(total) as total')
            )
                ->groupBy(DB::raw('DATE(period_start)'))
                ->orderBy('date')
                ->get();
        } elseif ($period === 'weekly') {
            $usage = $query->select(
                DB::raw('YEARWEEK(period_start) as yearweek'),
                DB::raw('MIN(period_start) as week_start'),
                DB::raw('SUM(download) as download'),
                DB::raw('SUM(upload) as upload'),
                DB::raw('SUM(total) as total')
            )
                ->groupBy(DB::raw('YEARWEEK(period_start)'))
                ->orderBy('yearweek')
                ->get();
        } elseif ($period === 'monthly') {
            $usage = $query->select(
                DB::raw('YEAR(period_start) as year'),
                DB::raw('MONTH(period_start) as month'),
                DB::raw('MIN(period_start) as month_start'),
                DB::raw('SUM(download) as download'),
                DB::raw('SUM(upload) as upload'),
                DB::raw('SUM(total) as total')
            )
                ->groupBy(DB::raw('YEAR(period_start)'), DB::raw('MONTH(period_start)'))
                ->orderBy('year')
                ->orderBy('month')
                ->get();
        } else {
            // All records without grouping
            $usage = $query->orderBy('period_start')->get();
        }

        // Calculate totals
        $totalDownload = $query->sum('download');
        $totalUpload = $query->sum('upload');
        $totalUsage = $query->sum('total');

        return response()->json([
            'usage' => $usage,
            'totals' => [
                'download' => $totalDownload,
                'download_mb' => $totalDownload / (1024 * 1024),
                'download_gb' => $totalDownload / (1024 * 1024 * 1024),
                'upload' => $totalUpload,
                'upload_mb' => $totalUpload / (1024 * 1024),
                'upload_gb' => $totalUpload / (1024 * 1024 * 1024),
                'total' => $totalUsage,
                'total_mb' => $totalUsage / (1024 * 1024),
                'total_gb' => $totalUsage / (1024 * 1024 * 1024),
            ],
            'period' => $period,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);
    }

    /**
     * Get top users by usage.
     */
    public function topUsers(Request $request)
    {
        $validated = $request->validate([
            'usageable_type' => 'required|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'limit' => 'nullable|integer|min:1|max:100',
            'sort_by' => 'nullable|string|in:download,upload,total',
        ]);

        // Set default values
        $startDate = $validated['start_date'] ?? now()->subMonth()->startOfDay();
        $endDate = $validated['end_date'] ?? now()->endOfDay();
        $limit = $validated['limit'] ?? 10;
        $sortBy = $validated['sort_by'] ?? 'total';

        // Get top users
        $topUsers = BandwidthUsage::where('usageable_type', $validated['usageable_type'])
            ->where('period_start', '>=', $startDate)
            ->where('period_end', '<=', $endDate)
            ->select('usageable_id')
            ->selectRaw('SUM(download) as download')
            ->selectRaw('SUM(upload) as upload')
            ->selectRaw('SUM(total) as total')
            ->groupBy('usageable_id')
            ->orderBy($sortBy, 'desc')
            ->limit($limit)
            ->with('usageable')
            ->get();

        return response()->json([
            'top_users' => $topUsers,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'sort_by' => $sortBy,
        ]);
    }

    /**
     * Get real-time usage dashboard data.
     */
    public function dashboard(Request $request)
    {
        $validated = $request->validate([
            'period' => 'nullable|string|in:hour,day,week,month',
            'device_id' => 'nullable|integer|exists:network_devices,id',
        ]);

        $period = $validated['period'] ?? 'day';
        $deviceId = $validated['device_id'] ?? null;

        // Calculate time range based on period
        $timeRanges = [
            'hour' => now()->subHour(),
            'day' => now()->subDay(),
            'week' => now()->subWeek(),
            'month' => now()->subMonth(),
        ];

        $startTime = $timeRanges[$period];

        // Build base query
        $query = BandwidthUsage::where('created_at', '>=', $startTime);

        // Filter by device if specified
        if ($deviceId) {
            $query->where(function ($q) use ($deviceId) {
                $q->whereHasMorph('usageable', ['App\Models\Services\StaticIpService'], function ($sq) use ($deviceId) {
                    $sq->where('device_id', $deviceId);
                })->orWhereHasMorph('usageable', ['App\Models\Services\PppoeService'], function ($sq) use ($deviceId) {
                    $sq->where('device_id', $deviceId);
                });
            });
        }

        // Get summary statistics
        $summary = $query->selectRaw('
            COUNT(*) as total_records,
            COUNT(DISTINCT usageable_id, usageable_type) as active_customers,
            SUM(download) as total_download,
            SUM(upload) as total_upload,
            SUM(total) as total_bandwidth,
            AVG(total) as avg_usage
        ')->first();

        // Get top users
        $topUsers = $query->select('usageable_id', 'usageable_type')
            ->selectRaw('SUM(total) as total_usage, SUM(download) as download, SUM(upload) as upload')
            ->with('usageable')
            ->groupBy('usageable_id', 'usageable_type')
            ->orderBy('total_usage', 'desc')
            ->limit(10)
            ->get();

        // Get hourly breakdown for charts
        $hourlyUsage = $query->selectRaw('
            DATE_FORMAT(created_at, "%Y-%m-%d %H:00") as hour,
            SUM(download) as download,
            SUM(upload) as upload,
            SUM(total) as total,
            COUNT(DISTINCT usageable_id, usageable_type) as customers
        ')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();

        // Get device breakdown
        $deviceUsage = [];
        if (! $deviceId) {
            $devices = \App\Models\Network\NetworkDevice::where('type', 'mikrotik')
                ->where('status', 'active')
                ->get();

            foreach ($devices as $device) {
                $deviceQuery = BandwidthUsage::where('created_at', '>=', $startTime)
                    ->where(function ($q) use ($device) {
                        $q->whereHasMorph('usageable', ['App\Models\Services\StaticIpService'], function ($sq) use ($device) {
                            $sq->where('device_id', $device->id);
                        })->orWhereHasMorph('usageable', ['App\Models\Services\PppoeService'], function ($sq) use ($device) {
                            $sq->where('device_id', $device->id);
                        });
                    });

                $deviceStats = $deviceQuery->selectRaw('
                    SUM(total) as total_usage,
                    COUNT(DISTINCT usageable_id, usageable_type) as customers
                ')->first();

                $deviceUsage[] = [
                    'device' => $device,
                    'total_usage' => $deviceStats->total_usage ?? 0,
                    'customers' => $deviceStats->customers ?? 0,
                ];
            }
        }

        return response()->json([
            'period' => $period,
            'start_time' => $startTime,
            'summary' => [
                'total_records' => $summary->total_records ?? 0,
                'active_customers' => $summary->active_customers ?? 0,
                'total_download' => $summary->total_download ?? 0,
                'total_upload' => $summary->total_upload ?? 0,
                'total_bandwidth' => $summary->total_bandwidth ?? 0,
                'avg_usage' => $summary->avg_usage ?? 0,
            ],
            'top_users' => $topUsers,
            'hourly_usage' => $hourlyUsage,
            'device_usage' => $deviceUsage,
            'last_updated' => now(),
        ]);
    }

    /**
     * Get usage statistics.
     */
    public function statistics(Request $request)
    {
        $validated = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        // Set default dates if not provided
        $startDate = $validated['start_date'] ?? now()->subMonth()->startOfDay();
        $endDate = $validated['end_date'] ?? now()->endOfDay();

        // Total usage
        $totalUsage = BandwidthUsage::where('period_start', '>=', $startDate)
            ->where('period_end', '<=', $endDate)
            ->selectRaw('SUM(download) as download')
            ->selectRaw('SUM(upload) as upload')
            ->selectRaw('SUM(total) as total')
            ->first();

        // Usage by usageable type
        $usageByType = BandwidthUsage::where('period_start', '>=', $startDate)
            ->where('period_end', '<=', $endDate)
            ->select('usageable_type')
            ->selectRaw('SUM(download) as download')
            ->selectRaw('SUM(upload) as upload')
            ->selectRaw('SUM(total) as total')
            ->groupBy('usageable_type')
            ->orderByRaw('SUM(total) DESC')
            ->get();

        // Daily usage
        $dailyUsage = BandwidthUsage::where('period_start', '>=', $startDate)
            ->where('period_end', '<=', $endDate)
            ->select(DB::raw('DATE(period_start) as date'))
            ->selectRaw('SUM(download) as download')
            ->selectRaw('SUM(upload) as upload')
            ->selectRaw('SUM(total) as total')
            ->groupBy(DB::raw('DATE(period_start)'))
            ->orderBy('date')
            ->get();

        return response()->json([
            'total_usage' => [
                'download' => $totalUsage->download,
                'download_mb' => $totalUsage->download / (1024 * 1024),
                'download_gb' => $totalUsage->download / (1024 * 1024 * 1024),
                'upload' => $totalUsage->upload,
                'upload_mb' => $totalUsage->upload / (1024 * 1024),
                'upload_gb' => $totalUsage->upload / (1024 * 1024 * 1024),
                'total' => $totalUsage->total,
                'total_mb' => $totalUsage->total / (1024 * 1024),
                'total_gb' => $totalUsage->total / (1024 * 1024 * 1024),
            ],
            'usage_by_type' => $usageByType,
            'daily_usage' => $dailyUsage,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);
    }

    /**
     * Get queue usage for a customer.
     */
    private function getQueueUsageForCustomer($customerId, $startDate, $endDate)
    {
        // Get customer's static IP services
        $staticIpServices = \App\Models\Services\StaticIpService::where('customer_id', $customerId)->get();

        if ($staticIpServices->isEmpty()) {
            return collect();
        }

        // Get queue usage for customer's IP addresses
        $ipAddresses = $staticIpServices->pluck('ip_address')->toArray();

        $queueUsage = \App\Models\Bandwidth\QueueUsage::where(function ($query) use ($ipAddresses) {
            foreach ($ipAddresses as $ip) {
                $query->orWhere('target_ip', $ip)
                    ->orWhere('target_ip', $ip.'/32');
            }
        })
            ->where('period_start', '>=', $startDate)
            ->where('period_end', '<=', $endDate)
            ->get();

        return $queueUsage;
    }

    /**
     * Combine bandwidth usage and queue usage data.
     */
    private function combineUsageData($bandwidthUsage, $queueUsage)
    {
        $combined = collect();

        // Add bandwidth usage records
        foreach ($bandwidthUsage as $usage) {
            $combined->push([
                'id' => $usage->id,
                'download' => $usage->download,
                'upload' => $usage->upload,
                'total' => $usage->total,
                'period_start' => $usage->period_start,
                'period_end' => $usage->period_end,
                'service_type' => class_basename($usage->usageable_type),
                'service_name' => $usage->usageable->name ?? 'Unknown Service',
                'source' => 'bandwidth_usage',
            ]);
        }

        // Add queue usage records
        foreach ($queueUsage as $usage) {
            // Determine service type based on the actual service_type in the record
            $serviceType = 'Static IP'; // Default for queue usage
            if ($usage->service_type === 'pppoe') {
                $serviceType = 'PPPoE';
            } elseif ($usage->service_type === 'static_ip') {
                $serviceType = 'Static IP';
            }

            $combined->push([
                'id' => 'queue_'.$usage->id,
                'download' => $usage->download_bytes,
                'upload' => $usage->upload_bytes,
                'total' => $usage->total_bytes,
                'period_start' => $usage->period_start,
                'period_end' => $usage->period_end,
                'service_type' => $serviceType,
                'service_name' => $usage->queue_name,
                'source' => 'queue_usage',
            ]);
        }

        return $combined->sortByDesc('period_start')->values();
    }

    /**
     * Calculate usage summary from combined data.
     */
    private function calculateUsageSummary($combinedUsage)
    {
        $totalDownload = $combinedUsage->sum('download');
        $totalUpload = $combinedUsage->sum('upload');
        $totalBandwidth = $combinedUsage->sum('total');

        // Calculate current month usage
        $currentMonthStart = now()->startOfMonth();
        $currentMonth = $combinedUsage->filter(function ($usage) use ($currentMonthStart) {
            return $usage['period_start'] >= $currentMonthStart;
        })->sum('total');

        // Calculate last month usage
        $lastMonthStart = now()->subMonth()->startOfMonth();
        $lastMonthEnd = now()->subMonth()->endOfMonth();
        $lastMonth = $combinedUsage->filter(function ($usage) use ($lastMonthStart, $lastMonthEnd) {
            return $usage['period_start'] >= $lastMonthStart && $usage['period_end'] <= $lastMonthEnd;
        })->sum('total');

        return [
            'total_download' => $totalDownload,
            'total_upload' => $totalUpload,
            'total_bandwidth' => $totalBandwidth,
            'current_month' => $currentMonth,
            'last_month' => $lastMonth,
        ];
    }
}
