<?php

namespace App\Http\Controllers\Bandwidth;

use App\Http\Controllers\Controller;
use App\Models\Bandwidth\BandwidthPolicy;
use App\Models\Bandwidth\BandwidthRule;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class BandwidthRuleController extends Controller
{
    /**
     * Display a listing of the rules.
     */
    public function index(Request $request)
    {
        $query = BandwidthRule::query();

        // Apply filters
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->search.'%')
                    ->orWhere('description', 'like', '%'.$request->search.'%');
            });
        }

        if ($request->has('policy_id')) {
            $query->where('policy_id', $request->policy_id);
        }

        if ($request->has('source_type')) {
            $query->where('source_type', $request->source_type);
        }

        if ($request->has('destination_type')) {
            $query->where('destination_type', $request->destination_type);
        }

        if ($request->has('protocol')) {
            $query->where('protocol', $request->protocol);
        }

        if ($request->has('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'priority');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $rules = $query->with('policy')->paginate($request->get('per_page', 15));

        return response()->json($rules);
    }

    /**
     * Store a newly created rule.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'policy_id' => 'required|exists:bandwidth_policies,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'source_type' => 'nullable|string|max:255',
            'source_value' => 'nullable|string|max:255',
            'destination_type' => 'nullable|string|max:255',
            'destination_value' => 'nullable|string|max:255',
            'protocol' => 'nullable|string|max:255',
            'port_range' => 'nullable|string|max:255',
            'time_range' => 'nullable|string|max:255',
            'priority' => 'integer|min:1',
            'active' => 'boolean',
        ]);

        $rule = BandwidthRule::create($validated);

        return response()->json($rule, 201);
    }

    /**
     * Display the specified rule.
     */
    public function show(BandwidthRule $rule)
    {
        $rule->load('policy');

        return response()->json($rule);
    }

    /**
     * Update the specified rule.
     */
    public function update(Request $request, BandwidthRule $rule)
    {
        $validated = $request->validate([
            'policy_id' => 'exists:bandwidth_policies,id',
            'name' => 'string|max:255',
            'description' => 'nullable|string',
            'source_type' => 'nullable|string|max:255',
            'source_value' => 'nullable|string|max:255',
            'destination_type' => 'nullable|string|max:255',
            'destination_value' => 'nullable|string|max:255',
            'protocol' => 'nullable|string|max:255',
            'port_range' => 'nullable|string|max:255',
            'time_range' => 'nullable|string|max:255',
            'priority' => 'integer|min:1',
            'active' => 'boolean',
        ]);

        $rule->update($validated);

        return response()->json($rule);
    }

    /**
     * Remove the specified rule.
     */
    public function destroy(BandwidthRule $rule)
    {
        $rule->delete();

        return response()->json(null, 204);
    }

    /**
     * Test if a rule matches the given parameters.
     */
    public function testMatch(Request $request, BandwidthRule $rule)
    {
        $validated = $request->validate([
            'source' => 'required|string',
            'destination' => 'required|string',
            'protocol' => 'nullable|string',
            'port' => 'nullable|integer',
            'time' => 'nullable|string',
        ]);

        $matches = $rule->matches(
            $validated['source'],
            $validated['destination'],
            $validated['protocol'] ?? null,
            $validated['port'] ?? null,
            $validated['time'] ?? null
        );

        return response()->json([
            'matches' => $matches,
            'rule' => $rule,
        ]);
    }

    /**
     * Get all rules for a specific policy.
     */
    public function policyRules(BandwidthPolicy $policy, Request $request)
    {
        $query = $policy->rules();

        // Apply filters
        if ($request->has('source_type')) {
            $query->where('source_type', $request->source_type);
        }

        if ($request->has('destination_type')) {
            $query->where('destination_type', $request->destination_type);
        }

        if ($request->has('protocol')) {
            $query->where('protocol', $request->protocol);
        }

        if ($request->has('active')) {
            $query->where('active', $request->boolean('active'));
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'priority');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $rules = $query->paginate($request->get('per_page', 15));

        return response()->json($rules);
    }

    /**
     * Reorder rules within a policy.
     */
    public function reorderRules(Request $request, BandwidthPolicy $policy)
    {
        $validated = $request->validate([
            'rule_ids' => 'required|array',
            'rule_ids.*' => 'required|exists:bandwidth_rules,id',
        ]);

        $ruleIds = $validated['rule_ids'];

        // Check if all rules belong to the policy
        $policyRuleIds = $policy->rules()->pluck('id')->toArray();
        $invalidRuleIds = array_diff($ruleIds, $policyRuleIds);

        if (! empty($invalidRuleIds)) {
            return response()->json([
                'message' => 'Some rules do not belong to this policy',
                'invalid_rule_ids' => $invalidRuleIds,
            ], 422);
        }

        // Update priorities based on the order of rule_ids
        foreach ($ruleIds as $index => $ruleId) {
            BandwidthRule::where('id', $ruleId)->update(['priority' => $index + 1]);
        }

        return response()->json([
            'message' => 'Rules reordered successfully',
        ]);
    }

    /**
     * Activate or deactivate a rule.
     */
    public function toggleActive(BandwidthRule $rule)
    {
        $rule->active = ! $rule->active;
        $rule->save();

        return response()->json([
            'message' => 'Rule '.($rule->active ? 'activated' : 'deactivated').' successfully',
            'rule' => $rule,
        ]);
    }

    /**
     * Clone an existing rule.
     */
    public function clone(Request $request, BandwidthRule $rule)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'policy_id' => 'nullable|exists:bandwidth_policies,id',
        ]);

        $newRule = $rule->replicate();
        $newRule->name = $validated['name'];
        $newRule->policy_id = $validated['policy_id'] ?? $rule->policy_id;
        $newRule->save();

        return response()->json([
            'message' => 'Rule cloned successfully',
            'rule' => $newRule,
        ], 201);
    }

    /**
     * Get source types used in rules.
     */
    public function sourceTypes()
    {
        $types = BandwidthRule::select('source_type')
            ->whereNotNull('source_type')
            ->distinct()
            ->orderBy('source_type')
            ->pluck('source_type');

        return response()->json($types);
    }

    /**
     * Get destination types used in rules.
     */
    public function destinationTypes()
    {
        $types = BandwidthRule::select('destination_type')
            ->whereNotNull('destination_type')
            ->distinct()
            ->orderBy('destination_type')
            ->pluck('destination_type');

        return response()->json($types);
    }

    /**
     * Get protocols used in rules.
     */
    public function protocols()
    {
        $protocols = BandwidthRule::select('protocol')
            ->whereNotNull('protocol')
            ->distinct()
            ->orderBy('protocol')
            ->pluck('protocol');

        return response()->json($protocols);
    }

    /**
     * Get rule statistics.
     */
    public function statistics()
    {
        $totalRules = BandwidthRule::count();
        $activeRules = BandwidthRule::where('active', true)->count();

        $rulesBySourceType = BandwidthRule::select('source_type')
            ->whereNotNull('source_type')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('source_type')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $rulesByDestinationType = BandwidthRule::select('destination_type')
            ->whereNotNull('destination_type')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('destination_type')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $rulesByProtocol = BandwidthRule::select('protocol')
            ->whereNotNull('protocol')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('protocol')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $rulesByPolicy = BandwidthRule::select('policy_id')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('policy_id')
            ->orderByRaw('COUNT(*) DESC')
            ->with('policy:id,name')
            ->get();

        return response()->json([
            'total_rules' => $totalRules,
            'active_rules' => $activeRules,
            'rules_by_source_type' => $rulesBySourceType,
            'rules_by_destination_type' => $rulesByDestinationType,
            'rules_by_protocol' => $rulesByProtocol,
            'rules_by_policy' => $rulesByPolicy,
        ]);
    }
}
