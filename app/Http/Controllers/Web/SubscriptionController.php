<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Subscription::query()
            ->with('customer');

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->filled('billing_cycle')) {
            $query->where('billing_cycle', $request->input('billing_cycle'));
        }

        $subscriptions = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('subscriptions/index', [
            'subscriptions' => $subscriptions,
            'filters' => $request->only(['search', 'status', 'billing_cycle']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $customers = Customer::select('id', 'name', 'email')
            ->orderBy('name')
            ->get();

        return Inertia::render('subscriptions/create', [
            'customers' => $customers,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,quarterly,biannually,annually',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|in:active,inactive,cancelled,expired',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        // Calculate next billing date based on start date and billing cycle
        $startDate = new \DateTime($data['start_date']);
        $nextBillingDate = clone $startDate;

        switch ($data['billing_cycle']) {
            case 'monthly':
                $nextBillingDate->modify('+1 month');
                break;
            case 'quarterly':
                $nextBillingDate->modify('+3 months');
                break;
            case 'biannually':
                $nextBillingDate->modify('+6 months');
                break;
            case 'annually':
                $nextBillingDate->modify('+1 year');
                break;
        }

        $data['next_billing_date'] = $nextBillingDate->format('Y-m-d');

        $subscription = Subscription::create($data);

        return redirect()->route('subscriptions.show', $subscription->id)
            ->with('success', 'Subscription created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Subscription $subscription)
    {
        $subscription->load([
            'customer',
            'invoices' => function ($query) {
                $query->latest()->take(5);
            },
        ]);

        return Inertia::render('subscriptions/show', [
            'subscription' => $subscription,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Subscription $subscription)
    {
        $subscription->load('customer');

        return Inertia::render('subscriptions/edit', [
            'subscription' => $subscription,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Subscription $subscription)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,quarterly,biannually,annually',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|in:active,inactive,cancelled,expired',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        // Recalculate next billing date if billing cycle or start date changed
        if ($subscription->billing_cycle !== $data['billing_cycle'] ||
            $subscription->start_date !== $data['start_date']) {

            $startDate = new \DateTime($data['start_date']);
            $nextBillingDate = clone $startDate;

            switch ($data['billing_cycle']) {
                case 'monthly':
                    $nextBillingDate->modify('+1 month');
                    break;
                case 'quarterly':
                    $nextBillingDate->modify('+3 months');
                    break;
                case 'biannually':
                    $nextBillingDate->modify('+6 months');
                    break;
                case 'annually':
                    $nextBillingDate->modify('+1 year');
                    break;
            }

            $data['next_billing_date'] = $nextBillingDate->format('Y-m-d');
        }

        $subscription->update($data);

        return redirect()->route('subscriptions.show', $subscription->id)
            ->with('success', 'Subscription updated successfully.');
    }

    /**
     * Update the status of the specified resource.
     */
    public function updateStatus(Request $request, Subscription $subscription)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:active,inactive,cancelled,expired',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $subscription->update([
            'status' => $request->status,
        ]);

        return back()->with('success', 'Subscription status updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Subscription $subscription)
    {
        // Check if subscription has invoices
        if ($subscription->invoices()->exists()) {
            return back()->with('error', 'Cannot delete subscription with associated invoices.');
        }

        $subscription->delete();

        return redirect()->route('subscriptions.index')
            ->with('success', 'Subscription deleted successfully.');
    }
}
