<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Subscription;
use App\Services\StreamlinedInvoiceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class StreamlinedSubscriptionController extends Controller
{
    protected StreamlinedInvoiceService $invoiceService;

    public function __construct(StreamlinedInvoiceService $invoiceService)
    {
        $this->invoiceService = $invoiceService;
    }

    /**
     * Show the form for creating a new streamlined subscription.
     */
    public function create(Request $request)
    {
        $customers = Customer::select('id', 'name', 'email', 'status')
            ->where('status', 'active')
            ->orderBy('name')
            ->get();

        $bandwidthPlans = BandwidthPlan::where('active', true)
            ->orderBy('name')
            ->get()
            ->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'description' => $plan->description,
                    'download_speed' => $plan->download_speed,
                    'upload_speed' => $plan->upload_speed,
                    'price' => $plan->price,
                    'priority' => $plan->priority,
                    'formatted_speed' => $plan->download_speed.'/'.$plan->upload_speed.' Mbps',
                    'formatted_price' => $plan->price ? '$'.number_format($plan->price, 2) : 'N/A',
                ];
            });

        // Pre-select customer if provided
        $selectedCustomer = null;
        if ($request->has('customer_id')) {
            $selectedCustomer = Customer::find($request->customer_id);
        }

        return Inertia::render('subscriptions/streamlined-create', [
            'customers' => $customers,
            'bandwidthPlans' => $bandwidthPlans,
            'selectedCustomer' => $selectedCustomer,
        ]);
    }

    /**
     * Store a newly created streamlined subscription.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'bandwidth_plan_id' => 'required|exists:bandwidth_plans,id',
            'billing_cycle' => 'required|in:monthly,quarterly,biannually,annually',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'customer_notes' => 'nullable|string|max:1000',
            'allow_overrides' => 'boolean',
            // Override fields (optional)
            'override_name' => 'nullable|string|max:255',
            'override_description' => 'nullable|string',
            'override_price' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        // Validate that the bandwidth plan is active
        $bandwidthPlan = BandwidthPlan::where('id', $data['bandwidth_plan_id'])
            ->where('active', true)
            ->first();

        if (! $bandwidthPlan) {
            return back()->withErrors(['bandwidth_plan_id' => 'Selected bandwidth plan is not available.'])->withInput();
        }

        // Prepare overrides
        $overrides = [
            'billing_cycle' => $data['billing_cycle'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'] ?? null,
            'customer_notes' => $data['customer_notes'] ?? null,
            'allow_overrides' => $data['allow_overrides'] ?? false,
        ];

        // Add override values if provided
        if (! empty($data['override_name'])) {
            $overrides['name'] = $data['override_name'];
        }
        if (! empty($data['override_description'])) {
            $overrides['description'] = $data['override_description'];
        }
        if (! empty($data['override_price'])) {
            $overrides['price'] = $data['override_price'];
        }

        // Create subscription from bandwidth plan
        $subscription = Subscription::createFromBandwidthPlan(
            $data['customer_id'],
            $data['bandwidth_plan_id'],
            $overrides
        );

        $subscription->save();

        // Attempt to create initial invoice
        $invoice = null;
        $invoiceCreated = false;

        try {
            $invoice = $this->invoiceService->createInitialInvoice($subscription);
            $invoiceCreated = $invoice !== null;

            if ($invoiceCreated) {
                Log::info('Initial invoice created successfully', [
                    'subscription_id' => $subscription->id,
                    'invoice_id' => $invoice->id,
                    'customer_id' => $subscription->customer_id,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to create initial invoice', [
                'subscription_id' => $subscription->id,
                'customer_id' => $subscription->customer_id,
                'error' => $e->getMessage(),
            ]);
            // Continue without failing - subscription creation should succeed even if invoice fails
        }

        // Prepare success message
        $successMessage = 'Subscription created successfully from bandwidth plan.';
        if ($invoiceCreated && $invoice) {
            $successMessage .= " Initial invoice #{$invoice->invoice_number} has been generated and is pending payment.";
        } else {
            $successMessage .= ' Note: Initial invoice could not be created automatically - you may need to create it manually.';
        }

        return redirect()->route('subscriptions.show', $subscription->id)
            ->with('success', $successMessage)
            ->with('invoice_created', $invoiceCreated)
            ->with('invoice_id', $invoice?->id);
    }

    /**
     * Get bandwidth plan details for AJAX requests.
     */
    public function getBandwidthPlan(BandwidthPlan $bandwidthPlan)
    {
        if (! $bandwidthPlan->active) {
            return response()->json(['error' => 'Bandwidth plan is not active'], 404);
        }

        return response()->json([
            'id' => $bandwidthPlan->id,
            'name' => $bandwidthPlan->name,
            'description' => $bandwidthPlan->description,
            'download_speed' => $bandwidthPlan->download_speed,
            'upload_speed' => $bandwidthPlan->upload_speed,
            'price' => $bandwidthPlan->price,
            'priority' => $bandwidthPlan->priority,
            'formatted_speed' => $bandwidthPlan->download_speed.'/'.$bandwidthPlan->upload_speed.' Mbps',
            'formatted_price' => $bandwidthPlan->price ? '$'.number_format($bandwidthPlan->price, 2) : 'N/A',
        ]);
    }
}
