<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\SystemSetting;
use App\Services\MpesaIdService;
use App\Services\QueryOptimizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class CustomerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = QueryOptimizationService::optimizedCustomersQuery();

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        $customers = $query->orderBy('created_at', 'desc')
            ->orderBy('id', 'desc')
            ->paginate(20)
            ->withQueryString();

        // Service status summary is now pre-loaded and optimized
        $customers->getCollection()->transform(function ($customer) {
            $customer->append('service_status_summary');

            return $customer;
        });

        return Inertia::render('customers/index', [
            'customers' => $customers,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get M-Pesa ID settings for the form
        $mpesaSettings = [
            'assignment_mode' => SystemSetting::get('mpesa_id_assignment_mode', 'hybrid'),
            'auto_generate_on_create' => SystemSetting::get('mpesa_id_auto_generate_on_create', true),
            'min_length' => SystemSetting::get('mpesa_id_min_length', 3),
            'max_length' => SystemSetting::get('mpesa_id_max_length', 20),
            'allowed_pattern' => SystemSetting::get('mpesa_id_allowed_pattern', '/^[A-Z0-9]+$/'),
        ];

        return Inertia::render('customers/create', [
            'mpesaSettings' => $mpesaSettings,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:customers,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'status' => 'required|in:active,inactive,suspended',
            'mpesa_id' => 'nullable|string|max:50|unique:customers,mpesa_id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // Create customer without M-Pesa ID first
            $customerData = $validator->validated();
            unset($customerData['mpesa_id']); // Remove mpesa_id from initial creation
            $customer = Customer::create($customerData);

            // Handle M-Pesa ID based on assignment mode
            $mpesaIdService = app(MpesaIdService::class);
            $assignmentMode = SystemSetting::get('mpesa_id_assignment_mode', 'hybrid');
            $autoGenerate = SystemSetting::get('mpesa_id_auto_generate_on_create', true);
            $manualId = $request->mpesa_id;

            $successMessage = 'Customer created successfully.';

            // Handle M-Pesa ID generation based on assignment mode
            if ($assignmentMode === 'manual') {
                // Manual mode: Only use provided M-Pesa ID, no auto-generation
                if ($manualId) {
                    try {
                        $mpesaIdService->validateMpesaId($manualId, null);
                        $customer->mpesa_id = $manualId;
                        $customer->save();

                        Log::info('Manual M-Pesa ID assigned to new customer', [
                            'customer_id' => $customer->id,
                            'customer_name' => $customer->name,
                            'mpesa_id' => $manualId,
                        ]);

                        $successMessage = 'Customer created successfully with M-Pesa ID: '.$manualId;
                    } catch (\Exception $e) {
                        Log::error('Invalid manual M-Pesa ID for new customer', [
                            'customer_id' => $customer->id,
                            'customer_name' => $customer->name,
                            'mpesa_id' => $manualId,
                            'error' => $e->getMessage(),
                        ]);

                        $successMessage = 'Customer created successfully. M-Pesa ID validation failed: '.$e->getMessage();
                    }
                }
            } elseif ($assignmentMode === 'auto') {
                // Auto mode: Always generate, ignore manual input
                if ($autoGenerate) {
                    try {
                        $mpesaId = $mpesaIdService->generateMpesaId($customer);
                        $customer->mpesa_id = $mpesaId;
                        $customer->save();

                        Log::info('Auto M-Pesa ID generated for new customer', [
                            'customer_id' => $customer->id,
                            'customer_name' => $customer->name,
                            'mpesa_id' => $mpesaId,
                        ]);

                        $successMessage = 'Customer created successfully with M-Pesa ID: '.$mpesaId;
                    } catch (\Exception $e) {
                        Log::warning('Failed to auto-generate M-Pesa ID for new customer', [
                            'customer_id' => $customer->id,
                            'customer_name' => $customer->name,
                            'error' => $e->getMessage(),
                        ]);

                        $successMessage = 'Customer created successfully. M-Pesa ID will be generated later.';
                    }
                }
            } elseif ($assignmentMode === 'hybrid') {
                // Hybrid mode: Use manual if provided, otherwise auto-generate
                if ($manualId) {
                    try {
                        $mpesaIdService->validateMpesaId($manualId, null);
                        $customer->mpesa_id = $manualId;
                        $customer->save();

                        Log::info('Manual M-Pesa ID assigned to new customer (hybrid mode)', [
                            'customer_id' => $customer->id,
                            'customer_name' => $customer->name,
                            'mpesa_id' => $manualId,
                        ]);

                        $successMessage = 'Customer created successfully with M-Pesa ID: '.$manualId;
                    } catch (\Exception $e) {
                        Log::error('Invalid manual M-Pesa ID, falling back to auto-generation', [
                            'customer_id' => $customer->id,
                            'customer_name' => $customer->name,
                            'mpesa_id' => $manualId,
                            'error' => $e->getMessage(),
                        ]);

                        // Fall back to auto-generation
                        if ($autoGenerate) {
                            try {
                                $mpesaId = $mpesaIdService->generateMpesaId($customer);
                                $customer->mpesa_id = $mpesaId;
                                $customer->save();

                                $successMessage = 'Customer created successfully. Invalid M-Pesa ID provided, auto-generated: '.$mpesaId;
                            } catch (\Exception $autoE) {
                                $successMessage = 'Customer created successfully. M-Pesa ID validation failed and auto-generation failed.';
                            }
                        } else {
                            $successMessage = 'Customer created successfully. Invalid M-Pesa ID provided.';
                        }
                    }
                } elseif ($autoGenerate) {
                    // No manual ID provided, auto-generate
                    try {
                        $mpesaId = $mpesaIdService->generateMpesaId($customer);
                        $customer->mpesa_id = $mpesaId;
                        $customer->save();

                        Log::info('Auto M-Pesa ID generated for new customer (hybrid mode)', [
                            'customer_id' => $customer->id,
                            'customer_name' => $customer->name,
                            'mpesa_id' => $mpesaId,
                        ]);

                        $successMessage = 'Customer created successfully with M-Pesa ID: '.$mpesaId;
                    } catch (\Exception $e) {
                        Log::warning('Failed to auto-generate M-Pesa ID for new customer (hybrid mode)', [
                            'customer_id' => $customer->id,
                            'customer_name' => $customer->name,
                            'error' => $e->getMessage(),
                        ]);

                        $successMessage = 'Customer created successfully. M-Pesa ID will be generated later.';
                    }
                }
            }

            DB::commit();

            return redirect()->route('customers.show', $customer->id)
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create customer', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return back()
                ->with('error', 'Failed to create customer: '.$e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Customer $customer)
    {
        $customer->load([
            'subscriptions' => function ($query) {
                $query->latest()->take(5);
            },
            'invoices' => function ($query) {
                $query->latest()->take(5);
            },
        ]);

        // Add full address attribute
        $customer->append('full_address');

        return Inertia::render('customers/show', [
            'customer' => $customer,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Customer $customer)
    {
        // Get M-Pesa ID settings for the form
        $mpesaSettings = [
            'assignment_mode' => SystemSetting::get('mpesa_id_assignment_mode', 'hybrid'),
            'auto_generate_on_create' => SystemSetting::get('mpesa_id_auto_generate_on_create', true),
            'min_length' => SystemSetting::get('mpesa_id_min_length', 3),
            'max_length' => SystemSetting::get('mpesa_id_max_length', 20),
            'allowed_pattern' => SystemSetting::get('mpesa_id_allowed_pattern', '/^[A-Z0-9]+$/'),
        ];

        return Inertia::render('customers/edit', [
            'customer' => $customer,
            'mpesaSettings' => $mpesaSettings,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Customer $customer)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:customers,email,'.$customer->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'status' => 'required|in:active,inactive,suspended',
            'mpesa_id' => 'nullable|string|max:50|unique:customers,mpesa_id,'.$customer->id,
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $assignmentMode = SystemSetting::get('mpesa_id_assignment_mode', 'hybrid');
            $newMpesaId = $request->mpesa_id;
            $currentMpesaId = $customer->mpesa_id;

            // Handle M-Pesa ID updates based on assignment mode
            if ($request->has('mpesa_id') && $newMpesaId !== $currentMpesaId) {
                if ($assignmentMode === 'auto') {
                    // Auto mode: Don't allow manual changes to M-Pesa ID
                    Log::info('M-Pesa ID change ignored in auto mode', [
                        'customer_id' => $customer->id,
                        'customer_name' => $customer->name,
                        'attempted_mpesa_id' => $newMpesaId,
                        'current_mpesa_id' => $currentMpesaId,
                    ]);

                    // Remove mpesa_id from update data
                    $updateData = $validator->validated();
                    unset($updateData['mpesa_id']);
                    $customer->update($updateData);

                    $successMessage = 'Customer updated successfully. M-Pesa ID changes are not allowed in auto mode.';
                } else {
                    // Manual or Hybrid mode: Allow M-Pesa ID changes with validation
                    if ($newMpesaId) {
                        $mpesaIdService = app(MpesaIdService::class);
                        $mpesaIdService->validateMpesaId($newMpesaId, $customer->id);

                        Log::info('M-Pesa ID updated for customer', [
                            'customer_id' => $customer->id,
                            'customer_name' => $customer->name,
                            'old_mpesa_id' => $currentMpesaId,
                            'new_mpesa_id' => $newMpesaId,
                            'assignment_mode' => $assignmentMode,
                        ]);

                        $successMessage = 'Customer updated successfully with new M-Pesa ID: '.$newMpesaId;
                    } else {
                        // Removing M-Pesa ID
                        Log::info('M-Pesa ID removed from customer', [
                            'customer_id' => $customer->id,
                            'customer_name' => $customer->name,
                            'old_mpesa_id' => $currentMpesaId,
                            'assignment_mode' => $assignmentMode,
                        ]);

                        $successMessage = 'Customer updated successfully. M-Pesa ID removed.';
                    }

                    $customer->update($validator->validated());
                }
            } else {
                // No M-Pesa ID change
                $customer->update($validator->validated());
                $successMessage = 'Customer updated successfully.';
            }

            return redirect()->route('customers.show', $customer->id)
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            Log::error('Failed to update customer', [
                'customer_id' => $customer->id,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return back()
                ->with('error', 'Failed to update customer: '.$e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Customer $customer)
    {
        // Check if customer has active subscriptions
        if ($customer->subscriptions()->where('status', 'active')->exists()) {
            return back()->with('error', 'Cannot delete customer with active subscriptions.');
        }

        $customer->delete();

        return redirect()->route('customers.index')
            ->with('success', 'Customer deleted successfully.');
    }

    /**
     * Generate M-Pesa ID for a customer.
     */
    public function generateMpesaId(Request $request, Customer $customer)
    {
        try {
            if ($customer->mpesa_id) {
                return back()->with('error', 'Customer already has an M-Pesa ID: '.$customer->mpesa_id);
            }

            $mpesaIdService = app(MpesaIdService::class);
            $manualId = $request->mpesa_id;

            $mpesaId = $mpesaIdService->generateMpesaId($customer, $manualId);
            $customer->mpesa_id = $mpesaId;
            $customer->save();

            Log::info('M-Pesa ID generated for existing customer', [
                'customer_id' => $customer->id,
                'customer_name' => $customer->name,
                'mpesa_id' => $mpesaId,
                'method' => $manualId ? 'manual' : 'auto',
            ]);

            return back()->with('success', 'M-Pesa ID generated successfully: '.$mpesaId);

        } catch (\Exception $e) {
            Log::error('Failed to generate M-Pesa ID for customer', [
                'customer_id' => $customer->id,
                'error' => $e->getMessage(),
            ]);

            return back()->with('error', 'Failed to generate M-Pesa ID: '.$e->getMessage());
        }
    }
}
