<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class CustomerSubscriptionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, Customer $customer)
    {
        $query = $customer->subscriptions();

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->filled('billing_cycle')) {
            $query->where('billing_cycle', $request->input('billing_cycle'));
        }

        $subscriptions = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('customers/subscriptions/index', [
            'customer' => $customer,
            'subscriptions' => $subscriptions,
            'filters' => $request->only(['search', 'status', 'billing_cycle']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Customer $customer)
    {
        return Inertia::render('customers/subscriptions/create', [
            'customer' => $customer,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Customer $customer)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,quarterly,biannually,annually',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|in:active,inactive,cancelled,expired',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        $data['customer_id'] = $customer->id;

        // Calculate next billing date based on start date and billing cycle
        $startDate = new \DateTime($data['start_date']);
        $nextBillingDate = clone $startDate;

        switch ($data['billing_cycle']) {
            case 'monthly':
                $nextBillingDate->modify('+1 month');
                break;
            case 'quarterly':
                $nextBillingDate->modify('+3 months');
                break;
            case 'biannually':
                $nextBillingDate->modify('+6 months');
                break;
            case 'annually':
                $nextBillingDate->modify('+1 year');
                break;
        }

        $data['next_billing_date'] = $nextBillingDate->format('Y-m-d');

        $subscription = Subscription::create($data);

        return redirect()->route('subscriptions.show', $subscription->id)
            ->with('success', 'Subscription created successfully.');
    }
}
