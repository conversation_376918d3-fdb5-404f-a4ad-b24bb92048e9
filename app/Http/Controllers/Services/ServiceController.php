<?php

namespace App\Http\Controllers\Services;

use App\Http\Controllers\Controller;
use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Network\NetworkDevice;
use App\Models\Services\IpPool;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class ServiceController extends Controller
{
    /**
     * Display the main services dashboard with real data.
     */
    public function index(Request $request)
    {
        // Get all services with relationships
        $staticIpServices = StaticIpService::with(['customer', 'bandwidthPlan', 'device', 'ipPool'])
            ->latest()
            ->limit(10)
            ->get();

        $pppoeServices = PppoeService::with(['customer', 'bandwidthPlan', 'device'])
            ->latest()
            ->limit(10)
            ->get();

        // Get statistics
        $stats = [
            'total_services' => StaticIpService::count() + PppoeService::count(),
            'static_ip_services' => StaticIpService::count(),
            'pppoe_services' => PppoeService::count(),
            'ip_pools' => IpPool::count(),
            'active_customers' => Customer::where('status', 'active')->count(),
            'total_customers' => Customer::count(),
        ];

        // Get service status breakdown
        $serviceStatusBreakdown = [
            'active' => StaticIpService::where('status', 'active')->count() + PppoeService::where('status', 'active')->count(),
            'suspended' => StaticIpService::where('status', 'suspended')->count() + PppoeService::where('status', 'suspended')->count(),
            'pending' => StaticIpService::where('status', 'pending')->count() + PppoeService::where('status', 'pending')->count(),
        ];

        // Combine all services for recent activity
        $recentServices = collect();

        // Add static IP services with type indicator
        $staticIpServices->each(function ($service) use ($recentServices) {
            $service->service_type = 'static_ip';
            $service->service_type_label = 'Static IP';
            $recentServices->push($service);
        });

        // Add PPPoE services with type indicator
        $pppoeServices->each(function ($service) use ($recentServices) {
            $service->service_type = 'pppoe';
            $service->service_type_label = 'PPPoE';
            $recentServices->push($service);
        });

        // Sort by creation date
        $recentServices = $recentServices->sortByDesc('created_at')->take(15);

        return Inertia::render('services/index', [
            'stats' => $stats,
            'serviceStatusBreakdown' => $serviceStatusBreakdown,
            'recentServices' => $recentServices->values(),
            'staticIpServices' => $staticIpServices,
            'pppoeServices' => $pppoeServices,
        ]);
    }

    /**
     * Validate the device is a valid MikroTik router.
     */
    protected function validateDevice(NetworkDevice $device): bool
    {
        if (! $device) {
            return false;
        }

        // Since all devices are now assumed to be MikroTik routers,
        // just check if device has an IP address and is active
        if (! $device->ip_address) {
            return false;
        }

        if ($device->status !== 'active') {
            return false;
        }

        return true;
    }

    /**
     * Test connection to the MikroTik device.
     */
    protected function testDeviceConnection(NetworkDevice $device): bool
    {
        try {
            // Try to execute a simple command to test connectivity
            $device->executeMikrotikCommand('/system/identity/print');

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to connect to MikroTik device', [
                'device_id' => $device->id,
                'ip_address' => $device->ip_address,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Validate the subscription is valid for service provisioning.
     */
    protected function validateSubscription(Subscription $subscription): bool
    {
        if (! $subscription) {
            return false;
        }

        // Check if subscription is active
        if (! $subscription->isActive()) {
            return false;
        }

        return true;
    }

    /**
     * Validate the customer is valid for service provisioning.
     */
    protected function validateCustomer(Customer $customer): bool
    {
        if (! $customer) {
            return false;
        }

        // Check if customer is active
        if ($customer->status !== 'active') {
            return false;
        }

        return true;
    }

    /**
     * Validate the bandwidth plan is valid.
     */
    protected function validateBandwidthPlan(?BandwidthPlan $bandwidthPlan): bool
    {
        if (! $bandwidthPlan) {
            return true; // Bandwidth plan is optional
        }

        // Check if bandwidth plan is active
        if (! $bandwidthPlan->active) {
            return false;
        }

        return true;
    }

    /**
     * Format bandwidth for MikroTik (convert to bits/s).
     *
     * @param  int  $bandwidth  Bandwidth in Mbps
     */
    protected function formatBandwidth(int $bandwidth): string
    {
        // Convert Mbps to bits/s (1 Mbps = 1,000,000 bits/s)
        return ($bandwidth * 1_000_000).'';
    }

    /**
     * Log service provisioning activity.
     */
    protected function logActivity(string $action, array $data): void
    {
        Log::info("Service provisioning: {$action}", $data);
    }
}
