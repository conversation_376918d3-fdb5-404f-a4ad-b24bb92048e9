<?php

namespace App\Http\Controllers\Services;

use App\Models\Network\NetworkDevice;
use App\Models\Network\NetworkInterface;
use App\Models\Services\IpPool;
use App\Services\IpPoolService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class IpPoolController extends ServiceController
{
    /**
     * Display a listing of IP pools.
     */
    public function index(Request $request)
    {
        $query = IpPool::with(['device']);

        // Apply filters
        if ($request->has('device_id')) {
            $query->where('device_id', $request->device_id);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Apply search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('network_address', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $ipPools = $query->paginate(15);

        // Calculate usage statistics for each pool
        $ipPools->getCollection()->transform(function ($pool) {
            $pool->used_ips = $pool->used_ips;
            $pool->available_ips = $pool->available_ips;
            $pool->usage_percentage = $pool->usage_percentage;

            return $pool;
        });

        return Inertia::render('services/ip-pools/index', [
            'ipPools' => $ipPools,
            'filters' => $request->only(['device_id', 'status', 'search']),
        ]);
    }

    /**
     * Show the form for creating a new IP pool.
     */
    public function create()
    {
        // Get all active network devices (assuming all are MikroTik routers)
        $devices = NetworkDevice::where('status', 'active')->get();

        return Inertia::render('services/ip-pools/create', [
            'devices' => $devices,
        ]);
    }

    /**
     * Get interfaces for a specific device (AJAX endpoint).
     */
    public function getDeviceInterfaces(NetworkDevice $device)
    {
        try {
            // Sync interfaces from MikroTik first
            $syncResult = NetworkInterface::syncFromDevice($device);

            if (! $syncResult['success']) {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to sync interfaces: '.$syncResult['error'],
                ], 500);
            }

            // Get usable interfaces
            $interfaces = NetworkInterface::getUsableForDevice($device);

            return response()->json([
                'success' => true,
                'interfaces' => $interfaces->map(function ($interface) {
                    return [
                        'name' => $interface->name,
                        'display_name' => $interface->display_name,
                        'type' => $interface->type,
                        'running' => $interface->running,
                        'enabled' => $interface->enabled,
                    ];
                }),
                'sync_result' => $syncResult,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Store a newly created IP pool in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'network_cidr' => 'required|string|regex:/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/\d{1,2}$/',
            'network_address' => 'required|ip',
            'subnet_mask' => 'required|ip',
            'gateway' => 'nullable|ip',
            'dns_servers' => 'nullable|array',
            'device_id' => 'required|exists:network_devices,id',
            'interface' => 'required|string|max:255',
            'excluded_ips' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Parse and validate CIDR notation
        $cidrValidation = $this->validateAndParseCidr($request->network_cidr);
        if (! $cidrValidation['valid']) {
            return redirect()->back()
                ->withErrors(['network_cidr' => $cidrValidation['error']])
                ->withInput();
        }

        // Override network_address and gateway with parsed CIDR values
        // Store the correct subnet mask corresponding to the user's CIDR input
        $prefixLength = $cidrValidation['prefix_length'];
        $subnetMask = $this->prefixLengthToSubnetMask($prefixLength);

        $request->merge([
            'network_address' => $cidrValidation['network_address'],
            'gateway' => $cidrValidation['gateway'],
            'subnet_mask' => $subnetMask, // Use the correct subnet mask from CIDR
            'cidr_range' => $request->network_cidr, // Store original CIDR for reference
        ]);

        // Get related models
        $device = NetworkDevice::findOrFail($request->device_id);

        // Validate device
        if (! $this->validateDevice($device)) {
            return redirect()->back()->withErrors(['device_id' => 'Device is not a valid MikroTik router.'])->withInput();
        }

        // Use the new IP Pool Service
        $ipPoolService = new IpPoolService;
        $result = $ipPoolService->createIpPool($request->all());

        if ($result['success']) {
            $this->logActivity('IP pool created', [
                'ip_pool_id' => $result['pool']->id,
                'name' => $result['pool']->name,
                'network' => $result['pool']->network_address.'/'.$this->getSubnetMaskBits($result['pool']->subnet_mask),
                'interface' => $result['pool']->interface,
                'addresses_created' => $result['addresses_created'],
                'available_addresses' => $result['available_addresses'],
            ]);

            return redirect()->route('services.ip-pools.show', $result['pool']->id)
                ->with('success', "IP pool created successfully. {$result['addresses_created']} IP addresses created in MikroTik.");
        } else {
            return redirect()->back()
                ->withErrors(['general' => 'Failed to create IP pool: '.$result['error']])
                ->withInput();
        }
    }

    /**
     * Display the specified IP pool.
     */
    public function show(IpPool $ipPool)
    {
        $ipPool->load(['device', 'staticIpServices']);

        // Calculate usage statistics
        $ipPool->used_ips = $ipPool->used_ips;
        $ipPool->available_ips = $ipPool->available_ips;
        $ipPool->usage_percentage = $ipPool->usage_percentage;

        return Inertia::render('services/ip-pools/show', [
            'ipPool' => $ipPool,
        ]);
    }

    /**
     * Show the form for editing the specified IP pool.
     */
    public function edit(IpPool $ipPool)
    {
        $ipPool->load(['device']);

        // Get all active network devices (assuming all are MikroTik routers)
        $devices = NetworkDevice::where('status', 'active')->get();

        return Inertia::render('services/ip-pools/edit', [
            'ipPool' => $ipPool,
            'devices' => $devices,
        ]);
    }

    /**
     * Update the specified IP pool in storage.
     */
    public function update(Request $request, IpPool $ipPool)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'gateway' => 'nullable|ip',
            'dns_servers' => 'nullable|array',
            'excluded_ips' => 'nullable|array',
            'status' => 'nullable|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Update IP pool in database
        $ipPool->name = $request->name;
        $ipPool->description = $request->description;
        $ipPool->gateway = $request->gateway;
        $ipPool->dns_servers = $request->dns_servers;
        $ipPool->excluded_ips = $request->excluded_ips;

        // Handle status change
        if ($request->has('status') && $request->status !== $ipPool->status) {
            $ipPool->status = $request->status;
        }

        $ipPool->save();

        // Update the IP pool on the MikroTik router
        try {
            $this->updateIpPoolOnMikrotik($ipPool);

            $this->logActivity('IP pool updated', [
                'ip_pool_id' => $ipPool->id,
                'name' => $ipPool->name,
            ]);

            return redirect()->route('services.ip-pools.show', $ipPool->id)
                ->with('success', 'IP pool updated successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to update IP pool on MikroTik', [
                'ip_pool_id' => $ipPool->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('services.ip-pools.show', $ipPool->id)
                ->with('warning', 'IP pool updated in database but failed to update on router: '.$e->getMessage());
        }
    }

    /**
     * Remove the specified IP pool from storage.
     */
    public function destroy(IpPool $ipPool)
    {
        // Check if the IP pool has any services using it
        if ($ipPool->staticIpServices()->count() > 0) {
            return redirect()->route('services.ip-pools.show', $ipPool->id)
                ->with('error', 'Cannot delete IP pool that has services using it.');
        }

        try {
            // Remove from MikroTik first
            $this->removeIpPoolFromMikrotik($ipPool);

            // Then remove from database
            $ipPool->delete();

            $this->logActivity('IP pool deleted', [
                'ip_pool_id' => $ipPool->id,
                'name' => $ipPool->name,
            ]);

            return redirect()->route('services.ip-pools.index')
                ->with('success', 'IP pool deleted successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to delete IP pool from MikroTik', [
                'ip_pool_id' => $ipPool->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('services.ip-pools.show', $ipPool->id)
                ->with('warning', 'Failed to delete IP pool from router: '.$e->getMessage());
        }
    }

    /**
     * Create an IP pool on a MikroTik router.
     */
    protected function createIpPoolOnMikrotik(IpPool $ipPool)
    {
        $device = $ipPool->device;

        if (! $this->testDeviceConnection($device)) {
            throw new \Exception('Cannot connect to MikroTik device.');
        }

        // Create IP pool on MikroTik
        $command = '/ip/pool/add';
        $params = [
            'name' => $ipPool->name,
            'ranges' => $this->calculateIpRange($ipPool),
            'comment' => $ipPool->description ?? "IP pool for {$ipPool->cidr}",
        ];

        $result = $device->executeMikrotikCommand($command, $params);

        // Store the MikroTik pool ID for future reference
        if (isset($result['.id'])) {
            $ipPool->mikrotik_pool_id = $result['.id'];
            $ipPool->save();
        }

        return $result;
    }

    /**
     * Update an IP pool on a MikroTik router.
     */
    protected function updateIpPoolOnMikrotik(IpPool $ipPool)
    {
        $device = $ipPool->device;

        if (! $this->testDeviceConnection($device)) {
            throw new \Exception('Cannot connect to MikroTik device.');
        }

        // If the pool doesn't have a MikroTik ID, create it
        if (! $ipPool->mikrotik_pool_id) {
            return $this->createIpPoolOnMikrotik($ipPool);
        }

        // Update IP pool on MikroTik
        $command = '/ip/pool/set';
        $params = [
            '.id' => $ipPool->mikrotik_pool_id,
            'name' => $ipPool->name,
            'ranges' => $this->calculateIpRange($ipPool),
            'comment' => $ipPool->description ?? "IP pool for {$ipPool->cidr}",
        ];

        $result = $device->executeMikrotikCommand($command, $params);

        return $result;
    }

    /**
     * Remove an IP pool from a MikroTik router.
     */
    protected function removeIpPoolFromMikrotik(IpPool $ipPool)
    {
        $device = $ipPool->device;

        if (! $this->testDeviceConnection($device)) {
            throw new \Exception('Cannot connect to MikroTik device.');
        }

        // If the pool doesn't have a MikroTik ID, nothing to do
        if (! $ipPool->mikrotik_pool_id) {
            return true;
        }

        // Remove IP pool from MikroTik
        $command = '/ip/pool/remove';
        $params = [
            '.id' => $ipPool->mikrotik_pool_id,
        ];

        $result = $device->executeMikrotikCommand($command, $params);

        return $result;
    }

    /**
     * Calculate the IP range for a MikroTik IP pool.
     * Uses the original CIDR range, not the forced /24 subnet mask.
     */
    protected function calculateIpRange(IpPool $ipPool): string
    {
        // Use original CIDR range for IP range calculation
        $cidr = $ipPool->cidr_range ?: $ipPool->network_cidr;
        if ($cidr && preg_match('/\/(\d+)$/', $cidr, $matches)) {
            $prefix_length = (int) $matches[1];
        } else {
            // Fallback to subnet mask calculation
            $subnet_binary = '';
            foreach (explode('.', $ipPool->subnet_mask) as $octet) {
                $subnet_binary .= str_pad(decbin((int) $octet), 8, '0', STR_PAD_LEFT);
            }
            $prefix_length = substr_count($subnet_binary, '1');
        }

        // Calculate the first and last usable IP addresses
        $first_ip_long = ip2long($ipPool->network_address) + 1; // Skip network address
        $last_ip_long = ip2long($ipPool->network_address) + pow(2, (32 - $prefix_length)) - 2; // Skip broadcast address

        // Special case for /31 and /32
        if ($prefix_length >= 31) {
            $first_ip_long = ip2long($ipPool->network_address);
            $last_ip_long = ip2long($ipPool->network_address) + pow(2, (32 - $prefix_length)) - 1;
        }

        // Exclude gateway if specified
        if ($ipPool->gateway) {
            $gateway_long = ip2long($ipPool->gateway);
            if ($gateway_long >= $first_ip_long && $gateway_long <= $last_ip_long) {
                // If gateway is the first IP, start from the next one
                if ($gateway_long === $first_ip_long) {
                    $first_ip_long++;
                }
                // If gateway is the last IP, end at the previous one
                elseif ($gateway_long === $last_ip_long) {
                    $last_ip_long--;
                }
                // Otherwise, split the range
                else {
                    // For simplicity, we'll just use the range after the gateway
                    $first_ip_long = $gateway_long + 1;
                }
            }
        }

        // Exclude other IPs if specified
        $excluded_ips = $ipPool->excluded_ips ?? [];
        if (! empty($excluded_ips)) {
            // For simplicity, we'll just adjust the start and end of the range
            // A more complex implementation would create multiple ranges
            foreach ($excluded_ips as $excluded_ip) {
                $excluded_ip_long = ip2long($excluded_ip);
                if ($excluded_ip_long === $first_ip_long) {
                    $first_ip_long++;
                }
                if ($excluded_ip_long === $last_ip_long) {
                    $last_ip_long--;
                }
            }
        }

        // Format the range for MikroTik
        return long2ip($first_ip_long).'-'.long2ip($last_ip_long);
    }

    /**
     * Convert subnet mask to bits.
     */
    private function getSubnetMaskBits(string $subnetMask): int
    {
        $binary = '';
        foreach (explode('.', $subnetMask) as $octet) {
            $binary .= str_pad(decbin((int) $octet), 8, '0', STR_PAD_LEFT);
        }

        return substr_count($binary, '1');
    }

    /**
     * Convert prefix length to subnet mask.
     */
    private function prefixLengthToSubnetMask(int $prefixLength): string
    {
        // Create a 32-bit mask with the specified number of 1s
        $mask = -1 << (32 - $prefixLength);

        // Convert to 4 octets
        $octet1 = ($mask >> 24) & 0xFF;
        $octet2 = ($mask >> 16) & 0xFF;
        $octet3 = ($mask >> 8) & 0xFF;
        $octet4 = $mask & 0xFF;

        return sprintf('%d.%d.%d.%d', $octet1, $octet2, $octet3, $octet4);
    }

    /**
     * Validate and parse CIDR notation.
     */
    protected function validateAndParseCidr(string $cidr): array
    {
        // Validate CIDR format
        if (! preg_match('/^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\/(\d{1,2})$/', $cidr, $matches)) {
            return [
                'valid' => false,
                'error' => 'Invalid CIDR format. Use format like ***********/24',
            ];
        }

        $networkIp = $matches[1];
        $prefixLength = (int) $matches[2];

        // Validate prefix length
        if ($prefixLength < 16 || $prefixLength > 30) {
            return [
                'valid' => false,
                'error' => 'Prefix length must be between /16 and /30',
            ];
        }

        // Validate IP address format
        $ipParts = explode('.', $networkIp);
        foreach ($ipParts as $part) {
            $num = (int) $part;
            if ($num < 0 || $num > 255) {
                return [
                    'valid' => false,
                    'error' => 'Invalid IP address in CIDR notation',
                ];
            }
        }

        // Calculate network address and gateway
        $ipSegments = explode('.', $networkIp);
        $networkAddress = $networkIp;
        $gateway = $ipSegments[0].'.'.$ipSegments[1].'.'.$ipSegments[2].'.1';

        return [
            'valid' => true,
            'network_address' => $networkAddress,
            'gateway' => $gateway,
            'prefix_length' => $prefixLength,
            'cidr' => $cidr,
        ];
    }
}
