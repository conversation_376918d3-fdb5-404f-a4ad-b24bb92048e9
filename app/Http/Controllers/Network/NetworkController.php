<?php

namespace App\Http\Controllers\Network;

use App\Http\Controllers\Controller;
use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Network\NetworkDevice;
use App\Models\Network\NetworkMap;
use App\Models\Network\NetworkSite;
use App\Models\Services\IpPool;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class NetworkController extends Controller
{
    /**
     * Display the network management dashboard.
     */
    public function index(Request $request)
    {
        // Get network infrastructure statistics
        $stats = $this->getNetworkStats();

        // Get recent network activities
        $recentActivities = $this->getRecentActivities();

        return Inertia::render('network/index', [
            'stats' => $stats,
            'recentActivities' => $recentActivities,
        ]);
    }

    /**
     * Get comprehensive network infrastructure statistics (optimized for performance).
     */
    protected function getNetworkStats(): array
    {
        // Get network stats directly without caching
        // Sites statistics
        $totalSites = NetworkSite::count();
        $activeSites = NetworkSite::where('status', 'active')->count();
        $inactiveSites = NetworkSite::where('status', 'inactive')->count();
        $maintenanceSites = NetworkSite::where('status', 'maintenance')->count();

        // Devices statistics (using database status only - no API calls)
        $totalDevices = NetworkDevice::count();
        $activeDevices = NetworkDevice::where('status', 'active')->count();
        $inactiveDevices = NetworkDevice::where('status', 'inactive')->count();

        // Use last_connected_at to determine recently connected devices (within last hour)
        $recentlyConnectedDevices = NetworkDevice::where('status', 'active')
            ->where('last_connected_at', '>=', now()->subHour())
            ->count();

        // IP Pool statistics (more useful than interface estimates)
        $totalIpPools = IpPool::count();
        $totalIpAddresses = IpPool::sum('total_addresses');
        $usedIpAddresses = StaticIpService::where('status', 'active')->count();
        $availableIpAddresses = $totalIpAddresses - $usedIpAddresses;
        $ipUtilizationPercentage = $totalIpAddresses > 0 ? round(($usedIpAddresses / $totalIpAddresses) * 100, 1) : 0;

        // Service statistics
        $totalServices = StaticIpService::count() + PppoeService::count();
        $activeServices = StaticIpService::where('status', 'active')->count() +
                         PppoeService::where('status', 'active')->count();
        $suspendedServices = StaticIpService::where('status', 'suspended')->count() +
                           PppoeService::where('status', 'suspended')->count();

        // Customer distribution across devices
        $servicesPerDevice = NetworkDevice::withCount(['staticIpServices', 'pppoeServices'])
            ->get()
            ->map(function ($device) {
                return [
                    'device_name' => $device->name,
                    'total_services' => $device->static_ip_services_count + $device->pppoe_services_count,
                    'static_ip_count' => $device->static_ip_services_count,
                    'pppoe_count' => $device->pppoe_services_count,
                ];
            })
            ->toArray();

        // Bandwidth plan usage (using direct queries since relationships don't exist)
        $totalBandwidthPlans = BandwidthPlan::where('active', true)->count();
        $bandwidthPlanUsage = BandwidthPlan::where('active', true)
            ->get()
            ->map(function ($plan) {
                $staticIpCount = StaticIpService::where('bandwidth_plan_id', $plan->id)->count();
                $pppoeCount = PppoeService::where('bandwidth_plan_id', $plan->id)->count();

                return [
                    'plan_name' => $plan->name,
                    'total_usage' => $staticIpCount + $pppoeCount,
                    'download_speed' => $plan->download_speed,
                    'upload_speed' => $plan->upload_speed,
                ];
            })
            ->sortByDesc('total_usage')
            ->take(5)
            ->values()
            ->toArray();

        // Maps statistics
        $totalMaps = NetworkMap::count();

        // Device model breakdown
        $devicesByModel = NetworkDevice::select('detected_model', DB::raw('count(*) as count'))
            ->whereNotNull('detected_model')
            ->groupBy('detected_model')
            ->pluck('count', 'detected_model')
            ->toArray();

        // Sites by status breakdown
        $sitesByStatus = NetworkSite::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        return [
            'sites' => [
                'total' => $totalSites,
                'active' => $activeSites,
                'inactive' => $inactiveSites,
                'maintenance' => $maintenanceSites,
                'by_status' => $sitesByStatus,
            ],
            'devices' => [
                'total' => $totalDevices,
                'active' => $activeDevices,
                'inactive' => $inactiveDevices,
                'recently_connected' => $recentlyConnectedDevices,
                'by_model' => $devicesByModel,
                'services_distribution' => $servicesPerDevice,
            ],
            'ip_pools' => [
                'total_pools' => $totalIpPools,
                'total_addresses' => $totalIpAddresses,
                'used_addresses' => $usedIpAddresses,
                'available_addresses' => $availableIpAddresses,
                'utilization_percentage' => $ipUtilizationPercentage,
            ],
            'services' => [
                'total' => $totalServices,
                'active' => $activeServices,
                'suspended' => $suspendedServices,
                'static_ip_count' => StaticIpService::count(),
                'pppoe_count' => PppoeService::count(),
            ],
            'bandwidth' => [
                'total_plans' => $totalBandwidthPlans,
                'plan_usage' => $bandwidthPlanUsage,
            ],
            'maps' => [
                'total' => $totalMaps,
            ],
            'health' => [
                'sites_online_percentage' => $totalSites > 0 ? round(($activeSites / $totalSites) * 100, 1) : 0,
                'devices_active_percentage' => $totalDevices > 0 ? round(($activeDevices / $totalDevices) * 100, 1) : 0,
                'ip_utilization_percentage' => $ipUtilizationPercentage,
                'services_active_percentage' => $totalServices > 0 ? round(($activeServices / $totalServices) * 100, 1) : 0,
                'overall_health' => $this->calculateOverallHealth($activeSites, $totalSites, $activeServices, $totalServices),
            ],
        ];
    }

    /**
     * Calculate overall network health score based on sites and services.
     */
    protected function calculateOverallHealth(int $activeSites, int $totalSites, int $activeServices, int $totalServices): array
    {
        $siteHealth = $totalSites > 0 ? ($activeSites / $totalSites) * 100 : 100;
        $serviceHealth = $totalServices > 0 ? ($activeServices / $totalServices) * 100 : 100;

        // Weight sites and services equally for overall health
        $overallScore = ($siteHealth + $serviceHealth) / 2;

        if ($overallScore >= 95) {
            $status = 'excellent';
            $color = 'green';
        } elseif ($overallScore >= 85) {
            $status = 'good';
            $color = 'blue';
        } elseif ($overallScore >= 70) {
            $status = 'warning';
            $color = 'yellow';
        } else {
            $status = 'critical';
            $color = 'red';
        }

        return [
            'score' => round($overallScore, 1),
            'status' => $status,
            'color' => $color,
        ];
    }

    /**
     * Get recent network management activities.
     */
    protected function getRecentActivities(): array
    {
        $activities = [];

        // Recent sites (last 7 days)
        $recentSites = NetworkSite::where('created_at', '>=', now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();

        foreach ($recentSites as $site) {
            $activities[] = [
                'type' => 'site_added',
                'title' => 'Site added',
                'description' => "New site '{$site->name}' was created",
                'icon' => 'building',
                'color' => 'blue',
                'timestamp' => $site->created_at,
                'url' => route('network.sites.show', $site->id),
            ];
        }

        // Recent devices (last 7 days)
        $recentDevices = NetworkDevice::where('created_at', '>=', now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();

        foreach ($recentDevices as $device) {
            $activities[] = [
                'type' => 'device_added',
                'title' => 'Device connected',
                'description' => "New device '{$device->name}' was registered",
                'icon' => 'router',
                'color' => 'green',
                'timestamp' => $device->created_at,
                'url' => route('network.devices.show', $device->id),
            ];
        }

        // Recent maps (last 7 days)
        $recentMaps = NetworkMap::where('created_at', '>=', now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->limit(2)
            ->get();

        foreach ($recentMaps as $map) {
            $activities[] = [
                'type' => 'map_created',
                'title' => 'Network map created',
                'description' => "New network map '{$map->name}' was created",
                'icon' => 'map',
                'color' => 'purple',
                'timestamp' => $map->created_at,
                'url' => '#', // Maps don't have show routes yet
            ];
        }

        // Sort activities by timestamp (most recent first)
        usort($activities, function ($a, $b) {
            return $b['timestamp']->timestamp - $a['timestamp']->timestamp;
        });

        // Format timestamps for display
        foreach ($activities as &$activity) {
            $activity['time_ago'] = $activity['timestamp']->diffForHumans();
            $activity['timestamp'] = $activity['timestamp']->toISOString();
        }

        return array_slice($activities, 0, 6); // Return max 6 activities
    }
}
