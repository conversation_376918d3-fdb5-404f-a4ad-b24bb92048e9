<?php

namespace App\Http\Controllers\Network;

use App\Http\Controllers\Controller;
use App\Models\Network\NetworkDevice;
use App\Models\Network\NetworkMap;
use App\Models\Network\NetworkMapItem;
use Illuminate\Http\Request;

class NetworkMapController extends Controller
{
    /**
     * Display a listing of the maps.
     */
    public function index(Request $request)
    {
        $query = NetworkMap::query();

        // Apply filters
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->search.'%')
                    ->orWhere('description', 'like', '%'.$request->search.'%');
            });
        }

        if ($request->has('site_id')) {
            $query->where('site_id', $request->site_id);
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $maps = $query->with('site')->paginate($request->get('per_page', 15));

        return response()->json($maps);
    }

    /**
     * Store a newly created map.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'site_id' => 'nullable|exists:network_sites,id',
            'background_image' => 'nullable|string|max:255',
            'width' => 'nullable|integer',
            'height' => 'nullable|integer',
            'settings' => 'nullable|json',
        ]);

        // Parse JSON settings if provided as string
        if (isset($validated['settings']) && is_string($validated['settings'])) {
            $validated['settings'] = json_decode($validated['settings'], true);
        }

        $map = NetworkMap::create($validated);

        return response()->json($map, 201);
    }

    /**
     * Display the specified map.
     */
    public function show(NetworkMap $map)
    {
        $map->load(['site', 'items.itemable']);

        return response()->json($map);
    }

    /**
     * Update the specified map.
     */
    public function update(Request $request, NetworkMap $map)
    {
        $validated = $request->validate([
            'name' => 'string|max:255',
            'description' => 'nullable|string',
            'site_id' => 'nullable|exists:network_sites,id',
            'background_image' => 'nullable|string|max:255',
            'width' => 'nullable|integer',
            'height' => 'nullable|integer',
            'settings' => 'nullable|json',
        ]);

        // Parse JSON settings if provided as string
        if (isset($validated['settings']) && is_string($validated['settings'])) {
            $validated['settings'] = json_decode($validated['settings'], true);
        }

        $map->update($validated);

        return response()->json($map);
    }

    /**
     * Remove the specified map.
     */
    public function destroy(NetworkMap $map)
    {
        // Check if the map has items
        $itemsCount = $map->items()->count();
        if ($itemsCount > 0) {
            // Option 1: Return an error
            // return response()->json([
            //     'message' => 'Cannot delete map with items. There are ' . $itemsCount . ' items on this map.',
            // ], 422);

            // Option 2: Delete all items first
            $map->items()->delete();
        }

        $map->delete();

        return response()->json(null, 204);
    }

    /**
     * Get the items on the specified map.
     */
    public function items(NetworkMap $map)
    {
        $items = $map->items()->with('itemable')->get();

        return response()->json($items);
    }

    /**
     * Get the devices on the specified map.
     */
    public function devices(NetworkMap $map)
    {
        $devices = $map->devices()->get();

        return response()->json($devices);
    }

    /**
     * Get the connections on the specified map.
     */
    public function connections(NetworkMap $map)
    {
        $connections = $map->getConnectionsAttribute();

        return response()->json($connections);
    }

    /**
     * Add a device to the map.
     */
    public function addDevice(Request $request, NetworkMap $map)
    {
        $validated = $request->validate([
            'device_id' => 'required|exists:network_devices,id',
            'x_position' => 'required|numeric',
            'y_position' => 'required|numeric',
            'scale' => 'nullable|numeric',
            'rotation' => 'nullable|integer',
            'settings' => 'nullable|json',
        ]);

        $device = NetworkDevice::findOrFail($validated['device_id']);

        // Check if the device is already on the map
        $existingItem = $map->items()
            ->where('itemable_type', get_class($device))
            ->where('itemable_id', $device->id)
            ->first();

        if ($existingItem) {
            return response()->json([
                'message' => 'Device is already on the map',
                'item' => $existingItem,
            ], 422);
        }

        // Parse JSON settings if provided as string
        if (isset($validated['settings']) && is_string($validated['settings'])) {
            $validated['settings'] = json_decode($validated['settings'], true);
        }

        $item = $map->addDevice(
            $device,
            $validated['x_position'],
            $validated['y_position'],
            $validated['scale'] ?? 1.0,
            $validated['rotation'] ?? 0,
            $validated['settings'] ?? null
        );

        return response()->json($item, 201);
    }

    /**
     * Remove a device from the map.
     */
    public function removeDevice(Request $request, NetworkMap $map)
    {
        $validated = $request->validate([
            'device_id' => 'required|exists:network_devices,id',
        ]);

        $device = NetworkDevice::findOrFail($validated['device_id']);

        $item = $map->items()
            ->where('itemable_type', get_class($device))
            ->where('itemable_id', $device->id)
            ->first();

        if (! $item) {
            return response()->json([
                'message' => 'Device is not on the map',
            ], 404);
        }

        $item->delete();

        return response()->json([
            'message' => 'Device removed from map successfully',
        ]);
    }

    /**
     * Update a map item's position.
     */
    public function updateItemPosition(Request $request, NetworkMap $map, NetworkMapItem $item)
    {
        // Verify the item belongs to the map
        if ($item->map_id !== $map->id) {
            return response()->json([
                'message' => 'Item does not belong to this map',
            ], 403);
        }

        $validated = $request->validate([
            'x_position' => 'required|numeric',
            'y_position' => 'required|numeric',
        ]);

        $item->moveTo($validated['x_position'], $validated['y_position']);
        $item->save();

        return response()->json($item);
    }

    /**
     * Update a map item's scale.
     */
    public function updateItemScale(Request $request, NetworkMap $map, NetworkMapItem $item)
    {
        // Verify the item belongs to the map
        if ($item->map_id !== $map->id) {
            return response()->json([
                'message' => 'Item does not belong to this map',
            ], 403);
        }

        $validated = $request->validate([
            'scale' => 'required|numeric',
        ]);

        $item->resize($validated['scale']);
        $item->save();

        return response()->json($item);
    }

    /**
     * Update a map item's rotation.
     */
    public function updateItemRotation(Request $request, NetworkMap $map, NetworkMapItem $item)
    {
        // Verify the item belongs to the map
        if ($item->map_id !== $map->id) {
            return response()->json([
                'message' => 'Item does not belong to this map',
            ], 403);
        }

        $validated = $request->validate([
            'rotation' => 'required|integer',
        ]);

        $item->rotate($validated['rotation']);
        $item->save();

        return response()->json($item);
    }

    /**
     * Update a map item's settings.
     */
    public function updateItemSettings(Request $request, NetworkMap $map, NetworkMapItem $item)
    {
        // Verify the item belongs to the map
        if ($item->map_id !== $map->id) {
            return response()->json([
                'message' => 'Item does not belong to this map',
            ], 403);
        }

        $validated = $request->validate([
            'settings' => 'required|json',
        ]);

        // Parse JSON settings if provided as string
        if (is_string($validated['settings'])) {
            $validated['settings'] = json_decode($validated['settings'], true);
        }

        $item->settings = $validated['settings'];
        $item->save();

        return response()->json($item);
    }

    /**
     * Get a setting value for the specified map.
     */
    public function getSettingValue(Request $request, NetworkMap $map)
    {
        $validated = $request->validate([
            'key' => 'required|string',
        ]);

        $value = $map->getSettingValue($validated['key']);

        if ($value === null) {
            return response()->json([
                'message' => 'Setting key not found',
            ], 404);
        }

        return response()->json([
            'key' => $validated['key'],
            'value' => $value,
        ]);
    }

    /**
     * Set a setting value for the specified map.
     */
    public function setSettingValue(Request $request, NetworkMap $map)
    {
        $validated = $request->validate([
            'key' => 'required|string',
            'value' => 'required',
        ]);

        $map->setSettingValue($validated['key'], $validated['value']);
        $map->save();

        return response()->json([
            'message' => 'Setting value updated successfully',
            'map' => $map,
        ]);
    }

    /**
     * Clone an existing map.
     */
    public function clone(Request $request, NetworkMap $map)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'include_items' => 'boolean',
        ]);

        $newMap = new NetworkMap;
        $newMap->name = $validated['name'];
        $newMap->description = $validated['description'] ?? $map->description;
        $newMap->site_id = $map->site_id;
        $newMap->background_image = $map->background_image;
        $newMap->width = $map->width;
        $newMap->height = $map->height;
        $newMap->settings = $map->settings;
        $newMap->save();

        // Clone items if requested
        if (isset($validated['include_items']) && $validated['include_items']) {
            foreach ($map->items as $item) {
                $newItem = new NetworkMapItem;
                $newItem->map_id = $newMap->id;
                $newItem->itemable_type = $item->itemable_type;
                $newItem->itemable_id = $item->itemable_id;
                $newItem->x_position = $item->x_position;
                $newItem->y_position = $item->y_position;
                $newItem->scale = $item->scale;
                $newItem->rotation = $item->rotation;
                $newItem->settings = $item->settings;
                $newItem->save();
            }
        }

        return response()->json([
            'message' => 'Map cloned successfully',
            'map' => $newMap->load('items'),
        ], 201);
    }
}
