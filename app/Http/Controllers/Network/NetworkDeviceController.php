<?php

namespace App\Http\Controllers\Network;

use App\Http\Controllers\Controller;
use App\Models\Network\NetworkDevice;
use App\Models\Network\NetworkSite;
use Illuminate\Http\Request;

class NetworkDeviceController extends Controller
{
    /**
     * Display a listing of the devices.
     */
    public function index(Request $request)
    {
        $query = NetworkDevice::query();

        // Apply filters
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->search.'%')
                    ->orWhere('ip_address', 'like', '%'.$request->search.'%')
                    ->orWhere('description', 'like', '%'.$request->search.'%')
                    ->orWhere('detected_model', 'like', '%'.$request->search.'%');
            });
        }

        if ($request->has('detected_model')) {
            $query->where('detected_model', 'like', '%'.$request->detected_model.'%');
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('site_id')) {
            $query->where('site_id', $request->site_id);
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $devices = $query->with('site')->paginate($request->get('per_page', 15));

        if ($request->wantsJson()) {
            return response()->json($devices);
        }

        return \Inertia\Inertia::render('network/devices/index', [
            'devices' => $devices,
        ]);
    }

    /**
     * Show the form for creating a new device.
     */
    public function create()
    {
        $sites = NetworkSite::select('id', 'name')->orderBy('name')->get();

        return \Inertia\Inertia::render('network/devices/create', [
            'sites' => $sites,
        ]);
    }

    /**
     * Store a newly created device.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'ip_address' => 'required|ip',
            'status' => 'required|string|in:active,inactive,maintenance',
            'site_id' => 'required|exists:network_sites,id',
            'api_username' => 'nullable|string|max:255',
            'api_password' => 'nullable|string|max:255',
            'api_port' => 'nullable|integer|min:1|max:65535',
        ]);

        // Set default API port if not provided
        if (! isset($validated['api_port'])) {
            $validated['api_port'] = 8728; // Default MikroTik API port
        }

        // Encrypt API password if provided
        if (isset($validated['api_password'])) {
            $validated['api_password'] = encrypt($validated['api_password']);
        }

        $device = NetworkDevice::create($validated);

        // Try to auto-detect device information via MikroTik API
        try {
            if ($device->ip_address && $device->api_username && $device->api_password) {
                $deviceInfo = $this->detectDeviceInfo($device);
                if ($deviceInfo) {
                    $device->update([
                        'detected_model' => $deviceInfo['model'] ?? null,
                        'detected_version' => $deviceInfo['version'] ?? null,
                        'last_connected_at' => now(),
                    ]);
                }
            }
        } catch (\Exception $e) {
            // Auto-detection failed, but device was still created
            \Log::warning('Failed to auto-detect device info', [
                'device_id' => $device->id,
                'error' => $e->getMessage(),
            ]);
        }

        return redirect()->route('network.devices.show', $device->id)->with('success', 'Device created successfully.');
    }

    /**
     * Display the specified device.
     */
    public function show(NetworkDevice $device)
    {
        $device->load(['site', 'interfaces']);

        return \Inertia\Inertia::render('network/devices/show', [
            'device' => $device,
        ]);
    }

    /**
     * Update the specified device.
     */
    public function update(Request $request, NetworkDevice $device)
    {
        $validated = $request->validate([
            'name' => 'string|max:255',
            'description' => 'nullable|string',
            'ip_address' => 'ip',
            'status' => 'string|in:active,inactive,maintenance',
            'site_id' => 'exists:network_sites,id',
            'api_username' => 'nullable|string|max:255',
            'api_password' => 'nullable|string|max:255',
            'api_port' => 'nullable|integer|min:1|max:65535',
        ]);

        // Encrypt API password if provided
        if (isset($validated['api_password'])) {
            $validated['api_password'] = encrypt($validated['api_password']);
        }

        $device->update($validated);

        return response()->json($device);
    }

    /**
     * Remove the specified device.
     */
    public function destroy(NetworkDevice $device)
    {
        // Check if the device has interfaces
        $interfacesCount = $device->interfaces()->count();
        if ($interfacesCount > 0) {
            return response()->json([
                'message' => 'Cannot delete device with interfaces. There are '.$interfacesCount.' interfaces on this device.',
            ], 422);
        }

        $device->delete();

        return response()->json(null, 204);
    }

    /**
     * Get the interfaces for the specified device (Inertia page).
     * Always returns Inertia response for page navigation.
     */
    public function interfaces(NetworkDevice $device, Request $request)
    {
        // Fetch interfaces from device for initial page load
        try {
            $mikrotikInterfaces = $device->fetchInterfacesFromDevice();

            // Transform Mikrotik interfaces to match the expected format
            $formattedInterfaces = collect($mikrotikInterfaces)->map(function ($interface) {
                // Default speed to 0 for interfaces that are down
                $speed = 0;

                // Only calculate speed if the interface is up
                if (isset($interface['running']) && $interface['running'] === 'true') {
                    // Calculate actual speed based on traffic data
                    if (isset($interface['rx-byte']) && isset($interface['tx-byte']) && isset($interface['last-link-up-time'])) {
                        try {
                            $rxBytes = $interface['rx-byte'];           // Received bytes
                            $txBytes = $interface['tx-byte'];           // Transmitted bytes
                            $totalBytes = $rxBytes + $txBytes;          // Total bytes transferred

                            $linkUpTime = strtotime($interface['last-link-up-time']); // Timestamp when the link came up
                            $currentTime = time();                      // Current timestamp
                            $durationSeconds = $currentTime - $linkUpTime; // Duration in seconds

                            // Prevent division by zero if the link just came up
                            if ($durationSeconds > 0) {
                                $totalBits = $totalBytes * 8;           // Convert bytes to bits
                                $bps = $totalBits / $durationSeconds;   // Bits per second
                                $mbps = round($bps / 1_000_000, 2);     // Convert to Mbps and round to 2 decimal places

                                $speed = $mbps;
                            }
                        } catch (\Exception $e) {
                            // Fallback to default values if calculation fails
                            if (isset($interface['type'])) {
                                switch ($interface['type']) {
                                    case 'ether':
                                        $speed = 150;
                                        break;
                                    case 'sfp-sfpplus':
                                        $speed = 10000;
                                        break;
                                    case 'wlan':
                                        $speed = 300;
                                        break;
                                }
                            }
                        }
                    } else {
                        // Fallback to default values if required data is missing
                        if (isset($interface['type'])) {
                            switch ($interface['type']) {
                                case 'ether':
                                    $speed = 150;
                                    break;
                                case 'sfp-sfpplus':
                                    $speed = 10000;
                                    break;
                                case 'wlan':
                                    $speed = 300;
                                    break;
                            }
                        }
                    }
                }

                return [
                    'id' => $interface['.id'] ?? null,
                    'name' => $interface['name'] ?? '',
                    'type' => $interface['type'] ?? '',
                    'status' => isset($interface['running']) && $interface['running'] === 'true' ? 'up' : 'down',
                    'ip_address' => $interface['ip-address'] ?? '',
                    'subnet_mask' => $interface['subnet-mask'] ?? '',
                    'mac_address' => $interface['mac-address'] ?? '',
                    'speed' => $speed,
                    'is_management' => false,
                    // Add any other properties needed by the view
                ];
            });

            // Return Inertia response with device and interfaces data
            return \Inertia\Inertia::render('network/devices/interfaces', [
                'device' => $device->load('site'),
                'interfaces' => $formattedInterfaces,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error fetching interfaces from device: '.$e->getMessage(), [
                'device_id' => $device->id,
                'ip_address' => $device->ip_address,
                'exception' => $e,
            ]);

            // Return Inertia response with error
            return \Inertia\Inertia::render('network/devices/interfaces', [
                'device' => $device->load('site'),
                'interfaces' => [],
                'error' => 'Failed to fetch interfaces from device: '.$e->getMessage(),
            ]);
        }
    }

    /**
     * Get the interfaces for the specified device (AJAX API).
     * Returns JSON response for real-time updates.
     */
    public function interfacesApi(NetworkDevice $device, Request $request)
    {
        // By default, fetch from the device for Mikrotik devices
        // Allow overriding with fetch_from_database=true parameter
        $fetchFromDatabase = $request->boolean('fetch_from_database', false);

        // Since we're MikroTik-focused, always try to fetch from device first unless explicitly requested from database
        if (! $fetchFromDatabase) {
            try {
                // For MikroTik devices, use RouterOS API to fetch interfaces directly
                $mikrotikInterfaces = $device->fetchInterfacesFromDevice();

                // Transform Mikrotik interfaces to match the expected format
                $formattedInterfaces = collect($mikrotikInterfaces)->map(function ($interface) {
                    // Default speed to 0 for interfaces that are down
                    $speed = 0;

                    // Only calculate speed if the interface is up
                    if (isset($interface['running']) && $interface['running'] === 'true') {
                        // Calculate actual speed based on traffic data
                        if (isset($interface['rx-byte']) && isset($interface['tx-byte']) && isset($interface['last-link-up-time'])) {
                            try {
                                $rxBytes = $interface['rx-byte'];           // Received bytes
                                $txBytes = $interface['tx-byte'];           // Transmitted bytes
                                $totalBytes = $rxBytes + $txBytes;          // Total bytes transferred

                                $linkUpTime = strtotime($interface['last-link-up-time']); // Timestamp when the link came up
                                $currentTime = time();                      // Current timestamp
                                $durationSeconds = $currentTime - $linkUpTime; // Duration in seconds

                                // Prevent division by zero if the link just came up
                                if ($durationSeconds > 0) {
                                    $totalBits = $totalBytes * 8;           // Convert bytes to bits
                                    $bps = $totalBits / $durationSeconds;   // Bits per second
                                    $mbps = round($bps / 1_000_000, 2);     // Convert to Mbps and round to 2 decimal places

                                    $speed = $mbps;
                                }
                            } catch (\Exception $e) {
                                \Log::warning('Error calculating interface speed: '.$e->getMessage(), [
                                    'interface' => $interface['name'] ?? 'unknown',
                                    'exception' => $e,
                                ]);

                                // Fallback to default values if calculation fails
                                if (isset($interface['type'])) {
                                    switch ($interface['type']) {
                                        case 'ether':
                                            $speed = 150;
                                            break;
                                        case 'sfp-sfpplus':
                                            $speed = 10000;
                                            break;
                                        case 'wlan':
                                            $speed = 300;
                                            break;
                                    }
                                }
                            }
                        } else {
                            // Fallback to default values if required data is missing
                            if (isset($interface['type'])) {
                                switch ($interface['type']) {
                                    case 'ether':
                                        $speed = 150;
                                        break;
                                    case 'sfp-sfpplus':
                                        $speed = 10000;
                                        break;
                                    case 'wlan':
                                        $speed = 300;
                                        break;
                                }
                            }
                        }
                    }

                    return [
                        'id' => $interface['.id'] ?? null,
                        'name' => $interface['name'] ?? '',
                        'type' => $interface['type'] ?? '',
                        'status' => isset($interface['running']) && $interface['running'] === 'true' ? 'up' : 'down',
                        'ip_address' => $interface['ip-address'] ?? '',
                        'subnet_mask' => $interface['subnet-mask'] ?? '',
                        'mac_address' => $interface['mac-address'] ?? '',
                        'speed' => $speed,
                        'is_management' => false,
                        // Add any other properties needed by the view
                    ];
                });

                return response()->json([
                    'status' => 'success',
                    'source' => 'device',
                    'data' => $formattedInterfaces,
                    'timestamp' => now()->toIso8601String(),
                ]);
            } catch (\Exception $e) {
                \Log::error('Error fetching interfaces from device: '.$e->getMessage(), [
                    'device_id' => $device->id,
                    'ip_address' => $device->ip_address,
                    'exception' => $e,
                ]);

                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to fetch interfaces from device: '.$e->getMessage(),
                    'timestamp' => now()->toIso8601String(),
                    'data' => [],
                ], 422);
            }
        }

        // Default behavior: fetch from database
        $query = $device->interfaces();

        // Apply filters
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('is_management')) {
            $query->where('is_management', $request->boolean('is_management'));
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $interfaces = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'status' => 'success',
            'source' => 'database',
            'data' => $interfaces,
        ]);
    }

    /**
     * Get a single interface for the specified device from MikroTik (live).
     *
     * @param  string  $interfaceId  MikroTik interface ID (usually '.id')
     */
    public function interfaceFromDevice(NetworkDevice $device, $interfaceId, Request $request)
    {
        // Since we're MikroTik-focused, always try to fetch from device
        if (true) {
            try {
                $mikrotikInterfaces = $device->fetchInterfacesFromDevice();
                $interface = collect($mikrotikInterfaces)->first(function ($iface) use ($interfaceId) {
                    return isset($iface['.id']) && $iface['.id'] == $interfaceId;
                });
                if (! $interface) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Interface not found on device',
                        'data' => null,
                    ], 404);
                }
                $speed = 0;
                if (isset($interface['running']) && $interface['running'] === 'true') {
                    if (isset($interface['rx-byte']) && isset($interface['tx-byte']) && isset($interface['last-link-up-time'])) {
                        try {
                            $rxBytes = $interface['rx-byte'];
                            $txBytes = $interface['tx-byte'];
                            $totalBytes = $rxBytes + $txBytes;
                            $linkUpTime = strtotime($interface['last-link-up-time']);
                            $currentTime = time();
                            $durationSeconds = $currentTime - $linkUpTime;
                            if ($durationSeconds > 0) {
                                $totalBits = $totalBytes * 8;
                                $bps = $totalBits / $durationSeconds;
                                $mbps = round($bps / 1_000_000, 2);
                                $speed = $mbps;
                            }
                        } catch (\Exception $e) {
                            // fallback below
                        }
                    } elseif (isset($interface['type'])) {
                        switch ($interface['type']) {
                            case 'ether': $speed = 150;
                                break;
                            case 'sfp-sfpplus': $speed = 10000;
                                break;
                            case 'wlan': $speed = 300;
                                break;
                        }
                    }
                }
                // Use only the base interface name for ARP lookup (e.g., 'ether2' from 'ether2 AL AMIN')
                $baseInterfaceName = explode(' ', $interface['name'] ?? '')[0];

                $clients_count = $device->getArpClientsCountForInterface($baseInterfaceName);
                $formatted = [
                    'id' => $interface['.id'] ?? null,
                    'name' => $interface['name'] ?? '',
                    'type' => $interface['type'] ?? '',
                    'status' => isset($interface['running']) && $interface['running'] === 'true' ? 'up' : 'down',
                    'ip_address' => $interface['ip-address'] ?? '',
                    'subnet_mask' => $interface['subnet-mask'] ?? '',
                    'mac_address' => $interface['mac-address'] ?? '',
                    'speed' => $speed,
                    'is_management' => false,
                    'clients_count' => $clients_count,
                ];

                return response()->json([
                    'status' => 'success',
                    'source' => 'device',
                    'data' => $formatted,
                    'timestamp' => now()->toIso8601String(),
                ]);
            } catch (\Exception $e) {
                \Log::error('Error fetching interface from device: '.$e->getMessage(), [
                    'device_id' => $device->id,
                    'ip_address' => $device->ip_address,
                    'exception' => $e,
                ]);

                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to fetch interface from device: '.$e->getMessage(),
                    'data' => null,
                ], 422);
            }
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Live interface fetch only supported for MikroTik/router/switch devices.',
            'data' => null,
        ], 400);
    }

    /**
     * Get all connections for the specified device.
     */
    public function connections(NetworkDevice $device)
    {
        $connections = $device->connections()->with(['sourceInterface.device', 'targetInterface.device'])->get();

        return response()->json($connections);
    }

    /**
     * Get the management interface for the specified device.
     */
    public function managementInterface(NetworkDevice $device)
    {
        $interface = $device->getManagementInterfaceAttribute();

        if (! $interface) {
            return response()->json([
                'message' => 'No management interface found for this device',
            ], 404);
        }

        return response()->json($interface);
    }

    /**
     * Add an interface to the specified device.
     */
    public function addInterface(Request $request, NetworkDevice $device)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'status' => 'string|max:255',
            'ip_address' => 'nullable|ip',
            'subnet_mask' => 'nullable|string|max:255',
            'mac_address' => 'nullable|string|max:255',
            'speed' => 'nullable|integer',
            'is_management' => 'boolean',
        ]);

        // If this is a management interface, unset any existing management interfaces
        if (isset($validated['is_management']) && $validated['is_management']) {
            $device->interfaces()->where('is_management', true)->update(['is_management' => false]);
        }

        $interface = $device->interfaces()->create($validated);

        return response()->json($interface, 201);
    }

    /**
     * Update device configuration.
     */
    public function updateConfiguration(Request $request, NetworkDevice $device)
    {
        $validated = $request->validate([
            'configuration' => 'required|json',
        ]);

        // Parse JSON configuration if provided as string
        if (is_string($validated['configuration'])) {
            $validated['configuration'] = json_decode($validated['configuration'], true);
        }

        $device->configuration = $validated['configuration'];
        $device->save();

        return response()->json($device);
    }

    /**
     * Get a configuration value for the specified device.
     */
    public function getConfigValue(Request $request, NetworkDevice $device)
    {
        $validated = $request->validate([
            'key' => 'required|string',
        ]);

        $value = $device->getConfigValue($validated['key']);

        if ($value === null) {
            return response()->json([
                'message' => 'Configuration key not found',
            ], 404);
        }

        return response()->json([
            'key' => $validated['key'],
            'value' => $value,
        ]);
    }

    /**
     * Set a configuration value for the specified device.
     */
    public function setConfigValue(Request $request, NetworkDevice $device)
    {
        $validated = $request->validate([
            'key' => 'required|string',
            'value' => 'required',
        ]);

        $device->setConfigValue($validated['key'], $validated['value']);
        $device->save();

        return response()->json([
            'message' => 'Configuration value updated successfully',
            'device' => $device,
        ]);
    }

    /**
     * Get device types.
     */
    public function types()
    {
        // Since all devices are now MikroTik routers, return a static list
        $types = ['mikrotik', 'router'];

        return response()->json($types);
    }

    /**
     * Get detected device models.
     */
    public function detectedModels()
    {
        $models = NetworkDevice::select('detected_model')
            ->whereNotNull('detected_model')
            ->distinct()
            ->orderBy('detected_model')
            ->pluck('detected_model');

        return response()->json($models);
    }

    /**
     * Get device statistics.
     */
    public function statistics()
    {
        $totalDevices = NetworkDevice::count();
        $activeDevices = NetworkDevice::where('status', 'active')->count();

        $devicesByDetectedModel = NetworkDevice::select('detected_model')
            ->whereNotNull('detected_model')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('detected_model')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $devicesBySite = NetworkDevice::select('site_id')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('site_id')
            ->orderByRaw('COUNT(*) DESC')
            ->with('site:id,name')
            ->limit(10)
            ->get();

        return response()->json([
            'total_devices' => $totalDevices,
            'active_devices' => $activeDevices,
            'devices_by_detected_model' => $devicesByDetectedModel,
            'devices_by_site' => $devicesBySite,
        ]);
    }

    /**
     * Test connection to the device and fetch its capabilities.
     */
    public function testConnection(NetworkDevice $device)
    {
        $capabilities = $device->testConnection();

        if ($capabilities === null) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to connect to the device. Please check the IP address and try again.',
            ], 422);
        }

        return response()->json($capabilities);
    }

    /**
     * Auto-detect device information via MikroTik API
     */
    private function detectDeviceInfo(NetworkDevice $device): ?array
    {
        try {
            // Decrypt password for API connection
            $password = decrypt($device->api_password);

            // Create MikroTik API connection
            $api = new \RouterOS\Client([
                'host' => $device->ip_address,
                'user' => $device->api_username,
                'pass' => $password,
                'port' => $device->api_port,
            ]);

            // Get system resource information
            $resource = $api->query('/system/resource/print')->read();

            if (! empty($resource)) {
                $info = $resource[0];

                return [
                    'model' => $info['board-name'] ?? null,
                    'version' => $info['version'] ?? null,
                    'architecture' => $info['architecture-name'] ?? null,
                    'cpu' => $info['cpu'] ?? null,
                    'uptime' => $info['uptime'] ?? null,
                ];
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to detect device info via API', [
                'device_id' => $device->id,
                'ip_address' => $device->ip_address,
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }
}
