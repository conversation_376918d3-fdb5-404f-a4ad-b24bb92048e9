<?php

namespace App\Http\Controllers\Network;

use App\Http\Controllers\Controller;
use App\Models\Network\NetworkConnection;
use App\Models\Network\NetworkDevice;
use App\Models\Network\NetworkInterface;
use Illuminate\Http\Request;

class NetworkInterfaceController extends Controller
{
    /**
     * Display a listing of the interfaces.
     */
    public function index(Request $request)
    {
        $query = NetworkInterface::query();

        // Apply filters
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->search.'%')
                    ->orWhere('type', 'like', '%'.$request->search.'%')
                    ->orWhere('ip_address', 'like', '%'.$request->search.'%')
                    ->orWhere('mac_address', 'like', '%'.$request->search.'%');
            });
        }

        if ($request->has('device_id')) {
            $query->where('device_id', $request->device_id);
        }

        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('is_management')) {
            $query->where('is_management', $request->boolean('is_management'));
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $interfaces = $query->with('device')->paginate($request->get('per_page', 15));

        return response()->json($interfaces);
    }

    /**
     * Store a newly created interface.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'device_id' => 'required|exists:network_devices,id',
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'status' => 'string|max:255',
            'ip_address' => 'nullable|ip',
            'subnet_mask' => 'nullable|string|max:255',
            'mac_address' => 'nullable|string|max:255',
            'speed' => 'nullable|integer',
            'is_management' => 'boolean',
        ]);

        $device = NetworkDevice::findOrFail($validated['device_id']);

        // If this is a management interface, unset any existing management interfaces
        if (isset($validated['is_management']) && $validated['is_management']) {
            $device->interfaces()->where('is_management', true)->update(['is_management' => false]);
        }

        $interface = NetworkInterface::create($validated);

        return response()->json($interface, 201);
    }

    /**
     * Display the specified interface.
     */
    public function show(NetworkInterface $interface)
    {
        $interface->load('device');

        return response()->json($interface);
    }

    /**
     * Update the specified interface.
     */
    public function update(Request $request, NetworkInterface $interface)
    {
        $validated = $request->validate([
            'name' => 'string|max:255',
            'type' => 'string|max:255',
            'status' => 'string|max:255',
            'ip_address' => 'nullable|ip',
            'subnet_mask' => 'nullable|string|max:255',
            'mac_address' => 'nullable|string|max:255',
            'speed' => 'nullable|integer',
            'is_management' => 'boolean',
        ]);

        // If this is being set as a management interface, unset any existing management interfaces
        if (isset($validated['is_management']) && $validated['is_management'] && ! $interface->is_management) {
            $interface->device->interfaces()->where('is_management', true)->update(['is_management' => false]);
        }

        $interface->update($validated);

        return response()->json($interface);
    }

    /**
     * Remove the specified interface.
     */
    public function destroy(NetworkInterface $interface)
    {
        // Check if the interface has connections
        $outgoingConnectionsCount = $interface->outgoingConnections()->count();
        $incomingConnectionsCount = $interface->incomingConnections()->count();

        if ($outgoingConnectionsCount > 0 || $incomingConnectionsCount > 0) {
            return response()->json([
                'message' => 'Cannot delete interface with connections. There are '.
                    ($outgoingConnectionsCount + $incomingConnectionsCount).' connections using this interface.',
            ], 422);
        }

        $interface->delete();

        return response()->json(null, 204);
    }

    /**
     * Get the outgoing connections for the specified interface.
     */
    public function outgoingConnections(NetworkInterface $interface)
    {
        $connections = $interface->outgoingConnections()->with(['targetInterface.device'])->get();

        return response()->json($connections);
    }

    /**
     * Get the incoming connections for the specified interface.
     */
    public function incomingConnections(NetworkInterface $interface)
    {
        $connections = $interface->incomingConnections()->with(['sourceInterface.device'])->get();

        return response()->json($connections);
    }

    /**
     * Get all connections for the specified interface.
     */
    public function allConnections(NetworkInterface $interface)
    {
        $connections = $interface->getAllConnectionsAttribute();

        return response()->json($connections);
    }

    /**
     * Create a connection between two interfaces.
     */
    public function createConnection(Request $request, NetworkInterface $interface)
    {
        $validated = $request->validate([
            'target_interface_id' => 'required|exists:network_interfaces,id',
            'type' => 'required|string|max:255',
            'status' => 'string|max:255',
            'bandwidth' => 'nullable|integer',
            'latency' => 'nullable|integer',
        ]);

        // Check if target interface is the same as source interface
        if ($validated['target_interface_id'] == $interface->id) {
            return response()->json([
                'message' => 'Cannot create a connection to the same interface',
            ], 422);
        }

        // Check if a connection already exists between these interfaces
        $existingConnection = NetworkConnection::where(function ($query) use ($interface, $validated) {
            $query->where('source_interface_id', $interface->id)
                ->where('target_interface_id', $validated['target_interface_id']);
        })->orWhere(function ($query) use ($interface, $validated) {
            $query->where('source_interface_id', $validated['target_interface_id'])
                ->where('target_interface_id', $interface->id);
        })->first();

        if ($existingConnection) {
            return response()->json([
                'message' => 'A connection already exists between these interfaces',
                'connection' => $existingConnection,
            ], 422);
        }

        $connection = NetworkConnection::create([
            'source_interface_id' => $interface->id,
            'target_interface_id' => $validated['target_interface_id'],
            'type' => $validated['type'],
            'status' => $validated['status'] ?? 'active',
            'bandwidth' => $validated['bandwidth'] ?? null,
            'latency' => $validated['latency'] ?? null,
        ]);

        return response()->json($connection, 201);
    }

    /**
     * Get interface types.
     */
    public function types()
    {
        $types = NetworkInterface::select('type')->distinct()->orderBy('type')->pluck('type');

        return response()->json($types);
    }

    /**
     * Get interface statistics.
     */
    public function statistics()
    {
        $totalInterfaces = NetworkInterface::count();
        $upInterfaces = NetworkInterface::where('status', 'up')->count();
        $downInterfaces = NetworkInterface::where('status', 'down')->count();
        $managementInterfaces = NetworkInterface::where('is_management', true)->count();

        $interfacesByType = NetworkInterface::select('type')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('type')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $interfacesByDevice = NetworkInterface::select('device_id')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('device_id')
            ->orderByRaw('COUNT(*) DESC')
            ->limit(10)
            ->with('device:id,name,type')
            ->get();

        return response()->json([
            'total_interfaces' => $totalInterfaces,
            'up_interfaces' => $upInterfaces,
            'down_interfaces' => $downInterfaces,
            'management_interfaces' => $managementInterfaces,
            'interfaces_by_type' => $interfacesByType,
            'interfaces_by_device' => $interfacesByDevice,
        ]);
    }

    /**
     * Get the CIDR notation for the interface.
     */
    public function cidr(NetworkInterface $interface)
    {
        $cidr = $interface->getCidrAttribute();

        if (! $cidr) {
            return response()->json([
                'message' => 'IP address or subnet mask not set for this interface',
            ], 404);
        }

        return response()->json([
            'cidr' => $cidr,
        ]);
    }

    /**
     * Set the interface status.
     */
    public function setStatus(Request $request, NetworkInterface $interface)
    {
        $validated = $request->validate([
            'status' => 'required|string|in:up,down',
        ]);

        $interface->status = $validated['status'];
        $interface->save();

        return response()->json([
            'message' => 'Interface status updated successfully',
            'interface' => $interface,
        ]);
    }

    /**
     * Set the interface as the management interface for its device.
     */
    public function setAsManagement(NetworkInterface $interface)
    {
        // Unset any existing management interfaces for this device
        $interface->device->interfaces()->where('is_management', true)->update(['is_management' => false]);

        // Set this interface as management
        $interface->is_management = true;
        $interface->save();

        return response()->json([
            'message' => 'Interface set as management interface successfully',
            'interface' => $interface,
        ]);
    }
}
