<?php

namespace App\Http\Controllers\Network;

use App\Http\Controllers\Controller;
use App\Models\Network\NetworkConnection;
use App\Models\Network\NetworkDevice;
use Illuminate\Http\Request;

class NetworkConnectionController extends Controller
{
    /**
     * Display a listing of the connections.
     */
    public function index(Request $request)
    {
        $query = NetworkConnection::query();

        // Apply filters
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('min_bandwidth')) {
            $query->where('bandwidth', '>=', $request->min_bandwidth);
        }

        if ($request->has('max_latency')) {
            $query->where('latency', '<=', $request->max_latency);
        }

        if ($request->has('device_id')) {
            $deviceId = $request->device_id;
            $query->involvingDevice($deviceId);
        }

        if ($request->has('source_device_id') && $request->has('target_device_id')) {
            $sourceDeviceId = $request->source_device_id;
            $targetDeviceId = $request->target_device_id;
            $query->betweenDevices($sourceDeviceId, $targetDeviceId);
        }

        if ($request->has('interface_id')) {
            $interfaceId = $request->interface_id;
            $query->where(function ($q) use ($interfaceId) {
                $q->where('source_interface_id', $interfaceId)
                    ->orWhere('target_interface_id', $interfaceId);
            });
        }

        // Apply sorting
        $sortField = $request->get('sort_field', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $connections = $query->with(['sourceInterface.device', 'targetInterface.device'])->paginate($request->get('per_page', 15));

        return response()->json($connections);
    }

    /**
     * Store a newly created connection.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'source_interface_id' => 'required|exists:network_interfaces,id',
            'target_interface_id' => 'required|exists:network_interfaces,id|different:source_interface_id',
            'type' => 'required|string|max:255',
            'status' => 'string|max:255',
            'bandwidth' => 'nullable|integer',
            'latency' => 'nullable|integer',
        ]);

        // Check if a connection already exists between these interfaces
        $existingConnection = NetworkConnection::where(function ($query) use ($validated) {
            $query->where('source_interface_id', $validated['source_interface_id'])
                ->where('target_interface_id', $validated['target_interface_id']);
        })->orWhere(function ($query) use ($validated) {
            $query->where('source_interface_id', $validated['target_interface_id'])
                ->where('target_interface_id', $validated['source_interface_id']);
        })->first();

        if ($existingConnection) {
            return response()->json([
                'message' => 'A connection already exists between these interfaces',
                'connection' => $existingConnection,
            ], 422);
        }

        $connection = NetworkConnection::create($validated);

        return response()->json($connection, 201);
    }

    /**
     * Display the specified connection.
     */
    public function show(NetworkConnection $connection)
    {
        $connection->load(['sourceInterface.device', 'targetInterface.device']);

        return response()->json($connection);
    }

    /**
     * Update the specified connection.
     */
    public function update(Request $request, NetworkConnection $connection)
    {
        $validated = $request->validate([
            'type' => 'string|max:255',
            'status' => 'string|max:255',
            'bandwidth' => 'nullable|integer',
            'latency' => 'nullable|integer',
        ]);

        $connection->update($validated);

        return response()->json($connection);
    }

    /**
     * Remove the specified connection.
     */
    public function destroy(NetworkConnection $connection)
    {
        $connection->delete();

        return response()->json(null, 204);
    }

    /**
     * Get the source device for the connection.
     */
    public function sourceDevice(NetworkConnection $connection)
    {
        $device = $connection->getSourceDeviceAttribute();

        return response()->json($device);
    }

    /**
     * Get the target device for the connection.
     */
    public function targetDevice(NetworkConnection $connection)
    {
        $device = $connection->getTargetDeviceAttribute();

        return response()->json($device);
    }

    /**
     * Get all connections between two devices.
     */
    public function betweenDevices(Request $request)
    {
        $validated = $request->validate([
            'source_device_id' => 'required|exists:network_devices,id',
            'target_device_id' => 'required|exists:network_devices,id|different:source_device_id',
        ]);

        $connections = NetworkConnection::betweenDevices(
            $validated['source_device_id'],
            $validated['target_device_id']
        )->with(['sourceInterface', 'targetInterface'])->get();

        return response()->json($connections);
    }

    /**
     * Get all connections for a device.
     */
    public function forDevice(Request $request)
    {
        $validated = $request->validate([
            'device_id' => 'required|exists:network_devices,id',
        ]);

        $connections = NetworkConnection::involvingDevice($validated['device_id'])
            ->with(['sourceInterface.device', 'targetInterface.device'])
            ->get();

        return response()->json($connections);
    }

    /**
     * Get connection types.
     */
    public function types()
    {
        $types = NetworkConnection::select('type')->distinct()->orderBy('type')->pluck('type');

        return response()->json($types);
    }

    /**
     * Get connection statistics.
     */
    public function statistics()
    {
        $totalConnections = NetworkConnection::count();
        $activeConnections = NetworkConnection::where('status', 'active')->count();

        $connectionsByType = NetworkConnection::select('type')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('type')
            ->orderByRaw('COUNT(*) DESC')
            ->get();

        $avgBandwidth = NetworkConnection::whereNotNull('bandwidth')->avg('bandwidth');
        $avgLatency = NetworkConnection::whereNotNull('latency')->avg('latency');

        $devicesWithMostConnections = NetworkDevice::withCount(['sourceInterfaces as outgoing_connections_count' => function ($query) {
            $query->has('outgoingConnections');
        }, 'targetInterfaces as incoming_connections_count' => function ($query) {
            $query->has('incomingConnections');
        }])
            ->selectRaw('(outgoing_connections_count + incoming_connections_count) as total_connections_count')
            ->orderByRaw('total_connections_count DESC')
            ->limit(10)
            ->get();

        return response()->json([
            'total_connections' => $totalConnections,
            'active_connections' => $activeConnections,
            'connections_by_type' => $connectionsByType,
            'average_bandwidth' => $avgBandwidth,
            'average_latency' => $avgLatency,
            'devices_with_most_connections' => $devicesWithMostConnections,
        ]);
    }

    /**
     * Set the connection status.
     */
    public function setStatus(Request $request, NetworkConnection $connection)
    {
        $validated = $request->validate([
            'status' => 'required|string|in:active,inactive,down',
        ]);

        $connection->status = $validated['status'];
        $connection->save();

        return response()->json([
            'message' => 'Connection status updated successfully',
            'connection' => $connection,
        ]);
    }

    /**
     * Update the bandwidth and latency of a connection.
     */
    public function updatePerformance(Request $request, NetworkConnection $connection)
    {
        $validated = $request->validate([
            'bandwidth' => 'nullable|integer',
            'latency' => 'nullable|integer',
        ]);

        if (isset($validated['bandwidth'])) {
            $connection->bandwidth = $validated['bandwidth'];
        }

        if (isset($validated['latency'])) {
            $connection->latency = $validated['latency'];
        }

        $connection->save();

        return response()->json([
            'message' => 'Connection performance metrics updated successfully',
            'connection' => $connection,
        ]);
    }

    /**
     * Find the path between two devices.
     */
    public function findPath(Request $request)
    {
        $validated = $request->validate([
            'source_device_id' => 'required|exists:network_devices,id',
            'target_device_id' => 'required|exists:network_devices,id|different:source_device_id',
        ]);

        // This would be a complex implementation using graph algorithms
        // For now, we'll return a placeholder response
        return response()->json([
            'message' => 'Path finding functionality not implemented yet',
            'source_device_id' => $validated['source_device_id'],
            'target_device_id' => $validated['target_device_id'],
        ]);
    }
}
