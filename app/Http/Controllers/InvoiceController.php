<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class InvoiceController extends Controller
{
    /**
     * Display a listing of the invoices.
     */
    public function index(Request $request)
    {
        $query = Invoice::with(['customer', 'subscription']);

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by customer if provided
        if ($request->has('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // Filter by date range if provided
        if ($request->has('from_date') && $request->has('to_date')) {
            $query->whereBetween('issue_date', [$request->from_date, $request->to_date]);
        }

        $invoices = $query->orderBy('issue_date', 'desc')->paginate(15);

        return response()->json([
            'status' => 'success',
            'data' => $invoices,
        ]);
    }

    /**
     * Store a newly created invoice in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'subscription_id' => 'nullable|exists:subscriptions,id',
            'issue_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:issue_date',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.description' => 'required|string',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.tax_rate' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Create the invoice
        $invoice = new Invoice([
            'customer_id' => $request->customer_id,
            'subscription_id' => $request->subscription_id,
            'invoice_number' => Invoice::generateInvoiceNumber(),
            'issue_date' => $request->issue_date,
            'due_date' => $request->due_date,
            'amount' => 0,
            'tax_amount' => 0,
            'total_amount' => 0,
            'status' => 'pending',
            'notes' => $request->notes,
        ]);

        $invoice->save();

        // Create the invoice items
        foreach ($request->items as $itemData) {
            $invoiceItem = new InvoiceItem([
                'invoice_id' => $invoice->id,
                'description' => $itemData['description'],
                'quantity' => $itemData['quantity'],
                'unit_price' => $itemData['unit_price'],
                'tax_rate' => $itemData['tax_rate'],
                'tax_amount' => 0,
                'subtotal' => 0,
                'total' => 0,
            ]);

            $invoiceItem->save();
            $invoiceItem->calculateAmounts();
        }

        // Update the invoice totals
        $invoice->calculateTotal();

        // Load the items for the response
        $invoice->load('items');

        return response()->json([
            'status' => 'success',
            'message' => 'Invoice created successfully',
            'data' => $invoice,
        ], 201);
    }

    /**
     * Display the specified invoice.
     */
    public function show(Invoice $invoice)
    {
        $invoice->load(['customer', 'subscription', 'items']);

        return response()->json([
            'status' => 'success',
            'data' => $invoice,
        ]);
    }

    /**
     * Update the specified invoice in storage.
     */
    public function update(Request $request, Invoice $invoice)
    {
        // Only allow updating certain fields if the invoice is not paid
        if ($invoice->status === 'paid') {
            return response()->json([
                'status' => 'error',
                'message' => 'Cannot update a paid invoice',
            ], 422);
        }

        $validator = Validator::make($request->all(), [
            'due_date' => 'sometimes|required|date|after_or_equal:issue_date',
            'notes' => 'nullable|string',
            'status' => 'sometimes|required|in:pending,paid,overdue,cancelled',
            'paid_date' => 'nullable|date|required_if:status,paid',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Update the invoice
        $invoice->update($request->only(['due_date', 'notes', 'status', 'paid_date']));

        // If marking as paid, set the paid date
        if ($request->status === 'paid' && ! $invoice->paid_date) {
            $invoice->paid_date = now();
            $invoice->save();
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Invoice updated successfully',
            'data' => $invoice,
        ]);
    }

    /**
     * Remove the specified invoice from storage.
     */
    public function destroy(Invoice $invoice)
    {
        // Only allow deleting if the invoice is not paid
        if ($invoice->status === 'paid') {
            return response()->json([
                'status' => 'error',
                'message' => 'Cannot delete a paid invoice',
            ], 422);
        }

        $invoice->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Invoice deleted successfully',
        ]);
    }

    /**
     * Mark an invoice as paid.
     */
    public function markAsPaid(Invoice $invoice)
    {
        if ($invoice->status === 'paid') {
            return response()->json([
                'status' => 'error',
                'message' => 'Invoice is already paid',
            ], 422);
        }

        $invoice->markAsPaid();

        return response()->json([
            'status' => 'success',
            'message' => 'Invoice marked as paid successfully',
            'data' => $invoice,
        ]);
    }

    /**
     * Cancel an invoice.
     */
    public function cancel(Invoice $invoice)
    {
        if ($invoice->status === 'paid') {
            return response()->json([
                'status' => 'error',
                'message' => 'Cannot cancel a paid invoice',
            ], 422);
        }

        $invoice->status = 'cancelled';
        $invoice->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Invoice cancelled successfully',
            'data' => $invoice,
        ]);
    }

    /**
     * Get all overdue invoices.
     */
    public function overdue()
    {
        $invoices = Invoice::overdue()->with(['customer', 'subscription'])->get();

        return response()->json([
            'status' => 'success',
            'data' => $invoices,
        ]);
    }
}
