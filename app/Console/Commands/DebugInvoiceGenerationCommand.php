<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Console\Command;

class DebugInvoiceGenerationCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'debug:invoice-generation 
                            {--limit=10 : Number of subscriptions to analyze}
                            {--show-all : Show all subscriptions, not just eligible ones}';

    /**
     * The console command description.
     */
    protected $description = 'Debug why factory-generated subscriptions are not generating invoices';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Debugging Invoice Generation for Factory-Generated Subscriptions');
        $this->newLine();

        $limit = $this->option('limit');
        $showAll = $this->option('show-all');

        // Get current date for comparison
        $now = now();
        $this->info("Current date/time: {$now}");
        $this->newLine();

        // Check total subscription counts
        $this->analyzeSubscriptionCounts();
        $this->newLine();

        // Analyze subscription filtering criteria
        $this->analyzeFilteringCriteria($now);
        $this->newLine();

        // Show sample subscriptions
        $this->analyzeSampleSubscriptions($limit, $showAll, $now);
    }

    /**
     * Analyze subscription counts by different criteria.
     */
    protected function analyzeSubscriptionCounts()
    {
        $this->info('📊 Subscription Counts Analysis:');

        $totalSubscriptions = Subscription::count();
        $activeSubscriptions = Subscription::where('status', 'active')->count();
        $autoBillingEnabled = Subscription::where('auto_billing_enabled', true)->count();
        $needsBillingToday = Subscription::where('next_billing_date', '<=', now())->count();

        $eligibleSubscriptions = Subscription::where('status', 'active')
            ->where('auto_billing_enabled', true)
            ->where('next_billing_date', '<=', now())
            ->count();

        $this->table(['Criteria', 'Count'], [
            ['Total Subscriptions', $totalSubscriptions],
            ['Active Status', $activeSubscriptions],
            ['Auto Billing Enabled', $autoBillingEnabled],
            ['Next Billing Date <= Today', $needsBillingToday],
            ['Eligible for Billing (All Criteria)', $eligibleSubscriptions],
        ]);
    }

    /**
     * Analyze each filtering criteria step by step.
     */
    protected function analyzeFilteringCriteria($now)
    {
        $this->info('🔍 Step-by-Step Filtering Analysis:');

        // Start with all subscriptions
        $query = Subscription::query();
        $step1Count = $query->count();
        $this->info("Step 1 - All subscriptions: {$step1Count}");

        // Apply status filter
        $query->where('status', 'active');
        $step2Count = $query->count();
        $this->info("Step 2 - Active status: {$step2Count}");

        // Apply auto billing filter
        $query->where('auto_billing_enabled', true);
        $step3Count = $query->count();
        $this->info("Step 3 - Auto billing enabled: {$step3Count}");

        // Apply billing date filter
        $query->where('next_billing_date', '<=', $now);
        $step4Count = $query->count();
        $this->info("Step 4 - Next billing date <= now: {$step4Count}");

        if ($step4Count === 0) {
            $this->warn('❌ No subscriptions pass all filtering criteria!');

            // Check what the next billing dates actually are
            $this->info('');
            $this->info('🗓️  Analyzing next_billing_date values:');

            $futureBilling = Subscription::where('status', 'active')
                ->where('auto_billing_enabled', true)
                ->where('next_billing_date', '>', $now)
                ->selectRaw('next_billing_date, COUNT(*) as count')
                ->groupBy('next_billing_date')
                ->orderBy('next_billing_date')
                ->limit(10)
                ->get();

            if ($futureBilling->isNotEmpty()) {
                $this->info('Future billing dates found:');
                foreach ($futureBilling as $billing) {
                    $daysFromNow = Carbon::parse($billing->next_billing_date)->diffInDays($now);
                    $this->info("  {$billing->next_billing_date} ({$billing->count} subscriptions, {$daysFromNow} days from now)");
                }
            }
        }
    }

    /**
     * Analyze sample subscriptions in detail.
     */
    protected function analyzeSampleSubscriptions($limit, $showAll, $now)
    {
        $this->info('📋 Sample Subscription Analysis:');

        $query = Subscription::with('customer');

        if (! $showAll) {
            // Show only subscriptions that should be eligible but aren't
            $query->where('status', 'active')
                ->where('auto_billing_enabled', true);
        }

        $subscriptions = $query->limit($limit)->get();

        if ($subscriptions->isEmpty()) {
            $this->warn('No subscriptions found to analyze.');

            return;
        }

        $headers = ['ID', 'Customer', 'Status', 'Auto Billing', 'Next Billing Date', 'Days From Now', 'Eligible?'];
        $rows = [];

        foreach ($subscriptions as $subscription) {
            $nextBillingDate = Carbon::parse($subscription->next_billing_date);
            $daysFromNow = $nextBillingDate->diffInDays($now, false); // false = future dates are positive

            $eligible = $subscription->status === 'active'
                       && $subscription->auto_billing_enabled
                       && $subscription->next_billing_date <= $now;

            $rows[] = [
                $subscription->id,
                $subscription->customer->name ?? 'N/A',
                $subscription->status,
                $subscription->auto_billing_enabled ? 'Yes' : 'No',
                $subscription->next_billing_date,
                $daysFromNow > 0 ? "+{$daysFromNow}" : $daysFromNow,
                $eligible ? '✅ Yes' : '❌ No',
            ];
        }

        $this->table($headers, $rows);

        // Provide recommendations
        $this->newLine();
        $this->info('💡 Recommendations:');

        $futureCount = Subscription::where('status', 'active')
            ->where('auto_billing_enabled', true)
            ->where('next_billing_date', '>', $now)
            ->count();

        if ($futureCount > 0) {
            $this->warn("Found {$futureCount} subscriptions with future billing dates.");
            $this->info('To fix this, you can:');
            $this->info('1. Update the SubscriptionFactory to set next_billing_date in the past');
            $this->info('2. Run a database update to set next_billing_date to yesterday for testing');
            $this->info('3. Use the needsBilling() factory state when creating test data');
        }
    }
}
