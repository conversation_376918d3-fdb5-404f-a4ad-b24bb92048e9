<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Subscription;
use App\Services\AutomatedBillingService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class VerifyBillingFixCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'verify:billing-fix 
                            {--customers=5 : Number of customers to verify}
                            {--reset-all : Reset all test customers}';

    /**
     * The console command description.
     */
    protected $description = 'Verify the billing fix works correctly across multiple customers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Verifying Billing Fix Across Multiple Customers');
        $this->newLine();

        $customerCount = (int) $this->option('customers');
        $resetAll = $this->option('reset-all');

        if ($resetAll) {
            $this->resetAllTestCustomers();

            return 0;
        }

        $this->verifyBillingLogic($customerCount);

        return 0;
    }

    /**
     * Reset all test customers to a clean state.
     */
    protected function resetAllTestCustomers()
    {
        $this->info('🔄 Resetting all test customers...');

        $yesterday = Carbon::now()->subDay();

        // Update all subscriptions to be eligible for billing
        $updatedSubscriptions = Subscription::where('status', 'active')
            ->update([
                'next_billing_date' => $yesterday,
                'last_billing_date' => null,
            ]);

        // Delete all invoices
        $deletedInvoices = Invoice::truncate();

        $this->info('✅ Reset complete:');
        $this->info("   - Updated {$updatedSubscriptions} subscriptions");
        $this->info("   - Set all next_billing_date to: {$yesterday->toDateString()}");
        $this->info('   - Cleared all last_billing_date fields');
        $this->info('   - Deleted all invoices');
        $this->newLine();
    }

    /**
     * Verify billing logic across multiple customers.
     */
    protected function verifyBillingLogic($customerCount)
    {
        $customers = Customer::with('subscriptions')
            ->whereHas('subscriptions')
            ->take($customerCount)
            ->get();

        if ($customers->isEmpty()) {
            $this->error('No customers with subscriptions found.');

            return;
        }

        $this->info("Testing billing logic with {$customers->count()} customers:");
        $this->newLine();

        $billingService = new AutomatedBillingService;
        $results = [
            'eligible_customers' => 0,
            'invoices_generated' => 0,
            'duplicate_attempts_blocked' => 0,
            'payments_processed' => 0,
            'billing_dates_updated' => 0,
        ];

        foreach ($customers as $customer) {
            $subscription = $customer->subscriptions->first();
            if (! $subscription) {
                continue;
            }

            $this->info("Testing Customer: {$customer->name} (ID: {$customer->id})");

            // Check eligibility
            $eligible = $this->isEligibleForBilling($subscription);
            if ($eligible) {
                $results['eligible_customers']++;
                $this->info('  ✅ Eligible for billing');

                // Test invoice generation
                $invoice = $billingService->generateInvoice($subscription);
                if ($invoice) {
                    $results['invoices_generated']++;
                    $this->info("  ✅ Invoice generated: {$invoice->invoice_number}");

                    // Test duplicate prevention
                    $duplicateInvoice = $billingService->generateInvoice($subscription->fresh());
                    if (! $duplicateInvoice) {
                        $results['duplicate_attempts_blocked']++;
                        $this->info('  ✅ Duplicate invoice blocked');
                    } else {
                        $this->error('  ❌ Duplicate invoice NOT blocked');
                    }

                    // Test payment processing
                    $originalNextBillingDate = $subscription->fresh()->next_billing_date;
                    $invoice->update(['status' => 'paid']);
                    $results['payments_processed']++;

                    $updatedSubscription = $subscription->fresh();
                    if ($updatedSubscription->next_billing_date != $originalNextBillingDate) {
                        $results['billing_dates_updated']++;
                        $this->info('  ✅ Next billing date updated after payment');
                        $this->info("     From: {$originalNextBillingDate}");
                        $this->info("     To: {$updatedSubscription->next_billing_date}");
                    } else {
                        $this->error('  ❌ Next billing date NOT updated after payment');
                    }
                } else {
                    $this->warn('  ⚠️  No invoice generated (may have existing invoice)');
                }
            } else {
                $this->info('  ❌ Not eligible for billing');
            }

            $this->newLine();
        }

        $this->showResults($results, $customers->count());
    }

    /**
     * Check if subscription is eligible for billing.
     */
    protected function isEligibleForBilling(Subscription $subscription): bool
    {
        $now = Carbon::now();

        return $subscription->status === 'active'
               && $subscription->auto_billing_enabled
               && $subscription->next_billing_date <= $now;
    }

    /**
     * Show test results summary.
     */
    protected function showResults(array $results, int $totalCustomers)
    {
        $this->info('📊 Billing Fix Verification Results:');
        $this->newLine();

        $this->table(['Metric', 'Count', 'Status'], [
            ['Total Customers Tested', $totalCustomers, '📋'],
            ['Eligible for Billing', $results['eligible_customers'], $results['eligible_customers'] > 0 ? '✅' : '❌'],
            ['Invoices Generated', $results['invoices_generated'], $results['invoices_generated'] > 0 ? '✅' : '❌'],
            ['Duplicate Attempts Blocked', $results['duplicate_attempts_blocked'], $results['duplicate_attempts_blocked'] === $results['invoices_generated'] ? '✅' : '❌'],
            ['Payments Processed', $results['payments_processed'], $results['payments_processed'] > 0 ? '✅' : '❌'],
            ['Billing Dates Updated', $results['billing_dates_updated'], $results['billing_dates_updated'] === $results['payments_processed'] ? '✅' : '❌'],
        ]);

        $this->newLine();

        // Overall assessment
        $allTestsPassed = $results['duplicate_attempts_blocked'] === $results['invoices_generated'] &&
                         $results['billing_dates_updated'] === $results['payments_processed'];

        if ($allTestsPassed && $results['invoices_generated'] > 0) {
            $this->info('🎉 ALL TESTS PASSED - Billing fix is working correctly!');
        } elseif ($results['eligible_customers'] === 0) {
            $this->warn('⚠️  No customers were eligible for billing. Run with --reset-all first.');
        } else {
            $this->error('❌ Some tests failed - billing fix needs attention.');
        }

        $this->newLine();
        $this->info('💡 To reset all customers for testing: --reset-all');
    }
}
