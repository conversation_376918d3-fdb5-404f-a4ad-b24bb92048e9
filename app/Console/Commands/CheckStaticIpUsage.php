<?php

namespace App\Console\Commands;

use App\Models\Network\NetworkDevice;
use Illuminate\Console\Command;

class CheckStaticIpUsage extends Command
{
    protected $signature = 'check:static-ip-usage {ip : The IP address to check}';

    protected $description = 'Check bandwidth usage for a static IP address';

    public function handle()
    {
        $ip = $this->argument('ip');
        $this->info("🔍 Checking bandwidth usage for IP: {$ip}");
        $this->line('');

        $device = NetworkDevice::find(1);
        if (! $device) {
            $this->error('Device not found');

            return 1;
        }

        try {
            // 1. Check queues
            $this->info('1. Checking queues...');
            $queues = $device->executeMikrotikCommand('/queue/simple/print');
            $this->line('   Total queues: '.count($queues));

            $foundQueue = false;
            foreach ($queues as $queue) {
                if (isset($queue['target']) && (
                    strpos($queue['target'], $ip) !== false ||
                    strpos($queue['target'], $ip.'/32') !== false
                )) {
                    $this->info("   ✅ Found queue for {$ip}:");
                    $this->line('      Name: '.($queue['name'] ?? 'N/A'));
                    $this->line('      Target: '.($queue['target'] ?? 'N/A'));
                    $this->line('      Bytes: '.($queue['bytes'] ?? 'N/A'));
                    $foundQueue = true;
                    break;
                }
            }

            if (! $foundQueue) {
                $this->warn("   ❌ No queue found for {$ip}");
            }

            $this->line('');

            // 2. Check firewall accounting
            $this->info('2. Checking firewall accounting...');
            try {
                $accounting = $device->executeMikrotikCommand('/ip/accounting/print');
                $this->line('   Accounting entries: '.count($accounting));

                foreach ($accounting as $entry) {
                    if (isset($entry['src-address']) && $entry['src-address'] === $ip) {
                        $this->info("   ✅ Found accounting for {$ip}:");
                        $this->line('      Src: '.($entry['src-address'] ?? 'N/A'));
                        $this->line('      Dst: '.($entry['dst-address'] ?? 'N/A'));
                        $this->line('      Bytes: '.($entry['bytes'] ?? 'N/A'));
                        break;
                    }
                }
            } catch (\Exception $e) {
                $this->warn('   Accounting check failed: '.$e->getMessage());
            }

            $this->line('');

            // 3. Check interface traffic
            $this->info('3. Checking interface traffic...');
            try {
                $interfaces = $device->executeMikrotikCommand('/interface/print');
                foreach ($interfaces as $interface) {
                    if (isset($interface['name']) && strpos($interface['name'], '192.168.7') !== false) {
                        $this->info('   Found interface with 192.168.7: '.$interface['name']);
                        if (isset($interface['rx-byte']) || isset($interface['tx-byte'])) {
                            $this->line('      RX: '.($interface['rx-byte'] ?? 'N/A'));
                            $this->line('      TX: '.($interface['tx-byte'] ?? 'N/A'));
                        }
                    }
                }
            } catch (\Exception $e) {
                $this->warn('   Interface check failed: '.$e->getMessage());
            }

            $this->line('');

            // 4. Check our database
            $this->info('4. Checking database...');
            $staticIpService = \App\Models\Services\StaticIpService::where('ip_address', $ip)->first();
            if ($staticIpService) {
                $this->info('   ✅ Found static IP service:');
                $this->line('      Customer: '.$staticIpService->customer->name);
                $this->line('      Status: '.$staticIpService->status);
                $this->line('      Device: '.$staticIpService->device->name);

                // Check if there's bandwidth usage data
                $usage = \App\Models\Bandwidth\QueueUsage::where('target_ip', $ip)
                    ->orWhere('target_ip', $ip.'/32')
                    ->latest()
                    ->first();

                if ($usage) {
                    $this->info('   ✅ Found usage data:');
                    $this->line('      Queue: '.$usage->queue_name);
                    $this->line('      Download: '.number_format($usage->download_bytes / 1024 / 1024, 2).' MB');
                    $this->line('      Upload: '.number_format($usage->upload_bytes / 1024 / 1024, 2).' MB');
                } else {
                    $this->warn('   ❌ No usage data in database');
                }
            } else {
                $this->warn('   ❌ No static IP service found in database');
            }

        } catch (\Exception $e) {
            $this->error('Error: '.$e->getMessage());

            return 1;
        }

        return 0;
    }
}
