<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SuspendDisabledCustomerServices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'customers:suspend-disabled-services {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Suspend services for customers with inactive or suspended status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('DRY RUN MODE - No changes will be made');
        }

        // Find customers with inactive or suspended status
        $disabledCustomers = Customer::whereIn('status', ['inactive', 'suspended'])->get();

        $this->info("Found {$disabledCustomers->count()} disabled customers");

        foreach ($disabledCustomers as $customer) {
            $this->info("Processing customer: {$customer->name} (ID: {$customer->id}, Status: {$customer->status})");

            // Find active services for this customer
            $activeStaticIpServices = StaticIpService::where('customer_id', $customer->id)
                ->where('status', 'active')
                ->get();

            $activePppoeServices = PppoeService::where('customer_id', $customer->id)
                ->where('status', 'active')
                ->get();

            if ($activeStaticIpServices->count() > 0) {
                $this->warn("  Found {$activeStaticIpServices->count()} active Static IP services that should be suspended");

                foreach ($activeStaticIpServices as $service) {
                    $this->line("    - Static IP Service {$service->id}: {$service->ip_address}");

                    if (! $dryRun) {
                        try {
                            // Only suspend if service is active
                            if ($service->status === 'active') {
                                // Update MikroTik configuration first
                                $controller = new \App\Http\Controllers\Services\StaticIpServiceController;
                                $reflection = new \ReflectionClass($controller);
                                $method = $reflection->getMethod('suspendStaticIpOnMikrotik');
                                $method->setAccessible(true);
                                $result = $method->invoke($controller, $service);

                                if ($result) {
                                    $this->info('      ✅ Suspended successfully');
                                } else {
                                    $this->error('      ❌ Failed to suspend on MikroTik');
                                }
                            } else {
                                $this->info('      ⚠️  Service already suspended');
                            }
                        } catch (\Exception $e) {
                            $this->error('      ❌ Error: '.$e->getMessage());
                            Log::error('Failed to suspend Static IP service', [
                                'customer_id' => $customer->id,
                                'service_id' => $service->id,
                                'error' => $e->getMessage(),
                            ]);
                        }
                    }
                }
            }

            if ($activePppoeServices->count() > 0) {
                $this->warn("  Found {$activePppoeServices->count()} active PPPoE services that should be suspended");

                foreach ($activePppoeServices as $service) {
                    $this->line("    - PPPoE Service {$service->id}: {$service->username}");

                    if (! $dryRun) {
                        try {
                            // Update service status
                            $service->status = 'suspended';
                            $service->save();

                            // Update MikroTik configuration
                            $controller = new \App\Http\Controllers\Services\PppoeServiceController;
                            $reflection = new \ReflectionClass($controller);
                            $method = $reflection->getMethod('suspendPppoeOnMikrotik');
                            $method->setAccessible(true);
                            $result = $method->invoke($controller, $service);

                            if ($result) {
                                $this->info('      ✅ Suspended successfully');
                            } else {
                                $this->error('      ❌ Failed to suspend on MikroTik');
                            }
                        } catch (\Exception $e) {
                            $this->error('      ❌ Error: '.$e->getMessage());
                            Log::error('Failed to suspend PPPoE service', [
                                'customer_id' => $customer->id,
                                'service_id' => $service->id,
                                'error' => $e->getMessage(),
                            ]);
                        }
                    }
                }
            }

            if ($activeStaticIpServices->count() === 0 && $activePppoeServices->count() === 0) {
                $this->info('  ✅ No active services found - customer properly suspended');
            }

            $this->line('');
        }

        if ($dryRun) {
            $this->info('DRY RUN COMPLETED - Run without --dry-run to make actual changes');
        } else {
            $this->info('SUSPENSION COMPLETED');
        }

        return 0;
    }
}
