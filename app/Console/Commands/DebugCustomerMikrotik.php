<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use Illuminate\Console\Command;

class DebugCustomerMikrotik extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:customer-mikrotik {customer_id : The customer ID to debug}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug MikroTik configuration for a specific customer';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $customerId = $this->argument('customer_id');

        $customer = Customer::find($customerId);
        if (! $customer) {
            $this->error("Customer with ID {$customerId} not found");

            return 1;
        }

        $this->info('=== CUSTOMER DEBUG REPORT ===');
        $this->info("Customer: {$customer->name} (ID: {$customer->id})");
        $this->info("Status: {$customer->status}");
        $this->info("Email: {$customer->email}");
        $this->line('');

        // Get all services for this customer
        $staticIpServices = StaticIpService::where('customer_id', $customer->id)->get();
        $pppoeServices = PppoeService::where('customer_id', $customer->id)->get();

        $this->info('=== SERVICES ===');
        $this->info("Static IP Services: {$staticIpServices->count()}");
        $this->info("PPPoE Services: {$pppoeServices->count()}");
        $this->line('');

        // Debug Static IP services
        foreach ($staticIpServices as $service) {
            $this->info("--- Static IP Service {$service->id} ---");
            $this->info("IP Address: {$service->ip_address}");
            $this->info("Status: {$service->status}");
            $this->info("Device: {$service->device->name} ({$service->device->ip_address})");
            $this->info('MikroTik Route ID: '.($service->mikrotik_route_id ?? 'NULL'));
            $this->info('MikroTik Firewall ID: '.($service->mikrotik_firewall_id ?? 'NULL'));
            $this->info('MikroTik NAT ID: '.($service->mikrotik_nat_id ?? 'NULL'));

            $this->line('');
            $this->info('Checking MikroTik configuration...');

            try {
                $device = $service->device;

                // Check routes
                $this->info('🔍 Checking IP routes...');
                $routes = $device->executeMikrotikCommand('/ip/route/print', [
                    '?dst-address' => $service->ip_address.'/32',
                ]);
                $this->info('Found {'.count($routes).'} routes for this IP:');
                foreach ($routes as $route) {
                    $this->line("  - Route: {$route['dst-address']} via {$route['gateway']} (Comment: ".($route['comment'] ?? 'none').')');
                }

                // Check firewall rules
                $this->info('🔍 Checking firewall rules...');
                $firewallRules = $device->executeMikrotikCommand('/ip/firewall/filter/print', [
                    '?src-address' => $service->ip_address.'/32',
                ]);
                $this->info('Found {'.count($firewallRules).'} firewall rules for this IP:');
                foreach ($firewallRules as $rule) {
                    $this->line("  - Rule: {$rule['chain']} {$rule['src-address']} -> {$rule['action']} (Comment: ".($rule['comment'] ?? 'none').')');
                }

                // Check NAT rules
                $this->info('🔍 Checking NAT rules...');
                $natRules = $device->executeMikrotikCommand('/ip/firewall/nat/print', [
                    '?src-address' => $service->ip_address.'/32',
                ]);
                $this->info('Found {'.count($natRules).'} NAT rules for this IP:');
                foreach ($natRules as $rule) {
                    $this->line("  - NAT: {$rule['chain']} {$rule['src-address']} -> {$rule['action']} (Comment: ".($rule['comment'] ?? 'none').')');
                }

                // Check bandwidth queues
                $this->info('🔍 Checking bandwidth queues...');
                $queues = $device->executeMikrotikCommand('/queue/simple/print', [
                    '?target' => $service->ip_address.'/32',
                ]);
                $this->info('Found {'.count($queues).'} bandwidth queues for this IP:');
                foreach ($queues as $queue) {
                    $disabled = isset($queue['disabled']) && $queue['disabled'] === 'true' ? 'DISABLED' : 'ENABLED';
                    $this->line("  - Queue: {$queue['name']} target {$queue['target']} limit {$queue['max-limit']} ({$disabled}) (Comment: ".($queue['comment'] ?? 'none').')');
                }

            } catch (\Exception $e) {
                $this->error('❌ Error connecting to MikroTik: '.$e->getMessage());
            }

            $this->line('');
        }

        // Debug PPPoE services
        foreach ($pppoeServices as $service) {
            $this->info("--- PPPoE Service {$service->id} ---");
            $this->info("Username: {$service->username}");
            $this->info("Status: {$service->status}");
            $this->info("Device: {$service->device->name} ({$service->device->ip_address})");
            $this->info('MikroTik Secret ID: '.($service->mikrotik_secret_id ?? 'NULL'));

            $this->line('');
            $this->info('Checking MikroTik configuration...');

            try {
                $device = $service->device;

                // Check PPP secrets
                $this->info('🔍 Checking PPP secrets...');
                $secrets = $device->executeMikrotikCommand('/ppp/secret/print', [
                    '?name' => $service->username,
                ]);
                $this->info('Found {'.count($secrets).'} PPP secrets for this username:');
                foreach ($secrets as $secret) {
                    $disabled = isset($secret['disabled']) && $secret['disabled'] === 'true' ? 'DISABLED' : 'ENABLED';
                    $this->line("  - Secret: {$secret['name']} profile {$secret['profile']} ({$disabled}) (Comment: ".($secret['comment'] ?? 'none').')');
                }

                // Check active sessions
                $this->info('🔍 Checking active PPP sessions...');
                $activeSessions = $device->executeMikrotikCommand('/ppp/active/print', [
                    '?name' => $service->username,
                ]);
                $this->info('Found {'.count($activeSessions).'} active sessions for this username:');
                foreach ($activeSessions as $session) {
                    $this->line("  - Session: {$session['name']} address {$session['address']} uptime {$session['uptime']}");
                }

            } catch (\Exception $e) {
                $this->error('❌ Error connecting to MikroTik: '.$e->getMessage());
            }

            $this->line('');
        }

        $this->info('=== RECOMMENDATIONS ===');

        if ($customer->status === 'inactive' || $customer->status === 'suspended') {
            $activeServices = $staticIpServices->where('status', 'active')->count() + $pppoeServices->where('status', 'active')->count();
            if ($activeServices > 0) {
                $this->warn("⚠️  Customer is {$customer->status} but has {$activeServices} active services!");
                $this->warn('   Run: php artisan customers:suspend-disabled-services');
            } else {
                $this->info('✅ Customer status matches service status');
            }
        }

        return 0;
    }
}
