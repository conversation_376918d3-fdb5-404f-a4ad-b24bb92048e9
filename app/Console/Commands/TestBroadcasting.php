<?php

namespace App\Console\Commands;

use App\Events\StaticIpProvisioningStarted;
use App\Events\TestBroadcastEvent;
use App\Models\Services\StaticIpService;
use Illuminate\Console\Command;

class TestBroadcasting extends Command
{
    protected $signature = 'test:broadcasting {sessionId}';
    protected $description = 'Test broadcasting functionality';

    public function handle()
    {
        $sessionId = $this->argument('sessionId');

        // Get any static IP service for testing
        $service = StaticIpService::first();

        if (!$service) {
            $this->error('No static IP service found for testing');
            return 1;
        }

        $this->info("Testing broadcasting with session ID: {$sessionId}");
        $this->info("Using service ID: {$service->id}");

        // Dispatch the simple test event first
        TestBroadcastEvent::dispatch("Hello from Laravel!", $sessionId);
        $this->info('Simple test event dispatched!');

        // Dispatch the provisioning event
        StaticIpProvisioningStarted::dispatch($service, $sessionId);
        $this->info('Provisioning event dispatched!');

        return 0;
    }
}
