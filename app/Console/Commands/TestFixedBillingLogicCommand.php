<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Subscription;
use App\Services\AutomatedBillingService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class TestFixedBillingLogicCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:fixed-billing-logic 
                            {customer? : Customer name to test}
                            {--reset : Reset test customer billing dates}
                            {--generate : Generate invoice for test customer}
                            {--pay : Mark invoice as paid}';

    /**
     * The console command description.
     */
    protected $description = 'Test the fixed billing logic with proper next_billing_date updates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Fixed Billing Logic');
        $this->newLine();

        $customerName = $this->argument('customer') ?? 'abdi';
        $reset = $this->option('reset');
        $generate = $this->option('generate');
        $pay = $this->option('pay');

        $customer = Customer::where('name', 'like', "%{$customerName}%")->first();

        if (! $customer) {
            $this->error("Customer '{$customerName}' not found.");

            return 1;
        }

        $subscription = $customer->subscriptions()->first();
        if (! $subscription) {
            $this->error('No subscription found for this customer.');

            return 1;
        }

        $this->info("Testing with Customer: {$customer->name} (ID: {$customer->id})");
        $this->info("Subscription ID: {$subscription->id}");
        $this->newLine();

        if ($reset) {
            $this->resetTestData($subscription);
        }

        if ($generate) {
            $this->testInvoiceGeneration($subscription);
        }

        if ($pay) {
            $this->testPaymentProcessing($subscription);
        }

        if (! $reset && ! $generate && ! $pay) {
            $this->showCurrentState($customer, $subscription);
            $this->showUsageInstructions();
        }

        return 0;
    }

    /**
     * Reset test data to a clean state.
     */
    protected function resetTestData(Subscription $subscription)
    {
        $this->info('🔄 Resetting test data...');

        // Set next billing date to yesterday to make it eligible for billing
        $yesterday = Carbon::now()->subDay();

        $subscription->update([
            'next_billing_date' => $yesterday,
            'last_billing_date' => null,
        ]);

        // Delete any existing invoices for this subscription
        $deletedCount = Invoice::where('subscription_id', $subscription->id)->delete();

        $this->info('✅ Reset complete:');
        $this->info("   - Set next_billing_date to: {$yesterday->toDateString()}");
        $this->info('   - Cleared last_billing_date');
        $this->info("   - Deleted {$deletedCount} existing invoices");
        $this->newLine();
    }

    /**
     * Test invoice generation with the new logic.
     */
    protected function testInvoiceGeneration(Subscription $subscription)
    {
        $this->info('📄 Testing invoice generation...');

        $billingService = new AutomatedBillingService;

        $this->info('Before invoice generation:');
        $this->showSubscriptionState($subscription);
        $this->newLine();

        // Test 1: Generate first invoice
        $this->info('🔹 Generating first invoice...');
        $invoice1 = $billingService->generateInvoice($subscription);

        if ($invoice1) {
            $this->info("✅ Invoice generated: {$invoice1->invoice_number} - \${$invoice1->total_amount}");
        } else {
            $this->warn('❌ No invoice generated');
        }

        $this->info('After first invoice generation:');
        $this->showSubscriptionState($subscription->fresh());
        $this->newLine();

        // Test 2: Try to generate duplicate invoice
        $this->info('🔹 Attempting to generate duplicate invoice...');
        $invoice2 = $billingService->generateInvoice($subscription->fresh());

        if ($invoice2) {
            $this->error("❌ PROBLEM: Duplicate invoice generated: {$invoice2->invoice_number}");
        } else {
            $this->info('✅ CORRECT: Duplicate invoice prevented');
        }

        $this->newLine();
    }

    /**
     * Test payment processing and next billing date update.
     */
    protected function testPaymentProcessing(Subscription $subscription)
    {
        $this->info('💳 Testing payment processing...');

        $pendingInvoice = Invoice::where('subscription_id', $subscription->id)
            ->where('status', 'pending')
            ->first();

        if (! $pendingInvoice) {
            $this->warn('No pending invoice found. Generate an invoice first with --generate');

            return;
        }

        $this->info('Before payment:');
        $this->showSubscriptionState($subscription);
        $this->info("Invoice status: {$pendingInvoice->status}");
        $this->newLine();

        // Mark invoice as paid
        $this->info('🔹 Marking invoice as paid...');
        $pendingInvoice->update(['status' => 'paid']);

        $this->info('After payment:');
        $this->showSubscriptionState($subscription->fresh());
        $this->info("Invoice status: {$pendingInvoice->fresh()->status}");
        $this->newLine();

        // Test if we can generate a new invoice now
        $this->info('🔹 Testing if new invoice can be generated after payment...');
        $billingService = new AutomatedBillingService;
        $newInvoice = $billingService->generateInvoice($subscription->fresh());

        if ($newInvoice) {
            $this->error('❌ PROBLEM: New invoice generated when next_billing_date is in future');
        } else {
            $this->info('✅ CORRECT: No new invoice generated (next_billing_date is in future)');
        }
    }

    /**
     * Show current state of customer and subscription.
     */
    protected function showCurrentState(Customer $customer, Subscription $subscription)
    {
        $this->info('📊 Current State:');
        $this->showSubscriptionState($subscription);

        $recentInvoices = Invoice::where('subscription_id', $subscription->id)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        $this->info('Recent Invoices:');
        if ($recentInvoices->isEmpty()) {
            $this->info('   No invoices found');
        } else {
            foreach ($recentInvoices as $invoice) {
                $this->info("   {$invoice->invoice_number} - \${$invoice->total_amount} - {$invoice->status} - {$invoice->created_at}");
            }
        }
        $this->newLine();
    }

    /**
     * Show subscription billing state.
     */
    protected function showSubscriptionState(Subscription $subscription)
    {
        $now = Carbon::now();
        $nextBillingDate = Carbon::parse($subscription->next_billing_date);
        $daysFromNow = $nextBillingDate->diffInDays($now, false);

        $eligible = $subscription->status === 'active'
                   && $subscription->auto_billing_enabled
                   && $subscription->next_billing_date <= $now;

        $this->info("   Status: {$subscription->status}");
        $this->info('   Auto Billing: '.($subscription->auto_billing_enabled ? 'Yes' : 'No'));
        $this->info("   Next Billing Date: {$subscription->next_billing_date}");
        $this->info('   Last Billing Date: '.($subscription->last_billing_date ?? 'Never'));
        $this->info('   Days from now: '.($daysFromNow > 0 ? "+{$daysFromNow}" : $daysFromNow));
        $this->info('   Eligible for billing: '.($eligible ? '✅ Yes' : '❌ No'));
    }

    /**
     * Show usage instructions.
     */
    protected function showUsageInstructions()
    {
        $this->info('💡 Usage Instructions:');
        $this->info('1. Reset test data: --reset');
        $this->info('2. Generate invoice: --generate');
        $this->info('3. Mark invoice as paid: --pay');
        $this->info('4. Combine options: --reset --generate --pay');
        $this->newLine();
        $this->info('Example: php artisan test:fixed-billing-logic abdi --reset --generate --pay');
    }
}
