<?php

namespace App\Console\Commands;

use App\Models\Network\NetworkDevice;
use App\Models\Network\NetworkInterface;
use App\Services\IpPoolService;
use Illuminate\Console\Command;

class TestIpPoolCreation extends Command
{
    protected $signature = 'test:ip-pool-creation';

    protected $description = 'Test IP pool creation functionality';

    public function handle()
    {
        $this->info('🧪 Testing IP Pool Creation Functionality');
        $this->line('');

        // Step 1: Test interface syncing
        $this->info('Step 1: Testing interface syncing...');
        $device = NetworkDevice::find(1);

        if (! $device) {
            $this->error('Device with ID 1 not found');

            return 1;
        }

        $this->line("Device: {$device->name} ({$device->ip_address})");

        try {
            $syncResult = NetworkInterface::syncFromDevice($device);

            if ($syncResult['success']) {
                $this->info('✅ Interface sync successful:');
                $this->line("   - Synced: {$syncResult['synced']} interfaces");
                $this->line("   - Created: {$syncResult['created']} new interfaces");
                $this->line("   - Updated: {$syncResult['updated']} existing interfaces");
            } else {
                $this->error("❌ Interface sync failed: {$syncResult['error']}");

                return 1;
            }
        } catch (\Exception $e) {
            $this->error("❌ Interface sync exception: {$e->getMessage()}");

            return 1;
        }

        $this->line('');

        // Step 2: Show available interfaces
        $this->info('Step 2: Available interfaces:');
        $interfaces = NetworkInterface::getUsableForDevice($device);

        if ($interfaces->isEmpty()) {
            $this->warn('No usable interfaces found');
        } else {
            foreach ($interfaces as $interface) {
                $status = $interface->running ? '🟢' : '🔴';
                $this->line("   {$status} {$interface->name} ({$interface->type}) - {$interface->display_name}");
            }
        }

        $this->line('');

        // Step 3: Test IP pool creation (dry run)
        $this->info('Step 3: Testing IP pool creation (simulation)...');

        $firstInterface = $interfaces->first();
        if (! $firstInterface) {
            $this->error('No interface available for testing');

            return 1;
        }

        $testData = [
            'name' => 'test-pool-'.time(),
            'description' => 'Test IP pool created by command',
            'network_address' => '*************',
            'subnet_mask' => '*************',
            'gateway' => '*************',
            'dns_servers' => ['*******', '*******'],
            'device_id' => $device->id,
            'interface' => $firstInterface->name,
        ];

        $this->line('Test pool data:');
        $this->line("   - Name: {$testData['name']}");
        $this->line("   - Network: {$testData['network_address']}/{$testData['subnet_mask']}");
        $this->line("   - Interface: {$testData['interface']}");
        $this->line("   - Gateway: {$testData['gateway']}");

        $this->line('');

        if ($this->confirm('Do you want to create this test IP pool?', false)) {
            try {
                $ipPoolService = new IpPoolService;
                $result = $ipPoolService->createIpPool($testData);

                if ($result['success']) {
                    $this->info('✅ IP pool created successfully!');
                    $this->line("   - Pool ID: {$result['pool']->id}");
                    $this->line("   - Addresses created: {$result['addresses_created']}");
                    $this->line("   - Available addresses: {$result['available_addresses']}");

                    if (! empty($result['interface_sync'])) {
                        $this->line("   - Interface sync: {$result['interface_sync']['synced']} interfaces");
                    }
                } else {
                    $this->error("❌ IP pool creation failed: {$result['error']}");

                    return 1;
                }
            } catch (\Exception $e) {
                $this->error("❌ IP pool creation exception: {$e->getMessage()}");

                return 1;
            }
        } else {
            $this->info('Skipped IP pool creation');
        }

        $this->line('');
        $this->info('🎉 Test completed successfully!');

        return 0;
    }
}
