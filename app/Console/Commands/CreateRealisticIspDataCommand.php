<?php

namespace App\Console\Commands;

use Database\Seeders\RealisticIspDataSeeder;
use Illuminate\Console\Command;

class CreateRealisticIspDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'isp:create-realistic-data
                            {--fresh : Clear existing data before creating new data}
                            {--verify : Verify data after creation}';

    /**
     * The console command description.
     */
    protected $description = 'Create realistic ISP test data simulating a fresh migration from another platform';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Creating Realistic ISP Test Data');
        $this->info('=====================================');
        $this->newLine();

        $fresh = $this->option('fresh');
        $verify = $this->option('verify');

        if ($fresh) {
            $this->warn('⚠️  This will clear ALL existing data!');
            if (! $this->confirm('Are you sure you want to continue?')) {
                $this->info('Operation cancelled.');

                return 0;
            }
        }

        // Run the seeder
        $this->info('Running RealisticIspDataSeeder...');
        $this->newLine();

        $seeder = new RealisticIspDataSeeder;
        $seeder->setCommand($this);

        // Set the fresh option for the seeder
        if ($fresh) {
            $this->option('fresh', true);
        }

        $seeder->run();

        if ($verify) {
            $this->newLine();
            $this->verifyData();
        }

        $this->newLine();
        $this->info('🎉 Realistic ISP test data creation completed!');

        return 0;
    }

    /**
     * Verify the created data.
     */
    protected function verifyData(): void
    {
        $this->info('🔍 Verifying created data...');
        $this->newLine();

        // Check data integrity
        $issues = [];

        // Check if all customers have subscriptions
        $customersWithoutSubscriptions = \App\Models\Customer::doesntHave('subscriptions')->count();
        if ($customersWithoutSubscriptions > 0) {
            $issues[] = "{$customersWithoutSubscriptions} customers without subscriptions";
        }

        // Check if all subscriptions have services
        $subscriptionsWithoutServices = \App\Models\Subscription::whereDoesntHave('staticIpService')
            ->whereDoesntHave('pppoeService')
            ->count();
        if ($subscriptionsWithoutServices > 0) {
            $issues[] = "{$subscriptionsWithoutServices} subscriptions without services";
        }

        // Check service distribution
        $staticIpCount = \App\Models\Services\StaticIpService::count();
        $pppoeCount = \App\Models\Services\PppoeService::count();
        $totalServices = $staticIpCount + $pppoeCount;

        if ($totalServices > 0) {
            $staticIpPercentage = round(($staticIpCount / $totalServices) * 100, 1);
            $pppoePercentage = round(($pppoeCount / $totalServices) * 100, 1);

            $this->info('Service Distribution:');
            $this->info("  Static IP: {$staticIpCount} ({$staticIpPercentage}%)");
            $this->info("  PPPoE: {$pppoeCount} ({$pppoePercentage}%)");

            // Check if distribution is roughly correct (40% Static IP, 60% PPPoE)
            if ($staticIpPercentage < 35 || $staticIpPercentage > 45) {
                $issues[] = "Static IP percentage ({$staticIpPercentage}%) is not close to target 40%";
            }
        }

        // Check IP address uniqueness
        $duplicateIps = \App\Models\Services\StaticIpService::select('ip_address')
            ->groupBy('ip_address')
            ->havingRaw('COUNT(*) > 1')
            ->count();
        if ($duplicateIps > 0) {
            $issues[] = "{$duplicateIps} duplicate IP addresses found";
        }

        // Check billing eligibility
        $eligibleForBilling = \App\Models\Subscription::where('status', 'active')
            ->where('auto_billing_enabled', true)
            ->where('next_billing_date', '<=', now())
            ->count();

        $this->info('Billing Status:');
        $this->info("  Subscriptions eligible for billing: {$eligibleForBilling}");

        // Report issues
        if (empty($issues)) {
            $this->info('✅ All data integrity checks passed!');
        } else {
            $this->warn('⚠️  Data integrity issues found:');
            foreach ($issues as $issue) {
                $this->error("  - {$issue}");
            }
        }

        $this->newLine();
    }
}
