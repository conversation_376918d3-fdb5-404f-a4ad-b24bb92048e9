<?php

namespace App\Console\Commands;

use Database\Seeders\TestSubscriptionSeeder;
use Illuminate\Console\Command;

class GenerateTestSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:generate-subscriptions
                            {--clear : Clear existing test data before generating new data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate 1000 test customers with subscriptions for testing automated invoice generation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Generating test data for invoice generation testing...');
        $this->newLine();

        // Confirm before proceeding if clearing data
        if ($this->option('clear')) {
            if (! $this->confirm('This will clear all existing customers and subscriptions. Are you sure?')) {
                $this->info('Operation cancelled.');

                return 0;
            }
        }

        // Run the seeder
        $seeder = new TestSubscriptionSeeder;
        $seeder->setCommand($this);
        $seeder->run();

        $this->newLine();
        $this->info('✅ Test data generation completed successfully!');
        $this->info('');
        $this->info('Next steps:');
        $this->info('1. Verify the data: Check customers and subscriptions tables');
        $this->info('2. Test invoice generation: php artisan invoices:generate-monthly');
        $this->info('3. Check generated invoices in the invoices table');

        return 0;
    }
}
