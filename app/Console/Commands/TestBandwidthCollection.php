<?php

namespace App\Console\Commands;

use App\Jobs\CollectDeviceBandwidthUsage;
use App\Models\Network\NetworkDevice;
use Illuminate\Console\Command;

class TestBandwidthCollection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bandwidth:test-collection {device-id? : Test specific device ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test bandwidth collection from MikroTik devices';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $deviceId = $this->argument('device-id');

        $this->info('🧪 Testing Bandwidth Collection');
        $this->line('');

        // Get device(s) to test
        if ($deviceId) {
            $device = NetworkDevice::find($deviceId);
            if (! $device) {
                $this->error("Device with ID {$deviceId} not found");

                return 1;
            }
            $devices = collect([$device]);
        } else {
            $devices = NetworkDevice::where('type', 'mikrotik')
                ->where('status', 'active')
                ->limit(3) // Test only first 3 devices
                ->get();
        }

        if ($devices->isEmpty()) {
            $this->warn('No MikroTik devices found to test');

            return 1;
        }

        $this->info("Testing {$devices->count()} device(s):");
        $this->line('');

        foreach ($devices as $device) {
            $this->testDevice($device);
            $this->line('');
        }

        return 0;
    }

    /**
     * Test bandwidth collection for a specific device.
     */
    protected function testDevice(NetworkDevice $device): void
    {
        $this->info("📡 Testing: {$device->name} ({$device->ip_address})");

        // Test basic connectivity
        $this->line('🔌 Testing connectivity...');

        // Test SNMP
        if ($device->testSnmpConnection()) {
            $this->info('  ✅ SNMP connection successful');

            // Get SNMP system info
            $sysInfo = $device->getSnmpSystemInfo();
            if (! empty($sysInfo)) {
                $this->line('  📋 System: '.($sysInfo['sysDescr'] ?? 'Unknown'));
                $this->line('  🏷️  Name: '.($sysInfo['sysName'] ?? 'Unknown'));
            }
        } else {
            $this->warn('  ⚠️  SNMP connection failed');
        }

        // Test API
        $this->line('🔗 Testing API connection...');
        try {
            $apiResult = $device->testConnection();
            $this->line('  📡 API call completed, checking result...');

            if ($apiResult !== null && isset($apiResult['status']) && $apiResult['status'] === 'success') {
                $this->info('  ✅ API connection successful');

                // Show device info if available
                if (isset($apiResult['system_capabilities']['software']['Identity'])) {
                    $this->line('  🏷️  Identity: '.$apiResult['system_capabilities']['software']['Identity']);
                }
                if (isset($apiResult['system_capabilities']['software']['Version'])) {
                    $this->line('  📋 Version: '.$apiResult['system_capabilities']['software']['Version']);
                }
            } else {
                $this->warn('  ⚠️  API connection failed');
                if (is_array($apiResult) && isset($apiResult['message'])) {
                    $this->line('  📝 Error: '.$apiResult['message']);
                } else {
                    $this->line('  📝 Result: '.json_encode($apiResult));
                }
            }
        } catch (\Exception $e) {
            $this->error('  ❌ API connection exception: '.$e->getMessage());
            $this->line('  📍 File: '.$e->getFile().':'.$e->getLine());
        }

        // Test SNMP interface collection
        $this->line('📊 Testing SNMP interface collection...');
        try {
            // Check if SNMP extension is available
            if (! function_exists('snmp2_walk')) {
                $this->warn('  ⚠️  SNMP extension not available - skipping SNMP tests');
                $this->line('  💡 Install with: brew install php-snmp (macOS) or apt install php-snmp (Ubuntu)');
            } else {
                $community = $device->getConfigValue('snmp_community', 'public');

                // Test basic SNMP walk
                $ifNames = snmp2_walk($device->ip_address, $community, '*******.*******.1.2');

                if ($ifNames && is_array($ifNames)) {
                    $this->info('  ✅ Found '.count($ifNames).' interfaces via SNMP');

                    // Show first few interfaces
                    $count = 0;
                    foreach ($ifNames as $oid => $name) {
                        if ($count >= 3) {
                            break;
                        }
                        $cleanName = trim($name, '"');
                        $this->line("    - {$cleanName}");
                        $count++;
                    }
                    if (count($ifNames) > 3) {
                        $this->line('    ... and '.(count($ifNames) - 3).' more');
                    }
                } else {
                    $this->warn('  ⚠️  No interfaces found via SNMP');
                }
            }
        } catch (\Exception $e) {
            $this->error('  ❌ SNMP interface collection failed: '.$e->getMessage());
        }

        // Test customer queue collection
        $this->line('👥 Testing customer queue collection...');
        try {
            // Get queue count
            $queues = $device->executeMikrotikCommand('/queue/simple/print');
            $this->info('  ✅ Found '.count($queues).' queues via API');

            // Get active PPPoE sessions
            $sessions = $device->executeMikrotikCommand('/ppp/active/print');
            $this->info('  ✅ Found '.count($sessions).' active PPPoE sessions');

            // Count services in database
            $staticIpCount = $device->staticIpServices()->where('status', 'active')->count();
            $pppoeCount = $device->pppoeServices()->where('status', 'active')->count();
            $this->info("  📋 Database: {$staticIpCount} Static IP + {$pppoeCount} PPPoE services");

        } catch (\Exception $e) {
            $this->error('  ❌ Customer queue collection failed: '.$e->getMessage());
        }

        // Test full collection job
        $this->line('🚀 Testing full collection job...');
        try {
            $job = new CollectDeviceBandwidthUsage($device, true); // Dry run
            $result = $job->handle();

            if ($result['success']) {
                $this->info('  ✅ Collection successful:');
                $this->line("    📊 Interfaces processed: {$result['interfaces_processed']}");
                $this->line("    👥 Customers processed: {$result['customers_processed']}");
            } else {
                $this->error("  ❌ Collection failed: {$result['error']}");
            }
        } catch (\Exception $e) {
            $this->error('  ❌ Collection job failed: '.$e->getMessage());
        }

        // Configuration check
        $this->line('⚙️  Configuration check...');
        $config = [
            'snmp_community' => $device->getConfigValue('snmp_community', 'public'),
            'snmp_version' => $device->getConfigValue('snmp_version', '2c'),
            'api_username' => $device->getConfigValue('api_username', 'billing'),
            'api_port' => $device->getConfigValue('api_port', 8411),
        ];

        foreach ($config as $key => $value) {
            $this->line("  {$key}: {$value}");
        }
    }
}
