<?php

namespace App\Console\Commands;

use App\Jobs\MikroTik\ProvisionStaticIpServiceJob;
use App\Models\Services\StaticIpService;
use Illuminate\Console\Command;

class TestStaticIpProvisioning extends Command
{
    protected $signature = 'test:static-ip-provisioning {serviceId}';
    protected $description = 'Test static IP provisioning with a specific service';

    public function handle()
    {
        $serviceId = $this->argument('serviceId');
        
        $service = StaticIpService::find($serviceId);
        
        if (!$service) {
            $this->error("Service with ID {$serviceId} not found");
            return 1;
        }
        
        $sessionId = \Illuminate\Support\Str::random(32);
        
        $this->info("Testing static IP provisioning for service {$serviceId}");
        $this->info("Session ID: {$sessionId}");
        $this->info("Customer: {$service->customer->name}");
        $this->info("IP Address: {$service->ip_address}");
        
        try {
            // Test synchronous dispatch
            $this->info("Dispatching job synchronously...");
            ProvisionStaticIpServiceJob::dispatchSync($service, $sessionId);
            $this->info("Job completed successfully!");
        } catch (\Exception $e) {
            $this->error("Job failed: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
