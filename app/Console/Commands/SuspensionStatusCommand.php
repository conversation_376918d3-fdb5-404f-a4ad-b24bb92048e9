<?php

namespace App\Console\Commands;

use App\Models\Invoice;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SuspensionStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'customers:suspension-status 
                            {--days=30 : Number of days to look back for suspension activity}
                            {--overdue-only : Show only customers with overdue invoices}
                            {--suspended-only : Show only currently suspended customers}';

    /**
     * The console command description.
     */
    protected $description = 'Display customer suspension status and overdue invoice summary';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $overdueOnly = $this->option('overdue-only');
        $suspendedOnly = $this->option('suspended-only');

        $this->info('📊 Customer Suspension Status Report');
        $this->info('===================================');
        $this->newLine();

        // Overall statistics
        $this->displayOverallStats($days);
        $this->newLine();

        // Overdue invoices summary
        if (! $suspendedOnly) {
            $this->displayOverdueInvoices();
            $this->newLine();
        }

        // Suspended customers summary
        if (! $overdueOnly) {
            $this->displaySuspendedCustomers($days);
            $this->newLine();
        }

        // Suspension activity summary
        $this->displaySuspensionActivity($days);
    }

    /**
     * Display overall statistics.
     */
    protected function displayOverallStats(int $days): void
    {
        $totalCustomers = DB::table('customers')->where('status', 'active')->count();
        $totalSubscriptions = DB::table('subscriptions')->where('status', 'active')->count();
        $suspendedSubscriptions = DB::table('subscriptions')->where('status', 'suspended')->count();

        $overdueInvoices = Invoice::where('status', 'pending')
            ->where('due_date', '<=', now())
            ->count();

        $recentSuspensions = DB::table('subscriptions')
            ->where('status', 'suspended')
            ->where('suspension_date', '>=', now()->subDays($days))
            ->count();

        $this->info("📈 Overall Statistics (Last {$days} days):");
        $this->table(
            ['Metric', 'Count', 'Percentage'],
            [
                ['Total Active Customers', $totalCustomers, '100%'],
                ['Total Active Subscriptions', $totalSubscriptions, '100%'],
                ['Currently Suspended', $suspendedSubscriptions, $totalSubscriptions > 0 ? round(($suspendedSubscriptions / $totalSubscriptions) * 100, 1).'%' : '0%'],
                ['Overdue Invoices', $overdueInvoices, ''],
                ['Recent Suspensions', $recentSuspensions, ''],
            ]
        );
    }

    /**
     * Display overdue invoices.
     */
    protected function displayOverdueInvoices(): void
    {
        $overdueInvoices = Invoice::with(['customer', 'subscription'])
            ->where('status', 'pending')
            ->where('due_date', '<=', now())
            ->orderBy('due_date', 'asc')
            ->get();

        if ($overdueInvoices->isEmpty()) {
            $this->info('✅ No overdue invoices found!');

            return;
        }

        $this->warn("⚠️  Overdue Invoices ({$overdueInvoices->count()} total):");

        $tableData = [];
        foreach ($overdueInvoices->take(10) as $invoice) {
            $daysOverdue = now()->diffInDays(Carbon::parse($invoice->due_date));
            $subscriptionStatus = $invoice->subscription ? $invoice->subscription->status : 'N/A';

            $tableData[] = [
                $invoice->customer->name ?? 'Unknown',
                $invoice->invoice_number,
                '$'.number_format($invoice->total_amount, 2),
                $invoice->due_date,
                $daysOverdue.' days',
                $subscriptionStatus,
            ];
        }

        $this->table(
            ['Customer', 'Invoice', 'Amount', 'Due Date', 'Days Overdue', 'Status'],
            $tableData
        );

        if ($overdueInvoices->count() > 10) {
            $this->info('... and '.($overdueInvoices->count() - 10).' more overdue invoices');
        }
    }

    /**
     * Display suspended customers.
     */
    protected function displaySuspendedCustomers(int $days): void
    {
        $suspendedSubscriptions = Subscription::with(['customer', 'staticIpService', 'pppoeService'])
            ->where('status', 'suspended')
            ->where('suspension_date', '>=', now()->subDays($days))
            ->orderBy('suspension_date', 'desc')
            ->get();

        if ($suspendedSubscriptions->isEmpty()) {
            $this->info("✅ No customers suspended in the last {$days} days");

            return;
        }

        $this->warn("🚫 Recently Suspended Customers ({$suspendedSubscriptions->count()} total):");

        $tableData = [];
        foreach ($suspendedSubscriptions->take(10) as $subscription) {
            $serviceType = $subscription->staticIpService ? 'Static IP' : ($subscription->pppoeService ? 'PPPoE' : 'Unknown');
            $suspensionDays = $subscription->suspension_date ? now()->diffInDays(Carbon::parse($subscription->suspension_date)) : 0;

            $tableData[] = [
                $subscription->customer->name ?? 'Unknown',
                $serviceType,
                $subscription->suspension_date ? Carbon::parse($subscription->suspension_date)->format('Y-m-d H:i') : 'N/A',
                $suspensionDays.' days ago',
                $subscription->suspension_days ?? 0,
            ];
        }

        $this->table(
            ['Customer', 'Service Type', 'Suspended At', 'Time Ago', 'Total Suspension Days'],
            $tableData
        );

        if ($suspendedSubscriptions->count() > 10) {
            $this->info('... and '.($suspendedSubscriptions->count() - 10).' more suspended customers');
        }
    }

    /**
     * Display suspension activity summary.
     */
    protected function displaySuspensionActivity(int $days): void
    {
        $this->info("📅 Suspension Activity Summary (Last {$days} days):");

        // Group suspensions by date
        $suspensionsByDate = DB::table('subscriptions')
            ->select(DB::raw('DATE(suspension_date) as date'), DB::raw('COUNT(*) as count'))
            ->where('status', 'suspended')
            ->where('suspension_date', '>=', now()->subDays($days))
            ->groupBy(DB::raw('DATE(suspension_date)'))
            ->orderBy('date', 'desc')
            ->get();

        if ($suspensionsByDate->isEmpty()) {
            $this->info("No suspension activity in the last {$days} days");

            return;
        }

        $tableData = [];
        foreach ($suspensionsByDate as $activity) {
            $tableData[] = [
                $activity->date,
                $activity->count,
                str_repeat('█', min($activity->count, 20)), // Simple bar chart
            ];
        }

        $this->table(
            ['Date', 'Suspensions', 'Activity'],
            $tableData
        );

        // Summary stats
        $totalSuspensions = $suspensionsByDate->sum('count');
        $avgPerDay = $suspensionsByDate->count() > 0 ? round($totalSuspensions / $suspensionsByDate->count(), 1) : 0;
        $peakDay = $suspensionsByDate->sortByDesc('count')->first();

        $this->newLine();
        $this->info('📊 Activity Summary:');
        $this->info("  • Total suspensions: {$totalSuspensions}");
        $this->info("  • Average per day: {$avgPerDay}");
        if ($peakDay) {
            $this->info("  • Peak day: {$peakDay->date} ({$peakDay->count} suspensions)");
        }
    }
}
