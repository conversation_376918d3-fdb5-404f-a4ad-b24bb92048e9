<?php

namespace App\Console\Commands;

use App\Jobs\MikroTik\ReactivateStaticIpServiceJob;
use App\Jobs\MikroTik\SuspendStaticIpServiceJob;
use App\Models\Services\StaticIpService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Queue;

class TestQueuedMikrotikOperations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:queued-mikrotik {operation=suspend} {service_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test queued MikroTik operations (suspend/reactivate)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $operation = $this->argument('operation');
        $serviceId = $this->argument('service_id');

        $this->info('=== TESTING QUEUED MIKROTIK OPERATIONS ===');

        if ($serviceId) {
            $service = StaticIpService::find($serviceId);
        } else {
            // Find a suitable service based on operation
            if ($operation === 'suspend') {
                $service = StaticIpService::where('status', 'active')->first();
            } else {
                $service = StaticIpService::where('status', 'suspended')
                    ->whereNotNull('mikrotik_id')
                    ->first();
            }
        }

        if (! $service) {
            $this->error("No suitable service found for operation: {$operation}");

            return 1;
        }

        $this->info("Found service: ID {$service->id}");
        $this->info("Customer: {$service->customer->name}");
        $this->info("IP Address: {$service->ip_address}");
        $this->info("Current Status: {$service->status}");
        $this->info("Device: {$service->device->name}");

        if ($operation === 'suspend') {
            return $this->testSuspension($service);
        } elseif ($operation === 'reactivate') {
            return $this->testReactivation($service);
        } else {
            $this->error("Invalid operation: {$operation}. Use 'suspend' or 'reactivate'");

            return 1;
        }
    }

    /**
     * Test service suspension via queue.
     */
    protected function testSuspension(StaticIpService $service): int
    {
        if ($service->status !== 'active') {
            $this->error("Service is not active (status: {$service->status})");

            return 1;
        }

        $this->newLine();
        $this->info('🔄 Dispatching suspension job...');

        // Dispatch the suspension job
        SuspendStaticIpServiceJob::dispatch($service);

        $this->info('✅ Suspension job dispatched successfully!');
        $this->info("Queue: mikrotik-device-{$service->device_id}");

        $this->newLine();
        $this->info('💡 To process the job, run:');
        $this->line("php artisan queue:work --queue=mikrotik-device-{$service->device_id}");

        $this->newLine();
        $this->info('📊 Current queue status:');
        $this->showQueueStatus();

        return 0;
    }

    /**
     * Test service reactivation via queue.
     */
    protected function testReactivation(StaticIpService $service): int
    {
        if ($service->status !== 'suspended') {
            $this->error("Service is not suspended (status: {$service->status})");

            return 1;
        }

        if (! $service->mikrotik_id) {
            $this->error('Service has no MikroTik ID stored - cannot reactivate');

            return 1;
        }

        $this->newLine();
        $this->info('🔄 Dispatching reactivation job...');

        // Dispatch the reactivation job
        ReactivateStaticIpServiceJob::dispatch($service);

        $this->info('✅ Reactivation job dispatched successfully!');
        $this->info("Queue: mikrotik-device-{$service->device_id}");

        $this->newLine();
        $this->info('💡 To process the job, run:');
        $this->line("php artisan queue:work --queue=mikrotik-device-{$service->device_id}");

        $this->newLine();
        $this->info('📊 Current queue status:');
        $this->showQueueStatus();

        return 0;
    }

    /**
     * Show current queue status.
     */
    protected function showQueueStatus(): void
    {
        try {
            // Get queue size for each device
            $devices = \App\Models\Network\NetworkDevice::all();

            foreach ($devices as $device) {
                $queueName = "mikrotik-device-{$device->id}";
                $size = Queue::size($queueName);

                if ($size > 0) {
                    $this->line("  Queue '{$queueName}': {$size} jobs pending");
                }
            }

            // Show default queue
            $defaultSize = Queue::size('default');
            if ($defaultSize > 0) {
                $this->line("  Queue 'default': {$defaultSize} jobs pending");
            }

        } catch (\Exception $e) {
            $this->warn("Could not retrieve queue status: {$e->getMessage()}");
        }
    }
}
