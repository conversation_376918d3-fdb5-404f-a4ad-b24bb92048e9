<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\Subscription;
use App\Services\AutomatedBillingService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class TestAutomatedBillingCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'billing:test-automation 
                            {--create-test-data : Create test subscription data}
                            {--subscription-id= : Test specific subscription ID}
                            {--cleanup : Remove test data after testing}';

    /**
     * The console command description.
     */
    protected $description = 'Test the automated billing system with sample data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Automated Billing System');
        $this->newLine();

        if ($this->option('create-test-data')) {
            $this->createTestData();
        }

        if ($this->option('subscription-id')) {
            $this->testSpecificSubscription((int) $this->option('subscription-id'));
        } else {
            $this->testBillingSystem();
        }

        if ($this->option('cleanup')) {
            $this->cleanupTestData();
        }
    }

    /**
     * Create test subscription data.
     */
    protected function createTestData()
    {
        $this->info('Creating test subscription data...');

        // Create test customer if not exists
        $customer = Customer::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Billing Customer',
                'phone' => '+1234567890',
                'address' => '123 Test Street',
                'city' => 'Test City',
                'state' => 'Test State',
                'zip_code' => '12345',
                'status' => 'active',
            ]
        );

        // Create test subscription
        $subscription = Subscription::create([
            'customer_id' => $customer->id,
            'name' => 'Test Monthly Internet Service',
            'description' => 'Test subscription for automated billing',
            'price' => 49.99,
            'billing_cycle' => 'monthly',
            'status' => 'active',
            'start_date' => now()->subDays(35), // Started 35 days ago
            'next_billing_date' => now()->subDays(5), // Due 5 days ago
            'auto_billing_enabled' => true,
            'grace_period_days' => 7,
            'suspension_days' => 0,
        ]);

        $this->info("Created test subscription ID: {$subscription->id}");
        $this->info("Customer: {$customer->name} ({$customer->email})");
        $this->newLine();
    }

    /**
     * Test specific subscription.
     */
    protected function testSpecificSubscription(int $subscriptionId)
    {
        $subscription = Subscription::with('customer')->find($subscriptionId);

        if (! $subscription) {
            $this->error("Subscription not found: {$subscriptionId}");

            return;
        }

        $this->info("Testing subscription ID: {$subscriptionId}");
        $this->info("Customer: {$subscription->customer->name}");
        $this->info("Price: \${$subscription->price}");
        $this->info("Next billing date: {$subscription->next_billing_date}");
        $this->newLine();

        $this->testBillingForSubscription($subscription);
    }

    /**
     * Test the billing system.
     */
    protected function testBillingSystem()
    {
        $billingService = new AutomatedBillingService;

        // Find subscriptions that need billing
        $subscriptions = Subscription::with('customer')
            ->where('status', 'active')
            ->where('auto_billing_enabled', true)
            ->where('next_billing_date', '<=', now())
            ->limit(5)
            ->get();

        if ($subscriptions->isEmpty()) {
            $this->warn('No subscriptions found that need billing.');
            $this->info('Use --create-test-data to create test subscription data.');

            return;
        }

        $this->info("Found {$subscriptions->count()} subscriptions that need billing:");
        $this->newLine();

        foreach ($subscriptions as $subscription) {
            $this->testBillingForSubscription($subscription);
        }
    }

    /**
     * Test billing for a specific subscription.
     */
    protected function testBillingForSubscription(Subscription $subscription)
    {
        $billingService = new AutomatedBillingService;

        $this->info("Testing billing for subscription: {$subscription->name}");
        $this->info("Customer: {$subscription->customer->name}");
        $this->info("Price: \${$subscription->price}");
        $this->info("Suspension days: {$subscription->suspension_days}");

        try {
            // Test suspension tracking
            if ($subscription->suspension_days > 0) {
                $this->info("Subscription has {$subscription->suspension_days} suspension days - testing pro-rated billing");
            }

            // Generate invoice
            $invoice = $billingService->generateInvoice($subscription);

            if ($invoice) {
                $this->info('✅ Invoice generated successfully!');
                $this->info("Invoice ID: {$invoice->id}");
                $this->info("Invoice Number: {$invoice->invoice_number}");
                $this->info("Amount: \${$invoice->total_amount}");
                $this->info('Pro-rated: '.($invoice->is_prorated ? 'Yes' : 'No'));

                if ($invoice->is_prorated) {
                    $this->info("Active days: {$invoice->active_days}/{$invoice->total_days}");
                }

                $this->info("Billing period: {$invoice->billing_period_start} to {$invoice->billing_period_end}");
                $this->info("Next billing date: {$subscription->fresh()->next_billing_date}");
            } else {
                $this->warn('No invoice generated (likely no billable amount)');
            }

        } catch (\Exception $e) {
            $this->error('❌ Failed to generate invoice: '.$e->getMessage());
        }

        $this->newLine();
    }

    /**
     * Test suspension and reactivation tracking.
     */
    protected function testSuspensionTracking()
    {
        $this->info('Testing suspension and reactivation tracking...');

        $subscription = Subscription::where('status', 'active')->first();

        if (! $subscription) {
            $this->warn('No active subscription found for testing');

            return;
        }

        $billingService = new AutomatedBillingService;

        // Test suspension
        $suspensionDate = now()->subDays(10);
        $billingService->recordSuspension($subscription, $suspensionDate);
        $this->info("✅ Recorded suspension on {$suspensionDate->toDateString()}");

        // Test reactivation
        $reactivationDate = now()->subDays(3);
        $billingService->recordReactivation($subscription, $reactivationDate);
        $this->info("✅ Recorded reactivation on {$reactivationDate->toDateString()}");

        $subscription->refresh();
        $this->info("Suspension days accumulated: {$subscription->suspension_days}");
        $this->info("Next billing date extended to: {$subscription->next_billing_date}");
    }

    /**
     * Clean up test data.
     */
    protected function cleanupTestData()
    {
        $this->info('Cleaning up test data...');

        // Find test customer
        $customer = Customer::where('email', '<EMAIL>')->first();

        if ($customer) {
            // Delete related invoices and subscriptions
            DB::table('subscription_billing_history')
                ->whereIn('subscription_id', $customer->subscriptions->pluck('id'))
                ->delete();

            foreach ($customer->subscriptions as $subscription) {
                $subscription->invoices()->delete();
            }

            $customer->subscriptions()->delete();
            $customer->delete();

            $this->info('✅ Test data cleaned up');
        } else {
            $this->info('No test data found to clean up');
        }
    }
}
