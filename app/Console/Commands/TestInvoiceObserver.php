<?php

namespace App\Console\Commands;

use App\Models\Invoice;
use App\Models\Services\StaticIpService;
use Illuminate\Console\Command;

class TestInvoiceObserver extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:invoice-observer {invoice_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the Invoice Observer automatic service reactivation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== TESTING INVOICE OBSERVER - AUTOMATIC SERVICE REACTIVATION ===');

        $invoiceId = $this->argument('invoice_id');

        if ($invoiceId) {
            $invoice = Invoice::find($invoiceId);
        } else {
            // Find a pending invoice with suspended services
            $invoice = Invoice::where('status', 'pending')
                ->whereHas('customer.staticIpServices', function ($query) {
                    $query->where('status', 'suspended')->whereNotNull('mikrotik_id');
                })
                ->first();
        }

        if (! $invoice) {
            $this->error('No suitable pending invoice found with suspended services');
            $this->info('Looking for any pending invoices...');

            $pendingInvoices = Invoice::where('status', 'pending')->with('customer')->get();
            if ($pendingInvoices->isEmpty()) {
                $this->error('No pending invoices found at all');

                return 1;
            }

            $this->info('Found pending invoices:');
            foreach ($pendingInvoices as $inv) {
                $this->line("  Invoice {$inv->invoice_number} - Customer: {$inv->customer->name} - Amount: \${$inv->total_amount}");
            }

            return 1;
        }

        $this->info("Found invoice: {$invoice->invoice_number}");
        $this->info("Customer: {$invoice->customer->name}");
        $this->info("Amount: \${$invoice->total_amount}");
        $this->info("Status: {$invoice->status}");

        // Check suspended services for this customer
        $suspendedServices = StaticIpService::where('customer_id', $invoice->customer_id)
            ->where('status', 'suspended')
            ->whereNotNull('mikrotik_id')
            ->get();

        if ($suspendedServices->isEmpty()) {
            $this->error('No suspended Static IP services found for this customer');

            return 1;
        }

        $this->info("Found {$suspendedServices->count()} suspended service(s):");
        foreach ($suspendedServices as $service) {
            $this->line("  Service ID {$service->id}: {$service->ip_address} (MikroTik ID: {$service->mikrotik_id})");
        }

        $this->newLine();
        $this->info('🔄 Marking invoice as paid (this should trigger the observer)...');

        // Mark invoice as paid - this should trigger the observer
        $invoice->markAsPaid();

        $this->info('✅ Invoice marked as paid!');
        $this->newLine();

        // Check service status after payment
        $this->info('Checking service status after payment...');
        $reactivatedCount = 0;
        $failedCount = 0;

        foreach ($suspendedServices as $originalService) {
            $service = StaticIpService::find($originalService->id);
            $this->line("Service ID {$service->id}:");
            $this->line("  Status: {$originalService->status} → {$service->status}");
            $this->line("  MikroTik ID: {$originalService->mikrotik_id} → ".($service->mikrotik_id ?: 'None (cleared)'));

            if ($service->status === 'active' && ! $service->mikrotik_id) {
                $this->line('  ✅ Successfully reactivated!');
                $reactivatedCount++;
            } else {
                $this->line('  ❌ Not reactivated');
                $failedCount++;
            }
            $this->newLine();
        }

        // Summary
        $this->info('=== SUMMARY ===');
        $this->info("Total services: {$suspendedServices->count()}");
        $this->info("Reactivated: {$reactivatedCount}");
        $this->info("Failed: {$failedCount}");

        if ($reactivatedCount > 0) {
            $this->info('🎉 Invoice Observer is working correctly!');

            return 0;
        } else {
            $this->error('❌ Invoice Observer did not reactivate any services');

            return 1;
        }
    }
}
