<?php

namespace App\Console\Commands;

use App\Models\Services\StaticIpService;
use Illuminate\Console\Command;

class FixBandwidthUnits extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'bandwidth:fix-units {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     */
    protected $description = 'Fix bandwidth unit conversion issue in MikroTik queues (convert from Kbps to Mbps format)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('DRY RUN MODE - No changes will be made');
        }

        $this->info('Scanning for Static IP services with bandwidth plans...');

        $services = StaticIpService::with(['bandwidthPlan', 'device'])
            ->whereHas('bandwidthPlan')
            ->where('status', 'active')
            ->get();

        if ($services->isEmpty()) {
            $this->info('No active Static IP services with bandwidth plans found.');

            return 0;
        }

        $this->info("Found {$services->count()} services to check.");

        $fixed = 0;
        $errors = 0;

        foreach ($services as $service) {
            $plan = $service->bandwidthPlan;
            $device = $service->device;

            if (! $device || ! $plan) {
                continue;
            }

            $queueName = "customer-{$service->customer_id}-{$service->id}";

            // Current incorrect format
            $oldFormat = "{$plan->download_speed_kbps}k/{$plan->upload_speed_kbps}k";

            // New correct format
            $newFormat = "{$plan->download_speed}M/{$plan->upload_speed}M";

            $this->line("Service ID {$service->id} - Customer: {$service->customer->name}");
            $this->line("  Queue: {$queueName}");
            $this->line("  Old format: {$oldFormat} (= {$plan->download_speed_kbps} Kbps)");
            $this->line("  New format: {$newFormat} (= {$plan->download_speed} Mbps)");

            if (! $dryRun) {
                try {
                    // Find the queue on MikroTik
                    $queues = $device->executeMikrotikCommand('/queue/simple/print', [
                        '?name' => $queueName,
                    ]);

                    if (! empty($queues)) {
                        $queueId = $queues[0]['.id'];

                        // Update the queue with correct bandwidth format
                        $device->executeMikrotikCommand('/queue/simple/set', [
                            '.id' => $queueId,
                            'max-limit' => $newFormat,
                            'comment' => "Bandwidth limit for customer {$service->customer->name} (Fixed units)",
                        ]);

                        $this->info('  ✅ Fixed queue bandwidth format');
                        $fixed++;
                    } else {
                        $this->warn('  ⚠️  Queue not found on MikroTik device');
                    }
                } catch (\Exception $e) {
                    $this->error("  ❌ Failed to update queue: {$e->getMessage()}");
                    $errors++;
                }
            } else {
                $this->info('  📋 Would update queue bandwidth format');
            }

            $this->line('');
        }

        if ($dryRun) {
            $this->info("DRY RUN COMPLETE - {$services->count()} services would be processed");
        } else {
            $this->info("COMPLETED - Fixed: {$fixed}, Errors: {$errors}");
        }

        return 0;
    }
}
