<?php

namespace App\Console\Commands;

use App\Models\Network\NetworkDevice;
use Illuminate\Console\Command;

class ListMikrotikQueues extends Command
{
    protected $signature = 'mikrotik:list-queues {device-id : The device ID to list queues from}';

    protected $description = 'List all queues from a MikroTik device';

    public function handle()
    {
        $deviceId = $this->argument('device-id');
        $device = NetworkDevice::find($deviceId);

        if (! $device) {
            $this->error("Device with ID {$deviceId} not found");

            return 1;
        }

        $this->info("📡 Listing queues from: {$device->name} ({$device->ip_address})");
        $this->line('');

        try {
            // Get all queues from MikroTik
            $queues = $device->executeMikrotikCommand('/queue/simple/print');

            if (empty($queues)) {
                $this->warn('No queues found on this device');

                return 0;
            }

            $this->info('Found '.count($queues).' queues:');
            $this->line('');

            // Display queue information
            $headers = ['#', 'Name', 'Target', 'Max Upload', 'Max Download', 'Bytes Up', 'Bytes Down'];
            $rows = [];

            foreach ($queues as $index => $queue) {
                $rows[] = [
                    $index + 1,
                    $queue['name'] ?? 'N/A',
                    $queue['target'] ?? 'N/A',
                    $this->formatSpeed($queue['max-limit'] ?? '0/0'),
                    $this->formatSpeed($queue['max-limit'] ?? '0/0', false),
                    $this->formatBytes($queue['bytes'] ?? '0/0'),
                    $this->formatBytes($queue['bytes'] ?? '0/0', false),
                ];

                // Only show first 20 for readability
                if ($index >= 19) {
                    $remaining = count($queues) - 20;
                    $rows[] = ['...', "... and {$remaining} more queues", '...', '...', '...', '...', '...'];
                    break;
                }
            }

            $this->table($headers, $rows);

            // Show some sample queue names for mapping
            $this->line('');
            $this->info('Sample queue names (for customer mapping):');
            foreach (array_slice($queues, 0, 10) as $queue) {
                $name = $queue['name'] ?? 'unnamed';
                $target = $queue['target'] ?? 'no-target';
                $this->line("  • {$name} → {$target}");
            }

        } catch (\Exception $e) {
            $this->error('Failed to retrieve queues: '.$e->getMessage());

            return 1;
        }

        return 0;
    }

    private function formatSpeed($maxLimit, $upload = true)
    {
        if (strpos($maxLimit, '/') !== false) {
            $parts = explode('/', $maxLimit);
            $speed = $upload ? ($parts[0] ?? '0') : ($parts[1] ?? '0');
        } else {
            $speed = $maxLimit;
        }

        if (is_numeric($speed)) {
            $speed = intval($speed);
            if ($speed >= 1000000) {
                return round($speed / 1000000, 1).' Mbps';
            } elseif ($speed >= 1000) {
                return round($speed / 1000, 1).' Kbps';
            }
        }

        return $speed;
    }

    private function formatBytes($bytes, $upload = true)
    {
        if (strpos($bytes, '/') !== false) {
            $parts = explode('/', $bytes);
            $byte_count = $upload ? ($parts[0] ?? '0') : ($parts[1] ?? '0');
        } else {
            $byte_count = $bytes;
        }

        if (is_numeric($byte_count)) {
            $byte_count = intval($byte_count);
            if ($byte_count >= 1073741824) {
                return round($byte_count / 1073741824, 2).' GB';
            } elseif ($byte_count >= 1048576) {
                return round($byte_count / 1048576, 2).' MB';
            } elseif ($byte_count >= 1024) {
                return round($byte_count / 1024, 2).' KB';
            }
        }

        return $byte_count.' B';
    }
}
