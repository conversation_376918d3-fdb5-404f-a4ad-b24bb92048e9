<?php

namespace App\Console\Commands;

use App\Services\PerformanceMonitoringService;
use App\Services\QueryOptimizationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AnalyzePerformanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'performance:analyze 
                            {--controller= : Specific controller to analyze}
                            {--action= : Specific action to analyze}
                            {--clear-cache : Clear optimization caches}
                            {--run-tests : Run performance tests on common operations}
                            {--optimization-report : Generate comprehensive optimization report}';

    /**
     * The console command description.
     */
    protected $description = 'Analyze system performance and provide optimization recommendations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 ISP Network Management System - Performance Analysis');
        $this->newLine();

        if ($this->option('run-tests')) {
            $this->runPerformanceTests();

            return;
        }

        if ($this->option('optimization-report')) {
            $this->generateOptimizationReport();

            return;
        }

        if ($this->option('clear-cache')) {
            $this->clearCaches();

            return;
        }

        // Default: Run general analysis
        $this->analyzeDatabase();
        $this->analyzeIndexes();
        $this->analyzeCaching();
        $this->analyzeMemory();
        $this->showRecommendations();
    }

    /**
     * Clear optimization caches
     */
    protected function clearOptimizationCaches(): void
    {
        $this->info('🧹 Clearing optimization caches...');
        QueryOptimizationService::clearOptimizationCaches();
        $this->info('✅ Optimization caches cleared');
        $this->newLine();
    }

    /**
     * Run performance tests on common operations
     */
    protected function runPerformanceTests(): void
    {
        $this->info('🧪 Running Performance Tests');
        $this->newLine();

        // Test 1: Dashboard loading
        $this->testOperation('Dashboard Loading', function () {
            return QueryOptimizationService::getDashboardStats();
        });

        // Test 2: Customer listing
        $this->testOperation('Customer Listing (100 records)', function () {
            return QueryOptimizationService::optimizedCustomersQuery()
                ->limit(100)
                ->get();
        });

        // Test 3: Service listing
        $this->testOperation('Static IP Services Listing (50 records)', function () {
            return QueryOptimizationService::optimizedStaticIpServicesQuery()
                ->limit(50)
                ->get();
        });

        // Test 4: Recent activities
        $this->testOperation('Recent Activities', function () {
            return QueryOptimizationService::getRecentActivities(20);
        });

        // Test 5: Device utilization
        $this->testOperation('Device Utilization Stats', function () {
            return QueryOptimizationService::getDeviceUtilizationStats();
        });

        $this->newLine();
    }

    /**
     * Test a specific operation and report performance
     */
    protected function testOperation(string $name, callable $operation): void
    {
        $this->line("Testing: {$name}");

        $analysis = PerformanceMonitoringService::monitorOperation($name, $operation);

        $executionTime = $analysis['analysis']['total_time'];
        $queryCount = $analysis['analysis']['total_queries'];
        $duplicateQueries = count($analysis['analysis']['duplicate_queries']);
        $slowQueries = count($analysis['analysis']['slow_queries']);

        // Performance indicators
        $timeStatus = $executionTime < 100 ? '🟢' : ($executionTime < 500 ? '🟡' : '🔴');
        $queryStatus = $queryCount < 10 ? '🟢' : ($queryCount < 25 ? '🟡' : '🔴');
        $duplicateStatus = $duplicateQueries == 0 ? '🟢' : ($duplicateQueries < 3 ? '🟡' : '🔴');

        $this->line("  {$timeStatus} Execution: {$executionTime}ms");
        $this->line("  {$queryStatus} Queries: {$queryCount}");
        $this->line("  {$duplicateStatus} Duplicates: {$duplicateQueries}");

        if ($slowQueries > 0) {
            $this->line("  🔴 Slow queries: {$slowQueries}");
        }

        $this->newLine();
    }

    /**
     * Analyze specific controller action
     */
    protected function analyzeSpecificControllerAction(): void
    {
        $controller = $this->option('controller');
        $action = $this->option('action');

        $this->info("🔬 Analyzing {$controller}::{$action}");
        $this->newLine();

        // This would require implementing specific controller analysis
        $this->warn('Specific controller analysis not implemented yet.');
        $this->info('Use --run-tests to analyze common operations instead.');
    }

    /**
     * Run general system analysis
     */
    protected function runGeneralAnalysis(): void
    {
        $this->info('📊 General System Performance Analysis');
        $this->newLine();

        // Database statistics
        $this->analyzeDatabase();

        // Index analysis
        $this->analyzeIndexes();

        // Cache analysis
        $this->analyzeCaching();

        // Memory analysis
        $this->analyzeMemory();

        // Recommendations
        $this->provideRecommendations();
    }

    /**
     * Analyze database performance
     */
    protected function analyzeDatabase(): void
    {
        $this->info('🗄️  Database Analysis:');

        try {
            $dbDriver = config('database.default');
            $connectionConfig = config("database.connections.{$dbDriver}");
            $isPostgreSQL = ($connectionConfig['driver'] ?? '') === 'pgsql';

            if ($isPostgreSQL) {
                // PostgreSQL compatible query
                $tables = DB::select("
                    SELECT 
                        pt.schemaname as table_schema,
                        pt.tablename as table_name,
                        COALESCE(pst.n_tup_ins + pst.n_tup_upd + pst.n_tup_del, 0) as table_rows,
                        ROUND(pg_total_relation_size(pt.schemaname||'.'||pt.tablename) / 1024.0 / 1024.0, 2) AS size_mb
                    FROM pg_tables pt
                    LEFT JOIN pg_stat_user_tables pst ON pt.tablename = pst.relname
                    WHERE pt.schemaname = 'public'
                    ORDER BY pg_total_relation_size(pt.schemaname||'.'||pt.tablename) DESC
                    LIMIT 10
                ");
            } else {
                // MySQL compatible query
                $tables = DB::select('
                    SELECT 
                        table_name,
                        table_rows,
                        ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE()
                    ORDER BY (data_length + index_length) DESC
                    LIMIT 10
                ');
            }

            $this->table(
                ['Table', 'Rows', 'Size (MB)'],
                array_map(function ($table) {
                    return [
                        $table->table_name,
                        number_format($table->table_rows ?? 0),
                        $table->size_mb ?? 'N/A',
                    ];
                }, $tables)
            );

        } catch (\Exception $e) {
            $this->warn('Could not analyze database tables: '.$e->getMessage());
        }

        $this->newLine();
    }

    /**
     * Analyze database indexes
     */
    protected function analyzeIndexes(): void
    {
        $this->info('📈 Index Analysis:');

        try {
            $dbDriver = config('database.default');
            $connectionConfig = config("database.connections.{$dbDriver}");
            $isPostgreSQL = ($connectionConfig['driver'] ?? '') === 'pgsql';

            if ($isPostgreSQL) {
                // PostgreSQL compatible query
                $missingIndexes = DB::select("
                    SELECT 
                        t.table_name,
                        c.column_name
                    FROM information_schema.tables t
                    JOIN information_schema.columns c ON t.table_name = c.table_name
                    WHERE t.table_schema = 'public'
                    AND c.column_name LIKE '%_id'
                    AND NOT EXISTS (
                        SELECT 1 FROM pg_indexes 
                        WHERE tablename = t.table_name 
                        AND indexdef LIKE '%' || c.column_name || '%'
                    )
                    LIMIT 10
                ");
            } else {
                // MySQL compatible query
                $missingIndexes = DB::select("
                    SELECT 
                        TABLE_NAME,
                        COLUMN_NAME
                    FROM information_schema.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND COLUMN_NAME LIKE '%_id'
                    AND TABLE_NAME NOT IN (
                        SELECT DISTINCT TABLE_NAME 
                        FROM information_schema.STATISTICS 
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND COLUMN_NAME = COLUMNS.COLUMN_NAME
                    )
                    LIMIT 10
                ");
            }

            if (count($missingIndexes) > 0) {
                $this->warn('🔴 Missing indexes detected:');
                foreach ($missingIndexes as $missing) {
                    $columnName = $missing->column_name ?? $missing->COLUMN_NAME;
                    $tableName = $missing->table_name ?? $missing->TABLE_NAME;
                    $this->line("  - {$tableName}.{$columnName}");
                }
            } else {
                $this->info('✅ All foreign key columns appear to have indexes');
            }

        } catch (\Exception $e) {
            $this->warn('Could not analyze indexes: '.$e->getMessage());
        }

        $this->newLine();
    }

    /**
     * Analyze caching effectiveness
     */
    protected function analyzeCaching(): void
    {
        $this->info('🚀 Cache Analysis:');

        // Check if optimization caches are working
        $dashboardStats = QueryOptimizationService::getDashboardStats();
        $this->info('✅ Dashboard statistics caching: Active');

        $recentActivities = QueryOptimizationService::getRecentActivities(5);
        $this->info('✅ Recent activities caching: Active');

        $deviceStats = QueryOptimizationService::getDeviceUtilizationStats();
        $this->info('✅ Device utilization caching: Active');

        $this->newLine();
    }

    /**
     * Analyze memory usage
     */
    protected function analyzeMemory(): void
    {
        $this->info('💾 Memory Analysis:');

        $memoryUsage = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        $memoryLimit = ini_get('memory_limit');

        $this->line('Current usage: '.$this->formatBytes($memoryUsage));
        $this->line('Peak usage: '.$this->formatBytes($peakMemory));
        $this->line("Memory limit: {$memoryLimit}");

        // Memory efficiency rating
        $memoryMB = $memoryUsage / 1024 / 1024;
        if ($memoryMB < 50) {
            $this->info('✅ Excellent memory efficiency');
        } elseif ($memoryMB < 100) {
            $this->info('🟡 Good memory efficiency');
        } else {
            $this->warn('🔴 High memory usage - consider optimization');
        }

        $this->newLine();
    }

    /**
     * Provide optimization recommendations
     */
    protected function provideRecommendations(): void
    {
        $this->info('💡 Optimization Recommendations:');

        $recommendations = [
            '1. Run database migrations to add performance indexes:',
            '   php artisan migrate',
            '',
            '2. Enable query caching in config/database.php',
            '',
            '3. Consider implementing Redis for cache backend:',
            '   CACHE_DRIVER=redis in .env',
            '',
            '4. Monitor slow queries in production:',
            '   Enable slow query log in MySQL',
            '',
            '5. Use eager loading for relationships:',
            '   Always use with() for related data',
            '',
            '6. Implement database connection pooling for high load',
            '',
            '7. Consider read replicas for reporting queries',
        ];

        foreach ($recommendations as $recommendation) {
            $this->line($recommendation);
        }

        $this->newLine();
        $this->info('📋 Next Steps:');
        $this->line('1. Apply database indexes: php artisan migrate');
        $this->line('2. Monitor performance: php artisan performance:analyze --run-tests');
        $this->line('3. Clear caches when needed: php artisan performance:analyze --clear-cache');
    }

    /**
     * Format bytes to human readable format
     */
    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $pow = floor(log($bytes, 1024));

        return round($bytes / (1024 ** $pow), 2).' '.$units[$pow];
    }

    /**
     * Generate comprehensive optimization report
     */
    protected function generateOptimizationReport(): void
    {
        $this->info('📊 Comprehensive Performance Optimization Report');
        $this->newLine();

        $report = PerformanceMonitoringService::generateOptimizationReport();

        // Current Performance
        $this->info('📈 Current Performance Metrics:');
        foreach ($report['current_performance'] as $metric => $value) {
            $this->line("  • {$metric}: {$value}");
        }
        $this->newLine();

        // Bottlenecks
        $this->warn('🔴 Identified Bottlenecks:');
        foreach ($report['bottlenecks'] as $bottleneck) {
            $impact = $bottleneck['impact'] === 'High' ? '🔴' : ($bottleneck['impact'] === 'Medium' ? '🟡' : '🟢');
            $this->line("  {$impact} {$bottleneck['area']}: {$bottleneck['issue']}");
            $this->line("     Solution: {$bottleneck['solution']}");
        }
        $this->newLine();

        // Recommendations
        $this->info('💡 Optimization Recommendations:');
        foreach ($report['recommendations'] as $rec) {
            $priority = $rec['priority'] === 'HIGH' ? '🔴' : ($rec['priority'] === 'MEDIUM' ? '🟡' : '🟢');
            $this->line("  {$priority} {$rec['title']}");
            $this->line("     {$rec['description']}");
            $this->line("     Impact: {$rec['estimated_improvement']} | Effort: {$rec['effort']}");
            $this->newLine();
        }

        // Implementation Priority
        $this->info('🎯 Implementation Priority:');
        foreach ($report['implementation_priority'] as $timeframe => $tasks) {
            $this->line("  📅 {$timeframe}:");
            foreach ($tasks as $task) {
                $this->line("    • {$task}");
            }
        }
        $this->newLine();

        // Query Pattern Analysis
        $patterns = PerformanceMonitoringService::analyzeQueryPatterns();
        $this->info('🔍 Query Pattern Analysis:');

        $this->line('  N+1 Query Patterns Detected:');
        foreach ($patterns['n_plus_one_patterns'] as $pattern) {
            $this->line("    🔴 {$pattern['pattern']}: {$pattern['description']}");
        }
        $this->newLine();

        $this->line('  Optimization Suggestions:');
        foreach ($patterns['optimization_suggestions'] as $suggestion) {
            $this->line("    💡 {$suggestion['type']}: {$suggestion['description']}");
            $this->line("       Impact: {$suggestion['impact']}");
        }
        $this->newLine();

        $this->line('  Index Recommendations:');
        foreach ($patterns['index_recommendations'] as $index) {
            $this->line("    📊 {$index['table']}: ".implode(', ', $index['columns'])." ({$index['type']})");
            $this->line("       Reason: {$index['reason']}");
        }
    }
}
