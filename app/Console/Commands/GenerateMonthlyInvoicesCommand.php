<?php

namespace App\Console\Commands;

use App\Jobs\GenerateMonthlyInvoices;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Queue;

class GenerateMonthlyInvoicesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'billing:generate-monthly-invoices';

    /**
     * The console command description.
     */
    protected $description = 'Automatically generate monthly invoices for all active subscriptions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $billingDate = now();
        $chunkSize = 100; // Process 100 subscriptions per chunk

        $this->info('Automated Monthly Invoice Generation');
        $this->info('Billing Date: '.$billingDate->toDateString());
        $this->info('Processing in chunks of: '.$chunkSize);
        $this->newLine();

        $this->dispatchBillingJob($billingDate, $chunkSize);
    }

    /**
     * Dispatch the billing job to the queue.
     */
    protected function dispatchBillingJob(Carbon $billingDate, int $chunkSize)
    {
        $batchId = 'monthly_'.$billingDate->format('Y_m_d_H_i_s');

        $job = new GenerateMonthlyInvoices($billingDate, $batchId, $chunkSize);
        Queue::push($job);

        $this->info('Monthly invoice generation job dispatched successfully');
        $this->info("Batch ID: {$batchId}");
        $this->info('Check logs at: '.storage_path('logs/monthly-billing.log'));
    }
}
