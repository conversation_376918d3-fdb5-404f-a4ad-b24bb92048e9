<?php

namespace App\Console\Commands;

use App\Models\Network\NetworkDevice;
use Illuminate\Console\Command;

class DebugMikrotikBytes extends Command
{
    protected $signature = 'mikrotik:debug-bytes {device-id : The device ID to debug}';

    protected $description = 'Debug MikroTik queue bytes format';

    public function handle()
    {
        $deviceId = $this->argument('device-id');
        $device = NetworkDevice::find($deviceId);

        if (! $device) {
            $this->error("Device with ID {$deviceId} not found");

            return 1;
        }

        $this->info("🔍 Debugging MikroTik bytes format for: {$device->name}");
        $this->line('');

        try {
            // Get first few queues
            $queues = $device->executeMikrotikCommand('/queue/simple/print');

            if (empty($queues)) {
                $this->warn('No queues found');

                return 1;
            }

            $this->info('Found '.count($queues).' queues. Showing first 5:');
            $this->line('');

            for ($i = 0; $i < min(5, count($queues)); $i++) {
                $queue = $queues[$i];

                $this->line("Queue #{$i}:");
                $this->line('  Name: '.($queue['name'] ?? 'Unknown'));
                $this->line('  Target: '.($queue['target'] ?? 'N/A'));
                $this->line('  Bytes: '.($queue['bytes'] ?? 'N/A'));

                // Parse the bytes
                if (isset($queue['bytes'])) {
                    $bytes = $queue['bytes'];
                    $this->line("  Raw bytes: '{$bytes}'");

                    // Test current parsing
                    $parts = explode('/', $bytes);
                    $this->line('  Parts count: '.count($parts));

                    if (count($parts) >= 2) {
                        $this->line('  Part[0] (first): '.$parts[0].' ('.$this->formatBytes($parts[0]).')');
                        $this->line('  Part[1] (second): '.$parts[1].' ('.$this->formatBytes($parts[1]).')');
                    }

                    // Test our current logic
                    $currentDownload = $this->parseBytes($bytes, 'tx'); // Current logic
                    $currentUpload = $this->parseBytes($bytes, 'rx');   // Current logic

                    $this->line('  Current parsing:');
                    $this->line('    Download (tx): '.$this->formatBytes($currentDownload));
                    $this->line('    Upload (rx): '.$this->formatBytes($currentUpload));
                }

                $this->line('');
            }

            // Show what we expect vs what we see
            $this->info('🎯 Analysis:');
            $this->line('Based on the queue listing we saw earlier:');
            $this->line('- FADUMO AHMED had 26.99 GB download, 0.36 GB upload');
            $this->line('- But our parsing shows 0 download for everyone');
            $this->line('- This suggests the bytes format might be different');

        } catch (\Exception $e) {
            $this->error('Failed to debug bytes: '.$e->getMessage());

            return 1;
        }

        return 0;
    }

    private function parseBytes(string $bytes, string $direction): int
    {
        // Current parsing logic
        $parts = explode('/', $bytes);

        if ($direction === 'rx' && isset($parts[0])) {
            return (int) $parts[0];
        }

        if ($direction === 'tx' && isset($parts[1])) {
            return (int) $parts[1];
        }

        return 0;
    }

    private function formatBytes($bytes): string
    {
        $bytes = (int) $bytes;
        if ($bytes >= 1024 * 1024 * 1024) {
            return round($bytes / (1024 * 1024 * 1024), 2).' GB';
        } elseif ($bytes >= 1024 * 1024) {
            return round($bytes / (1024 * 1024), 2).' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2).' KB';
        } else {
            return $bytes.' B';
        }
    }
}
