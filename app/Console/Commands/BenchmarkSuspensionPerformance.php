<?php

namespace App\Console\Commands;

use App\Models\Services\StaticIpService;
use App\Services\MikrotikAddressListService;
use Illuminate\Console\Command;

class BenchmarkSuspensionPerformance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'benchmark:suspension-performance {service_id : Static IP service ID to benchmark}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Benchmark the performance of the optimized suspension system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $serviceId = $this->argument('service_id');

        $service = StaticIpService::with(['customer', 'device'])->find($serviceId);

        if (! $service) {
            $this->error("Static IP service with ID {$serviceId} not found");

            return 1;
        }

        $this->info('🚀 Benchmarking suspension performance for:');
        $this->line("  Service ID: {$service->id}");
        $this->line("  Customer: {$service->customer->name}");
        $this->line("  IP Address: {$service->ip_address}");
        $this->line("  Device: {$service->device->name}");
        $this->newLine();

        $addressListService = new MikrotikAddressListService;
        $device = $service->device;

        // Ensure clean state
        $addressListService->removeIpFromSuspendedList($device, $service->ip_address, $service->customer->name);

        $this->info('📊 Running performance benchmarks...');
        $this->newLine();

        // Benchmark 1: Suspension Performance
        $this->line('🔴 Test 1: Suspension Performance');
        $suspensionTimes = [];

        for ($i = 1; $i <= 5; $i++) {
            // Remove first to ensure clean state
            $addressListService->removeIpFromSuspendedList($device, $service->ip_address, $service->customer->name);

            $startTime = microtime(true);
            $success = $addressListService->addIpToSuspendedList(
                $device,
                $service->ip_address,
                $service->customer->name
            );
            $endTime = microtime(true);

            $duration = ($endTime - $startTime) * 1000; // Convert to milliseconds
            $suspensionTimes[] = $duration;

            $this->line("  Run {$i}: ".number_format($duration, 2).'ms '.($success ? '✓' : '✗'));
        }

        $avgSuspension = array_sum($suspensionTimes) / count($suspensionTimes);
        $this->line('  Average: '.number_format($avgSuspension, 2).'ms');
        $this->newLine();

        // Benchmark 2: Unsuspension Performance
        $this->line('🟢 Test 2: Unsuspension Performance');
        $unsuspensionTimes = [];

        for ($i = 1; $i <= 5; $i++) {
            // Add first to ensure IP is in list
            $addressListService->addIpToSuspendedList($device, $service->ip_address, $service->customer->name);

            $startTime = microtime(true);
            $success = $addressListService->removeIpFromSuspendedList(
                $device,
                $service->ip_address,
                $service->customer->name
            );
            $endTime = microtime(true);

            $duration = ($endTime - $startTime) * 1000; // Convert to milliseconds
            $unsuspensionTimes[] = $duration;

            $this->line("  Run {$i}: ".number_format($duration, 2).'ms '.($success ? '✓' : '✗'));
        }

        $avgUnsuspension = array_sum($unsuspensionTimes) / count($unsuspensionTimes);
        $this->line('  Average: '.number_format($avgUnsuspension, 2).'ms');
        $this->newLine();

        // Benchmark 3: Status Check Performance
        $this->line('🔍 Test 3: Status Check Performance');
        $statusCheckTimes = [];

        // Add IP to list for testing
        $addressListService->addIpToSuspendedList($device, $service->ip_address, $service->customer->name);

        for ($i = 1; $i <= 10; $i++) {
            $startTime = microtime(true);
            $isSuspended = $addressListService->isIpSuspended($device, $service->ip_address);
            $endTime = microtime(true);

            $duration = ($endTime - $startTime) * 1000; // Convert to milliseconds
            $statusCheckTimes[] = $duration;

            $this->line("  Run {$i}: ".number_format($duration, 2).'ms '.($isSuspended ? '✓' : '✗'));
        }

        $avgStatusCheck = array_sum($statusCheckTimes) / count($statusCheckTimes);
        $this->line('  Average: '.number_format($avgStatusCheck, 2).'ms');
        $this->newLine();

        // Performance Summary
        $this->info('📈 Performance Summary:');
        $this->line('  Suspension:   '.number_format($avgSuspension, 2).'ms average');
        $this->line('  Unsuspension: '.number_format($avgUnsuspension, 2).'ms average');
        $this->line('  Status Check: '.number_format($avgStatusCheck, 2).'ms average');
        $this->newLine();

        // Performance Assessment
        $maxAcceptableTime = 2000; // 2 seconds
        $goodPerformanceTime = 500; // 500ms

        if ($avgSuspension < $goodPerformanceTime && $avgUnsuspension < $goodPerformanceTime) {
            $this->info('🎉 EXCELLENT: All operations under 500ms - Production ready!');
        } elseif ($avgSuspension < $maxAcceptableTime && $avgUnsuspension < $maxAcceptableTime) {
            $this->info('✅ GOOD: All operations under 2 seconds - Acceptable performance');
        } else {
            $this->warn('⚠️  SLOW: Some operations over 2 seconds - Consider optimization');
        }

        // Clean up
        $addressListService->removeIpFromSuspendedList($device, $service->ip_address, $service->customer->name);

        return 0;
    }
}
