<?php

namespace App\Console\Commands;

use App\Models\Bandwidth\QueueUsage;
use App\Models\Network\NetworkDevice;
use Illuminate\Console\Command;

class DebugBandwidthMultiplication extends Command
{
    protected $signature = 'debug:bandwidth-multiplication {ip : IP address to debug}';

    protected $description = 'Debug bandwidth multiplication issue';

    public function handle()
    {
        $ip = $this->argument('ip');
        $this->info("🔍 Debugging bandwidth multiplication for IP: {$ip}");
        $this->line('');

        $device = NetworkDevice::find(1);
        if (! $device) {
            $this->error('Device not found');

            return 1;
        }

        try {
            // 1. Get raw MikroTik queue data
            $this->info('1. Raw MikroTik Queue Data:');
            $queues = $device->executeMikrotikCommand('/queue/simple/print');

            $targetQueue = null;
            foreach ($queues as $queue) {
                if (isset($queue['target']) && (
                    strpos($queue['target'], $ip) !== false ||
                    strpos($queue['target'], $ip.'/32') !== false
                )) {
                    $targetQueue = $queue;
                    break;
                }
            }

            if ($targetQueue) {
                $this->line('   Queue Name: '.($targetQueue['name'] ?? 'N/A'));
                $this->line('   Target: '.($targetQueue['target'] ?? 'N/A'));
                $this->line('   Raw Bytes: '.($targetQueue['bytes'] ?? 'N/A'));
                $this->line('   Rate: '.($targetQueue['rate'] ?? 'N/A'));

                // Parse bytes field (format: "upload/download")
                if (isset($targetQueue['bytes'])) {
                    $bytes = explode('/', $targetQueue['bytes']);
                    $rawUpload = isset($bytes[0]) ? (int) $bytes[0] : 0;
                    $rawDownload = isset($bytes[1]) ? (int) $bytes[1] : 0;

                    $this->line('   Raw Upload Bytes: '.number_format($rawUpload));
                    $this->line('   Raw Download Bytes: '.number_format($rawDownload));
                    $this->line('   Raw Upload MB: '.number_format($rawUpload / 1024 / 1024, 2));
                    $this->line('   Raw Download MB: '.number_format($rawDownload / 1024 / 1024, 2));
                }
            } else {
                $this->warn("   No queue found for {$ip}");

                return 1;
            }

            $this->line('');

            // 2. Check database records
            $this->info('2. Database QueueUsage Records:');
            $queueUsage = QueueUsage::where('target_ip', $ip)
                ->orWhere('target_ip', $ip.'/32')
                ->latest()
                ->first();

            if ($queueUsage) {
                $this->line('   DB Upload Bytes: '.number_format($queueUsage->upload_bytes));
                $this->line('   DB Download Bytes: '.number_format($queueUsage->download_bytes));
                $this->line('   DB Upload MB: '.number_format($queueUsage->upload_bytes / 1024 / 1024, 2));
                $this->line('   DB Download MB: '.number_format($queueUsage->download_bytes / 1024 / 1024, 2));
                $this->line('   Period: '.$queueUsage->period_start.' to '.$queueUsage->period_end);
            } else {
                $this->warn('   No database records found');
            }

            $this->line('');

            // 3. Compare and calculate multiplication factor
            if ($targetQueue && $queueUsage && isset($targetQueue['bytes'])) {
                $this->info('3. Multiplication Analysis:');

                $bytes = explode('/', $targetQueue['bytes']);
                $rawUpload = isset($bytes[0]) ? (int) $bytes[0] : 0;
                $rawDownload = isset($bytes[1]) ? (int) $bytes[1] : 0;

                if ($rawUpload > 0) {
                    $uploadMultiplier = $queueUsage->upload_bytes / $rawUpload;
                    $this->line('   Upload Multiplier: '.number_format($uploadMultiplier, 2).'x');
                }

                if ($rawDownload > 0) {
                    $downloadMultiplier = $queueUsage->download_bytes / $rawDownload;
                    $this->line('   Download Multiplier: '.number_format($downloadMultiplier, 2).'x');
                }
            }

            $this->line('');

            // 4. Check bandwidth collection logic
            $this->info('4. Bandwidth Collection Logic Check:');
            $this->line('   Looking at the CollectBandwidthUsage command...');

            // Check if there's a conversion issue in the collection command
            $collectionFile = app_path('Console/Commands/CollectBandwidthUsage.php');
            if (file_exists($collectionFile)) {
                $this->line('   ✅ Collection command exists');
                $this->line("   📁 File: {$collectionFile}");
            } else {
                $this->warn('   ❌ Collection command not found');
            }

        } catch (\Exception $e) {
            $this->error('Error: '.$e->getMessage());

            return 1;
        }

        return 0;
    }
}
