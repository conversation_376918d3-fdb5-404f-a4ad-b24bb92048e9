<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Console\Command;

class InvestigateBillingIssueCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'investigate:billing-issue 
                            {customer? : Customer name to investigate}
                            {--all : Show all customers with billing issues}';

    /**
     * The console command description.
     */
    protected $description = 'Investigate why customers with future billing dates got invoiced';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Investigating Billing Issue - Future Dated Subscriptions');
        $this->newLine();

        $customerName = $this->argument('customer');
        $showAll = $this->option('all');

        $now = now();
        $this->info("Current date/time: {$now}");
        $this->newLine();

        if ($customerName) {
            $this->investigateSpecificCustomer($customerName, $now);
        } elseif ($showAll) {
            $this->investigateAllProblematicCustomers($now);
        } else {
            $this->showOverview($now);
        }
    }

    /**
     * Investigate a specific customer.
     */
    protected function investigateSpecificCustomer($customerName, $now)
    {
        $customer = Customer::where('name', 'like', "%{$customerName}%")->first();

        if (! $customer) {
            $this->error("Customer '{$customerName}' not found.");

            return;
        }

        $this->info("🔍 Investigating Customer: {$customer->name} (ID: {$customer->id})");
        $this->newLine();

        // Get all subscriptions for this customer
        $subscriptions = Subscription::where('customer_id', $customer->id)->get();

        $this->info("📋 Subscriptions for {$customer->name}:");
        foreach ($subscriptions as $subscription) {
            $this->analyzeSubscription($subscription, $now);
        }

        $this->newLine();

        // Get all invoices for this customer
        $invoices = Invoice::where('customer_id', $customer->id)->orderBy('created_at', 'desc')->get();

        $this->info("📄 Recent Invoices for {$customer->name}:");
        if ($invoices->isEmpty()) {
            $this->info('  No invoices found.');
        } else {
            foreach ($invoices as $invoice) {
                $createdAt = Carbon::parse($invoice->created_at);
                $daysAgo = $createdAt->diffInDays($now);
                $this->info("  Invoice #{$invoice->invoice_number} - \${$invoice->total_amount} - {$invoice->status} - Created: {$invoice->created_at} ({$daysAgo} days ago)");
            }
        }
    }

    /**
     * Show overview of the billing issue.
     */
    protected function showOverview($now)
    {
        $this->info('📊 Billing Issue Overview:');
        $this->newLine();

        // Find customers who have invoices but their subscriptions have future billing dates
        $problematicCustomers = Customer::whereHas('invoices')
            ->whereHas('subscriptions', function ($query) use ($now) {
                $query->where('next_billing_date', '>', $now);
            })
            ->with(['subscriptions', 'invoices'])
            ->get();

        if ($problematicCustomers->isEmpty()) {
            $this->info('✅ No customers found with billing issues.');

            return;
        }

        $this->warn("Found {$problematicCustomers->count()} customers with potential billing issues:");
        $this->newLine();

        foreach ($problematicCustomers->take(10) as $customer) {
            $this->info("Customer: {$customer->name}");

            $futureSubscriptions = $customer->subscriptions->where('next_billing_date', '>', $now);
            $recentInvoices = $customer->invoices->where('created_at', '>', $now->copy()->subDay());

            $this->info("  - Future billing subscriptions: {$futureSubscriptions->count()}");
            $this->info("  - Recent invoices: {$recentInvoices->count()}");

            if ($futureSubscriptions->isNotEmpty()) {
                $nextBilling = $futureSubscriptions->first()->next_billing_date;
                $daysUntilBilling = Carbon::parse($nextBilling)->diffInDays($now);
                $this->info("  - Next billing date: {$nextBilling} ({$daysUntilBilling} days from now)");
            }

            $this->newLine();
        }

        $this->info('💡 Use --all to see detailed analysis of all problematic customers');
        $this->info('💡 Use a customer name to investigate a specific customer');
    }

    /**
     * Investigate all problematic customers.
     */
    protected function investigateAllProblematicCustomers($now)
    {
        $this->info('🔍 Detailed Investigation of All Problematic Customers:');
        $this->newLine();

        // Find customers who have recent invoices but their subscriptions have future billing dates
        $problematicCustomers = Customer::whereHas('invoices', function ($query) use ($now) {
            $query->where('created_at', '>', $now->copy()->subDay());
        })
            ->whereHas('subscriptions', function ($query) use ($now) {
                $query->where('next_billing_date', '>', $now);
            })
            ->with(['subscriptions', 'invoices'])
            ->get();

        if ($problematicCustomers->isEmpty()) {
            $this->info('✅ No customers found with recent invoices and future billing dates.');

            return;
        }

        $this->warn("Found {$problematicCustomers->count()} customers with billing issues:");
        $this->newLine();

        foreach ($problematicCustomers as $customer) {
            $this->info("🔍 Customer: {$customer->name} (ID: {$customer->id})");

            // Analyze subscriptions
            $futureSubscriptions = $customer->subscriptions->where('next_billing_date', '>', $now);
            foreach ($futureSubscriptions as $subscription) {
                $this->analyzeSubscription($subscription, $now, '  ');
            }

            // Show recent invoices
            $recentInvoices = $customer->invoices->where('created_at', '>', $now->copy()->subDay());
            $this->info('  Recent Invoices:');
            foreach ($recentInvoices as $invoice) {
                $this->info("    - Invoice #{$invoice->invoice_number} - \${$invoice->total_amount} - Created: {$invoice->created_at}");
            }

            $this->newLine();
        }
    }

    /**
     * Analyze a specific subscription.
     */
    protected function analyzeSubscription($subscription, $now, $indent = '')
    {
        $nextBillingDate = Carbon::parse($subscription->next_billing_date);
        $daysFromNow = $nextBillingDate->diffInDays($now, false);

        $shouldBeBilled = $subscription->status === 'active'
                         && $subscription->auto_billing_enabled
                         && $subscription->next_billing_date <= $now;

        $this->info("{$indent}Subscription ID: {$subscription->id}");
        $this->info("{$indent}  Status: {$subscription->status}");
        $this->info("{$indent}  Auto Billing: ".($subscription->auto_billing_enabled ? 'Yes' : 'No'));
        $this->info("{$indent}  Next Billing Date: {$subscription->next_billing_date}");
        $this->info("{$indent}  Days from now: ".($daysFromNow > 0 ? "+{$daysFromNow}" : $daysFromNow));
        $this->info("{$indent}  Should be billed: ".($shouldBeBilled ? '✅ Yes' : '❌ No'));

        if (! $shouldBeBilled && $subscription->next_billing_date > $now) {
            $this->warn("{$indent}  ⚠️  This subscription has a FUTURE billing date but may have been billed!");
        }
    }
}
