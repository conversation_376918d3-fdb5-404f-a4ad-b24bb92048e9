<?php

namespace App\Console\Commands;

use App\Http\Controllers\Bandwidth\BandwidthUsageController;
use Illuminate\Console\Command;
use Illuminate\Http\Request;

class TestBandwidthApi extends Command
{
    protected $signature = 'test:bandwidth-api {customer_id : Customer ID to test}';

    protected $description = 'Test bandwidth API for a customer';

    public function handle()
    {
        $customerId = $this->argument('customer_id');
        $this->info("🧪 Testing Bandwidth API for Customer ID: {$customerId}");
        $this->line('');

        try {
            // Create controller and request
            $controller = new BandwidthUsageController;
            $request = new Request([
                'customer_id' => $customerId,
                'period' => 'month',
            ]);

            // Call the API method
            $response = $controller->getUsage($request);
            $statusCode = $response->getStatusCode();

            $this->info("✅ API Response Status: {$statusCode}");

            if ($statusCode === 200) {
                $data = json_decode($response->getContent(), true);

                $this->line('');
                $this->info('📊 Summary Data:');
                if (isset($data['summary'])) {
                    $summary = $data['summary'];
                    $this->line('   Download: '.$this->formatBytes($summary['total_download'] ?? 0));
                    $this->line('   Upload: '.$this->formatBytes($summary['total_upload'] ?? 0));
                    $this->line('   Total: '.$this->formatBytes($summary['total_bandwidth'] ?? 0));
                    $this->line('   Current Month: '.$this->formatBytes($summary['current_month'] ?? 0));
                } else {
                    $this->warn('   No summary data found');
                }

                $this->line('');
                $this->info('📋 Usage Records:');
                if (isset($data['usage']) && count($data['usage']) > 0) {
                    $this->line('   Found '.count($data['usage']).' usage records');

                    // Show first few records
                    $records = array_slice($data['usage'], 0, 3);
                    foreach ($records as $record) {
                        $this->line('   - '.($record['service_name'] ?? 'Unknown').': '.
                                   $this->formatBytes($record['total'] ?? 0).
                                   ' (Source: '.($record['source'] ?? 'unknown').
                                   ', Type: '.($record['service_type'] ?? 'unknown').')');
                    }
                } else {
                    $this->warn('   No usage records found');
                }

            } else {
                $this->error("❌ API returned error status: {$statusCode}");
                $this->line($response->getContent());
            }

        } catch (\Exception $e) {
            $this->error('❌ Exception: '.$e->getMessage());

            return 1;
        }

        $this->line('');
        $this->info('🎉 Test completed!');

        return 0;
    }

    private function formatBytes($bytes)
    {
        if ($bytes >= 1024 * 1024 * 1024) {
            return number_format($bytes / (1024 * 1024 * 1024), 2).' GB';
        } elseif ($bytes >= 1024 * 1024) {
            return number_format($bytes / (1024 * 1024), 2).' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2).' KB';
        } else {
            return $bytes.' B';
        }
    }
}
