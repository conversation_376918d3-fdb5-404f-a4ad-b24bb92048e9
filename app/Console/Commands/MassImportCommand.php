<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Network\IpAddress;
use App\Models\Services\IpPool;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Models\Subscription;
use App\Services\Import\MassImportService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MassImportCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'import:mass-migration
                            {--customers=2000 : Number of customers to generate}
                            {--clear-data : Clear existing customer data before import}
                            {--force : Skip confirmation prompts for automated runs}
                            {--dry-run : Show what would be imported without actually importing}
                            {--chunk-size=50 : Number of records per batch job}';

    /**
     * The console command description.
     */
    protected $description = 'Mass import system for ISP customer migration from external platforms';

    /**
     * Execute the console command.
     */
    public function handle(MassImportService $importService)
    {
        $customerCount = (int) $this->option('customers');
        $clearData = $this->option('clear-data');
        $force = $this->option('force');
        $dryRun = $this->option('dry-run');
        $chunkSize = (int) $this->option('chunk-size');

        $this->info('🚀 ISP Mass Customer Migration System');
        $this->info('=====================================');
        $this->newLine();

        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No data will be imported');
        }

        $this->info('📊 Migration Configuration:');
        $this->info("  • Customers to import: {$customerCount}");
        $this->info('  • Clear existing data: '.($clearData ? 'Yes' : 'No'));
        $this->info("  • Chunk size: {$chunkSize}");
        $this->info('  • Service distribution: ~60% PPPoE, ~40% Static IP');
        $this->info('  • IP pools to create: 4 pools');
        $this->newLine();

        // Show current data status
        $this->displayCurrentDataStatus();

        if ($clearData && ! $dryRun) {
            // Skip confirmation if --force flag is used (for automated runs like seeders)
            if (! $force && ! $this->confirm('⚠️  This will DELETE all existing customer data. Continue?')) {
                $this->info('Operation cancelled.');

                return 0;
            }

            if ($force) {
                $this->info('🤖 AUTOMATED RUN: Clearing data (--force flag enabled)');
            }

            $this->clearExistingData();
        }

        if ($dryRun) {
            $this->info('🔍 DRY RUN: Would generate and import the following data:');
            $this->displayMigrationPreview($customerCount);

            return 0;
        }

        // Generate migration data
        $this->info('📋 Generating migration data...');
        $migrationData = MassImportService::createSampleMigrationData($customerCount);

        $this->info('✅ Migration data generated:');
        $this->info("  • IP pools: {$migrationData['ip_pools']->getRecordCount()}");
        $this->info("  • Customers: {$migrationData['customers']->getRecordCount()}");
        $this->newLine();

        // Process migration
        $this->info('🚀 Starting migration process...');

        $results = $importService->processMigration($migrationData, [
            'chunk_size' => $chunkSize,
        ]);

        $this->info('✅ Migration jobs dispatched:');
        foreach ($results as $type => $batchId) {
            $this->info("  • {$type}: {$batchId}");
        }

        $this->newLine();
        $this->info('📝 Migration Process Information:');
        $this->info('  • Jobs are processing in the background via Laravel queues');
        $this->info('  • IP pools will be created first, then customers');
        $this->info('  • Each customer will get a subscription and initial invoice');
        $this->info('  • Static IP customers will be assigned available IPs from pools');
        $this->info('  • PPPoE customers will get generated usernames/passwords');
        $this->newLine();

        $this->info('🔧 To monitor progress:');
        $this->info('  • Run queue worker: php artisan queue:work');
        $this->info('  • Check logs: tail -f storage/logs/laravel.log');
        $this->info('  • Monitor database: Check customers, subscriptions, services tables');

        Log::info('Mass migration initiated', [
            'customer_count' => $customerCount,
            'batch_ids' => $results,
            'initiated_by' => 'console_command',
        ]);

        return 0;
    }

    /**
     * Display current data status
     */
    protected function displayCurrentDataStatus(): void
    {
        $customers = Customer::count();
        $subscriptions = Subscription::count();
        $invoices = Invoice::count();
        $staticIpServices = StaticIpService::count();
        $pppoeServices = PppoeService::count();
        $ipPools = IpPool::count();
        $ipAddresses = IpAddress::count();

        $this->info('📊 Current Database Status:');
        $this->table(
            ['Data Type', 'Count'],
            [
                ['Customers', $customers],
                ['Subscriptions', $subscriptions],
                ['Invoices', $invoices],
                ['Static IP Services', $staticIpServices],
                ['PPPoE Services', $pppoeServices],
                ['IP Pools', $ipPools],
                ['IP Addresses', $ipAddresses],
            ]
        );
        $this->newLine();
    }

    /**
     * Clear existing customer data
     */
    protected function clearExistingData(): void
    {
        $this->info('🗑️  Clearing existing customer data...');

        DB::transaction(function () {
            // Delete in correct order to respect foreign key constraints
            Invoice::truncate();
            StaticIpService::truncate();
            PppoeService::truncate();
            Subscription::truncate();
            Customer::truncate();
            IpAddress::truncate();
            IpPool::truncate();
        });

        $this->info('✅ Existing data cleared successfully');
        $this->newLine();
    }

    /**
     * Display migration preview for dry run
     */
    protected function displayMigrationPreview(int $customerCount): void
    {
        $staticIpCount = (int) ($customerCount * 0.4); // 40% static IP
        $pppoeCount = $customerCount - $staticIpCount; // 60% PPPoE

        $this->info('📋 Migration Preview:');
        $this->newLine();

        $this->info('🏢 IP Pools (4 total):');
        $this->info('  • Residential Pool A (*************/24) - IPs: **************-100');
        $this->info('  • Residential Pool B (*************/24) - IPs: **************-100');
        $this->info('  • Business Pool A (*************/24) - IPs: **************-50');
        $this->info('  • Premium Pool (*************/24) - IPs: **************-30');
        $this->newLine();

        $this->info("👥 Customers ({$customerCount} total):");
        $this->info("  • Static IP customers: {$staticIpCount} (~40%)");
        $this->info("  • PPPoE customers: {$pppoeCount} (~60%)");
        $this->info('  • All customers get subscriptions and initial invoices');
        $this->info('  • Random bandwidth plans (Basic 10Mbps, Standard 25Mbps, Premium 50Mbps)');
        $this->info('  • Realistic names, emails, addresses');
        $this->newLine();

        $this->info('🔧 Processing Strategy:');
        $this->info('  • Step 1: Create IP pools on MikroTik device');
        $this->info('  • Step 2: Generate IP addresses for each pool');
        $this->info('  • Step 3: Create customers with services');
        $this->info('  • Step 4: Assign IPs to Static IP customers');
        $this->info('  • Step 5: Generate PPPoE credentials');
        $this->info('  • Step 6: Create subscriptions and invoices');
        $this->newLine();

        $this->info('⚡ Performance Features:');
        $this->info('  • Queue-based processing for scalability');
        $this->info('  • Batch operations to prevent timeouts');
        $this->info('  • Real MikroTik integration');
        $this->info('  • Comprehensive error handling');
        $this->info('  • Progress tracking and logging');
    }
}
