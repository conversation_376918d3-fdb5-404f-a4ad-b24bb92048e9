<?php

namespace App\Console\Commands;

use App\Models\Network\NetworkDevice;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use Illuminate\Console\Command;

class ProvisionTestServicesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'mikrotik:provision-test-services 
                            {--device=3 : Device ID to provision services on}
                            {--dry-run : Show what would be created without actually creating}';

    /**
     * The console command description.
     */
    protected $description = 'Provision test services (PPPoE users and queues) on real MikroTik device';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $deviceId = $this->option('device');
        $dryRun = $this->option('dry-run');

        $this->info('🔧 Provisioning Test Services on Real MikroTik');
        $this->info('===============================================');
        $this->newLine();

        // Get the device
        $device = NetworkDevice::find($deviceId);
        if (! $device) {
            $this->error("❌ Device with ID {$deviceId} not found!");

            return 1;
        }

        $this->info("📡 Using device: {$device->name} ({$device->ip_address})");

        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made to MikroTik');
        }

        $this->newLine();

        // Test connection
        try {
            $this->info('🔌 Testing MikroTik connection...');
            $identity = $device->executeMikrotikCommand('/system/identity/print');
            $this->info("✅ Connected to: {$identity[0]['name']}");
        } catch (\Exception $e) {
            $this->error('❌ Cannot connect to MikroTik: '.$e->getMessage());

            return 1;
        }

        $this->newLine();

        // Provision PPPoE users
        $this->provisionPppoeUsers($device, $dryRun);

        $this->newLine();

        // Provision Static IP queues
        $this->provisionStaticIpQueues($device, $dryRun);

        $this->newLine();
        $this->info('✅ Test service provisioning completed!');

        if (! $dryRun) {
            $this->info('🎯 Check your MikroTik:');
            $this->info('  - PPPoE users: /ppp/secret');
            $this->info('  - Queues: /queue/simple');
        }

        return 0;
    }

    /**
     * Provision PPPoE users on MikroTik.
     */
    protected function provisionPppoeUsers(NetworkDevice $device, bool $dryRun): void
    {
        $this->info('👥 Provisioning PPPoE users...');

        $pppoeServices = PppoeService::with(['customer', 'bandwidthPlan'])
            ->where('device_id', $device->id)
            ->get();

        if ($pppoeServices->isEmpty()) {
            $this->warn('⚠️  No PPPoE services found in database');

            return;
        }

        foreach ($pppoeServices as $service) {
            $this->info("Creating PPPoE user: {$service->username} for {$service->customer->name}");

            if (! $dryRun) {
                try {
                    // Create PPPoE user
                    $response = $device->executeMikrotikCommand('/ppp/secret/add', [
                        'name' => $service->username,
                        'password' => $service->password,
                        'service' => 'pppoe',
                        'profile' => 'default',
                        'comment' => 'Customer: '.$service->customer->name,
                    ]);

                    // Create queue for PPPoE user
                    $queueName = $service->customer->name.' PPPoE';
                    $device->executeMikrotikCommand('/queue/simple/add', [
                        'name' => $queueName,
                        'target' => $service->username,
                        'max-limit' => $service->bandwidthPlan->upload_speed.'M/'.$service->bandwidthPlan->download_speed.'M',
                        'comment' => 'PPPoE Customer: '.$service->customer->name,
                    ]);

                    $this->info("  ✅ Created PPPoE user and queue for {$service->customer->name}");

                } catch (\Exception $e) {
                    $this->error("  ❌ Failed to create PPPoE user {$service->username}: ".$e->getMessage());
                }
            } else {
                $this->line("  [DRY RUN] Would create PPPoE user: {$service->username}");
                $this->line("  [DRY RUN] Would create queue: {$service->customer->name} PPPoE");
            }
        }

        $count = $pppoeServices->count();
        $this->info("📊 Processed {$count} PPPoE services");
    }

    /**
     * Provision Static IP queues on MikroTik.
     */
    protected function provisionStaticIpQueues(NetworkDevice $device, bool $dryRun): void
    {
        $this->info('🌐 Provisioning Static IP queues...');

        $staticServices = StaticIpService::with(['customer', 'bandwidthPlan'])
            ->where('device_id', $device->id)
            ->get();

        if ($staticServices->isEmpty()) {
            $this->warn('⚠️  No Static IP services found in database');

            return;
        }

        foreach ($staticServices as $service) {
            $this->info("Creating queue for: {$service->customer->name} - {$service->ip_address}");

            if (! $dryRun) {
                try {
                    // Create queue for Static IP
                    $queueName = $service->customer->name.' '.$service->ip_address;
                    $device->executeMikrotikCommand('/queue/simple/add', [
                        'name' => $queueName,
                        'target' => $service->ip_address.'/32',
                        'max-limit' => $service->bandwidthPlan->upload_speed.'M/'.$service->bandwidthPlan->download_speed.'M',
                        'comment' => 'Static IP Customer: '.$service->customer->name,
                    ]);

                    $this->info("  ✅ Created queue for {$service->customer->name}");

                } catch (\Exception $e) {
                    $this->error("  ❌ Failed to create queue for {$service->customer->name}: ".$e->getMessage());
                }
            } else {
                $this->line("  [DRY RUN] Would create queue: {$service->customer->name} {$service->ip_address}");
                $this->line("  [DRY RUN] Target: {$service->ip_address}/32");
                $this->line("  [DRY RUN] Speed: {$service->bandwidthPlan->upload_speed}M/{$service->bandwidthPlan->download_speed}M");
            }
        }

        $count = $staticServices->count();
        $this->info("📊 Processed {$count} Static IP services");
    }
}
