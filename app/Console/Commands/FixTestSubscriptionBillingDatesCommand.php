<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixTestSubscriptionBillingDatesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'fix:test-subscription-billing-dates 
                            {--dry-run : Show what would be updated without making changes}
                            {--days-ago=1 : Number of days ago to set the next_billing_date}';

    /**
     * The console command description.
     */
    protected $description = 'Fix next_billing_date for test subscriptions to make them eligible for billing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Fixing Test Subscription Billing Dates');
        $this->newLine();

        $dryRun = $this->option('dry-run');
        $daysAgo = (int) $this->option('days-ago');

        // Calculate the target billing date
        $targetBillingDate = Carbon::now()->subDays($daysAgo);

        $this->info("Target next_billing_date: {$targetBillingDate->toDateString()}");
        $this->info("Days ago: {$daysAgo}");

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        $this->newLine();

        // Find subscriptions with future billing dates
        $subscriptionsToFix = Subscription::where('status', 'active')
            ->where('auto_billing_enabled', true)
            ->where('next_billing_date', '>', Carbon::now())
            ->get();

        if ($subscriptionsToFix->isEmpty()) {
            $this->info('✅ No subscriptions found that need fixing.');

            return 0;
        }

        $this->info("Found {$subscriptionsToFix->count()} subscriptions with future billing dates:");

        // Group by current billing date for summary
        $groupedByDate = $subscriptionsToFix->groupBy('next_billing_date');

        foreach ($groupedByDate as $date => $subscriptions) {
            $count = $subscriptions->count();
            $daysFromNow = Carbon::parse($date)->diffInDays(Carbon::now());
            $this->info("  {$date}: {$count} subscriptions ({$daysFromNow} days from now)");
        }

        $this->newLine();

        if (! $dryRun) {
            if (! $this->confirm('Do you want to update these subscriptions?')) {
                $this->info('Operation cancelled.');

                return 0;
            }
        }

        // Update the subscriptions
        if (! $dryRun) {
            $this->info('Updating subscriptions...');

            $updatedCount = DB::table('subscriptions')
                ->where('status', 'active')
                ->where('auto_billing_enabled', true)
                ->where('next_billing_date', '>', Carbon::now())
                ->update([
                    'next_billing_date' => $targetBillingDate->toDateString(),
                    'updated_at' => Carbon::now(),
                ]);

            $this->info("✅ Updated {$updatedCount} subscriptions.");
        } else {
            $this->info("Would update {$subscriptionsToFix->count()} subscriptions to have next_billing_date = {$targetBillingDate->toDateString()}");
        }

        $this->newLine();

        // Verify the fix
        $this->info('🔍 Verification:');

        $eligibleCount = Subscription::where('status', 'active')
            ->where('auto_billing_enabled', true)
            ->where('next_billing_date', '<=', Carbon::now())
            ->count();

        $this->info("Subscriptions now eligible for billing: {$eligibleCount}");

        if (! $dryRun && $eligibleCount > 0) {
            $this->newLine();
            $this->info('🚀 Next steps:');
            $this->info('1. Run the debug command to verify: php artisan debug:invoice-generation');
            $this->info('2. Test invoice generation: php artisan billing:generate-monthly-invoices');
        }

        return 0;
    }
}
