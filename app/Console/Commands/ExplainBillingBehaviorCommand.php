<?php

namespace App\Console\Commands;

use App\Models\Customer;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ExplainBillingBehaviorCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'explain:billing-behavior {customer?}';

    /**
     * The console command description.
     */
    protected $description = 'Explain the billing system behavior and timeline';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📚 ISP Billing System - Behavior Explanation');
        $this->newLine();

        $customerName = $this->argument('customer');

        if ($customerName) {
            $this->explainSpecificCustomer($customerName);
        } else {
            $this->explainGeneralBehavior();
        }
    }

    /**
     * Explain general billing behavior.
     */
    protected function explainGeneralBehavior()
    {
        $this->info('🔄 How the Automated Billing System Works:');
        $this->newLine();

        $this->info('1. 📅 ELIGIBILITY CHECK:');
        $this->info('   - System looks for subscriptions where next_billing_date <= today');
        $this->info('   - Only active subscriptions with auto_billing_enabled = true');
        $this->info('   - This ensures customers are only billed when their billing cycle is due');
        $this->newLine();

        $this->info('2. 📄 INVOICE GENERATION:');
        $this->info('   - Creates invoice for the billing period');
        $this->info('   - Calculates pro-rated amounts if there were suspensions');
        $this->info('   - Sets due date based on grace period (usually 7 days)');
        $this->newLine();

        $this->info('3. 🔄 BILLING DATE UPDATE:');
        $this->info('   - AFTER generating invoice, updates next_billing_date to NEXT cycle');
        $this->info('   - For monthly billing: adds 1 month to the billing period end');
        $this->info('   - This prevents duplicate billing and sets up the next cycle');
        $this->newLine();

        $this->info('✅ EXPECTED BEHAVIOR:');
        $this->info('   - Customer has next_billing_date in the FUTURE after being billed');
        $this->info('   - Customer has recent invoice(s) for the current/past period');
        $this->info('   - This is NORMAL and CORRECT behavior');
        $this->newLine();

        $this->info('❌ PROBLEMATIC BEHAVIOR WOULD BE:');
        $this->info('   - Customer billed multiple times for the same period');
        $this->info('   - Customer billed when next_billing_date is far in the future');
        $this->info('   - Customer not billed when next_billing_date is in the past');
        $this->newLine();

        $this->info('💡 To see a specific customer example, run:');
        $this->info('   php artisan explain:billing-behavior "customer-name"');
    }

    /**
     * Explain behavior for a specific customer.
     */
    protected function explainSpecificCustomer($customerName)
    {
        $customer = Customer::where('name', 'like', "%{$customerName}%")->first();

        if (! $customer) {
            $this->error("Customer '{$customerName}' not found.");

            return;
        }

        $this->info("🔍 Billing Explanation for Customer: {$customer->name}");
        $this->newLine();

        $subscription = $customer->subscriptions()->first();
        if (! $subscription) {
            $this->warn('No subscriptions found for this customer.');

            return;
        }

        $recentInvoices = $customer->invoices()
            ->where('created_at', '>', Carbon::now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->get();

        $this->info('📋 Current Subscription Status:');
        $this->info("   Subscription ID: {$subscription->id}");
        $this->info("   Status: {$subscription->status}");
        $this->info('   Auto Billing: '.($subscription->auto_billing_enabled ? 'Enabled' : 'Disabled'));
        $this->info("   Billing Cycle: {$subscription->billing_cycle}");
        $this->info("   Next Billing Date: {$subscription->next_billing_date}");
        $this->info('   Last Billing Date: '.($subscription->last_billing_date ?? 'Never'));
        $this->newLine();

        $this->info('📄 Recent Invoices (Last 7 Days):');
        if ($recentInvoices->isEmpty()) {
            $this->info('   No recent invoices found.');
        } else {
            foreach ($recentInvoices as $invoice) {
                $this->info("   Invoice {$invoice->invoice_number}: \${$invoice->total_amount} - {$invoice->status} - {$invoice->created_at}");
            }
        }
        $this->newLine();

        // Analyze the situation
        $now = Carbon::now();
        $nextBillingDate = Carbon::parse($subscription->next_billing_date);
        $daysUntilBilling = $nextBillingDate->diffInDays($now, false);

        $this->info('🔍 Analysis:');

        if ($recentInvoices->isNotEmpty() && $nextBillingDate > $now) {
            $this->info('✅ NORMAL BEHAVIOR DETECTED:');
            $this->info("   - Customer was recently billed (has {$recentInvoices->count()} recent invoice(s))");
            $this->info("   - Next billing date is {$daysUntilBilling} days in the future");
            $this->info('   - This is expected after successful billing');
            $this->info('   - Customer will be billed again on: '.$nextBillingDate->toDateString());
        } elseif ($recentInvoices->isEmpty() && $nextBillingDate <= $now) {
            $this->warn('⚠️  NEEDS BILLING:');
            $this->info('   - No recent invoices found');
            $this->info('   - Next billing date is in the past or today');
            $this->info('   - This customer should be processed in the next billing run');
        } elseif ($recentInvoices->isNotEmpty() && $nextBillingDate <= $now) {
            $this->error('❌ POTENTIAL ISSUE:');
            $this->info('   - Customer has recent invoices');
            $this->info('   - But next billing date is still in the past');
            $this->info('   - This might indicate the billing date was not updated properly');
        } else {
            $this->info('ℹ️  WAITING FOR BILLING CYCLE:');
            $this->info('   - No recent invoices (normal if not in billing cycle)');
            $this->info('   - Next billing date is in the future');
            $this->info('   - Customer will be billed on: '.$nextBillingDate->toDateString());
        }
    }
}
