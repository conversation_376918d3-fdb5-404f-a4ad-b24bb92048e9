<?php

namespace App\Console\Commands;

use App\Models\Network\NetworkDevice;
use App\Services\MikrotikAddressListService;
use Illuminate\Console\Command;

class ManageSuspendedCustomersAddressList extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mikrotik:address-list
                            {action : Action to perform (setup|list|cleanup)}
                            {--device= : Specific device ID to operate on}
                            {--all : Apply to all MikroTik devices}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage suspended customers address list on MikroTik devices';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');
        $deviceId = $this->option('device');
        $allDevices = $this->option('all');

        if (! $deviceId && ! $allDevices) {
            $this->error('Please specify either --device=ID or --all');

            return 1;
        }

        $devices = $this->getDevices($deviceId, $allDevices);

        if ($devices->isEmpty()) {
            $this->error('No MikroTik devices found');

            return 1;
        }

        $addressListService = new MikrotikAddressListService;

        foreach ($devices as $device) {
            $this->info("Processing device: {$device->name} ({$device->ip_address})");

            switch ($action) {
                case 'setup':
                    $this->setupAddressList($device, $addressListService);
                    break;
                case 'list':
                    $this->listSuspendedIps($device, $addressListService);
                    break;
                case 'cleanup':
                    $this->cleanupLegacyRules($device);
                    break;
                default:
                    $this->error("Unknown action: {$action}");

                    return 1;
            }
        }

        return 0;
    }

    /**
     * Get devices based on options.
     */
    private function getDevices($deviceId, $allDevices)
    {
        if ($deviceId) {
            return NetworkDevice::where('id', $deviceId)
                ->where(function ($query) {
                    $query->where('type', 'mikrotik')->orWhere('type', 'router');
                })
                ->get();
        }

        return NetworkDevice::where('type', 'mikrotik')
            ->orWhere('type', 'router')
            ->get();
    }

    /**
     * Setup address list and master rule on device.
     */
    private function setupAddressList(NetworkDevice $device, MikrotikAddressListService $service)
    {
        $this->line('  Setting up suspended customers address list...');

        if ($service->ensureAddressListExists($device)) {
            $this->info('  ✓ Address list created/verified');
        } else {
            $this->error('  ✗ Failed to create address list');

            return;
        }

        if ($service->ensureMasterFirewallRuleExists($device)) {
            $this->info('  ✓ Master firewall rule created/verified');
        } else {
            $this->error('  ✗ Failed to create master firewall rule');
        }
    }

    /**
     * List suspended IPs on device.
     */
    private function listSuspendedIps(NetworkDevice $device, MikrotikAddressListService $service)
    {
        $suspendedIps = $service->getSuspendedIpAddresses($device);

        if (empty($suspendedIps)) {
            $this->line('  No suspended IPs found');

            return;
        }

        $this->line('  Suspended IP addresses:');
        foreach ($suspendedIps as $entry) {
            $this->line("    - {$entry['address']} ({$entry['comment']})");
        }
    }

    /**
     * Cleanup legacy individual firewall rules.
     */
    private function cleanupLegacyRules(NetworkDevice $device)
    {
        $this->line('  Cleaning up legacy firewall rules...');

        try {
            // Find individual blocking rules that should be replaced by address list
            $legacyRules = $device->executeMikrotikCommand('/ip/firewall/filter/print', [
                '?chain' => 'forward',
                '?action' => 'drop',
                '?comment' => 'SUSPENDED:*',
            ]);

            if (empty($legacyRules)) {
                $this->line('  No legacy rules found');

                return;
            }

            $this->line('  Found '.count($legacyRules).' legacy rules to remove');

            foreach ($legacyRules as $rule) {
                $device->executeMikrotikCommand('/ip/firewall/filter/remove', [
                    '.id' => $rule['.id'],
                ]);

                $this->line("    Removed rule: {$rule['comment']}");
            }

            $this->info('  ✓ Cleanup completed');
        } catch (\Exception $e) {
            $this->error('  ✗ Cleanup failed: '.$e->getMessage());
        }
    }
}
