<?php

namespace App\Console\Commands;

use App\Models\Services\StaticIpService;
use App\Services\MikrotikAddressListService;
use Illuminate\Console\Command;

class TestAddressListSuspension extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:address-list-suspension {service_id : Static IP service ID to test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the new address list suspension system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $serviceId = $this->argument('service_id');

        $service = StaticIpService::with(['customer', 'device'])->find($serviceId);

        if (! $service) {
            $this->error("Static IP service with ID {$serviceId} not found");

            return 1;
        }

        $this->info('Testing address list suspension for:');
        $this->line("  Service ID: {$service->id}");
        $this->line("  Customer: {$service->customer->name}");
        $this->line("  IP Address: {$service->ip_address}");
        $this->line("  Device: {$service->device->name} ({$service->device->ip_address})");
        $this->line("  Current Status: {$service->status}");
        $this->newLine();

        $addressListService = new MikrotikAddressListService;
        $device = $service->device;

        // Test 1: Setup address list and master rule
        $this->info('🔧 Step 1: Setting up address list infrastructure...');

        if ($addressListService->ensureAddressListExists($device)) {
            $this->info('  ✓ Address list exists/created');
        } else {
            $this->error('  ✗ Failed to create address list');

            return 1;
        }

        if ($addressListService->ensureMasterFirewallRuleExists($device)) {
            $this->info('  ✓ Master firewall rule exists/created');
        } else {
            $this->error('  ✗ Failed to create master firewall rule');

            return 1;
        }

        // Test 2: Check current suspension status
        $this->info('🔍 Step 2: Checking current suspension status...');
        $isSuspended = $addressListService->isIpSuspended($device, $service->ip_address);
        $this->line('  IP suspension status: '.($isSuspended ? 'SUSPENDED' : 'ACTIVE'));

        // Test 3: Suspend if active, or activate if suspended
        if ($service->status === 'active') {
            $this->info('🚫 Step 3: Testing suspension...');

            $success = $addressListService->addIpToSuspendedList(
                $device,
                $service->ip_address,
                $service->customer->name
            );

            if ($success) {
                $this->info('  ✓ IP added to suspended list');

                // Verify it's in the list
                $isSuspended = $addressListService->isIpSuspended($device, $service->ip_address);
                $this->line('  Verification: '.($isSuspended ? 'SUSPENDED ✓' : 'FAILED ✗'));
            } else {
                $this->error('  ✗ Failed to add IP to suspended list');
            }

        } else {
            $this->info('✅ Step 3: Testing activation...');

            $success = $addressListService->removeIpFromSuspendedList(
                $device,
                $service->ip_address,
                $service->customer->name
            );

            if ($success) {
                $this->info('  ✓ IP removed from suspended list');

                // Verify it's not in the list
                $isSuspended = $addressListService->isIpSuspended($device, $service->ip_address);
                $this->line('  Verification: '.(! $isSuspended ? 'ACTIVE ✓' : 'FAILED ✗'));
            } else {
                $this->error('  ✗ Failed to remove IP from suspended list');
            }
        }

        // Test 4: List all suspended IPs
        $this->info('📋 Step 4: Listing all suspended IPs...');
        $suspendedIps = $addressListService->getSuspendedIpAddresses($device);

        if (empty($suspendedIps)) {
            $this->line('  No suspended IPs found');
        } else {
            $this->line('  Found '.count($suspendedIps).' suspended IPs:');
            foreach ($suspendedIps as $entry) {
                $this->line("    - {$entry['address']} ({$entry['comment']})");
            }
        }

        $this->newLine();
        $this->info('🎉 Address list suspension test completed!');

        return 0;
    }
}
