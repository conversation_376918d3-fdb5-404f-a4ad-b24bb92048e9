<?php

namespace App\Console\Commands;

use App\Models\Services\StaticIpService;
use App\Services\MikrotikAddressListService;
use Illuminate\Console\Command;

class TestSimplifiedSuspension extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:simplified-suspension {service_id : Static IP service ID to test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the simplified suspension system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $serviceId = $this->argument('service_id');

        $service = StaticIpService::with(['customer', 'device'])->find($serviceId);

        if (! $service) {
            $this->error("Static IP service with ID {$serviceId} not found");

            return 1;
        }

        $this->info('🚀 Testing simplified suspension system for:');
        $this->line("  Service ID: {$service->id}");
        $this->line("  Customer: {$service->customer->name}");
        $this->line("  IP Address: {$service->ip_address}");
        $this->line("  Current Status: {$service->status}");
        $this->line('  Current MikroTik ID: '.($service->mikrotik_id ?? 'null'));
        $this->newLine();

        $addressListService = new MikrotikAddressListService;
        $device = $service->device;

        if ($service->status === 'active') {
            $this->info('🔴 Testing Suspension...');

            $startTime = microtime(true);
            $mikrotikId = $addressListService->suspendCustomer(
                $device,
                $service->ip_address,
                $service->customer->name
            );
            $endTime = microtime(true);

            $duration = ($endTime - $startTime) * 1000;

            if ($mikrotikId) {
                $this->info('  ✓ Suspended successfully in '.number_format($duration, 2).'ms');
                $this->line("  MikroTik ID: {$mikrotikId}");

                // Update service
                $service->mikrotik_id = $mikrotikId;
                $service->status = 'suspended';
                $service->save();

                $this->line('  ✓ Database updated');
            } else {
                $this->error('  ✗ Suspension failed');

                return 1;
            }

        } elseif ($service->status === 'suspended') {
            $this->info('🟢 Testing Reactivation...');

            if (! $service->mikrotik_id) {
                $this->error('  ✗ No MikroTik ID stored - cannot reactivate');

                return 1;
            }

            $startTime = microtime(true);
            $success = $addressListService->reactivateCustomer(
                $device,
                $service->mikrotik_id,
                $service->customer->name
            );
            $endTime = microtime(true);

            $duration = ($endTime - $startTime) * 1000;

            if ($success) {
                $this->info('  ✓ Reactivated successfully in '.number_format($duration, 2).'ms');

                // Update service
                $service->mikrotik_id = null;
                $service->status = 'active';
                $service->save();

                $this->line('  ✓ Database updated');
            } else {
                $this->error('  ✗ Reactivation failed');

                return 1;
            }
        } else {
            $this->warn("Service status is '{$service->status}' - cannot test suspension/reactivation");

            return 1;
        }

        // Show current suspended list
        $this->newLine();
        $this->info('📋 Current suspended IPs:');
        $suspendedIps = $addressListService->getSuspendedIpAddresses($device);

        if (empty($suspendedIps)) {
            $this->line('  No suspended IPs found');
        } else {
            foreach ($suspendedIps as $entry) {
                $this->line("  - {$entry['address']} ({$entry['comment']})");
            }
        }

        $this->newLine();
        $this->info('🎉 Simplified suspension test completed successfully!');

        return 0;
    }
}
