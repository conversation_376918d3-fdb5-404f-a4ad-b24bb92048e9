<?php

namespace App\Console\Commands;

use App\Models\Network\IpAddress;
use App\Models\Services\IpPool;
use Illuminate\Console\Command;

class CreateIpAddresses extends Command
{
    protected $signature = 'ip:create-addresses {pool_id?}';

    protected $description = 'Create IP addresses for IP pools';

    public function handle()
    {
        $poolId = $this->argument('pool_id');

        if ($poolId) {
            $pools = IpPool::where('id', $poolId)->get();
        } else {
            $pools = IpPool::all();
        }

        foreach ($pools as $pool) {
            $this->info("Processing pool: {$pool->name} ({$pool->network_address})");

            if ($pool->addresses_created) {
                $this->warn("Pool {$pool->name} already has addresses created. Skipping.");

                continue;
            }

            // Parse network address (e.g., *************/24)
            [$network, $cidr] = explode('/', $pool->network_address);
            $networkParts = explode('.', $network);
            $baseNetwork = $networkParts[0].'.'.$networkParts[1].'.'.$networkParts[2];

            $created = 0;
            $this->info("Creating IP addresses from {$baseNetwork}.2 to {$baseNetwork}.254...");

            // Create IP addresses from .2 to .254 (excluding .0, .1, .255)
            for ($i = 2; $i <= 254; $i++) {
                $ipAddress = $baseNetwork.'.'.$i;

                // Check if IP already exists
                $exists = IpAddress::where('ip_address', $ipAddress)->exists();
                if (! $exists) {
                    IpAddress::create([
                        'ip_address' => $ipAddress,
                        'ip_pool_id' => $pool->id,
                        'status' => 'available',
                    ]);
                    $created++;
                }
            }

            $this->info("Created {$created} IP addresses");

            // Update pool statistics
            $pool->update([
                'addresses_created' => true,
                'total_addresses' => 253,
                'available_addresses' => $created,
                'assigned_addresses' => 0,
            ]);

            $this->info("Pool {$pool->name} updated successfully");
        }

        $this->info('IP address creation completed!');
    }
}
