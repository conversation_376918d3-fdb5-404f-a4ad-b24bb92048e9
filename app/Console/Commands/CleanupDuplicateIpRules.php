<?php

namespace App\Console\Commands;

use App\Models\Network\NetworkDevice;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupDuplicateIpRules extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mikrotik:cleanup-duplicate-ip-rules {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up duplicate firewall rules for the same IP addresses on MikroTik devices';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('DRY RUN MODE - No changes will be made');
        }

        // Get all MikroTik devices
        $devices = NetworkDevice::where('type', 'mikrotik')->get();

        $this->info("Found {$devices->count()} MikroTik devices");

        foreach ($devices as $device) {
            $this->info("Processing device: {$device->name} ({$device->ip_address})");

            try {
                // Get all firewall filter rules
                $rules = $device->executeMikrotikCommand('/ip/firewall/filter/print');

                $this->info('  Found {'.count($rules).'} firewall rules');

                // Group rules by IP address and action to find duplicates
                $ipGroups = [];
                foreach ($rules as $rule) {
                    if (isset($rule['src-address']) && isset($rule['action']) && isset($rule['chain'])) {
                        $key = $rule['src-address'].'|'.$rule['action'].'|'.$rule['chain'];
                        if (! isset($ipGroups[$key])) {
                            $ipGroups[$key] = [];
                        }
                        $ipGroups[$key][] = $rule;
                    }
                }

                $duplicatesFound = 0;
                $duplicatesRemoved = 0;

                foreach ($ipGroups as $key => $groupRules) {
                    if (count($groupRules) > 1) {
                        $duplicatesFound += count($groupRules) - 1;

                        [$srcAddress, $action, $chain] = explode('|', $key);
                        $this->warn('  Found '.count($groupRules)." duplicate rules for: {$srcAddress} {$action} in {$chain}");

                        // Keep the first rule (usually the oldest), remove the rest
                        $keepRule = array_shift($groupRules);
                        $this->line("    Keeping rule: #{$keepRule['.id']} (comment: ".($keepRule['comment'] ?? 'none').')');

                        foreach ($groupRules as $duplicateRule) {
                            $this->line("    Removing duplicate: #{$duplicateRule['.id']} (comment: ".($duplicateRule['comment'] ?? 'none').')');

                            if (! $dryRun) {
                                try {
                                    $device->executeMikrotikCommand('/ip/firewall/filter/remove', [
                                        '.id' => $duplicateRule['.id'],
                                    ]);
                                    $duplicatesRemoved++;
                                    $this->info('      ✅ Removed successfully');
                                } catch (\Exception $e) {
                                    $this->error('      ❌ Failed to remove: '.$e->getMessage());
                                }
                            }
                        }

                        // Update the kept rule with a proper comment if it doesn't have one
                        if (! isset($keepRule['comment']) || empty($keepRule['comment'])) {
                            if ($action === 'drop' && ! $dryRun) {
                                try {
                                    $device->executeMikrotikCommand('/ip/firewall/filter/set', [
                                        '.id' => $keepRule['.id'],
                                        'comment' => "Blocking rule for {$srcAddress}",
                                    ]);
                                    $this->info('    Updated comment for kept rule');
                                } catch (\Exception $e) {
                                    $this->error('    Failed to update comment: '.$e->getMessage());
                                }
                            }
                        }
                    }
                }

                if ($duplicatesFound > 0) {
                    if ($dryRun) {
                        $this->warn("  Would remove {$duplicatesFound} duplicate rules");
                    } else {
                        $this->info("  Removed {$duplicatesRemoved} of {$duplicatesFound} duplicate rules");
                    }
                } else {
                    $this->info('  ✅ No duplicate rules found');
                }

            } catch (\Exception $e) {
                $this->error('  ❌ Error connecting to device: '.$e->getMessage());
                Log::error('Failed to connect to MikroTik device for cleanup', [
                    'device_id' => $device->id,
                    'device_name' => $device->name,
                    'error' => $e->getMessage(),
                ]);
            }

            $this->line('');
        }

        if ($dryRun) {
            $this->info('DRY RUN COMPLETED - Run without --dry-run to make actual changes');
        } else {
            $this->info('CLEANUP COMPLETED');
        }

        return 0;
    }
}
