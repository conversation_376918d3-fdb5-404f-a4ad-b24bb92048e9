<?php

namespace App\Console\Commands;

use App\Jobs\ProcessMassCustomerSuspensions;
use App\Models\Invoice;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SuspendOverdueCustomersCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'customers:suspend-overdue
                            {--dry-run : Show what would be suspended without actually suspending}
                            {--force : Skip confirmation prompt for automated runs}
                            {--site= : Limit to specific site ID}
                            {--device= : Limit to specific device ID}
                            {--grace-days=0 : Additional grace period in days beyond due date}';

    /**
     * The console command description.
     */
    protected $description = 'Automatically suspend customers with overdue invoices, grouped by site/device/service type for efficient processing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $siteFilter = $this->option('site');
        $deviceFilter = $this->option('device');
        $graceDays = (int) $this->option('grace-days');

        $this->info('🔍 Mass Customer Suspension System');
        $this->info('=====================================');
        $this->newLine();

        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No customers will be suspended');
        }

        $this->info('📅 Processing overdue invoices as of: '.now()->toDateString());
        $this->info("⏰ Grace period: {$graceDays} days");
        $this->newLine();

        // Calculate the effective due date with grace period
        $effectiveDueDate = now()->subDays($graceDays)->toDateString();

        // Get overdue invoices with customer and subscription relationships
        $overdueInvoices = $this->getOverdueInvoices($effectiveDueDate, $siteFilter);

        if ($overdueInvoices->isEmpty()) {
            $this->info('✅ No overdue invoices found. All customers are up to date!');

            return 0;
        }

        $this->info("📊 Found {$overdueInvoices->count()} overdue invoices");

        // Group customers by site, device, and service type for efficient processing
        $suspensionGroups = $this->groupCustomersForSuspension($overdueInvoices, $deviceFilter);

        $this->displaySuspensionSummary($suspensionGroups);

        if (! $dryRun) {
            // Skip confirmation if --force flag is used (for automated runs)
            if (! $force && ! $this->confirm('Do you want to proceed with suspending these customers?')) {
                $this->info('Operation cancelled.');

                return 0;
            }

            if ($force) {
                $this->info('🤖 AUTOMATED RUN: Proceeding with suspensions (--force flag enabled)');
            }

            $this->processSuspensions($suspensionGroups);
        } else {
            $this->info('🔍 DRY RUN: Would process suspensions for the groups shown above');
        }

        return 0;
    }

    /**
     * Get overdue invoices with related data.
     */
    protected function getOverdueInvoices(string $effectiveDueDate, ?string $siteFilter): \Illuminate\Database\Eloquent\Collection
    {
        $query = Invoice::with([
            'customer',
            'subscription.staticIpService.device.site',
            'subscription.pppoeService.device.site',
            'subscription.networkSite',
        ])
            ->where('status', 'pending')
            ->where('due_date', '<=', $effectiveDueDate)
            ->whereHas('customer', function ($q) {
                $q->where('status', 'active');
            })
            ->whereHas('subscription', function ($q) {
                $q->where('status', 'active');
            });

        if ($siteFilter) {
            $query->whereHas('subscription', function ($q) use ($siteFilter) {
                $q->where('network_site_id', $siteFilter);
            });
        }

        return $query->get();
    }

    /**
     * Group customers by site, device, and service type for efficient batch processing.
     */
    protected function groupCustomersForSuspension($overdueInvoices, ?string $deviceFilter): array
    {
        $groups = [];

        foreach ($overdueInvoices as $invoice) {
            $subscription = $invoice->subscription;

            // Determine service type and device
            $staticIpService = $subscription->staticIpService;
            $pppoeService = $subscription->pppoeService;

            if ($staticIpService) {
                $device = $staticIpService->device;
                $serviceType = 'static_ip';
                $serviceId = $staticIpService->id;
            } elseif ($pppoeService) {
                $device = $pppoeService->device;
                $serviceType = 'pppoe';
                $serviceId = $pppoeService->id;
            } else {
                Log::warning("No service found for subscription {$subscription->id}");

                continue;
            }

            if (! $device) {
                Log::warning("No device found for subscription {$subscription->id}");

                continue;
            }

            // Apply device filter if specified
            if ($deviceFilter && $device->id != $deviceFilter) {
                continue;
            }

            $site = $device->site;
            if (! $site) {
                Log::warning("No site found for device {$device->id}");

                continue;
            }

            // Create grouping key: site_id -> device_id -> service_type
            $siteId = $site->id;
            $deviceId = $device->id;

            if (! isset($groups[$siteId])) {
                $groups[$siteId] = [
                    'site' => $site,
                    'devices' => [],
                ];
            }

            if (! isset($groups[$siteId]['devices'][$deviceId])) {
                $groups[$siteId]['devices'][$deviceId] = [
                    'device' => $device,
                    'service_types' => [],
                ];
            }

            if (! isset($groups[$siteId]['devices'][$deviceId]['service_types'][$serviceType])) {
                $groups[$siteId]['devices'][$deviceId]['service_types'][$serviceType] = [
                    'services' => [],
                    'customers' => [],
                ];
            }

            // Add to the appropriate group
            $groups[$siteId]['devices'][$deviceId]['service_types'][$serviceType]['services'][] = [
                'service_id' => $serviceId,
                'customer_id' => $invoice->customer_id,
                'subscription_id' => $subscription->id,
                'invoice_id' => $invoice->id,
                'customer_name' => $invoice->customer->name,
                'due_date' => $invoice->due_date,
                'amount' => $invoice->total_amount,
            ];

            $groups[$siteId]['devices'][$deviceId]['service_types'][$serviceType]['customers'][] = $invoice->customer->name;
        }

        return $groups;
    }

    /**
     * Display suspension summary.
     */
    protected function displaySuspensionSummary(array $suspensionGroups): void
    {
        $this->newLine();
        $this->info('📋 Suspension Summary by Site/Device/Service Type:');
        $this->newLine();

        $totalCustomers = 0;

        foreach ($suspensionGroups as $siteId => $siteGroup) {
            $site = $siteGroup['site'];
            $this->info("🏢 Site: {$site->name} (ID: {$site->id})");

            foreach ($siteGroup['devices'] as $deviceId => $deviceGroup) {
                $device = $deviceGroup['device'];
                $this->info("  📡 Device: {$device->name} ({$device->ip_address})");

                foreach ($deviceGroup['service_types'] as $serviceType => $serviceGroup) {
                    $count = count($serviceGroup['services']);
                    $totalCustomers += $count;

                    $serviceTypeLabel = $serviceType === 'static_ip' ? 'Static IP' : 'PPPoE';
                    $this->info("    🔧 {$serviceTypeLabel}: {$count} customers");

                    // Show first few customer names
                    $customerNames = array_unique($serviceGroup['customers']);
                    $displayNames = array_slice($customerNames, 0, 3);
                    if (count($customerNames) > 3) {
                        $displayNames[] = '... and '.(count($customerNames) - 3).' more';
                    }
                    $this->line('      👥 '.implode(', ', $displayNames));
                }
            }
            $this->newLine();
        }

        $this->info("📊 Total customers to suspend: {$totalCustomers}");
    }

    /**
     * Process suspensions using queue-based jobs.
     */
    protected function processSuspensions(array $suspensionGroups): void
    {
        $this->info('🚀 Dispatching suspension jobs...');
        $this->newLine();

        $jobCount = 0;

        foreach ($suspensionGroups as $siteId => $siteGroup) {
            foreach ($siteGroup['devices'] as $deviceId => $deviceGroup) {
                foreach ($deviceGroup['service_types'] as $serviceType => $serviceGroup) {
                    // Dispatch a job for each device/service type combination
                    ProcessMassCustomerSuspensions::dispatch(
                        $deviceId,
                        $serviceType,
                        $serviceGroup['services']
                    );

                    $jobCount++;
                    $count = count($serviceGroup['services']);
                    $serviceTypeLabel = $serviceType === 'static_ip' ? 'Static IP' : 'PPPoE';

                    $this->info("✅ Dispatched job for {$deviceGroup['device']->name} - {$serviceTypeLabel} ({$count} customers)");
                }
            }
        }

        $this->newLine();
        $this->info("🎯 Dispatched {$jobCount} suspension jobs to queue");
        $this->info('⏳ Jobs will be processed asynchronously');
        $this->info('📝 Check logs for detailed suspension results');

        Log::info('Mass customer suspension initiated', [
            'jobs_dispatched' => $jobCount,
            'total_customers' => array_sum(array_map(function ($site) {
                return array_sum(array_map(function ($device) {
                    return array_sum(array_map(function ($serviceType) {
                        return count($serviceType['services']);
                    }, $device['service_types']));
                }, $site['devices']));
            }, $suspensionGroups)),
            'initiated_by' => 'automated_command',
            'timestamp' => now(),
        ]);
    }
}
