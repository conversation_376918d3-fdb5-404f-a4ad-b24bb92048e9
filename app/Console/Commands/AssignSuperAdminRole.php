<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class AssignSuperAdminRole extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:make-super-admin {email : The email of the user to make super admin}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign Super Admin role to a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        $user = User::where('email', $email)->first();

        if (! $user) {
            $this->error("User with email {$email} not found.");

            return 1;
        }

        $superAdminRole = Role::where('name', 'Super Admin')->first();

        if (! $superAdminRole) {
            $this->error('Super Admin role not found. Please run the RolePermissionSeeder first.');

            return 1;
        }

        if ($user->hasRole('Super Admin')) {
            $this->info("User {$user->name} is already a Super Admin.");

            return 0;
        }

        $user->assignRole('Super Admin');

        $this->info("Successfully assigned Super Admin role to {$user->name} ({$user->email}).");

        return 0;
    }
}
