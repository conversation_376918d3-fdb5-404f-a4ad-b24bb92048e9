<?php

namespace App\Console\Commands;

use App\Models\Network\NetworkDevice;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupDuplicateFirewallRules extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mikrotik:cleanup-duplicate-rules {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up duplicate firewall rules on MikroTik devices';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('DRY RUN MODE - No changes will be made');
        }

        // Get all MikroTik devices
        $devices = NetworkDevice::where('type', 'mikrotik')->get();

        $this->info("Found {$devices->count()} MikroTik devices");

        foreach ($devices as $device) {
            $this->info("Processing device: {$device->name} ({$device->ip_address})");

            try {
                // Get all firewall filter rules
                $rules = $device->executeMikrotikCommand('/ip/firewall/filter/print');

                $this->info('  Found {'.count($rules).'} firewall rules');

                // Group rules by src-address and comment to find duplicates
                $ruleGroups = [];
                foreach ($rules as $rule) {
                    if (isset($rule['comment']) && strpos($rule['comment'], 'SUSPENDED:') !== false) {
                        $key = ($rule['src-address'] ?? '').'|'.($rule['comment'] ?? '');
                        if (! isset($ruleGroups[$key])) {
                            $ruleGroups[$key] = [];
                        }
                        $ruleGroups[$key][] = $rule;
                    }
                }

                $duplicatesFound = 0;
                $duplicatesRemoved = 0;

                foreach ($ruleGroups as $key => $groupRules) {
                    if (count($groupRules) > 1) {
                        $duplicatesFound += count($groupRules) - 1;

                        $this->warn('  Found '.count($groupRules)." duplicate rules for: {$key}");

                        // Keep the first rule, remove the rest
                        $keepRule = array_shift($groupRules);
                        $this->line("    Keeping rule: {$keepRule['.id']} (disabled: ".($keepRule['disabled'] ?? 'no').')');

                        foreach ($groupRules as $duplicateRule) {
                            $this->line("    Removing duplicate: {$duplicateRule['.id']} (disabled: ".($duplicateRule['disabled'] ?? 'no').')');

                            if (! $dryRun) {
                                try {
                                    $device->executeMikrotikCommand('/ip/firewall/filter/remove', [
                                        '.id' => $duplicateRule['.id'],
                                    ]);
                                    $duplicatesRemoved++;
                                    $this->info('      ✅ Removed successfully');
                                } catch (\Exception $e) {
                                    $this->error('      ❌ Failed to remove: '.$e->getMessage());
                                }
                            }
                        }
                    }
                }

                if ($duplicatesFound > 0) {
                    if ($dryRun) {
                        $this->warn("  Would remove {$duplicatesFound} duplicate rules");
                    } else {
                        $this->info("  Removed {$duplicatesRemoved} of {$duplicatesFound} duplicate rules");
                    }
                } else {
                    $this->info('  ✅ No duplicate rules found');
                }

            } catch (\Exception $e) {
                $this->error('  ❌ Error connecting to device: '.$e->getMessage());
                Log::error('Failed to connect to MikroTik device for cleanup', [
                    'device_id' => $device->id,
                    'device_name' => $device->name,
                    'error' => $e->getMessage(),
                ]);
            }

            $this->line('');
        }

        if ($dryRun) {
            $this->info('DRY RUN COMPLETED - Run without --dry-run to make actual changes');
        } else {
            $this->info('CLEANUP COMPLETED');
        }

        return 0;
    }
}
