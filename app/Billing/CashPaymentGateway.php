<?php

namespace App\Billing;

use App\Contracts\PaymentGatewayInterface;
use App\Models\Invoice;
use App\Models\Payment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CashPaymentGateway implements PaymentGatewayInterface
{
    /**
     * Process a cash payment for an invoice.
     */
    public function processPayment(Invoice $invoice, array $paymentData): array
    {
        try {
            DB::beginTransaction();

            // Validate payment data
            $validation = $this->validatePayment($paymentData, $invoice);
            if (! $validation['success']) {
                return $validation;
            }

            // Create payment record with completed status (cash payments are immediately confirmed)
            $payment = Payment::create([
                'invoice_id' => $invoice->id,
                'payment_method' => 'cash',
                'amount' => $paymentData['amount'],
                'reference_number' => $paymentData['reference_number'] ?? null,
                'status' => 'completed',
                'payment_date' => $paymentData['payment_date'] ?? now(),
                'confirmed_by' => Auth::id(),
                'notes' => $paymentData['notes'] ?? null,
                'gateway_response' => [
                    'gateway' => 'cash',
                    'processed_at' => now()->toISOString(),
                    'confirmed_by_user' => Auth::id(),
                ],
            ]);

            // Update invoice status based on payments
            $invoice->updateStatusBasedOnPayments();

            DB::commit();

            Log::info('Cash payment processed successfully', [
                'payment_id' => $payment->id,
                'invoice_id' => $invoice->id,
                'amount' => $paymentData['amount'],
                'confirmed_by' => Auth::id(),
                'invoice_status' => $invoice->fresh()->status,
            ]);

            return [
                'success' => true,
                'payment_id' => $payment->id,
                'status' => 'completed',
                'message' => 'Cash payment recorded successfully.',
                'invoice_status' => $invoice->fresh()->status,
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cash payment processing failed', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process cash payment: '.$e->getMessage(),
            ];
        }
    }

    /**
     * Validate payment data before processing.
     */
    public function validatePayment(array $paymentData, ?Invoice $invoice = null): array
    {
        $errors = [];

        // Validate amount
        if (! isset($paymentData['amount']) || ! is_numeric($paymentData['amount']) || $paymentData['amount'] <= 0) {
            $errors[] = 'Payment amount must be a positive number.';
        }

        // For cash payments, amount must equal the full remaining balance (no partial payments)
        if ($invoice && isset($paymentData['amount'])) {
            $remainingBalance = $invoice->remaining_balance;
            if (abs($paymentData['amount'] - $remainingBalance) > 0.01) { // Allow for small floating point differences
                $errors[] = 'Payment amount must equal the full remaining balance of '.number_format($remainingBalance, 2).'. Partial payments are not supported.';
            }
        }

        // Validate payment date
        if (isset($paymentData['payment_date'])) {
            try {
                $date = \Carbon\Carbon::parse($paymentData['payment_date']);
                if ($date->isFuture()) {
                    $errors[] = 'Payment date cannot be in the future.';
                }
            } catch (\Exception $e) {
                $errors[] = 'Invalid payment date format.';
            }
        }

        // Validate invoice status
        if ($invoice && $invoice->status === 'paid') {
            $errors[] = 'This invoice is already paid.';
        }

        // Validate user authentication
        if (! Auth::check()) {
            $errors[] = 'You must be logged in to record payments.';
        }

        return [
            'success' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Get the status of a payment.
     */
    public function getPaymentStatus(string $transactionId): array
    {
        $payment = Payment::where('transaction_id', $transactionId)
            ->where('payment_method', 'cash')
            ->first();

        if (! $payment) {
            return [
                'success' => false,
                'message' => 'Payment not found.',
            ];
        }

        return [
            'success' => true,
            'status' => $payment->status,
            'payment_id' => $payment->id,
            'amount' => $payment->amount,
            'payment_date' => $payment->payment_date,
        ];
    }

    /**
     * Refund a payment.
     */
    public function refundPayment(Payment $payment, ?float $amount = null): array
    {
        if ($payment->payment_method !== 'cash') {
            return [
                'success' => false,
                'message' => 'This payment was not made via cash.',
            ];
        }

        if ($payment->status !== 'completed') {
            return [
                'success' => false,
                'message' => 'Only completed payments can be refunded.',
            ];
        }

        $refundAmount = $amount ?? $payment->amount;

        if ($refundAmount > $payment->amount) {
            return [
                'success' => false,
                'message' => 'Refund amount cannot exceed the original payment amount.',
            ];
        }

        try {
            DB::beginTransaction();

            // Update payment status
            $payment->status = 'refunded';
            $payment->notes = ($payment->notes ?? '')."\nRefunded: $refundAmount on ".now()->toDateString();
            $payment->save();

            // Update invoice status if needed
            $payment->invoice->updateStatusBasedOnPayments();

            DB::commit();

            Log::info('Cash payment refunded successfully', [
                'payment_id' => $payment->id,
                'refund_amount' => $refundAmount,
            ]);

            return [
                'success' => true,
                'message' => 'Cash payment refunded successfully.',
                'refund_amount' => $refundAmount,
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cash payment refund failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to refund payment: '.$e->getMessage(),
            ];
        }
    }

    /**
     * Get the payment method name.
     */
    public function getPaymentMethod(): string
    {
        return 'cash';
    }

    /**
     * Check if the gateway supports automatic processing.
     */
    public function supportsAutomaticProcessing(): bool
    {
        return true; // Cash payments are processed immediately
    }

    /**
     * Confirm a cash payment (admin action).
     *
     * @deprecated This method is deprecated as cash payments are now processed immediately.
     */
    public function confirmPayment(Payment $payment, int $confirmedBy): array
    {
        if ($payment->payment_method !== 'cash') {
            return [
                'success' => false,
                'message' => 'This payment was not made via cash.',
            ];
        }

        if ($payment->status === 'completed') {
            return [
                'success' => true,
                'message' => 'Payment is already completed.',
                'payment_id' => $payment->id,
            ];
        }

        if ($payment->status !== 'pending') {
            return [
                'success' => false,
                'message' => 'Only pending payments can be confirmed.',
            ];
        }

        try {
            DB::beginTransaction();

            // Update payment status
            $payment->status = 'completed';
            $payment->confirmed_by = $confirmedBy;
            $payment->payment_date = now();
            $payment->save();

            // Update invoice status
            $payment->invoice->updateStatusBasedOnPayments();

            DB::commit();

            Log::info('Cash payment confirmed successfully (legacy method)', [
                'payment_id' => $payment->id,
                'confirmed_by' => $confirmedBy,
            ]);

            return [
                'success' => true,
                'message' => 'Cash payment confirmed successfully.',
                'payment_id' => $payment->id,
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cash payment confirmation failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to confirm payment: '.$e->getMessage(),
            ];
        }
    }
}
