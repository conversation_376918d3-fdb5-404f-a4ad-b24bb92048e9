<?php

namespace App\Billing;

use App\Contracts\PaymentGatewayInterface;
use App\Jobs\PaymentConfirmation;
use App\Models\Customer;
use App\Models\Income;
use App\Models\Invoice;
use App\Models\Payment;
use Carbon\Carbon;
use Http;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MpesaPaymentGateway implements PaymentGatewayInterface
{
    public function token()
    {
        $response = Http::withBasicAuth(
            config('services.mpesa.key'),
            config('services.mpesa.secret'),
        )
            ->get(config('services.mpesa.tokenUrl'))
            ->json(['access_token']);

        return $response;
    }

    public function registerUrl()
    {
        return Http::withToken($this->token())
            ->post(config('services.mpesa.registerUrl'), [
                'ValidationURL' => env('APP_URL').'/validation',
                'ConfirmationURL' => env('APP_URL').'/confirmation',
                'ResponseType' => 'completed',
                'ShortCode' => config('services.mpesa.shortCode'),
            ])->json();
    }

    public function validation()
    {
        $accountNumber = preg_replace('/\s+/', '', request('BillRefNumber'));

        $customer = Customer::where('mpesaId', $accountNumber)->first();

        if (! $customer) {
            return response()->json([
                'ResultCode' => 'C2B00012',
                'ResultDesc' => 'Rejected',
            ]);
        }

        return response()->json([
            'ResultCode' => 0,
            'ResultDesc' => 'Accepted',
        ]);
    }

    public function formatDate($transTime)
    {
        $parsedDateTime = Carbon::createFromFormat('YmdHis', $transTime);

        $formattedDateTime = $parsedDateTime->format('d-M-Y H:i:s');

        return $formattedDateTime;
    }

    public function confirmation()
    {
        $accountNumber = preg_replace('/\s+/', '', request('BillRefNumber'));
        $customer = Customer::where('mpesa_id', $accountNumber)->first();
        $formattedDate = $this->formatDate(request('TransTime'));

        // get the pending invoice and mark as paid
        $invoice = $customer->invoices()->where('status', 'pending')->first();

        if ($invoice) {
            // Create payment record for M-Pesa confirmation
            $payment = Payment::create([
                'invoice_id' => $invoice->id,
                'payment_method' => 'mpesa',
                'amount' => request('TransAmount'),
                'transaction_id' => request('TransID'),
                'status' => 'completed',
                'payment_date' => $formattedDate,
                'phone_number' => request('MSISDN'),
                'mpesa_receipt_number' => request('TransID'),
                'gateway_response' => [
                    'gateway' => 'mpesa',
                    'transaction_id' => request('TransID'),
                    'phone_number' => request('MSISDN'),
                    'first_name' => request('FirstName'),
                    'bill_ref_number' => request('BillRefNumber'),
                    'processed_at' => now()->toISOString(),
                ],
            ]);

            // Update invoice status based on payments
            $invoice->updateStatusBasedOnPayments();
        }

        // (new Income)->make(
        //     $customer,
        //     request('TransID'),
        //     $formattedDate,
        //     request('FirstName'),
        //     request('TransAmount'),
        //     request('MSISDN'),
        //     request('BillRefNumber')
        // );

        // PaymentConfirmation::dispatch($customer);

        return 'done';
    }

    /**
     * Process an M-Pesa payment for an invoice.
     */
    public function processPayment(Invoice $invoice, array $paymentData): array
    {
        try {
            DB::beginTransaction();

            // Validate payment data
            $validation = $this->validatePayment($paymentData, $invoice);
            if (! $validation['success']) {
                return $validation;
            }

            // For manual M-Pesa entries, create completed payment (staff has already verified)
            $payment = Payment::create([
                'invoice_id' => $invoice->id,
                'payment_method' => 'mpesa',
                'amount' => $paymentData['amount'],
                'transaction_id' => $paymentData['transaction_id'] ?? null,
                'phone_number' => $paymentData['phone_number'] ?? null,
                'status' => 'completed', // Manual entries are immediately completed
                'payment_date' => $paymentData['payment_date'] ?? now(),
                'confirmed_by' => Auth::id(), // Set the staff member who recorded the payment
                'notes' => $paymentData['notes'] ?? null,
                'gateway_response' => [
                    'gateway' => 'mpesa',
                    'mode' => 'manual',
                    'processed_at' => now()->toISOString(),
                    'confirmed_by_user' => Auth::id(),
                ],
            ]);

            // Update invoice status based on payments
            $invoice->updateStatusBasedOnPayments();

            DB::commit();

            Log::info('M-Pesa payment processed successfully', [
                'payment_id' => $payment->id,
                'invoice_id' => $invoice->id,
                'amount' => $paymentData['amount'],
                'confirmed_by' => Auth::id(),
                'invoice_status' => $invoice->fresh()->status,
            ]);

            return [
                'success' => true,
                'payment_id' => $payment->id,
                'status' => 'completed',
                'message' => 'M-Pesa payment recorded successfully.',
                'invoice_status' => $invoice->fresh()->status,
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('M-Pesa payment processing failed', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to process M-Pesa payment: '.$e->getMessage(),
            ];
        }
    }

    /**
     * Validate payment data before processing.
     */
    public function validatePayment(array $paymentData, ?Invoice $invoice = null): array
    {
        $errors = [];

        // Validate amount
        if (! isset($paymentData['amount']) || ! is_numeric($paymentData['amount']) || $paymentData['amount'] <= 0) {
            $errors[] = 'Payment amount must be a positive number.';
        }

        // Validate amount against invoice remaining balance
        if ($invoice && isset($paymentData['amount'])) {
            $remainingBalance = $invoice->remaining_balance;
            if ($paymentData['amount'] > $remainingBalance) {
                $errors[] = 'Payment amount cannot exceed the remaining balance of '.number_format($remainingBalance, 2).'.';
            }
        }

        // Validate phone number for M-Pesa
        if (isset($paymentData['phone_number'])) {
            $phone = preg_replace('/[^0-9]/', '', $paymentData['phone_number']);
            if (! preg_match('/^254[0-9]{9}$/', $phone) && ! preg_match('/^0[0-9]{9}$/', $phone)) {
                $errors[] = 'Invalid phone number format. Use format: 254XXXXXXXXX or 0XXXXXXXXX';
            }
        }

        // Validate payment date
        if (isset($paymentData['payment_date'])) {
            try {
                $date = \Carbon\Carbon::parse($paymentData['payment_date']);
                if ($date->isFuture()) {
                    $errors[] = 'Payment date cannot be in the future.';
                }
            } catch (\Exception $e) {
                $errors[] = 'Invalid payment date format.';
            }
        }

        // Validate invoice status
        if ($invoice && $invoice->status === 'paid') {
            $errors[] = 'This invoice is already paid.';
        }

        // Validate user authentication
        if (! Auth::check()) {
            $errors[] = 'You must be logged in to record payments.';
        }

        return [
            'success' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Get the status of a payment.
     */
    public function getPaymentStatus(string $transactionId): array
    {
        $payment = Payment::where('transaction_id', $transactionId)
            ->where('payment_method', 'mpesa')
            ->first();

        if (! $payment) {
            return [
                'success' => false,
                'message' => 'Payment not found.',
            ];
        }

        return [
            'success' => true,
            'status' => $payment->status,
            'payment_id' => $payment->id,
            'amount' => $payment->amount,
            'payment_date' => $payment->payment_date,
            'phone_number' => $payment->phone_number,
        ];
    }

    /**
     * Refund a payment.
     */
    public function refundPayment(Payment $payment, ?float $amount = null): array
    {
        return [
            'success' => false,
            'message' => 'M-Pesa refunds are not supported through this gateway. Please process manually.',
        ];
    }

    /**
     * Get the payment method name.
     */
    public function getPaymentMethod(): string
    {
        return 'mpesa';
    }

    /**
     * Check if the gateway supports automatic processing.
     */
    public function supportsAutomaticProcessing(): bool
    {
        return true; // M-Pesa supports automatic confirmation via callbacks
    }
}
