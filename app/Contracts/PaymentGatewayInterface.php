<?php

namespace App\Contracts;

use App\Models\Invoice;
use App\Models\Payment;

interface PaymentGatewayInterface
{
    /**
     * Process a payment for an invoice.
     */
    public function processPayment(Invoice $invoice, array $paymentData): array;

    /**
     * Validate payment data before processing.
     */
    public function validatePayment(array $paymentData): array;

    /**
     * Get the status of a payment.
     */
    public function getPaymentStatus(string $transactionId): array;

    /**
     * Refund a payment.
     */
    public function refundPayment(Payment $payment, ?float $amount = null): array;

    /**
     * Get the payment method name.
     */
    public function getPaymentMethod(): string;

    /**
     * Check if the gateway supports automatic processing.
     */
    public function supportsAutomaticProcessing(): bool;
}
