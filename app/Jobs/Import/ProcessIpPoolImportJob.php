<?php

namespace App\Jobs\Import;

use App\Models\Network\NetworkDevice;
use App\Models\Services\IpPool;
use App\Services\IpPoolService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessIpPoolImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes

    public $tries = 3;

    public $backoff = [30, 60, 120];

    protected array $poolsData;

    protected string $batchId;

    public function __construct(array $poolsData, string $batchId)
    {
        $this->poolsData = $poolsData;
        $this->batchId = $batchId;
    }

    public function handle(IpPoolService $ipPoolService): void
    {
        Log::info('Starting IP pool import batch', [
            'batch_id' => $this->batchId,
            'pools_count' => count($this->poolsData),
        ]);

        $successCount = 0;
        $failureCount = 0;
        $errors = [];

        DB::beginTransaction();

        try {
            foreach ($this->poolsData as $poolData) {
                try {
                    $result = $this->createIpPool($poolData, $ipPoolService);

                    if ($result['success']) {
                        $successCount++;
                        Log::info('IP pool created successfully', [
                            'batch_id' => $this->batchId,
                            'pool_name' => $poolData['name'],
                            'pool_id' => $result['pool_id'],
                        ]);
                    } else {
                        $failureCount++;
                        $errors[] = [
                            'pool_name' => $poolData['name'],
                            'error' => $result['error'],
                        ];
                    }
                } catch (\Exception $e) {
                    $failureCount++;
                    $errors[] = [
                        'pool_name' => $poolData['name'] ?? 'Unknown',
                        'error' => $e->getMessage(),
                    ];

                    Log::error('Failed to create IP pool', [
                        'batch_id' => $this->batchId,
                        'pool_data' => $poolData,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            DB::commit();

            Log::info('IP pool import batch completed', [
                'batch_id' => $this->batchId,
                'success_count' => $successCount,
                'failure_count' => $failureCount,
                'errors' => $errors,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('IP pool import batch failed', [
                'batch_id' => $this->batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    protected function createIpPool(array $poolData, IpPoolService $ipPoolService): array
    {
        try {
            // Get the device for MikroTik operations
            $device = NetworkDevice::find($poolData['device_id']);
            if (! $device) {
                return ['success' => false, 'error' => 'Device not found'];
            }

            // Parse subnet to get network and CIDR
            $subnetParts = explode('/', $poolData['subnet']);
            $network = $subnetParts[0];
            $cidr = $subnetParts[1] ?? '24';

            // Auto-calculate gateway if not provided
            if (! $poolData['gateway']) {
                $networkParts = explode('.', $network);
                $networkParts[3] = '1'; // Use .1 as gateway
                $poolData['gateway'] = implode('.', $networkParts);
            }

            // Use the existing IpPoolService to create the pool with MikroTik integration
            $ipPoolData = [
                'name' => $poolData['name'],
                'description' => $poolData['description'],
                'device_id' => $poolData['device_id'],
                'network_address' => $network,
                'network_cidr' => $poolData['subnet'], // e.g., "*************/24"
                'cidr_range' => $poolData['subnet'], // Store original CIDR for range calculation
                'subnet_mask' => $poolData['subnet_mask'],
                'gateway' => $poolData['gateway'],
                'dns_servers' => [$poolData['dns_primary'], $poolData['dns_secondary']],
                'interface' => $poolData['interface_name'] ?? 'ether11',
                'excluded_ips' => [$poolData['gateway']], // Exclude gateway from assignment
            ];

            // Create the IP pool using the existing service (includes MikroTik provisioning)
            $result = $ipPoolService->createIpPool($ipPoolData);

            if (! $result['success']) {
                throw new \Exception('IpPoolService failed: '.$result['error']);
            }

            $ipPool = $result['pool'];

            return [
                'success' => true,
                'pool_id' => $ipPool->id,
                'pool' => $ipPool,
                'total_addresses' => $result['total_addresses'] ?? 0,
                'available_addresses' => $result['available_addresses'] ?? 0,
                'mikrotik_result' => $result,
            ];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    protected function createIpPoolOnMikroTik(NetworkDevice $device, array $poolData, string $cidr): array
    {
        try {
            // Create IP address on MikroTik
            $result = $device->executeMikrotikCommand('/ip/address/add', [
                'address' => $poolData['gateway'].'/'.$cidr,
                'interface' => $poolData['interface_name'] ?? 'ether1',
                'comment' => 'Pool: '.$poolData['name'],
            ]);

            $mikrotikId = $result['after']['ret'] ?? null;

            if (! $mikrotikId) {
                return ['success' => false, 'error' => 'MikroTik did not return an entry ID'];
            }

            return ['success' => true, 'mikrotik_id' => $mikrotikId];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => 'MikroTik error: '.$e->getMessage()];
        }
    }

    protected function generateIpAddresses(IpPool $ipPool, array $poolData): void
    {
        if (! isset($poolData['start_ip']) || ! isset($poolData['end_ip'])) {
            return; // Skip IP generation if range not specified
        }

        $startIp = ip2long($poolData['start_ip']);
        $endIp = ip2long($poolData['end_ip']);
        $excludeIps = $poolData['exclude_ips'] ?? [];

        $ipAddresses = [];
        for ($ip = $startIp; $ip <= $endIp; $ip++) {
            $ipAddress = long2ip($ip);

            // Skip excluded IPs
            if (in_array($ipAddress, $excludeIps)) {
                continue;
            }

            $ipAddresses[] = [
                'ip_pool_id' => $ipPool->id,
                'ip_address' => $ipAddress,
                'subnet_mask' => $poolData['subnet_mask'],
                'full_address' => $ipAddress.'/'.$this->getCidrFromSubnetMask($poolData['subnet_mask']),
                'status' => 'available',
                'type' => 'usable',
                'exists_in_mikrotik' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Batch insert IP addresses
        if (! empty($ipAddresses)) {
            DB::table('ip_addresses')->insert($ipAddresses);
        }
    }

    protected function getCidrFromSubnetMask(string $subnetMask): int
    {
        // Convert subnet mask to CIDR notation
        if ($subnetMask === '*************') {
            return 24;
        } elseif ($subnetMask === '***********') {
            return 16;
        } elseif ($subnetMask === '*********') {
            return 8;
        }

        // Default to /24 for most common case
        return 24;
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('IP pool import job failed', [
            'batch_id' => $this->batchId,
            'pools_count' => count($this->poolsData),
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
