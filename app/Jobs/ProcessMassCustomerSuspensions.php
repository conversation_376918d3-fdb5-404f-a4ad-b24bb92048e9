<?php

namespace App\Jobs;

use App\Models\Customer;
use App\Models\Network\NetworkDevice;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Models\Subscription;
use App\Services\MikrotikAddressListService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessMassCustomerSuspensions implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes timeout

    public $tries = 3;

    public $backoff = [30, 60, 120]; // Retry delays in seconds

    protected $deviceId;

    protected $serviceType;

    protected $services;

    /**
     * Create a new job instance.
     */
    public function __construct(int $deviceId, string $serviceType, array $services)
    {
        $this->deviceId = $deviceId;
        $this->serviceType = $serviceType;
        $this->services = $services;
    }

    /**
     * Execute the job.
     */
    public function handle(MikrotikAddressListService $addressListService): void
    {
        $device = NetworkDevice::find($this->deviceId);
        if (! $device) {
            Log::error('Device not found for mass suspension', ['device_id' => $this->deviceId]);

            return;
        }

        $serviceTypeLabel = $this->serviceType === 'static_ip' ? 'Static IP' : 'PPPoE';
        $serviceCount = count($this->services);

        Log::info('Starting mass suspension job', [
            'device' => $device->name,
            'device_ip' => $device->ip_address,
            'service_type' => $serviceTypeLabel,
            'customer_count' => $serviceCount,
            'job_id' => $this->job->getJobId(),
        ]);

        $successCount = 0;
        $failureCount = 0;
        $errors = [];

        // Process suspensions in batches for better performance
        $batchSize = 10;
        $batches = array_chunk($this->services, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            Log::info('Processing batch '.($batchIndex + 1).' of '.count($batches), [
                'device' => $device->name,
                'service_type' => $serviceTypeLabel,
                'batch_size' => count($batch),
            ]);

            $batchResults = $this->processBatch($device, $batch, $addressListService);
            $successCount += $batchResults['success'];
            $failureCount += $batchResults['failure'];
            $errors = array_merge($errors, $batchResults['errors']);

            // Small delay between batches to avoid overwhelming MikroTik
            if ($batchIndex < count($batches) - 1) {
                sleep(1);
            }
        }

        // Log final results
        Log::info('Mass suspension job completed', [
            'device' => $device->name,
            'service_type' => $serviceTypeLabel,
            'total_customers' => $serviceCount,
            'successful_suspensions' => $successCount,
            'failed_suspensions' => $failureCount,
            'success_rate' => $serviceCount > 0 ? round(($successCount / $serviceCount) * 100, 2).'%' : '0%',
            'errors' => $errors,
            'job_id' => $this->job->getJobId(),
        ]);

        if ($failureCount > 0) {
            Log::warning('Some suspensions failed', [
                'device' => $device->name,
                'failed_count' => $failureCount,
                'errors' => $errors,
            ]);
        }
    }

    /**
     * Process a batch of suspensions.
     */
    protected function processBatch(NetworkDevice $device, array $batch, MikrotikAddressListService $addressListService): array
    {
        $successCount = 0;
        $failureCount = 0;
        $errors = [];

        foreach ($batch as $serviceData) {
            try {
                $result = $this->suspendCustomer($device, $serviceData, $addressListService);

                if ($result['success']) {
                    $successCount++;
                    Log::info('Customer suspended successfully', [
                        'customer' => $serviceData['customer_name'],
                        'service_type' => $this->serviceType,
                        'device' => $device->name,
                    ]);
                } else {
                    $failureCount++;
                    $errors[] = [
                        'customer' => $serviceData['customer_name'],
                        'error' => $result['error'],
                    ];
                    Log::error('Failed to suspend customer', [
                        'customer' => $serviceData['customer_name'],
                        'service_type' => $this->serviceType,
                        'device' => $device->name,
                        'error' => $result['error'],
                    ]);
                }
            } catch (\Exception $e) {
                $failureCount++;
                $errors[] = [
                    'customer' => $serviceData['customer_name'],
                    'error' => $e->getMessage(),
                ];
                Log::error('Exception during customer suspension', [
                    'customer' => $serviceData['customer_name'],
                    'service_type' => $this->serviceType,
                    'device' => $device->name,
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        return [
            'success' => $successCount,
            'failure' => $failureCount,
            'errors' => $errors,
        ];
    }

    /**
     * Suspend an individual customer.
     */
    protected function suspendCustomer(NetworkDevice $device, array $serviceData, MikrotikAddressListService $addressListService): array
    {
        DB::beginTransaction();

        try {
            // Get the customer and subscription
            $customer = Customer::find($serviceData['customer_id']);
            $subscription = Subscription::find($serviceData['subscription_id']);

            if (! $customer || ! $subscription) {
                throw new \Exception('Customer or subscription not found');
            }

            // Update subscription status
            $subscription->update([
                'status' => 'suspended',
                'suspension_date' => now(),
                'suspension_days' => $subscription->suspension_days + 1,
            ]);

            // Suspend the specific service
            if ($this->serviceType === 'static_ip') {
                $result = $this->suspendStaticIpService($serviceData['service_id'], $addressListService);
            } else {
                $result = $this->suspendPppoeService($serviceData['service_id'], $device);
            }

            if (! $result['success']) {
                throw new \Exception($result['error']);
            }

            DB::commit();

            return ['success' => true];

        } catch (\Exception $e) {
            DB::rollBack();

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Suspend Static IP service.
     */
    protected function suspendStaticIpService(int $serviceId, MikrotikAddressListService $addressListService): array
    {
        try {
            $service = StaticIpService::find($serviceId);
            if (! $service) {
                return ['success' => false, 'error' => 'Static IP service not found'];
            }

            // Use existing address list service for suspension
            $device = $service->device;
            $mikrotikId = $addressListService->suspendCustomer(
                $device,
                $service->ip_address,
                $service->customer->name
            );

            if ($mikrotikId) {
                // Store the MikroTik ID and update status
                $service->update([
                    'status' => 'suspended',
                    'mikrotik_id' => $mikrotikId,
                ]);

                return ['success' => true];
            } else {
                return ['success' => false, 'error' => 'Failed to add to MikroTik address list'];
            }

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Suspend PPPoE service.
     */
    protected function suspendPppoeService(int $serviceId, NetworkDevice $device): array
    {
        try {
            $service = PppoeService::find($serviceId);
            if (! $service) {
                return ['success' => false, 'error' => 'PPPoE service not found'];
            }

            // Disable PPPoE user on MikroTik
            $device->executeMikrotikCommand('/ppp/secret/set', [
                '.id' => $service->mikrotik_id ?? $service->username,
                'disabled' => 'yes',
            ]);

            // Disconnect active sessions
            try {
                $activeSessions = $device->executeMikrotikCommand('/ppp/active/print', [
                    '?name' => $service->username,
                ]);

                foreach ($activeSessions as $session) {
                    if (isset($session['.id'])) {
                        $device->executeMikrotikCommand('/ppp/active/remove', [
                            '.id' => $session['.id'],
                        ]);
                    }
                }
            } catch (\Exception $e) {
                Log::warning('Could not disconnect PPPoE session', [
                    'username' => $service->username,
                    'error' => $e->getMessage(),
                ]);
            }

            $service->update(['status' => 'suspended']);

            return ['success' => true];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Mass suspension job failed', [
            'device_id' => $this->deviceId,
            'service_type' => $this->serviceType,
            'customer_count' => count($this->services),
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
