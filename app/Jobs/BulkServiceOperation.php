<?php

namespace App\Jobs;

use App\Models\Subscription;
use App\Services\AutomatedBillingService;
use App\Services\ServiceSuspensionService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BulkServiceOperation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $operation; // 'suspend' or 'reactivate'

    protected $subscriptionIds;

    protected $batchId;

    protected $operationDate;

    /**
     * Create a new job instance.
     */
    public function __construct(string $operation, array $subscriptionIds, ?string $batchId = null, ?Carbon $operationDate = null)
    {
        $this->operation = $operation;
        $this->subscriptionIds = $subscriptionIds;
        $this->batchId = $batchId ?? $operation.'_'.now()->format('Y_m_d_H_i_s');
        $this->operationDate = $operationDate ?? now();
    }

    /**
     * Execute the job.
     */
    public function handle(ServiceSuspensionService $suspensionService, AutomatedBillingService $billingService)
    {
        $startTime = microtime(true);

        Log::info("Starting bulk {$this->operation} operation", [
            'batch_id' => $this->batchId,
            'operation' => $this->operation,
            'subscription_count' => count($this->subscriptionIds),
            'operation_date' => $this->operationDate->toDateString(),
        ]);

        try {
            // Create billing batch record
            $batchRecord = $this->createBillingBatch();

            $successCount = 0;
            $failureCount = 0;
            $errors = [];

            // Process subscriptions one by one to avoid overwhelming MikroTik devices
            foreach ($this->subscriptionIds as $subscriptionId) {
                try {
                    $subscription = Subscription::with(['customer'])->find($subscriptionId);

                    if (! $subscription) {
                        throw new \Exception("Subscription not found: {$subscriptionId}");
                    }

                    if ($this->operation === 'suspend') {
                        $result = $this->suspendSubscription($subscription, $suspensionService, $billingService);
                    } else {
                        $result = $this->reactivateSubscription($subscription, $suspensionService, $billingService);
                    }

                    if ($result['success']) {
                        $successCount++;
                        Log::debug("Subscription {$this->operation}ed successfully", [
                            'subscription_id' => $subscriptionId,
                            'customer_name' => $subscription->customer->name,
                            'services' => $result['services'] ?? [],
                        ]);
                    } else {
                        throw new \Exception($result['message'] ?? 'Operation failed');
                    }

                    // Update batch progress
                    DB::table('billing_batches')
                        ->where('id', $batchRecord)
                        ->increment('processed_items');

                    // Small delay to prevent overwhelming MikroTik devices
                    usleep(100000); // 100ms delay

                } catch (\Exception $e) {
                    $failureCount++;
                    $errors[] = [
                        'subscription_id' => $subscriptionId,
                        'customer_name' => $subscription->customer->name ?? 'Unknown',
                        'error' => $e->getMessage(),
                    ];

                    Log::error("Failed to {$this->operation} subscription", [
                        'subscription_id' => $subscriptionId,
                        'error' => $e->getMessage(),
                    ]);

                    // Update batch progress
                    DB::table('billing_batches')
                        ->where('id', $batchRecord)
                        ->increment('processed_items');
                }
            }

            // Update batch completion
            $executionTime = round(microtime(true) - $startTime, 2);

            DB::table('billing_batches')
                ->where('id', $batchRecord)
                ->update([
                    'status' => $failureCount > 0 ? 'completed_with_errors' : 'completed',
                    'successful_items' => $successCount,
                    'failed_items' => $failureCount,
                    'completed_at' => now(),
                    'summary' => json_encode([
                        'operation' => $this->operation,
                        'total_subscriptions' => count($this->subscriptionIds),
                        'successful_operations' => $successCount,
                        'failed_operations' => $failureCount,
                        'execution_time_seconds' => $executionTime,
                        'operation_date' => $this->operationDate->toDateString(),
                    ]),
                    'error_log' => $failureCount > 0 ? json_encode($errors) : null,
                ]);

            Log::info("Bulk {$this->operation} operation completed", [
                'batch_id' => $this->batchId,
                'total_subscriptions' => count($this->subscriptionIds),
                'successful_operations' => $successCount,
                'failed_operations' => $failureCount,
                'execution_time' => $executionTime.'s',
            ]);

        } catch (\Exception $e) {
            Log::error("Bulk {$this->operation} operation failed", [
                'batch_id' => $this->batchId,
                'error' => $e->getMessage(),
            ]);

            // Update batch as failed
            if (isset($batchRecord)) {
                DB::table('billing_batches')
                    ->where('id', $batchRecord)
                    ->update([
                        'status' => 'failed',
                        'completed_at' => now(),
                        'error_log' => $e->getMessage(),
                    ]);
            }

            throw $e;
        }
    }

    /**
     * Suspend a subscription and its services.
     */
    protected function suspendSubscription(Subscription $subscription, ServiceSuspensionService $suspensionService, AutomatedBillingService $billingService): array
    {
        // Suspend services
        $result = $suspensionService->suspendServices($subscription);

        if ($result['success']) {
            // Record suspension in billing system
            $billingService->recordSuspension($subscription, $this->operationDate);
        }

        return $result;
    }

    /**
     * Reactivate a subscription and its services.
     */
    protected function reactivateSubscription(Subscription $subscription, ServiceSuspensionService $suspensionService, AutomatedBillingService $billingService): array
    {
        // Reactivate services
        $result = $suspensionService->reactivateServices($subscription);

        if ($result['success']) {
            // Record reactivation in billing system
            $billingService->recordReactivation($subscription, $this->operationDate);
        }

        return $result;
    }

    /**
     * Create billing batch record.
     */
    protected function createBillingBatch()
    {
        return DB::table('billing_batches')->insertGetId([
            'batch_id' => $this->batchId,
            'operation_type' => 'bulk_'.$this->operation,
            'billing_date' => $this->operationDate,
            'total_items' => count($this->subscriptionIds),
            'status' => 'processing',
            'started_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * The job failed to process.
     */
    public function failed(\Throwable $exception)
    {
        Log::error("Bulk {$this->operation} job failed", [
            'batch_id' => $this->batchId,
            'operation' => $this->operation,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
