<?php

namespace App\Jobs;

use App\Models\Subscription;
use App\Services\AutomatedBillingService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GenerateMonthlyInvoices implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $billingDate;

    protected $batchId;

    protected $chunkSize;

    /**
     * Create a new job instance.
     */
    public function __construct(?Carbon $billingDate = null, ?string $batchId = null, int $chunkSize = 100)
    {
        $this->billingDate = $billingDate ?? now();
        $this->batchId = $batchId ?? 'monthly_'.$this->billingDate->format('Y_m_d_H_i_s');
        $this->chunkSize = $chunkSize;
    }

    /**
     * Execute the job.
     */
    public function handle(AutomatedBillingService $billingService)
    {
        $startTime = microtime(true);

        Log::info('Starting monthly invoice generation', [
            'batch_id' => $this->batchId,
            'billing_date' => $this->billingDate->toDateString(),
            'chunk_size' => $this->chunkSize,
        ]);

        try {
            // Create billing batch record
            $batch = $this->createBillingBatch();

            // Get subscriptions that need billing
            $subscriptions = $this->getSubscriptionsNeedingBilling();

            if ($batch) {
                DB::table('billing_batches')->where('id', $batch)->update(['total_items' => $subscriptions->count()]);
            }

            $successCount = 0;
            $failureCount = 0;
            $errors = [];

            // Process subscriptions in chunks to manage memory
            foreach ($subscriptions->chunk($this->chunkSize) as $chunk) {
                foreach ($chunk as $subscription) {
                    try {
                        $invoice = $billingService->generateInvoice($subscription, $this->billingDate);

                        if ($invoice) {
                            $successCount++;
                            Log::debug('Invoice generated', [
                                'subscription_id' => $subscription->id,
                                'invoice_id' => $invoice->id,
                                'amount' => $invoice->total_amount,
                            ]);
                        } else {
                            Log::debug('Invoice skipped - no billable amount', [
                                'subscription_id' => $subscription->id,
                            ]);
                        }

                        if ($batch) {
                            DB::table('billing_batches')->where('id', $batch)->increment('processed_items');
                            DB::table('billing_batches')->where('id', $batch)->update(['successful_items' => $successCount]);
                        }

                    } catch (\Exception $e) {
                        $failureCount++;
                        $errors[] = [
                            'subscription_id' => $subscription->id,
                            'customer_name' => $subscription->customer->name,
                            'error' => $e->getMessage(),
                        ];

                        Log::error('Failed to generate invoice', [
                            'subscription_id' => $subscription->id,
                            'error' => $e->getMessage(),
                        ]);

                        if ($batch) {
                            DB::table('billing_batches')->where('id', $batch)->increment('processed_items');
                            DB::table('billing_batches')->where('id', $batch)->update(['failed_items' => $failureCount]);
                        }
                    }
                }
            }

            // Update batch completion
            $executionTime = round(microtime(true) - $startTime, 2);

            if ($batch) {
                DB::table('billing_batches')->where('id', $batch)->update([
                    'status' => $failureCount > 0 ? 'completed_with_errors' : 'completed',
                    'completed_at' => now(),
                    'summary' => json_encode([
                        'total_subscriptions' => $subscriptions->count(),
                        'successful_invoices' => $successCount,
                        'failed_invoices' => $failureCount,
                        'execution_time_seconds' => $executionTime,
                        'billing_date' => $this->billingDate->toDateString(),
                    ]),
                    'error_log' => $failureCount > 0 ? json_encode($errors) : null,
                ]);
            }

            Log::info('Monthly invoice generation completed', [
                'batch_id' => $this->batchId,
                'total_subscriptions' => $subscriptions->count(),
                'successful_invoices' => $successCount,
                'failed_invoices' => $failureCount,
                'execution_time' => $executionTime.'s',
            ]);

        } catch (\Exception $e) {
            Log::error('Monthly invoice generation failed', [
                'batch_id' => $this->batchId,
                'error' => $e->getMessage(),
            ]);

            // Update batch as failed
            if (isset($batch) && $batch) {
                DB::table('billing_batches')->where('id', $batch)->update([
                    'status' => 'failed',
                    'completed_at' => now(),
                    'error_log' => $e->getMessage(),
                ]);
            }

            throw $e;
        }
    }

    /**
     * Create billing batch record.
     */
    protected function createBillingBatch()
    {
        try {
            return DB::table('billing_batches')->insertGetId([
                'batch_id' => $this->batchId,
                'operation_type' => 'monthly_billing',
                'billing_date' => $this->billingDate,
                'status' => 'processing',
                'started_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } catch (\Exception $e) {
            // If billing_batches table doesn't exist, just log and continue
            Log::warning('Billing batches table not available, continuing without batch tracking', [
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get subscriptions that need billing.
     */
    protected function getSubscriptionsNeedingBilling()
    {
        return Subscription::with(['customer'])
            ->where('status', 'active')
            ->where('auto_billing_enabled', true)
            ->where('next_billing_date', '<=', $this->billingDate)
            ->orderBy('customer_id')
            ->get();
    }

    /**
     * The job failed to process.
     */
    public function failed(\Throwable $exception)
    {
        Log::error('Monthly invoice generation job failed', [
            'batch_id' => $this->batchId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
