<?php

namespace App\Jobs\MikroTik;

use App\Models\Network\NetworkDevice;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

abstract class BaseMikroTikJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run before timing out.
     */
    public int $timeout = 60;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public int $backoff = 30;

    /**
     * The device to perform the operation on.
     */
    protected NetworkDevice $device;

    /**
     * Operation metadata for logging.
     */
    protected array $metadata = [];

    /**
     * Create a new job instance.
     */
    public function __construct(NetworkDevice $device, array $metadata = [])
    {
        $this->device = $device;
        $this->metadata = $metadata;

        // Set queue name based on device to distribute load
        $this->onQueue("mikrotik-device-{$device->id}");
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $operationType = $this->getOperationType();

        Log::info("Starting MikroTik operation: {$operationType}", [
            'device_id' => $this->device->id,
            'device_name' => $this->device->name,
            'attempt' => $this->attempts(),
            'metadata' => $this->metadata,
        ]);

        try {
            // Test device connection first
            if (! $this->device->testConnection()) {
                throw new \Exception('Cannot connect to MikroTik device');
            }

            // Execute the specific operation
            $result = $this->executeOperation();

            Log::info("MikroTik operation completed successfully: {$operationType}", [
                'device_id' => $this->device->id,
                'result' => $result,
                'metadata' => $this->metadata,
            ]);

        } catch (\Exception $e) {
            Log::error("MikroTik operation failed: {$operationType}", [
                'device_id' => $this->device->id,
                'device_name' => $this->device->name,
                'attempt' => $this->attempts(),
                'error' => $e->getMessage(),
                'metadata' => $this->metadata,
            ]);

            // If this is the last attempt, handle the failure
            if ($this->attempts() >= $this->tries) {
                $this->handleFinalFailure($e);
            }

            throw $e; // Re-throw to trigger retry
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        $operationType = $this->getOperationType();

        Log::error("MikroTik operation permanently failed: {$operationType}", [
            'device_id' => $this->device->id,
            'device_name' => $this->device->name,
            'total_attempts' => $this->attempts(),
            'error' => $exception->getMessage(),
            'metadata' => $this->metadata,
        ]);

        $this->handleFinalFailure($exception);
    }

    /**
     * Get the operation type for logging.
     */
    abstract protected function getOperationType(): string;

    /**
     * Execute the specific MikroTik operation.
     */
    abstract protected function executeOperation(): mixed;

    /**
     * Handle final failure after all retries are exhausted.
     */
    protected function handleFinalFailure(\Throwable $exception): void
    {
        // Default implementation - can be overridden by specific jobs
        Log::critical('MikroTik operation permanently failed', [
            'operation' => $this->getOperationType(),
            'device_id' => $this->device->id,
            'error' => $exception->getMessage(),
            'metadata' => $this->metadata,
        ]);
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        // Exponential backoff: 30s, 60s, 120s
        return [30, 60, 120];
    }
}
