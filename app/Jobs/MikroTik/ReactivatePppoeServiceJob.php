<?php

namespace App\Jobs\MikroTik;

use App\Models\Services\PppoeService;
use Illuminate\Support\Facades\Log;

class ReactivatePppoeServiceJob extends BaseMikroTikJob
{
    /**
     * The PPPoE service to reactivate.
     */
    protected PppoeService $service;

    /**
     * The invoice ID that triggered this reactivation (optional).
     */
    protected ?int $invoiceId;

    /**
     * Create a new job instance.
     */
    public function __construct(PppoeService $service, ?int $invoiceId = null)
    {
        $this->service = $service;
        $this->invoiceId = $invoiceId;

        parent::__construct($service->device, [
            'service_id' => $service->id,
            'customer_id' => $service->customer_id,
            'customer_name' => $service->customer->name,
            'username' => $service->username,
            'invoice_id' => $invoiceId,
        ]);
    }

    /**
     * Get the operation type for logging.
     */
    protected function getOperationType(): string
    {
        return 'reactivate_pppoe_service';
    }

    /**
     * Execute the PPPoE service reactivation operation.
     */
    protected function executeOperation(): mixed
    {
        // Check if service is already active
        $this->service->refresh();
        if ($this->service->status === 'active') {
            Log::info('PPPoE service already active, skipping', [
                'service_id' => $this->service->id,
            ]);

            return ['status' => 'already_active'];
        }

        // Validate service state
        if ($this->service->status !== 'suspended') {
            throw new \Exception("Cannot reactivate service with status: {$this->service->status}");
        }

        // Find and enable the PPP secret
        $this->enablePppSecret();

        // Update service status
        $this->service->status = 'active';
        $this->service->save();

        // Update subscription status to active if this service has a subscription
        $this->updateSubscriptionStatus('active');

        return [
            'status' => 'active',
            'invoice_id' => $this->invoiceId,
        ];
    }

    /**
     * Enable the PPP secret for this service.
     */
    protected function enablePppSecret(): void
    {
        // Find the PPP secret on the router
        $secrets = $this->device->executeMikrotikCommand('/ppp/secret/print', [
            '?name' => $this->service->username,
        ]);

        if (empty($secrets)) {
            $this->recreatePppSecret();

            return;
        }

        $secret = $secrets[0];
        $secretId = $secret['.id'];

        // Check if secret is already enabled
        $isDisabled = isset($secret['disabled']) && $secret['disabled'] === 'true';

        if (! $isDisabled) {

            return;
        }

        // Enable the PPP secret
        $this->device->executeMikrotikCommand('/ppp/secret/set', [
            '.id' => $secretId,
            'disabled' => 'no',
        ]);

    }

    /**
     * Recreate PPP secret if it doesn't exist.
     */
    protected function recreatePppSecret(): void
    {
        try {
            $profileName = 'default';

            // Use bandwidth plan profile if available
            if ($this->service->bandwidthPlan) {
                $profileName = "plan-{$this->service->bandwidthPlan->id}";

                // Ensure the profile exists
                $this->ensurePppProfileExists();
            }

            // Create the PPPoE secret
            $this->device->executeMikrotikCommand('/ppp/secret/add', [
                'name' => $this->service->username,
                'password' => $this->service->password,
                'service' => 'pppoe',
                'profile' => $this->service->service_profile ?: $profileName,
                'comment' => "Customer: {$this->service->customer->name}, ID: {$this->service->customer->id} (Recreated during reactivation)",
            ]);

        } catch (\Exception $e) {
            throw new \Exception('Failed to recreate PPP secret: '.$e->getMessage());
        }
    }

    /**
     * Ensure PPP profile exists for bandwidth plan.
     */
    protected function ensurePppProfileExists(): void
    {
        if (! $this->service->bandwidthPlan) {
            return;
        }

        $plan = $this->service->bandwidthPlan;
        $profileName = "plan-{$plan->id}";

        try {
            // Check if profile exists
            $profiles = $this->device->executeMikrotikCommand('/ppp/profile/print', [
                '?name' => $profileName,
            ]);

            if (empty($profiles)) {
                // Create the profile
                $this->device->executeMikrotikCommand('/ppp/profile/add', [
                    'name' => $profileName,
                    'rate-limit' => "{$plan->upload_speed}M/{$plan->download_speed}M",
                    'comment' => "Bandwidth Plan: {$plan->name} (Recreated during reactivation)",
                ]);
            }

        } catch (\Exception $e) {
            Log::warning('Failed to ensure PPP profile exists during reactivation', [
                'service_id' => $this->service->id,
                'profile_name' => $profileName,
                'error' => $e->getMessage(),
            ]);
            // Don't fail reactivation if profile creation fails
        }
    }

    /**
     * Update subscription status when service is reactivated.
     */
    protected function updateSubscriptionStatus(string $status): void
    {
        if (! $this->service->subscription_id) {
            Log::info('Service has no subscription, skipping subscription status update', [
                'service_id' => $this->service->id,
                'service_status' => $status,
            ]);

            return;
        }

        try {
            $subscription = $this->service->subscription;

            if (! $subscription) {
                Log::warning('Subscription not found for service', [
                    'service_id' => $this->service->id,
                    'subscription_id' => $this->service->subscription_id,
                ]);

                return;
            }

            $oldStatus = $subscription->status;

            // Only update if status is actually changing
            if ($oldStatus === $status) {
                Log::info('Subscription status already matches service status', [
                    'service_id' => $this->service->id,
                    'subscription_id' => $subscription->id,
                    'status' => $status,
                ]);

                return;
            }

            // Update subscription status and timestamps
            $subscription->status = $status;

            if ($status === 'active') {
                $subscription->reactivation_date = now();
            }

            $subscription->save();

            Log::info('Subscription status updated after service reactivation', [
                'service_id' => $this->service->id,
                'subscription_id' => $subscription->id,
                'old_status' => $oldStatus,
                'new_status' => $status,
                'customer_name' => $this->service->customer->name,
                'invoice_id' => $this->invoiceId,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update subscription status after service reactivation', [
                'service_id' => $this->service->id,
                'subscription_id' => $this->service->subscription_id,
                'target_status' => $status,
                'invoice_id' => $this->invoiceId,
                'error' => $e->getMessage(),
            ]);
            // Don't throw - service reactivation should still succeed
        }
    }

    /**
     * Handle final failure after all retries are exhausted.
     */
    protected function handleFinalFailure(\Throwable $exception): void
    {
        // Mark service as failed reactivation
        $this->service->refresh();
        $this->service->status = 'reactivation_failed';
        $this->service->save();

        // Could send notification to admin here
    }
}
