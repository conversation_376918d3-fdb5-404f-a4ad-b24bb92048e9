<?php

namespace App\Jobs\MikroTik;

use App\Models\Services\StaticIpService;
use App\Services\MikrotikAddressListService;
use Illuminate\Support\Facades\Log;

class SuspendStaticIpServiceJob extends BaseMikroTikJob
{
    /**
     * The Static IP service to suspend.
     */
    protected StaticIpService $service;

    /**
     * Create a new job instance.
     */
    public function __construct(StaticIpService $service)
    {
        $this->service = $service;

        parent::__construct($service->device, [
            'service_id' => $service->id,
            'customer_id' => $service->customer_id,
            'customer_name' => $service->customer->name,
            'ip_address' => $service->ip_address,
        ]);
    }

    /**
     * Get the operation type for logging.
     */
    protected function getOperationType(): string
    {
        return 'suspend_static_ip_service';
    }

    /**
     * Execute the Static IP service suspension operation.
     */
    protected function executeOperation(): mixed
    {
        // Check if service is already suspended
        $this->service->refresh();
        if ($this->service->status === 'suspended') {
            Log::info('Static IP service already suspended, skipping', [
                'service_id' => $this->service->id,
            ]);

            return ['status' => 'already_suspended'];
        }

        $addressListService = new MikrotikAddressListService;

        // Suspend customer (add to address list)
        $mikrotikId = $addressListService->suspendCustomer(
            $this->device,
            $this->service->ip_address,
            $this->service->customer->name
        );

        if (! $mikrotikId) {
            throw new \Exception('Failed to suspend customer on MikroTik - no ID returned');
        }

        // Disable bandwidth queues
        // $this->disableBandwidthQueues();

        // Update service status and store MikroTik ID
        $this->service->mikrotik_id = $mikrotikId;
        $this->service->status = 'suspended';
        $this->service->save();

        Log::info('Static IP service suspended successfully', [
            'service_id' => $this->service->id,
            'mikrotik_id' => $mikrotikId,
            'customer_name' => $this->service->customer->name,
            'ip_address' => $this->service->ip_address,
        ]);

        return [
            'status' => 'suspended',
            'mikrotik_id' => $mikrotikId,
        ];
    }

    /**
     * Disable bandwidth queues for the suspended service.
     */
    protected function disableBandwidthQueues(): void
    {
        try {
            $queues = $this->device->executeMikrotikCommand('/queue/simple/print', [
                '?target' => $this->service->ip_address.'/32',
            ]);

            foreach ($queues as $queue) {
                $this->device->executeMikrotikCommand('/queue/simple/set', [
                    '.id' => $queue['.id'],
                    'disabled' => 'yes',
                ]);

                Log::info('Disabled bandwidth queue for suspended service', [
                    'service_id' => $this->service->id,
                    'queue_id' => $queue['.id'],
                    'queue_name' => $queue['name'] ?? 'unnamed',
                ]);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to disable bandwidth queues for suspended service', [
                'service_id' => $this->service->id,
                'ip_address' => $this->service->ip_address,
                'error' => $e->getMessage(),
            ]);
            // Don't fail the suspension if queue disabling fails
        }
    }

    /**
     * Handle final failure after all retries are exhausted.
     */
    protected function handleFinalFailure(\Throwable $exception): void
    {
        // Mark service as failed suspension
        $this->service->refresh();
        $this->service->status = 'suspension_failed';
        $this->service->save();

        Log::critical('Static IP service suspension permanently failed', [
            'service_id' => $this->service->id,
            'customer_name' => $this->service->customer->name,
            'ip_address' => $this->service->ip_address,
            'error' => $exception->getMessage(),
        ]);

        // Could send notification to admin here
    }
}
