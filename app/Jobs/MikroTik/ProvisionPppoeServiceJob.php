<?php

namespace App\Jobs\MikroTik;

use App\Models\Services\PppoeService;
use Illuminate\Support\Facades\Log;

class ProvisionPppoeServiceJob extends BaseMikroTikJob
{
    /**
     * The PPPoE service to provision.
     */
    protected PppoeService $service;

    /**
     * Create a new job instance.
     */
    public function __construct(PppoeService $service)
    {
        $this->service = $service;

        parent::__construct($service->device, [
            'service_id' => $service->id,
            'customer_id' => $service->customer_id,
            'customer_name' => $service->customer->name,
            'username' => $service->username,
        ]);
    }

    /**
     * Get the operation type for logging.
     */
    protected function getOperationType(): string
    {
        return 'provision_pppoe_service';
    }

    /**
     * Execute the PPPoE service provisioning operation.
     */
    protected function executeOperation(): mixed
    {
        // Check if service is already provisioned
        $this->service->refresh();
        if ($this->service->mikrotik_id) {
            Log::info('PPPoE service already provisioned, skipping', [
                'service_id' => $this->service->id,
            ]);

            return ['status' => 'already_provisioned'];
        }

        $results = [];

        // Create PPP profile if bandwidth plan exists
        if ($this->service->bandwidthPlan) {
            $profileName = $this->createPppProfile();
            $results['profile_name'] = $profileName;
        }

        // Create PPPoE secret
        $secretId = $this->createPppoeSecret();
        $results['secret_id'] = $secretId;

        // Update service with MikroTik ID
        $this->service->mikrotik_id = $secretId;
        $this->service->status = 'active';
        $this->service->save();

        Log::info('PPPoE service provisioned successfully', [
            'service_id' => $this->service->id,
            'customer_name' => $this->service->customer->name,
            'username' => $this->service->username,
            'results' => $results,
        ]);

        return $results;
    }

    /**
     * Create PPP profile for bandwidth plan.
     */
    protected function createPppProfile(): string
    {
        $plan = $this->service->bandwidthPlan;
        $profileName = "plan-{$plan->id}";

        try {
            // Check if profile already exists
            $profiles = $this->device->executeMikrotikCommand('/ppp/profile/print', [
                '?name' => $profileName,
            ]);

            if (empty($profiles)) {
                // Create new profile with bandwidth limits
                $this->device->executeMikrotikCommand('/ppp/profile/add', [
                    'name' => $profileName,
                    'rate-limit' => "{$plan->upload_speed}M/{$plan->download_speed}M",
                    'comment' => "Bandwidth Plan: {$plan->name}",
                ]);

                Log::info('Created PPP profile for bandwidth plan', [
                    'service_id' => $this->service->id,
                    'profile_name' => $profileName,
                    'bandwidth_plan' => $plan->name,
                    'limits' => "{$plan->upload_speed}M/{$plan->download_speed}M",
                ]);
            }

            return $profileName;
        } catch (\Exception $e) {
            Log::warning('Failed to create PPP profile for bandwidth plan', [
                'service_id' => $this->service->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);

            // Return default profile
            return 'default';
        }
    }

    /**
     * Create PPPoE secret.
     */
    protected function createPppoeSecret(): string
    {
        $profileName = 'default';

        // Use bandwidth plan profile if available
        if ($this->service->bandwidthPlan) {
            $profileName = "plan-{$this->service->bandwidthPlan->id}";
        }

        $result = $this->device->executeMikrotikCommand('/ppp/secret/add', [
            'name' => $this->service->username,
            'password' => $this->service->password,
            'service' => 'pppoe',
            'profile' => $this->service->service_profile ?: $profileName,
            'comment' => "Customer: {$this->service->customer->name}, ID: {$this->service->customer->id}",
        ]);

        // Log the full response for debugging
        Log::info('MikroTik API response for PPPoE secret creation', [
            'service_id' => $this->service->id,
            'username' => $this->service->username,
            'full_response' => $result,
        ]);

        // Try multiple possible response formats
        $secretId = null;

        // Format 1: Standard RouterOS API response
        if (isset($result['after']['ret'])) {
            $secretId = $result['after']['ret'];
        }
        // Format 2: Direct ID in response
        elseif (isset($result['ret'])) {
            $secretId = $result['ret'];
        }
        // Format 3: ID in different location
        elseif (isset($result[0]['ret'])) {
            $secretId = $result[0]['ret'];
        }
        // Format 4: Check if username already exists (duplicate)
        elseif (is_array($result) && empty($result)) {
            // Empty response might mean success, try to find the secret
            $existingSecrets = $this->device->executeMikrotikCommand('/ppp/secret/print', [
                '?name' => $this->service->username,
            ]);

            if (! empty($existingSecrets) && isset($existingSecrets[0]['.id'])) {
                $secretId = $existingSecrets[0]['.id'];
                Log::info('Found existing PPPoE secret with same username', [
                    'service_id' => $this->service->id,
                    'username' => $this->service->username,
                    'secret_id' => $secretId,
                ]);
            }
        }

        if (! $secretId) {
            // Check if it's a duplicate username error in the response
            $responseStr = json_encode($result);
            if (strpos($responseStr, 'already exists') !== false ||
                strpos($responseStr, 'same name') !== false ||
                (isset($result['after']['message']) && strpos($result['after']['message'], 'already exists') !== false)) {

                Log::info('PPPoE username already exists, attempting to find existing secret', [
                    'service_id' => $this->service->id,
                    'username' => $this->service->username,
                ]);

                // Try to find the existing secret
                $existingSecrets = $this->device->executeMikrotikCommand('/ppp/secret/print', [
                    '?name' => $this->service->username,
                ]);

                if (! empty($existingSecrets) && isset($existingSecrets[0]['.id'])) {
                    $secretId = $existingSecrets[0]['.id'];
                    Log::info('Successfully found existing PPPoE secret', [
                        'service_id' => $this->service->id,
                        'username' => $this->service->username,
                        'secret_id' => $secretId,
                    ]);

                    // Update the existing secret with current service details
                    $this->device->executeMikrotikCommand('/ppp/secret/set', [
                        '.id' => $secretId,
                        'password' => $this->service->password,
                        'profile' => $this->service->service_profile ?: $profileName,
                        'comment' => "Customer: {$this->service->customer->name}, ID: {$this->service->customer->id}",
                    ]);

                } else {
                    throw new \Exception("PPPoE username '{$this->service->username}' already exists in MikroTik but could not be found. Please use a different username or check MikroTik manually.");
                }
            } else {
                throw new \Exception('Failed to create PPPoE secret - no ID returned. Response: '.json_encode($result));
            }
        }

        Log::info('Created PPPoE secret', [
            'service_id' => $this->service->id,
            'secret_id' => $secretId,
            'username' => $this->service->username,
            'profile' => $this->service->service_profile ?: $profileName,
        ]);

        return $secretId;
    }

    /**
     * Handle final failure after all retries are exhausted.
     */
    protected function handleFinalFailure(\Throwable $exception): void
    {
        // Mark service as failed provisioning
        $this->service->refresh();
        $this->service->status = 'provisioning_failed';
        $this->service->save();

        Log::critical('PPPoE service provisioning permanently failed', [
            'service_id' => $this->service->id,
            'customer_name' => $this->service->customer->name,
            'username' => $this->service->username,
            'error' => $exception->getMessage(),
        ]);

        // Could send notification to admin here
    }
}
