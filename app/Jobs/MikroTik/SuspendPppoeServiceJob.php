<?php

namespace App\Jobs\MikroTik;

use App\Models\Services\PppoeService;
use Illuminate\Support\Facades\Log;

class SuspendPppoeServiceJob extends BaseMikroTikJob
{
    /**
     * The PPPoE service to suspend.
     */
    protected PppoeService $service;

    /**
     * Create a new job instance.
     */
    public function __construct(PppoeService $service)
    {
        $this->service = $service;

        parent::__construct($service->device, [
            'service_id' => $service->id,
            'customer_id' => $service->customer_id,
            'customer_name' => $service->customer->name,
            'username' => $service->username,
        ]);
    }

    /**
     * Get the operation type for logging.
     */
    protected function getOperationType(): string
    {
        return 'suspend_pppoe_service';
    }

    /**
     * Execute the PPPoE service suspension operation.
     */
    protected function executeOperation(): mixed
    {
        // Check if service is already suspended
        $this->service->refresh();
        if ($this->service->status === 'suspended') {
            Log::info('PPPoE service already suspended, skipping', [
                'service_id' => $this->service->id,
            ]);

            return ['status' => 'already_suspended'];
        }

        // Disable the PPP secret
        $this->disablePppSecret();

        // Disconnect any active sessions
        $this->disconnectActiveSessions();

        // Update service status
        $this->service->status = 'suspended';
        $this->service->save();

        Log::info('PPPoE service suspended successfully', [
            'service_id' => $this->service->id,
            'customer_name' => $this->service->customer->name,
            'username' => $this->service->username,
        ]);

        return [
            'status' => 'suspended',
        ];
    }

    /**
     * Disable the PPP secret for this service.
     */
    protected function disablePppSecret(): void
    {
        $secrets = $this->device->executeMikrotikCommand('/ppp/secret/print', [
            '?name' => $this->service->username,
        ]);

        if (empty($secrets)) {
            Log::warning('PPP secret not found for suspension', [
                'service_id' => $this->service->id,
                'username' => $this->service->username,
            ]);

            return;
        }

        $secret = $secrets[0];
        $secretId = $secret['.id'];

        $this->device->executeMikrotikCommand('/ppp/secret/set', [
            '.id' => $secretId,
            'disabled' => 'yes',
        ]);

        Log::info('PPP secret disabled for suspended service', [
            'service_id' => $this->service->id,
            'username' => $this->service->username,
            'secret_id' => $secretId,
        ]);
    }

    /**
     * Disconnect any active PPP sessions for this service.
     */
    protected function disconnectActiveSessions(): void
    {
        try {
            $activeSessions = $this->device->executeMikrotikCommand('/ppp/active/print', [
                '?name' => $this->service->username,
            ]);

            foreach ($activeSessions as $session) {
                $sessionId = $session['.id'];

                $this->device->executeMikrotikCommand('/ppp/active/remove', [
                    '.id' => $sessionId,
                ]);

                Log::info('Disconnected active PPP session for suspended service', [
                    'service_id' => $this->service->id,
                    'username' => $this->service->username,
                    'session_id' => $sessionId,
                ]);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to disconnect active PPP sessions', [
                'service_id' => $this->service->id,
                'username' => $this->service->username,
                'error' => $e->getMessage(),
            ]);
            // Don't fail the suspension if session disconnection fails
        }
    }

    /**
     * Handle final failure after all retries are exhausted.
     */
    protected function handleFinalFailure(\Throwable $exception): void
    {
        // Mark service as failed suspension
        $this->service->refresh();
        $this->service->status = 'suspension_failed';
        $this->service->save();

        Log::critical('PPPoE service suspension permanently failed', [
            'service_id' => $this->service->id,
            'customer_name' => $this->service->customer->name,
            'username' => $this->service->username,
            'error' => $exception->getMessage(),
        ]);

        // Could send notification to admin here
    }
}
