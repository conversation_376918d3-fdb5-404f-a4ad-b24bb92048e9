<?php

namespace App\Jobs\MikroTik;

use App\Events\StaticIpProvisioningCompleted;
use App\Events\StaticIpProvisioningProgress;
use App\Events\StaticIpProvisioningStarted;
use App\Models\Services\StaticIpService;
use Illuminate\Support\Facades\Log;

class ProvisionStaticIpServiceJob extends BaseMikroTikJob
{
    /**
     * The Static IP service to provision.
     */
    protected StaticIpService $service;

    /**
     * The session ID for progress tracking.
     */
    protected string $sessionId;

    /**
     * Create a new job instance.
     */
    public function __construct(StaticIpService $service, string $sessionId)
    {
        $this->service = $service;
        $this->sessionId = $sessionId;

        parent::__construct($service->device, [
            'service_id' => $service->id,
            'customer_id' => $service->customer_id,
            'customer_name' => $service->customer->name,
            'ip_address' => $service->ip_address,
            'session_id' => $sessionId,
        ]);
    }

    /**
     * Get the operation type for logging.
     */
    protected function getOperationType(): string
    {
        return 'provision_static_ip_service';
    }

    /**
     * Execute the Static IP service provisioning operation.
     */
    protected function executeOperation(): mixed
    {
        // Fire started event
        StaticIpProvisioningStarted::dispatch($this->service, $this->sessionId);

        // Check if service is already provisioned
        $this->service->refresh();
        if ($this->service->mikrotik_route_id && $this->service->mikrotik_nat_id) {
            Log::info('Static IP service already provisioned, skipping', [
                'service_id' => $this->service->id,
            ]);

            StaticIpProvisioningCompleted::dispatch($this->service, $this->sessionId, true);
            return ['status' => 'already_provisioned'];
        }

        $results = [];

        try {
            // Step 1: Create route
            StaticIpProvisioningProgress::dispatch(
                $this->service,
                $this->sessionId,
                'creating_route',
                'Creating network route...',
                1,
                4
            );
            $routeId = $this->createRoute();
            $results['route_id'] = $routeId;

            // Step 2: Create NAT rule
            StaticIpProvisioningProgress::dispatch(
                $this->service,
                $this->sessionId,
                'creating_nat',
                'Configuring NAT rules...',
                2,
                4
            );
            $natId = $this->createNatRule();
            $results['nat_id'] = $natId;

            // Step 3: Create bandwidth queue if bandwidth plan exists
            if ($this->service->bandwidthPlan) {
                StaticIpProvisioningProgress::dispatch(
                    $this->service,
                    $this->sessionId,
                    'creating_queue',
                    'Setting up bandwidth limits...',
                    3,
                    4
                );
                $queueId = $this->createBandwidthQueue();
                $results['queue_id'] = $queueId;
            }

            // Step 4: Finalize
            StaticIpProvisioningProgress::dispatch(
                $this->service,
                $this->sessionId,
                'finalizing',
                'Finalizing service configuration...',
                4,
                4
            );

            // Update service with MikroTik IDs
            $this->service->mikrotik_route_id = $routeId;
            $this->service->mikrotik_nat_id = $natId;
            $this->service->status = 'active';
            $this->service->save();

            Log::info('Static IP service provisioned successfully', [
                'service_id' => $this->service->id,
                'customer_name' => $this->service->customer->name,
                'ip_address' => $this->service->ip_address,
                'results' => $results,
            ]);

            // Fire completion event
            StaticIpProvisioningCompleted::dispatch($this->service, $this->sessionId, true);

            return $results;

        } catch (\Exception $e) {
            Log::error('Static IP service provisioning failed', [
                'service_id' => $this->service->id,
                'error' => $e->getMessage(),
            ]);

            // Fire failure event
            StaticIpProvisioningCompleted::dispatch($this->service, $this->sessionId, false, $e->getMessage());

            throw $e;
        }
    }

    /**
     * Create route for the static IP service.
     */
    protected function createRoute(): string
    {
        $result = $this->device->executeMikrotikCommand('/ip/route/add', [
            'dst-address' => $this->service->ip_address.'/32',
            'gateway' => $this->service->ipPool->gateway,
            'comment' => "Static IP for {$this->service->customer->name}",
        ]);

        $routeId = $result['after']['ret'] ?? null;
        if (! $routeId) {
            throw new \Exception('Failed to create route - no ID returned');
        }

        Log::info('Created route for static IP service', [
            'service_id' => $this->service->id,
            'route_id' => $routeId,
            'ip_address' => $this->service->ip_address,
        ]);

        return $routeId;
    }

    /**
     * Create NAT rule for the static IP service.
     */
    protected function createNatRule(): string
    {
        $result = $this->device->executeMikrotikCommand('/ip/firewall/nat/add', [
            'chain' => 'srcnat',
            'src-address' => $this->service->ip_address.'/32',
            'action' => 'masquerade',
            'comment' => "NAT for {$this->service->customer->name}",
        ]);

        $natId = $result['after']['ret'] ?? null;
        if (! $natId) {
            throw new \Exception('Failed to create NAT rule - no ID returned');
        }

        Log::info('Created NAT rule for static IP service', [
            'service_id' => $this->service->id,
            'nat_id' => $natId,
            'ip_address' => $this->service->ip_address,
        ]);

        return $natId;
    }

    /**
     * Create bandwidth queue for the static IP service.
     */
    protected function createBandwidthQueue(): ?string
    {
        if (! $this->service->bandwidthPlan) {
            return null;
        }

        $plan = $this->service->bandwidthPlan;
        $downloadLimit = $plan->download_speed.'M';
        $uploadLimit = $plan->upload_speed.'M';

        try {
            $result = $this->device->executeMikrotikCommand('/queue/simple/add', [
                'name' => "customer-{$this->service->customer_id}-{$this->service->id}",
                'target' => $this->service->ip_address.'/32',
                'max-limit' => "{$uploadLimit}/{$downloadLimit}",
                'comment' => "Bandwidth for {$this->service->customer->name}",
            ]);

            $queueId = $result['after']['ret'] ?? null;

            Log::info('Created bandwidth queue for static IP service', [
                'service_id' => $this->service->id,
                'queue_id' => $queueId,
                'bandwidth_plan' => $plan->name,
                'limits' => "{$uploadLimit}/{$downloadLimit}",
            ]);

            return $queueId;
        } catch (\Exception $e) {
            Log::warning('Failed to create bandwidth queue for static IP service', [
                'service_id' => $this->service->id,
                'error' => $e->getMessage(),
            ]);

            // Don't fail provisioning if queue creation fails
            return null;
        }
    }

    /**
     * Handle final failure after all retries are exhausted.
     */
    protected function handleFinalFailure(\Throwable $exception): void
    {
        // Mark service as failed provisioning
        $this->service->refresh();
        $this->service->status = 'provisioning_failed';
        $this->service->save();

        Log::critical('Static IP service provisioning permanently failed', [
            'service_id' => $this->service->id,
            'customer_name' => $this->service->customer->name,
            'ip_address' => $this->service->ip_address,
            'error' => $exception->getMessage(),
        ]);

        // Fire failure event
        StaticIpProvisioningCompleted::dispatch($this->service, $this->sessionId, false, $exception->getMessage());

        // Could send notification to admin here
    }
}
