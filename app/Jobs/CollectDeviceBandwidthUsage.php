<?php

namespace App\Jobs;

use App\Models\Bandwidth\BandwidthUsage;
use App\Models\Network\NetworkDevice;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CollectDeviceBandwidthUsage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $device;

    protected $dryRun;

    protected $collectionTime;

    /**
     * Create a new job instance.
     */
    public function __construct(NetworkDevice $device, bool $dryRun = false)
    {
        $this->device = $device;
        $this->dryRun = $dryRun;
        $this->collectionTime = now();
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        $startTime = microtime(true);

        Log::info('Starting bandwidth collection for device', [
            'device_id' => $this->device->id,
            'device_name' => $this->device->name,
            'device_ip' => $this->device->ip_address,
            'dry_run' => $this->dryRun,
        ]);

        try {
            // Test device connectivity
            if (! $this->testDeviceConnection()) {
                throw new \Exception('Cannot connect to device');
            }

            $result = [
                'success' => true,
                'customers_processed' => 0,
                'interfaces_processed' => 0,
                'error' => null,
            ];

            // Collect interface statistics via SNMP
            $interfaceStats = $this->collectInterfaceStatistics();
            $result['interfaces_processed'] = count($interfaceStats);

            // Collect customer queue statistics via API
            $customerUsage = $this->collectCustomerQueueStatistics();
            $result['customers_processed'] = count($customerUsage);

            // Store usage data
            if (! $this->dryRun) {
                $this->storeUsageData($customerUsage, $interfaceStats);
            }

            $duration = round((microtime(true) - $startTime) * 1000, 2);

            Log::info('Bandwidth collection completed successfully', [
                'device_id' => $this->device->id,
                'customers_processed' => $result['customers_processed'],
                'interfaces_processed' => $result['interfaces_processed'],
                'duration_ms' => $duration,
            ]);

            return $result;

        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);

            Log::error('Bandwidth collection failed', [
                'device_id' => $this->device->id,
                'device_name' => $this->device->name,
                'error' => $e->getMessage(),
                'duration_ms' => $duration,
            ]);

            return [
                'success' => false,
                'customers_processed' => 0,
                'interfaces_processed' => 0,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Test device connection.
     */
    protected function testDeviceConnection(): bool
    {
        try {
            // Check if SNMP extension is available
            if (function_exists('snmp2_get')) {
                // Test SNMP connection
                $community = $this->device->getConfigValue('snmp_community', 'public');
                $result = snmp2_get($this->device->ip_address, $community, '*******.*******.0'); // sysDescr

                if ($result !== false) {
                    return true;
                }
            }

            // Fallback to API test - testConnection() returns array, check if successful
            $apiResult = $this->device->testConnection();

            return $apiResult !== null && isset($apiResult['status']) && $apiResult['status'] === 'success';
        } catch (\Exception $e) {
            Log::warning('Device connection test failed', [
                'device_id' => $this->device->id,
                'error' => $e->getMessage(),
            ]);

            // Try API as fallback
            try {
                $apiResult = $this->device->testConnection();

                return $apiResult !== null && isset($apiResult['status']) && $apiResult['status'] === 'success';
            } catch (\Exception $apiException) {
                Log::error('Both SNMP and API connection failed', [
                    'device_id' => $this->device->id,
                    'snmp_error' => $e->getMessage(),
                    'api_error' => $apiException->getMessage(),
                ]);

                return false;
            }
        }
    }

    /**
     * Collect interface statistics via SNMP.
     */
    protected function collectInterfaceStatistics(): array
    {
        $interfaces = [];

        // Check if SNMP extension is available
        if (! function_exists('snmp2_walk')) {
            Log::info('SNMP extension not available, skipping interface statistics', [
                'device_id' => $this->device->id,
            ]);

            return $interfaces;
        }

        $community = $this->device->getConfigValue('snmp_community', 'public');

        try {
            // Get interface names
            $ifNames = snmp2_walk($this->device->ip_address, $community, '*******.*******.1.2');

            // Get interface input octets (bytes received)
            $ifInOctets = snmp2_walk($this->device->ip_address, $community, '*******.*******.1.10');

            // Get interface output octets (bytes transmitted)
            $ifOutOctets = snmp2_walk($this->device->ip_address, $community, '*******.*******.1.16');

            // Parse and combine data
            foreach ($ifNames as $oid => $name) {
                $index = str_replace('*******.*******.1.2.', '', $oid);

                $inOid = "*******.*******.1.10.{$index}";
                $outOid = "*******.*******.1.16.{$index}";

                $interfaces[$index] = [
                    'name' => trim($name, '"'),
                    'in_octets' => isset($ifInOctets[$inOid]) ? (int) $ifInOctets[$inOid] : 0,
                    'out_octets' => isset($ifOutOctets[$outOid]) ? (int) $ifOutOctets[$outOid] : 0,
                    'total_octets' => (isset($ifInOctets[$inOid]) ? (int) $ifInOctets[$inOid] : 0) +
                                    (isset($ifOutOctets[$outOid]) ? (int) $ifOutOctets[$outOid] : 0),
                ];
            }

            Log::info('Collected interface statistics via SNMP', [
                'device_id' => $this->device->id,
                'interfaces_count' => count($interfaces),
            ]);

        } catch (\Exception $e) {
            Log::warning('SNMP interface collection failed', [
                'device_id' => $this->device->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $interfaces;
    }

    /**
     * Collect customer queue statistics via MikroTik API.
     */
    protected function collectCustomerQueueStatistics(): array
    {
        $customerUsage = [];

        try {
            // Collect Static IP customer usage
            $staticIpUsage = $this->collectStaticIpUsage();
            $customerUsage = array_merge($customerUsage, $staticIpUsage);

            // Collect PPPoE customer usage
            $pppoeUsage = $this->collectPppoeUsage();
            $customerUsage = array_merge($customerUsage, $pppoeUsage);

            Log::info('Collected customer queue statistics', [
                'device_id' => $this->device->id,
                'static_ip_customers' => count($staticIpUsage),
                'pppoe_customers' => count($pppoeUsage),
                'total_customers' => count($customerUsage),
            ]);

        } catch (\Exception $e) {
            Log::error('Customer queue collection failed', [
                'device_id' => $this->device->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $customerUsage;
    }

    /**
     * Collect Static IP customer usage.
     */
    protected function collectStaticIpUsage(): array
    {
        $usage = [];

        try {
            // Get all queue statistics
            $queues = $this->device->executeMikrotikCommand('/queue/simple/print', [
                '?disabled' => 'false',
            ]);

            foreach ($queues as $queue) {
                // Extract IP from target (format: *************/32)
                if (! isset($queue['target'])) {
                    continue;
                }

                $target = $queue['target'];
                $ip = str_replace('/32', '', $target);

                // Find corresponding service
                $service = StaticIpService::where('device_id', $this->device->id)
                    ->where('ip_address', $ip)
                    ->where('status', 'active')
                    ->first();

                // Include ALL queues, even if not mapped to database customers
                $usage[] = [
                    'service_type' => 'static_ip',
                    'service_id' => $service ? $service->id : null,
                    'customer_id' => $service ? $service->customer_id : null,
                    'identifier' => $ip,
                    'download_bytes' => isset($queue['bytes']) ? $this->parseBytes($queue['bytes'], 'download') : 0,
                    'upload_bytes' => isset($queue['bytes']) ? $this->parseBytes($queue['bytes'], 'upload') : 0,
                    'queue_name' => $queue['name'] ?? '',
                    'is_mapped' => $service !== null,
                    'target' => $target,
                ];
            }

        } catch (\Exception $e) {
            Log::warning('Static IP usage collection failed', [
                'device_id' => $this->device->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $usage;
    }

    /**
     * Collect PPPoE customer usage.
     */
    protected function collectPppoeUsage(): array
    {
        $usage = [];

        try {
            // Get active PPPoE sessions
            $sessions = $this->device->executeMikrotikCommand('/ppp/active/print');

            foreach ($sessions as $session) {
                if (! isset($session['name'])) {
                    continue;
                }

                $username = $session['name'];

                // Find corresponding service
                $service = PppoeService::where('device_id', $this->device->id)
                    ->where('username', $username)
                    ->where('status', 'active')
                    ->first();

                if ($service) {
                    $usage[] = [
                        'service_type' => 'pppoe',
                        'service_id' => $service->id,
                        'customer_id' => $service->customer_id,
                        'identifier' => $username,
                        'download_bytes' => isset($session['bytes-in']) ? (int) $session['bytes-in'] : 0,
                        'upload_bytes' => isset($session['bytes-out']) ? (int) $session['bytes-out'] : 0,
                        'session_uptime' => $session['uptime'] ?? '',
                    ];
                }
            }

        } catch (\Exception $e) {
            Log::warning('PPPoE usage collection failed', [
                'device_id' => $this->device->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $usage;
    }

    /**
     * Parse bytes from MikroTik queue format.
     */
    protected function parseBytes(string $bytes, string $direction): int
    {
        // Format: "upload_bytes/download_bytes" (first/second)
        $parts = explode('/', $bytes);

        if ($direction === 'upload' && isset($parts[0])) {
            return (int) $parts[0];
        }

        if ($direction === 'download' && isset($parts[1])) {
            return (int) $parts[1];
        }

        return 0;
    }

    /**
     * Store usage data in database.
     */
    protected function storeUsageData(array $customerUsage, array $interfaceStats): void
    {
        $periodStart = $this->collectionTime->startOfMinute();
        $periodEnd = $this->collectionTime->copy()->addMinutes(15)->startOfMinute();

        $mappedCount = 0;
        $unmappedCount = 0;

        foreach ($customerUsage as $usage) {
            try {
                // Store in queue_usage table (for ALL queues)
                \App\Models\Bandwidth\QueueUsage::recordQueueUsage(
                    array_merge($usage, ['device_id' => $this->device->id]),
                    $periodStart,
                    $periodEnd
                );

                // Also store in bandwidth_usage table if mapped to a customer
                if ($usage['is_mapped'] && $usage['service_id']) {
                    $modelClass = $usage['service_type'] === 'static_ip'
                        ? StaticIpService::class
                        : PppoeService::class;

                    BandwidthUsage::recordUsage(
                        $modelClass::find($usage['service_id']),
                        $usage['download_bytes'],
                        $usage['upload_bytes'],
                        $periodStart,
                        $periodEnd
                    );
                    $mappedCount++;
                } else {
                    $unmappedCount++;
                }

            } catch (\Exception $e) {
                Log::warning('Failed to store usage data', [
                    'device_id' => $this->device->id,
                    'queue_name' => $usage['queue_name'] ?? 'unknown',
                    'service_id' => $usage['service_id'],
                    'error' => $e->getMessage(),
                ]);
            }
        }

        Log::info('Stored usage data', [
            'device_id' => $this->device->id,
            'total_queues' => count($customerUsage),
            'mapped_customers' => $mappedCount,
            'unmapped_queues' => $unmappedCount,
            'period_start' => $periodStart,
            'period_end' => $periodEnd,
        ]);
    }
}
