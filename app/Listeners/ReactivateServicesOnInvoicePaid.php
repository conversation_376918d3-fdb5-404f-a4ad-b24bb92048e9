<?php

namespace App\Listeners;

use App\Events\InvoicePaid;
use App\Services\ServiceSuspensionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ReactivateServicesOnInvoicePaid implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The service suspension service.
     *
     * @var \App\Services\ServiceSuspensionService
     */
    protected $suspensionService;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(ServiceSuspensionService $suspensionService)
    {
        $this->suspensionService = $suspensionService;
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(InvoicePaid $event)
    {
        $invoice = $event->invoice;

        // Only process subscription invoices
        if (! $invoice->subscription_id) {
            return;
        }

        $subscription = $invoice->subscription;

        // Check if the subscription is suspended
        if ($subscription->status !== 'suspended') {

            return;
        }

        // Check if customer has other overdue invoices
        $otherOverdueInvoices = $invoice->customer->invoices()
            ->where('id', '!=', $invoice->id)
            ->where('subscription_id', $subscription->id)
            ->where('status', 'overdue')
            ->count();

        if ($otherOverdueInvoices > 0) {

            return;
        }

        // Reactivate services for the subscription
        $result = $this->suspensionService->reactivateServices($subscription);

        if (! $result['success']) {
            Log::error('Failed to reactivate services for subscription', [
                'subscription_id' => $subscription->id,
                'invoice_id' => $invoice->id,
                'error' => $result['message'],
            ]);

            // You could retry or notify an admin here
            // $this->release(60); // Retry after 60 seconds
        } else {
            Log::info('Successfully reactivated services for subscription', [
                'subscription_id' => $subscription->id,
                'invoice_id' => $invoice->id,
                'services' => $result['services'],
            ]);
        }
    }
}
