<?php

namespace App\Listeners;

use App\Events\SubscriptionActivated;
use App\Services\ServiceProvisioningService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ProvisionServicesOnSubscriptionActivation implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The service provisioning service.
     *
     * @var \App\Services\ServiceProvisioningService
     */
    protected $provisioningService;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(ServiceProvisioningService $provisioningService)
    {
        $this->provisioningService = $provisioningService;
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(SubscriptionActivated $event)
    {
        $subscription = $event->subscription;

        Log::info('Handling subscription activation for service provisioning', [
            'subscription_id' => $subscription->id,
            'customer_id' => $subscription->customer_id,
        ]);

        // Provision services for the subscription
        $result = $this->provisioningService->provisionServices($subscription);

        if (! $result['success']) {
            Log::error('Failed to provision services for subscription', [
                'subscription_id' => $subscription->id,
                'error' => $result['message'],
            ]);

            // You could retry or notify an admin here
            // $this->release(60); // Retry after 60 seconds
        } else {
            Log::info('Successfully provisioned services for subscription', [
                'subscription_id' => $subscription->id,
                'services' => $result['services'],
            ]);
        }
    }
}
