<?php

namespace App\Listeners;

use App\Events\InvoiceOverdue;
use App\Services\ServiceSuspensionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SuspendServicesOnInvoiceOverdue implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The service suspension service.
     *
     * @var \App\Services\ServiceSuspensionService
     */
    protected $suspensionService;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(ServiceSuspensionService $suspensionService)
    {
        $this->suspensionService = $suspensionService;
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(InvoiceOverdue $event)
    {
        $invoice = $event->invoice;

        // Only process subscription invoices
        if (! $invoice->subscription_id) {
            return;
        }

        $subscription = $invoice->subscription;

        // Check if the subscription is already suspended
        if ($subscription->status === 'suspended') {
            Log::info('Subscription already suspended, skipping service suspension', [
                'subscription_id' => $subscription->id,
                'invoice_id' => $invoice->id,
            ]);

            return;
        }

        Log::info('Handling invoice overdue for service suspension', [
            'invoice_id' => $invoice->id,
            'subscription_id' => $subscription->id,
            'customer_id' => $invoice->customer_id,
            'days_overdue' => $event->daysOverdue,
        ]);

        // Only suspend services if the invoice is more than 7 days overdue
        if ($event->daysOverdue < 7) {
            Log::info('Invoice not overdue enough for service suspension', [
                'invoice_id' => $invoice->id,
                'days_overdue' => $event->daysOverdue,
            ]);

            return;
        }

        // Suspend services for the subscription
        $result = $this->suspensionService->suspendServices($subscription);

        if (! $result['success']) {
            Log::error('Failed to suspend services for subscription', [
                'subscription_id' => $subscription->id,
                'invoice_id' => $invoice->id,
                'error' => $result['message'],
            ]);

            // You could retry or notify an admin here
            // $this->release(60); // Retry after 60 seconds
        } else {
            Log::info('Successfully suspended services for subscription', [
                'subscription_id' => $subscription->id,
                'invoice_id' => $invoice->id,
                'services' => $result['services'],
            ]);
        }
    }
}
