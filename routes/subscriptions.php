<?php

use App\Http\Controllers\Web\StreamlinedSubscriptionController;
use App\Http\Controllers\Web\SubscriptionController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Subscription routes with permission middleware
    Route::get('/subscriptions', [SubscriptionController::class, 'index'])->middleware('permission:view subscriptions')->name('subscriptions.index');
    Route::get('/subscriptions/create', [SubscriptionController::class, 'create'])->middleware('permission:create subscriptions')->name('subscriptions.create');
    Route::post('/subscriptions', [SubscriptionController::class, 'store'])->middleware('permission:create subscriptions')->name('subscriptions.store');
    Route::get('/subscriptions/{subscription}', [SubscriptionController::class, 'show'])->middleware('permission:view subscriptions')->name('subscriptions.show');
    Route::get('/subscriptions/{subscription}/edit', [SubscriptionController::class, 'edit'])->middleware('permission:edit subscriptions')->name('subscriptions.edit');
    Route::put('/subscriptions/{subscription}', [SubscriptionController::class, 'update'])->middleware('permission:edit subscriptions')->name('subscriptions.update');
    Route::put('/subscriptions/{subscription}/status', [SubscriptionController::class, 'updateStatus'])->middleware('permission:edit subscriptions')->name('subscriptions.update-status');
    Route::delete('/subscriptions/{subscription}', [SubscriptionController::class, 'destroy'])->middleware('permission:delete subscriptions')->name('subscriptions.destroy');

    // Streamlined subscription routes with permission middleware
    Route::get('/subscriptions/streamlined/create', [StreamlinedSubscriptionController::class, 'create'])->middleware('permission:create subscriptions')->name('subscriptions.streamlined.create');
    Route::post('/subscriptions/streamlined', [StreamlinedSubscriptionController::class, 'store'])->middleware('permission:create subscriptions')->name('subscriptions.streamlined.store');
    Route::get('/api/bandwidth-plans/{bandwidthPlan}', [StreamlinedSubscriptionController::class, 'getBandwidthPlan'])->middleware('permission:view subscriptions')->name('api.bandwidth-plans.show');

    // Subscription invoices routes (placeholder for future implementation)
    Route::get('/subscriptions/{subscription}/invoices', function () {
        return redirect()->route('subscriptions.show', request()->route('subscription'));
    })->name('subscriptions.invoices.index');

    Route::get('/subscriptions/{subscription}/invoices/create', function () {
        return redirect()->route('subscriptions.show', request()->route('subscription'));
    })->name('subscriptions.invoices.create');
});
