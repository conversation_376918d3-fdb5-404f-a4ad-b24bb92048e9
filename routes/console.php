<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Bandwidth Usage Collection - Every 15 minutes
Schedule::command('bandwidth:collect-usage')
    ->everyFifteenMinutes()
    ->withoutOverlapping(10) // Prevent overlapping runs, timeout after 10 minutes
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/bandwidth-collection.log'));

// Bandwidth Collection Health Check - Every hour
Schedule::command('bandwidth:collection-status --hours=2')
    ->hourly()
    ->appendOutputTo(storage_path('logs/bandwidth-health.log'));

// Automated Monthly Invoice Generation - 1st of every month at 12:00 AM
Schedule::command('billing:generate-monthly-invoices')
    ->dailyAt('00:00')
    ->withoutOverlapping(120) // Prevent overlapping runs, timeout after 2 hours
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/monthly-billing.log'));

// Automated Customer Suspension (Strict) - Daily at 6:00 PM for invoices overdue by 14+ days
Schedule::command('customers:suspend-overdue --grace-days=0 --force')
    ->dailyAt('18:00')
    ->withoutOverlapping(60)
    ->runInBackground()
    ->appendOutputTo(storage_path('logs/customer-suspensions-strict.log'));
