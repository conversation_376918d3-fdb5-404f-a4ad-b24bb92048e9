<?php

use Illuminate\Support\Facades\Broadcast;

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('static-ip-provisioning.{sessionId}', function ($user, $sessionId) {
    // Allow authenticated users to listen to provisioning progress
    // In a production environment, you might want to add additional authorization
    // to ensure users can only listen to their own provisioning sessions
    return $user !== null;
});
