<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware(['auth', 'verified'])->group(function () {
    // Main Network Dashboard with permission middleware
    Route::get('network', [\App\Http\Controllers\Network\NetworkController::class, 'index'])->middleware('permission:view network')->name('network.index');

    // Network Sites with permission middleware
    Route::get('network/sites', [\App\Http\Controllers\Network\NetworkSiteController::class, 'index'])->middleware('permission:view network')->name('network.sites.index');
    Route::get('network/sites/create', [\App\Http\Controllers\Network\NetworkSiteController::class, 'create'])->middleware('permission:manage network sites')->name('network.sites.create');
    Route::post('network/sites', [\App\Http\Controllers\Network\NetworkSiteController::class, 'store'])->middleware('permission:manage network sites')->name('network.sites.store');
    Route::get('network/sites/{site}', [\App\Http\Controllers\Network\NetworkSiteController::class, 'show'])->middleware('permission:view network')->name('network.sites.show');
    Route::get('network/sites/{site}/edit', [\App\Http\Controllers\Network\NetworkSiteController::class, 'edit'])->middleware('permission:manage network sites')->name('network.sites.edit');
    Route::put('network/sites/{site}', [\App\Http\Controllers\Network\NetworkSiteController::class, 'update'])->middleware('permission:manage network sites')->name('network.sites.update');
    Route::delete('network/sites/{site}', [\App\Http\Controllers\Network\NetworkSiteController::class, 'destroy'])->middleware('permission:manage network sites')->name('network.sites.destroy');

    // Network Devices with permission middleware
    Route::get('network/devices', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'index'])->middleware('permission:view network')->name('network.devices.index');

    Route::get('network/devices/create', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'create'])->middleware('permission:manage network devices')->name('network.devices.create');

    Route::get('network/devices/{device}', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'show'])->middleware('permission:view network')->name('network.devices.show');

    Route::get('network/devices/{device}/edit', function ($device) {
        return Inertia::render('network/devices/edit', [
            'deviceId' => $device,
        ]);
    })->middleware('permission:manage network devices')->name('network.devices.edit');

    Route::get('network/devices/{device}/interfaces', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'interfaces'])->middleware('permission:view network')->name('network.devices.interfaces');

    // AJAX route for real-time interface updates
    Route::get('network/api/devices/{device}/interfaces', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'interfacesApi'])->middleware('permission:view network')->name('network.devices.interfaces.api');

    // Live MikroTik interface fetch
    Route::get('network/devices/{device}/interfaces/{interface}/live', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'interfaceFromDevice'])->middleware('permission:view network')->name('network.devices.interfaces.live');

    // Network Device API Routes with permission middleware
    Route::post('network/devices', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'store'])->middleware('permission:manage network devices')->name('network.devices.store');
    Route::put('network/devices/{device}', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'update'])->middleware('permission:manage network devices')->name('network.devices.update');
    Route::delete('network/devices/{device}', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'destroy'])->middleware('permission:manage network devices')->name('network.devices.destroy');
    Route::post('network/devices/{device}/test-connection', [\App\Http\Controllers\Network\NetworkDeviceController::class, 'testConnection'])->middleware('permission:manage network devices')->name('network.devices.test-connection');
    Route::get('network/api/interfaces/{interface}', [\App\Http\Controllers\Network\NetworkInterfaceController::class, 'show'])->middleware('permission:view network')->name('network.interfaces.show.api');

    // Network Interfaces with permission middleware
    Route::get('network/interfaces', function () {
        return Inertia::render('network/interfaces/index');
    })->middleware('permission:view network')->name('network.interfaces.index');

    Route::get('network/interfaces/create', function () {
        return Inertia::render('network/interfaces/create');
    })->middleware('permission:manage network devices')->name('network.interfaces.create');

    Route::get('network/interfaces/{interface}', function ($interface) {
        return Inertia::render('network/interfaces/show', [
            'interfaceId' => $interface,
        ]);
    })->middleware('permission:view network')->name('network.interfaces.show');

    Route::get('network/interfaces/{interface}/edit', function ($interface) {
        return Inertia::render('network/interfaces/edit', [
            'interfaceId' => $interface,
        ]);
    })->middleware('permission:manage network devices')->name('network.interfaces.edit');

    // Network Connections with permission middleware
    Route::get('network/connections', function () {
        return Inertia::render('network/connections/index');
    })->middleware('permission:view network')->name('network.connections.index');

    Route::get('network/connections/create', function () {
        return Inertia::render('network/connections/create');
    })->middleware('permission:manage network devices')->name('network.connections.create');

    Route::get('network/connections/{connection}', function ($connection) {
        return Inertia::render('network/connections/show', [
            'connectionId' => $connection,
        ]);
    })->middleware('permission:view network')->name('network.connections.show');

    Route::get('network/connections/{connection}/edit', function ($connection) {
        return Inertia::render('network/connections/edit', [
            'connectionId' => $connection,
        ]);
    })->middleware('permission:manage network devices')->name('network.connections.edit');

    // Network Maps with permission middleware
    Route::get('network/maps', function () {
        return Inertia::render('network/maps/index');
    })->middleware('permission:view network')->name('network.maps.index');

    Route::get('network/maps/create', function () {
        return Inertia::render('network/maps/create');
    })->middleware('permission:manage network devices')->name('network.maps.create');

    Route::get('network/maps/{map}', function ($map) {
        return Inertia::render('network/maps/show', [
            'mapId' => $map,
        ]);
    })->middleware('permission:view network')->name('network.maps.show');

    Route::get('network/maps/{map}/edit', function ($map) {
        return Inertia::render('network/maps/edit', [
            'mapId' => $map,
        ]);
    })->middleware('permission:manage network devices')->name('network.maps.edit');
});
