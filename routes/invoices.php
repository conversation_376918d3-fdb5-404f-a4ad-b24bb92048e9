<?php

use App\Http\Controllers\Web\InvoiceController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Invoice routes with permission middleware
    Route::get('/invoices', [InvoiceController::class, 'index'])->middleware('permission:view invoices')->name('invoices.index');
    Route::get('/invoices/create', [InvoiceController::class, 'create'])->middleware('permission:create invoices')->name('invoices.create');
    Route::post('/invoices', [InvoiceController::class, 'store'])->middleware('permission:create invoices')->name('invoices.store');
    Route::get('/invoices/{invoice}', [InvoiceController::class, 'show'])->middleware('permission:view invoices')->name('invoices.show');
    Route::get('/invoices/{invoice}/edit', [InvoiceController::class, 'edit'])->middleware('permission:edit invoices')->name('invoices.edit');
    Route::put('/invoices/{invoice}', [InvoiceController::class, 'update'])->middleware('permission:edit invoices')->name('invoices.update');
    Route::delete('/invoices/{invoice}', [InvoiceController::class, 'destroy'])->middleware('permission:delete invoices')->name('invoices.destroy');
    Route::get('/invoices/{invoice}/view-pdf', [InvoiceController::class, 'viewPdf'])->middleware('permission:view invoices')->name('invoices.view-pdf');
    Route::get('/invoices/{invoice}/download', [InvoiceController::class, 'downloadPdf'])->middleware('permission:view invoices')->name('invoices.download');

    // Payment routes with permission middleware
    Route::get('/payments', [\App\Http\Controllers\PaymentController::class, 'index'])->middleware('permission:view payments')->name('payments.index');
    Route::get('/payments/list', [\App\Http\Controllers\PaymentController::class, 'paymentsList'])->middleware('permission:view payments')->name('payments.list');
    Route::get('/payments/export', [\App\Http\Controllers\PaymentController::class, 'export'])->middleware('permission:view payments')->name('payments.export');
    Route::post('/payments', [\App\Http\Controllers\PaymentController::class, 'store'])->middleware('permission:process payments')->name('payments.store');
    Route::get('/payments/{payment}', [\App\Http\Controllers\PaymentController::class, 'show'])->middleware('permission:view payments')->name('payments.show');
    Route::post('/payments/{payment}/confirm', [\App\Http\Controllers\PaymentController::class, 'confirm'])->middleware('permission:process payments')->name('payments.confirm');
    Route::get('/payments/pending/cash', [\App\Http\Controllers\PaymentController::class, 'pendingCashPayments'])->middleware('permission:view payments')->name('payments.pending-cash');
});
