<?php

use App\Http\Controllers\PaymentsController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

// Test PDF route (remove in production)
Route::get('/test-pdf', function () {
    try {
        $html = '<!DOCTYPE html><html><head><title>Test</title></head><body><h1>Simple Test</h1><p>Hello World</p><p>Time: '.date('Y-m-d H:i:s').'</p></body></html>';

        $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML($html);
        $pdf->setPaper('A4', 'portrait');

        $output = $pdf->output();

        return response($output, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="test.pdf"',
        ]);
    } catch (\Exception $e) {
        return response('PDF Error: '.$e->getMessage().'<br><br>Trace: '.$e->getTraceAsString(), 500);
    }
})->middleware('auth');

// Test invoice PDF with simple template
Route::get('/test-invoice-pdf/{invoice}', function ($invoiceId) {
    $invoice = \App\Models\Invoice::with(['customer', 'items'])->findOrFail($invoiceId);

    $companyInfo = [
        'name' => 'Test Company',
    ];

    $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('invoices.pdf-simple', [
        'invoice' => $invoice,
        'companyInfo' => $companyInfo,
    ]);

    $pdf->setPaper('A4', 'portrait');

    return response($pdf->output(), 200, [
        'Content-Type' => 'application/pdf',
        'Content-Disposition' => 'inline; filename="test-invoice.pdf"',
    ]);
})->middleware('auth');

// Test HTML output (to see if template is working)
Route::get('/test-html/{invoice}', function ($invoiceId) {
    $invoice = \App\Models\Invoice::with(['customer', 'items'])->findOrFail($invoiceId);

    $companyInfo = [
        'name' => 'Test Company',
    ];

    return view('invoices.pdf-simple', [
        'invoice' => $invoice,
        'companyInfo' => $companyInfo,
    ]);
})->middleware('auth');

// Debug PDF route to test actual PDF viewing
Route::get('/debug-pdf/{invoice}', function ($invoiceId) {
    try {
        $invoice = \App\Models\Invoice::with(['customer', 'items', 'payments.confirmedBy'])->findOrFail($invoiceId);
        $invoice->append(['total_paid', 'remaining_balance']);

        $companyInfo = [
            'name' => env('ISP_COMPANY_NAME', env('APP_NAME', 'ISP Management System')),
            'address' => '',
            'email' => env('ISP_ADMIN_EMAIL', env('MAIL_FROM_ADDRESS', '')),
            'phone' => '',
            'website' => env('APP_URL', ''),
        ];

        $formatCurrency = function ($amount) {
            return formatCurrency($amount ?? 0);
        };

        $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('invoices.pdf-simple', [
            'invoice' => $invoice,
            'companyInfo' => $companyInfo,
            'formatCurrency' => $formatCurrency,
        ]);

        $pdf->setPaper('A4', 'portrait');
        $pdf->setOptions([
            'isHtml5ParserEnabled' => false,
            'isPhpEnabled' => false,
            'defaultFont' => 'Arial',
            'isRemoteEnabled' => false,
        ]);

        return response($pdf->output(), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="debug-invoice-'.$invoice->invoice_number.'.pdf"',
        ]);

    } catch (\Exception $e) {
        return response('PDF Error: '.$e->getMessage().'<br><br>Trace: '.$e->getTraceAsString(), 500);
    }
})->middleware('auth');

// Test WebSocket connection
Route::get('/test-websocket', function () {
    return inertia('test-websocket');
})->middleware('auth');

Route::post('/test-broadcast', function (Illuminate\Http\Request $request) {
    $sessionId = $request->input('sessionId');

    // Broadcast a test event
    broadcast(new class($sessionId) implements \Illuminate\Contracts\Broadcasting\ShouldBroadcastNow {
        use \Illuminate\Broadcasting\InteractsWithSockets;
        use \Illuminate\Foundation\Events\Dispatchable;

        public function __construct(public string $sessionId) {}

        public function broadcastOn(): array {
            return [new \Illuminate\Broadcasting\Channel('test-channel')];
        }

        public function broadcastAs(): string {
            return 'test.message';
        }

        public function broadcastWith(): array {
            return [
                'message' => 'Test broadcast successful!',
                'session_id' => $this->sessionId,
                'timestamp' => now()->toISOString(),
            ];
        }
    });

    return response()->json(['status' => 'broadcast_sent', 'session_id' => $sessionId]);
})->middleware('auth');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

    // Progress tracking routes
    Route::get('/progress/{sessionId}/stream', [App\Http\Controllers\ProgressController::class, 'stream'])->name('progress.stream');
    Route::get('/progress/{sessionId}/status', [App\Http\Controllers\ProgressController::class, 'status'])->name('progress.status');
    Route::delete('/progress/{sessionId}', [App\Http\Controllers\ProgressController::class, 'clear'])->name('progress.clear');
});

Route::post('/validation', [PaymentsController::class, 'validation']);
Route::get('/registerUrl', [PaymentsController::class, 'registerUrl']);
Route::post('/confirmation', [PaymentsController::class, 'confirmation']);

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/bandwidth.php';
require __DIR__.'/network.php';
require __DIR__.'/services.php';
require __DIR__.'/customers.php';
require __DIR__.'/subscriptions.php';
require __DIR__.'/invoices.php';
