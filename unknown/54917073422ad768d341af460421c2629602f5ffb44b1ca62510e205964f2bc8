import { usePage } from '@inertiajs/react';

/**
 * Custom hook for checking user permissions
 * 
 * @returns Object with permission checking utilities
 */
export function usePermissions() {
  const { auth } = usePage().props as any;
  const permissions = auth.permissions || [];

  /**
   * Check if user has a specific permission
   * 
   * @param permission - The permission string to check
   * @returns boolean - True if user has the permission
   */
  const hasPermission = (permission: string): boolean => {
    return permissions.includes(permission);
  };

  /**
   * Check if user has any of the provided permissions
   * 
   * @param permissionList - Array of permission strings
   * @returns boolean - True if user has at least one permission
   */
  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(permission => permissions.includes(permission));
  };

  /**
   * Check if user has all of the provided permissions
   * 
   * @param permissionList - Array of permission strings
   * @returns boolean - True if user has all permissions
   */
  const hasAllPermissions = (permissionList: string[]): boolean => {
    return permissionList.every(permission => permissions.includes(permission));
  };

  /**
   * Get all user permissions
   * 
   * @returns string[] - Array of all user permissions
   */
  const getAllPermissions = (): string[] => {
    return permissions;
  };

  /**
   * Check if user has permission for a specific resource action
   * Convenience method for common CRUD operations
   * 
   * @param resource - The resource name (e.g., 'customers', 'invoices')
   * @param action - The action ('view', 'create', 'edit', 'delete')
   * @returns boolean - True if user has the permission
   */
  const canAccess = (resource: string, action: 'view' | 'create' | 'edit' | 'delete'): boolean => {
    return hasPermission(`${action} ${resource}`);
  };

  /**
   * Check if user can perform any CRUD operation on a resource
   * 
   * @param resource - The resource name (e.g., 'customers', 'invoices')
   * @returns boolean - True if user can view, create, edit, or delete the resource
   */
  const canAccessResource = (resource: string): boolean => {
    return hasAnyPermission([
      `view ${resource}`,
      `create ${resource}`,
      `edit ${resource}`,
      `delete ${resource}`
    ]);
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getAllPermissions,
    canAccess,
    canAccessResource,
    permissions
  };
}

/**
 * Type definitions for permission-related props
 */
export interface PermissionProps {
  permission?: string;
  permissions?: string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
}

/**
 * Common permission strings for easy reference
 */
export const PERMISSIONS = {
  // Customer permissions
  CUSTOMERS: {
    VIEW: 'view customers',
    CREATE: 'create customers',
    EDIT: 'edit customers',
    DELETE: 'delete customers'
  },
  
  // Subscription permissions
  SUBSCRIPTIONS: {
    VIEW: 'view subscriptions',
    CREATE: 'create subscriptions',
    EDIT: 'edit subscriptions',
    DELETE: 'delete subscriptions'
  },
  
  // Invoice permissions
  INVOICES: {
    VIEW: 'view invoices',
    CREATE: 'create invoices',
    EDIT: 'edit invoices',
    DELETE: 'delete invoices'
  },
  
  // Payment permissions
  PAYMENTS: {
    VIEW: 'view payments',
    PROCESS: 'process payments',
    REFUND: 'refund payments'
  },
  
  // Service permissions
  SERVICES: {
    VIEW: 'view services',
    CREATE: 'create services',
    EDIT: 'edit services',
    DELETE: 'delete services'
  },
  
  // Network permissions
  NETWORK: {
    VIEW: 'view network',
    MANAGE_SITES: 'manage network sites',
    MANAGE_DEVICES: 'manage network devices'
  },
  
  // Bandwidth permissions
  BANDWIDTH: {
    VIEW: 'view bandwidth',
    MANAGE_PLANS: 'manage bandwidth plans',
    MANAGE_POLICIES: 'manage bandwidth policies',
    MANAGE_QUOTAS: 'manage bandwidth quotas'
  },
  
  // IP Pool permissions
  IP_POOLS: {
    MANAGE: 'manage ip pools'
  },
  
  // Admin permissions
  ADMIN: {
    VIEW_SETTINGS: 'view admin settings',
    MANAGE_SYSTEM: 'manage system settings',
    MANAGE_MPESA: 'manage mpesa settings',
    VIEW_USERS: 'view users',
    CREATE_USERS: 'create users',
    EDIT_USERS: 'edit users',
    DELETE_USERS: 'delete users',
    VIEW_ROLES: 'view roles',
    CREATE_ROLES: 'create roles',
    EDIT_ROLES: 'edit roles',
    DELETE_ROLES: 'delete roles'
  },
  
  // Report permissions
  REPORTS: {
    VIEW: 'view reports',
    EXPORT: 'export reports'
  }
} as const;
