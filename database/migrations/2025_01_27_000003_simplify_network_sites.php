<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Simplify network sites table by removing unnecessary fields.
     * Keep only essential fields for basic site management.
     */
    public function up(): void
    {
        Schema::table('network_sites', function (Blueprint $table) {
            // Remove complex location fields - use single address field instead
            $table->dropColumn([
                'city',
                'state',
                'country',
                'postal_code',
                'latitude',
                'longitude',
            ]);

            // Remove hierarchical site structure - not needed for basic operations
            $table->dropForeign(['parent_id']);
            $table->dropColumn('parent_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('network_sites', function (Blueprint $table) {
            // Restore removed columns
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('country')->nullable();
            $table->string('postal_code')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->foreignId('parent_id')->nullable()->constrained('network_sites')->onDelete('set null');
        });
    }
};
