<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Remove ACS (Auto Configuration Server) functionality as it's not needed
     * for MikroTik infrastructure management. ACS is primarily for CPE device
     * management using TR-069 protocol, which is not applicable to our use case.
     */
    public function up(): void
    {
        // Drop ACS tables in reverse dependency order
        Schema::dropIfExists('acs_device_profile');
        Schema::dropIfExists('acs_events');
        Schema::dropIfExists('acs_tasks');
        Schema::dropIfExists('acs_parameters');
        Schema::dropIfExists('acs_profiles');
        Schema::dropIfExists('acs_devices');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Note: This down migration is provided for completeness but ACS functionality
        // is being permanently removed. Recreating these tables would require
        // also restoring all the associated models, controllers, and UI components.

        // ACS devices table
        Schema::create('acs_devices', function (Blueprint $table) {
            $table->id();
            $table->string('serial_number')->unique();
            $table->string('manufacturer');
            $table->string('model');
            $table->string('oui')->nullable();
            $table->string('product_class')->nullable();
            $table->string('software_version')->nullable();
            $table->string('hardware_version')->nullable();
            $table->string('connection_request_url')->nullable();
            $table->string('connection_request_username')->nullable();
            $table->string('connection_request_password')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('mac_address')->nullable();
            $table->timestamp('last_inform')->nullable();
            $table->timestamp('last_successful_inform')->nullable();
            $table->boolean('active')->default(true);
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
        });

        // ACS profiles table
        Schema::create('acs_profiles', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('parameters')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
        });

        // ACS parameters table
        Schema::create('acs_parameters', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('acs_devices')->onDelete('cascade');
            $table->string('name');
            $table->text('value')->nullable();
            $table->string('type')->default('string');
            $table->boolean('writable')->default(false);
            $table->timestamps();
            $table->unique(['device_id', 'name']);
        });

        // ACS tasks table
        Schema::create('acs_tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('acs_devices')->onDelete('cascade');
            $table->string('type');
            $table->json('parameters')->nullable();
            $table->string('status')->default('pending');
            $table->text('result')->nullable();
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
        });

        // ACS events table
        Schema::create('acs_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('acs_devices')->onDelete('cascade');
            $table->string('type');
            $table->text('description')->nullable();
            $table->json('data')->nullable();
            $table->timestamps();
        });

        // ACS device profile pivot table
        Schema::create('acs_device_profile', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('acs_devices')->onDelete('cascade');
            $table->foreignId('profile_id')->constrained('acs_profiles')->onDelete('cascade');
            $table->timestamps();
            $table->unique(['device_id', 'profile_id']);
        });
    }
};
