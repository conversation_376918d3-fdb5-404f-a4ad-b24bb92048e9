<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('queue_usage', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('network_devices')->onDelete('cascade');
            $table->string('queue_name');
            $table->string('target_ip');
            $table->foreignId('customer_id')->nullable()->constrained('customers')->onDelete('set null');
            $table->unsignedBigInteger('service_id')->nullable();
            $table->string('service_type')->nullable();
            $table->bigInteger('download_bytes')->default(0);
            $table->bigInteger('upload_bytes')->default(0);
            $table->bigInteger('total_bytes')->default(0);
            $table->timestamp('period_start');
            $table->timestamp('period_end');
            $table->boolean('is_mapped')->default(false);
            $table->timestamps();

            // Indexes for performance
            $table->index(['device_id', 'period_start', 'period_end']);
            $table->index(['customer_id', 'period_start']);
            $table->index(['queue_name', 'device_id']);
            $table->index(['target_ip', 'device_id']);
            $table->index('is_mapped');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('queue_usage');
    }
};
