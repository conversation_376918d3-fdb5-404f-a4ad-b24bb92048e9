<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Bandwidth plans table
        Schema::create('bandwidth_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('download_speed')->comment('in Kbps'); // Download speed in Kbps
            $table->integer('upload_speed')->comment('in Kbps'); // Upload speed in Kbps
            $table->integer('burst_download_speed')->nullable()->comment('in Kbps'); // Burst download speed in Kbps
            $table->integer('burst_upload_speed')->nullable()->comment('in Kbps'); // Burst upload speed in Kbps
            $table->integer('burst_time')->nullable()->comment('in seconds'); // Burst time in seconds
            $table->integer('priority')->default(8); // QoS priority (1-8, 1 being highest)
            $table->boolean('active')->default(true);
            $table->timestamps();
        });

        // Bandwidth policies table
        Schema::create('bandwidth_policies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['limit', 'shape', 'prioritize', 'quota']);
            $table->json('parameters')->nullable(); // Store policy-specific parameters
            $table->boolean('active')->default(true);
            $table->timestamps();
        });

        // Bandwidth rules table
        Schema::create('bandwidth_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('policy_id')->constrained('bandwidth_policies')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('source_type')->nullable(); // IP, network, user, group, etc.
            $table->string('source_value')->nullable();
            $table->string('destination_type')->nullable(); // IP, network, domain, etc.
            $table->string('destination_value')->nullable();
            $table->string('protocol')->nullable(); // TCP, UDP, ICMP, etc.
            $table->string('port_range')->nullable();
            $table->string('time_range')->nullable();
            $table->integer('priority')->default(1);
            $table->boolean('active')->default(true);
            $table->timestamps();
        });

        // Bandwidth quotas table
        Schema::create('bandwidth_quotas', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->bigInteger('download_limit')->comment('in MB'); // Download limit in MB
            $table->bigInteger('upload_limit')->comment('in MB'); // Upload limit in MB
            $table->bigInteger('total_limit')->comment('in MB'); // Total limit in MB
            $table->enum('period', ['daily', 'weekly', 'monthly', 'custom']);
            $table->integer('custom_period')->nullable()->comment('in days'); // Custom period in days
            $table->enum('action_on_exceed', ['notify', 'throttle', 'block']);
            $table->json('action_parameters')->nullable(); // Store action-specific parameters
            $table->boolean('active')->default(true);
            $table->timestamps();
        });

        // Bandwidth usage table
        Schema::create('bandwidth_usage', function (Blueprint $table) {
            $table->id();
            $table->morphs('usageable'); // Polymorphic relation to users, devices, etc.
            $table->bigInteger('download')->default(0)->comment('in bytes'); // Download usage in bytes
            $table->bigInteger('upload')->default(0)->comment('in bytes'); // Upload usage in bytes
            $table->bigInteger('total')->default(0)->comment('in bytes'); // Total usage in bytes
            $table->timestamp('period_start');
            $table->timestamp('period_end');
            $table->timestamps();
        });

        // Bandwidth assignments table (for assigning plans, policies, quotas to users, devices, etc.)
        Schema::create('bandwidth_assignments', function (Blueprint $table) {
            $table->id();
            $table->morphs('assignable'); // Polymorphic relation to plans, policies, quotas
            $table->morphs('assignee'); // Polymorphic relation to users, devices, etc.
            $table->timestamp('starts_at')->nullable();
            $table->timestamp('ends_at')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bandwidth_assignments');
        Schema::dropIfExists('bandwidth_usage');
        Schema::dropIfExists('bandwidth_quotas');
        Schema::dropIfExists('bandwidth_rules');
        Schema::dropIfExists('bandwidth_policies');
        Schema::dropIfExists('bandwidth_plans');
    }
};
