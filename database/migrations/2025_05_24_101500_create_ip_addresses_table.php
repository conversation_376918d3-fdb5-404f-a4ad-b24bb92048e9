<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ip_addresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ip_pool_id')->constrained()->onDelete('cascade');
            $table->string('ip_address'); // e.g., "***********"
            $table->string('subnet_mask'); // e.g., "24" or "*************"
            $table->string('full_address'); // e.g., "***********/24"
            $table->enum('status', ['available', 'assigned', 'reserved'])->default('available');
            $table->enum('type', ['usable', 'network', 'gateway', 'broadcast'])->default('usable');
            $table->boolean('exists_in_mikrotik')->default(false);
            $table->string('mikrotik_id')->nullable(); // MikroTik internal ID
            $table->foreignId('assigned_to_service_id')->nullable()->constrained('static_ip_services')->onDelete('set null');
            $table->timestamp('assigned_at')->nullable();
            $table->json('mikrotik_response')->nullable(); // Store MikroTik response for debugging
            $table->timestamps();

            // Indexes
            $table->unique(['ip_pool_id', 'ip_address']);
            $table->index(['status', 'ip_pool_id']);
            $table->index(['exists_in_mikrotik']);
            $table->index(['assigned_to_service_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ip_addresses');
    }
};
