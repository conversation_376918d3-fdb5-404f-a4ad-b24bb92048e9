<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Network sites table
        Schema::create('network_sites', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('country')->nullable();
            $table->string('postal_code')->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->string('status')->default('active');
            $table->foreignId('parent_id')->nullable()->constrained('network_sites')->onDelete('set null');
            $table->timestamps();
        });

        // Network devices table
        Schema::create('network_devices', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // router, switch, access point, etc.
            $table->text('description')->nullable();
            $table->string('model')->nullable();
            $table->string('manufacturer')->nullable();
            $table->string('serial_number')->nullable();
            $table->ipAddress('ip_address')->nullable();
            $table->string('mac_address')->nullable();
            $table->string('status')->default('active');
            $table->foreignId('site_id')->constrained('network_sites')->onDelete('cascade');
            $table->decimal('x_position', 10, 2)->nullable(); // For positioning on map
            $table->decimal('y_position', 10, 2)->nullable(); // For positioning on map
            $table->json('configuration')->nullable(); // Store device-specific configuration
            $table->timestamps();
        });

        // Network interfaces table
        Schema::create('network_interfaces', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('network_devices')->onDelete('cascade');
            $table->string('name');
            $table->string('type'); // ethernet, wireless, etc.
            $table->string('status')->default('up');
            $table->ipAddress('ip_address')->nullable();
            $table->string('subnet_mask')->nullable();
            $table->string('mac_address')->nullable();
            $table->integer('speed')->nullable(); // in Mbps
            $table->boolean('is_management')->default(false);
            $table->timestamps();
        });

        // Network connections table (for links between devices)
        Schema::create('network_connections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('source_interface_id')->constrained('network_interfaces')->onDelete('cascade');
            $table->foreignId('target_interface_id')->constrained('network_interfaces')->onDelete('cascade');
            $table->string('type'); // ethernet, fiber, wireless, etc.
            $table->string('status')->default('active');
            $table->integer('bandwidth')->nullable(); // in Mbps
            $table->integer('latency')->nullable(); // in ms
            $table->timestamps();
        });

        // Network maps table
        Schema::create('network_maps', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('site_id')->nullable()->constrained('network_sites')->onDelete('cascade');
            $table->string('background_image')->nullable();
            $table->integer('width')->nullable();
            $table->integer('height')->nullable();
            $table->json('settings')->nullable(); // Store map-specific settings
            $table->timestamps();
        });

        // Network map items table (for devices on maps)
        Schema::create('network_map_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('map_id')->constrained('network_maps')->onDelete('cascade');
            $table->morphs('itemable'); // Polymorphic relation to devices or other items
            $table->decimal('x_position', 10, 2);
            $table->decimal('y_position', 10, 2);
            $table->decimal('scale', 5, 2)->default(1.0);
            $table->integer('rotation')->default(0);
            $table->json('settings')->nullable(); // Store item-specific settings
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('network_map_items');
        Schema::dropIfExists('network_maps');
        Schema::dropIfExists('network_connections');
        Schema::dropIfExists('network_interfaces');
        Schema::dropIfExists('network_devices');
        Schema::dropIfExists('network_sites');
    }
};
