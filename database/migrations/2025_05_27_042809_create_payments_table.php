<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->enum('payment_method', ['cash', 'mpesa'])->default('cash');
            $table->decimal('amount', 10, 2);
            $table->string('reference_number')->nullable();
            $table->string('transaction_id')->nullable();
            $table->enum('status', ['pending', 'completed', 'failed', 'refunded'])->default('pending');
            $table->timestamp('payment_date')->nullable();
            $table->foreignId('confirmed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->json('gateway_response')->nullable();
            $table->text('notes')->nullable();
            $table->string('phone_number')->nullable(); // For M-Pesa payments
            $table->string('mpesa_receipt_number')->nullable(); // M-Pesa specific
            $table->timestamps();

            // Indexes for better performance
            $table->index(['invoice_id', 'status']);
            $table->index(['payment_method', 'status']);
            $table->index(['transaction_id']);
            $table->index(['reference_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
