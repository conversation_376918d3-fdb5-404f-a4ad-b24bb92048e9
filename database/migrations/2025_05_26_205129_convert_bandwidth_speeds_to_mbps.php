<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Convert existing bandwidth speeds from Kbps to Mbps
        DB::statement('
            UPDATE bandwidth_plans
            SET
                download_speed = ROUND(download_speed / 1024, 2),
                upload_speed = ROUND(upload_speed / 1024, 2),
                burst_download_speed = CASE
                    WHEN burst_download_speed IS NOT NULL
                    THEN ROUND(burst_download_speed / 1024, 2)
                    ELSE NULL
                END,
                burst_upload_speed = CASE
                    WHEN burst_upload_speed IS NOT NULL
                    THEN ROUND(burst_upload_speed / 1024, 2)
                    ELSE NULL
                END
            WHERE download_speed > 100 OR upload_speed > 100
        ');

        // Update column comments to reflect Mbps storage
        Schema::table('bandwidth_plans', function (Blueprint $table) {
            $table->integer('download_speed')->comment('in Mbps')->change();
            $table->integer('upload_speed')->comment('in Mbps')->change();
            $table->integer('burst_download_speed')->nullable()->comment('in Mbps')->change();
            $table->integer('burst_upload_speed')->nullable()->comment('in Mbps')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Convert back from Mbps to Kbps
        DB::statement('
            UPDATE bandwidth_plans
            SET
                download_speed = download_speed * 1024,
                upload_speed = upload_speed * 1024,
                burst_download_speed = CASE
                    WHEN burst_download_speed IS NOT NULL
                    THEN burst_download_speed * 1024
                    ELSE NULL
                END,
                burst_upload_speed = CASE
                    WHEN burst_upload_speed IS NOT NULL
                    THEN burst_upload_speed * 1024
                    ELSE NULL
                END
        ');

        // Revert column comments to reflect Kbps storage
        Schema::table('bandwidth_plans', function (Blueprint $table) {
            $table->integer('download_speed')->comment('in Kbps')->change();
            $table->integer('upload_speed')->comment('in Kbps')->change();
            $table->integer('burst_download_speed')->nullable()->comment('in Kbps')->change();
            $table->integer('burst_upload_speed')->nullable()->comment('in Kbps')->change();
        });
    }
};
