<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pppoe_services', function (Blueprint $table) {
            // Make subscription_id nullable to allow direct service creation
            $table->foreignId('subscription_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pppoe_services', function (Blueprint $table) {
            // Revert subscription_id to NOT NULL
            $table->foreignId('subscription_id')->nullable(false)->change();
        });
    }
};
