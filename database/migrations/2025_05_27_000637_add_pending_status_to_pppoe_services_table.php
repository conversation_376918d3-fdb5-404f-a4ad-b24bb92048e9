<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For PostgreSQL, we need to handle enum changes differently
        if (config('database.default') === 'pgsql') {
            // Drop the existing constraint if it exists
            DB::statement('ALTER TABLE pppoe_services DROP CONSTRAINT IF EXISTS pppoe_services_status_check');

            // Change column to string and add new constraint
            Schema::table('pppoe_services', function (Blueprint $table) {
                $table->string('status')->default('pending')->change();
            });

            // Add check constraint for the new values
            DB::statement("ALTER TABLE pppoe_services ADD CONSTRAINT pppoe_services_status_check CHECK (status IN ('active', 'suspended', 'terminated', 'pending', 'provisioning_failed', 'reactivation_failed'))");
        } else {
            // MySQL/other databases
            Schema::table('pppoe_services', function (Blueprint $table) {
                $table->enum('status', ['active', 'suspended', 'terminated', 'pending', 'provisioning_failed', 'reactivation_failed'])->default('pending')->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (config('database.default') === 'pgsql') {
            // Drop the constraint
            DB::statement('ALTER TABLE pppoe_services DROP CONSTRAINT IF EXISTS pppoe_services_status_check');

            // Change back to original values
            Schema::table('pppoe_services', function (Blueprint $table) {
                $table->string('status')->default('active')->change();
            });

            // Add original constraint
            DB::statement("ALTER TABLE pppoe_services ADD CONSTRAINT pppoe_services_status_check CHECK (status IN ('active', 'suspended', 'terminated'))");
        } else {
            Schema::table('pppoe_services', function (Blueprint $table) {
                $table->enum('status', ['active', 'suspended', 'terminated'])->default('active')->change();
            });
        }
    }
};
