<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create IP Pools table
        Schema::create('ip_pools', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('network_address');
            $table->string('subnet_mask');
            $table->string('gateway')->nullable();
            $table->json('dns_servers')->nullable();
            $table->foreignId('device_id')->constrained('network_devices')->onDelete('cascade');
            $table->enum('status', ['active', 'inactive', 'depleted'])->default('active');
            $table->string('mikrotik_pool_id')->nullable();
            $table->json('excluded_ips')->nullable();
            $table->timestamps();
        });

        // Create PPPoE Services table
        Schema::create('pppoe_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subscription_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('device_id')->constrained('network_devices')->onDelete('cascade');
            $table->string('username')->unique();
            $table->string('password');
            $table->string('service_profile')->nullable();
            $table->foreignId('bandwidth_plan_id')->nullable()->constrained('bandwidth_plans')->onDelete('set null');
            $table->string('ip_address')->nullable();
            $table->string('remote_address')->nullable();
            $table->enum('status', ['active', 'suspended', 'terminated'])->default('active');
            $table->timestamp('last_connected')->nullable();
            $table->timestamp('last_disconnected')->nullable();
            $table->text('comment')->nullable();
            $table->string('mikrotik_id')->nullable();
            $table->timestamps();
        });

        // Create Static IP Services table
        Schema::create('static_ip_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subscription_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('device_id')->constrained('network_devices')->onDelete('cascade');
            $table->string('ip_address');
            $table->string('subnet_mask');
            $table->string('gateway')->nullable();
            $table->json('dns_servers')->nullable();
            $table->foreignId('bandwidth_plan_id')->nullable()->constrained('bandwidth_plans')->onDelete('set null');
            $table->foreignId('ip_pool_id')->nullable()->constrained('ip_pools')->onDelete('set null');
            $table->enum('status', ['active', 'suspended', 'terminated'])->default('active');
            $table->json('firewall_rules')->nullable();
            $table->json('nat_rules')->nullable();
            $table->text('comment')->nullable();
            $table->string('mikrotik_route_id')->nullable();
            $table->string('mikrotik_firewall_id')->nullable();
            $table->string('mikrotik_nat_id')->nullable();
            $table->timestamps();

            // Add unique constraint for IP address
            $table->unique('ip_address');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('static_ip_services');
        Schema::dropIfExists('pppoe_services');
        Schema::dropIfExists('ip_pools');
    }
};
