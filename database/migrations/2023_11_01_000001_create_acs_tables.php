<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // ACS devices table (CPE - Customer Premises Equipment)
        Schema::create('acs_devices', function (Blueprint $table) {
            $table->id();
            $table->string('serial_number')->unique();
            $table->string('manufacturer');
            $table->string('model');
            $table->string('oui')->nullable(); // Organizationally Unique Identifier
            $table->string('product_class')->nullable();
            $table->string('software_version')->nullable();
            $table->string('hardware_version')->nullable();
            $table->string('connection_request_url')->nullable();
            $table->string('connection_request_username')->nullable();
            $table->string('connection_request_password')->nullable();
            $table->ipAddress('ip_address')->nullable();
            $table->string('mac_address')->nullable();
            $table->timestamp('last_inform')->nullable();
            $table->timestamp('last_successful_inform')->nullable();
            $table->boolean('active')->default(true);
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
        });

        // ACS parameters table
        Schema::create('acs_parameters', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('acs_devices')->onDelete('cascade');
            $table->string('name');
            $table->text('value')->nullable();
            $table->string('type')->default('string');
            $table->boolean('writable')->default(false);
            $table->timestamps();

            $table->unique(['device_id', 'name']);
        });

        // ACS tasks table (for provisioning, firmware updates, etc.)
        Schema::create('acs_tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('acs_devices')->onDelete('cascade');
            $table->string('type'); // GetParameterValues, SetParameterValues, Download, Reboot, etc.
            $table->json('parameters')->nullable();
            $table->string('status')->default('pending'); // pending, in_progress, completed, failed
            $table->text('result')->nullable();
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
        });

        // ACS profiles table (templates for device configuration)
        Schema::create('acs_profiles', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('parameters'); // JSON object with parameter name/value pairs
            $table->boolean('active')->default(true);
            $table->timestamps();
        });

        // ACS device profiles pivot table
        Schema::create('acs_device_profile', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('acs_devices')->onDelete('cascade');
            $table->foreignId('profile_id')->constrained('acs_profiles')->onDelete('cascade');
            $table->timestamps();

            $table->unique(['device_id', 'profile_id']);
        });

        // ACS events table (for logging device events)
        Schema::create('acs_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('acs_devices')->onDelete('cascade');
            $table->string('type');
            $table->text('description')->nullable();
            $table->json('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_events');
        Schema::dropIfExists('acs_device_profile');
        Schema::dropIfExists('acs_profiles');
        Schema::dropIfExists('acs_tasks');
        Schema::dropIfExists('acs_parameters');
        Schema::dropIfExists('acs_devices');
    }
};
