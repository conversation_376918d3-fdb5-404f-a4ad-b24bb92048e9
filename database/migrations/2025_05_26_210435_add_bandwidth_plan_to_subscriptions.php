<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->foreignId('bandwidth_plan_id')->nullable()->after('customer_id')->constrained('bandwidth_plans')->nullOnDelete();
            $table->text('customer_notes')->nullable()->after('description');
            $table->boolean('allow_overrides')->default(false)->after('customer_notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropForeign(['bandwidth_plan_id']);
            $table->dropColumn(['bandwidth_plan_id', 'customer_notes', 'allow_overrides']);
        });
    }
};
