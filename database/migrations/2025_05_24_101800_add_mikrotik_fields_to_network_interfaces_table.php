<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('network_interfaces', function (Blueprint $table) {
            $table->boolean('running')->default(false)->after('status');
            $table->boolean('enabled')->default(true)->after('running');
            $table->string('comment')->nullable()->after('enabled');
            $table->string('mikrotik_id')->nullable()->after('comment');
            $table->json('mikrotik_data')->nullable()->after('mikrotik_id');
            $table->timestamp('last_synced_at')->nullable()->after('mikrotik_data');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('network_interfaces', function (Blueprint $table) {
            $table->dropColumn([
                'running',
                'enabled',
                'comment',
                'mikrotik_id',
                'mikrotik_data',
                'last_synced_at',
            ]);
        });
    }
};
