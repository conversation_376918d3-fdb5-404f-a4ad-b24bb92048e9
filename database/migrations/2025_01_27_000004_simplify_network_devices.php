<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Simplify network devices table for MikroTik-focused management.
     * Remove fields that can be auto-detected via MikroTik API or are not essential.
     */
    public function up(): void
    {
        Schema::table('network_devices', function (Blueprint $table) {
            // Remove fields that can be auto-detected via MikroTik API
            $table->dropColumn([
                'model',
                'manufacturer',
                'serial_number',
                'mac_address',
            ]);

            // Remove positioning fields - not needed for basic operations
            $table->dropColumn([
                'x_position',
                'y_position',
            ]);

            // Remove generic type field - assume MikroTik router
            $table->dropColumn('type');

            // Remove complex configuration JSON - use MikroTik API directly
            $table->dropColumn('configuration');

            // Add MikroTik API credentials (encrypted)
            $table->string('api_username')->nullable();
            $table->string('api_password')->nullable(); // Will be encrypted
            $table->integer('api_port')->default(8728);

            // Add auto-detected device info (populated via API)
            $table->string('detected_model')->nullable();
            $table->string('detected_version')->nullable();
            $table->timestamp('last_connected_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('network_devices', function (Blueprint $table) {
            // Restore removed columns
            $table->string('type')->default('router');
            $table->string('model')->nullable();
            $table->string('manufacturer')->nullable();
            $table->string('serial_number')->nullable();
            $table->string('mac_address')->nullable();
            $table->decimal('x_position', 10, 2)->nullable();
            $table->decimal('y_position', 10, 2)->nullable();
            $table->json('configuration')->nullable();

            // Remove added columns
            $table->dropColumn([
                'api_username',
                'api_password',
                'api_port',
                'detected_model',
                'detected_version',
                'last_connected_at',
            ]);
        });
    }
};
