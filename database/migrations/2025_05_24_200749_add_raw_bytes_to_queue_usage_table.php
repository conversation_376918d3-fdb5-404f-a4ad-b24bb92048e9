<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('queue_usage', function (Blueprint $table) {
            $table->bigInteger('raw_download_bytes')->default(0)->after('total_bytes');
            $table->bigInteger('raw_upload_bytes')->default(0)->after('raw_download_bytes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('queue_usage', function (Blueprint $table) {
            $table->dropColumn(['raw_download_bytes', 'raw_upload_bytes']);
        });
    }
};
