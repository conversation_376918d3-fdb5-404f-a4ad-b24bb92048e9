<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes for customers table
        Schema::table('customers', function (Blueprint $table) {
            $table->index(['status', 'created_at'], 'customers_status_created_idx');
            $table->index(['email', 'status'], 'customers_email_status_idx');
            $table->index(['created_at'], 'customers_created_at_idx');
            $table->index(['name'], 'customers_name_idx');
        });

        // Add indexes for subscriptions table
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->index(['customer_id', 'status'], 'subscriptions_customer_status_idx');
            $table->index(['status', 'next_billing_date'], 'subscriptions_status_billing_date_idx');
            $table->index(['auto_billing_enabled', 'status'], 'subscriptions_auto_billing_status_idx');
            $table->index(['bandwidth_plan_id'], 'subscriptions_bandwidth_plan_idx');
            $table->index(['network_site_id'], 'subscriptions_network_site_idx');
            $table->index(['created_at'], 'subscriptions_created_at_idx');
        });

        // Add indexes for static_ip_services table
        Schema::table('static_ip_services', function (Blueprint $table) {
            $table->index(['customer_id', 'status'], 'static_ip_customer_status_idx');
            $table->index(['device_id', 'status'], 'static_ip_device_status_idx');
            $table->index(['ip_pool_id', 'status'], 'static_ip_pool_status_idx');
            $table->index(['bandwidth_plan_id'], 'static_ip_bandwidth_plan_idx');
            $table->index(['subscription_id'], 'static_ip_subscription_idx');
            $table->index(['ip_address'], 'static_ip_address_idx');
            $table->index(['status', 'created_at'], 'static_ip_status_created_idx');
            $table->index(['mikrotik_id'], 'static_ip_mikrotik_id_idx');
        });

        // Add indexes for pppoe_services table
        Schema::table('pppoe_services', function (Blueprint $table) {
            $table->index(['customer_id', 'status'], 'pppoe_customer_status_idx');
            $table->index(['device_id', 'status'], 'pppoe_device_status_idx');
            $table->index(['bandwidth_plan_id'], 'pppoe_bandwidth_plan_idx');
            $table->index(['subscription_id'], 'pppoe_subscription_idx');
            $table->index(['username'], 'pppoe_username_idx');
            $table->index(['status', 'created_at'], 'pppoe_status_created_idx');
            $table->index(['mikrotik_id'], 'pppoe_mikrotik_id_idx');
        });

        // Add indexes for invoices table
        Schema::table('invoices', function (Blueprint $table) {
            $table->index(['customer_id', 'status'], 'invoices_customer_status_idx');
            $table->index(['subscription_id'], 'invoices_subscription_idx');
            $table->index(['status', 'due_date'], 'invoices_status_due_date_idx');
            $table->index(['issue_date'], 'invoices_issue_date_idx');
            $table->index(['due_date'], 'invoices_due_date_idx');
            $table->index(['auto_generated'], 'invoices_auto_generated_idx');
            $table->index(['billing_period_start', 'billing_period_end'], 'invoices_billing_period_idx');
        });

        // Add indexes for payments table (if exists)
        if (Schema::hasTable('payments')) {
            Schema::table('payments', function (Blueprint $table) {
                $table->index(['invoice_id', 'status'], 'payments_invoice_status_idx');
                $table->index(['payment_method', 'status'], 'payments_method_status_idx');
                $table->index(['payment_date'], 'payments_date_idx');
                $table->index(['status', 'created_at'], 'payments_status_created_idx');
            });
        }

        // Add indexes for network_devices table
        Schema::table('network_devices', function (Blueprint $table) {
            $table->index(['status'], 'network_devices_status_idx');
            $table->index(['detected_model'], 'network_devices_model_idx');
            $table->index(['ip_address'], 'network_devices_ip_idx');
            $table->index(['site_id'], 'network_devices_site_idx');
            $table->index(['last_connected_at'], 'network_devices_last_connected_idx');
        });

        // Add indexes for bandwidth_plans table
        Schema::table('bandwidth_plans', function (Blueprint $table) {
            $table->index(['active'], 'bandwidth_plans_active_idx');
            $table->index(['download_speed'], 'bandwidth_plans_download_speed_idx');
            $table->index(['upload_speed'], 'bandwidth_plans_upload_speed_idx');
            $table->index(['priority'], 'bandwidth_plans_priority_idx');
        });

        // Add indexes for ip_pools table
        Schema::table('ip_pools', function (Blueprint $table) {
            $table->index(['device_id', 'status'], 'ip_pools_device_status_idx');
            $table->index(['status'], 'ip_pools_status_idx');
            $table->index(['network_address'], 'ip_pools_network_address_idx');
        });

        // Add indexes for bandwidth_usage table (if exists)
        if (Schema::hasTable('bandwidth_usage')) {
            Schema::table('bandwidth_usage', function (Blueprint $table) {
                $table->index(['usageable_type', 'usageable_id'], 'bandwidth_usage_usageable_idx');
                $table->index(['period_start', 'period_end'], 'bandwidth_usage_period_idx');
                $table->index(['created_at'], 'bandwidth_usage_created_at_idx');
            });
        }

        // Add indexes for queue_usage table (if exists)
        if (Schema::hasTable('queue_usage')) {
            Schema::table('queue_usage', function (Blueprint $table) {
                $table->index(['device_id', 'period_start'], 'queue_usage_device_period_idx');
                $table->index(['customer_id', 'period_start'], 'queue_usage_customer_period_idx');
                $table->index(['target_ip'], 'queue_usage_target_ip_idx');
                $table->index(['is_mapped'], 'queue_usage_is_mapped_idx');
                $table->index(['service_id', 'service_type'], 'queue_usage_service_idx');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes for customers table
        Schema::table('customers', function (Blueprint $table) {
            $table->dropIndex('customers_status_created_idx');
            $table->dropIndex('customers_email_status_idx');
            $table->dropIndex('customers_created_at_idx');
            $table->dropIndex('customers_name_idx');
        });

        // Drop indexes for subscriptions table
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropIndex('subscriptions_customer_status_idx');
            $table->dropIndex('subscriptions_status_billing_date_idx');
            $table->dropIndex('subscriptions_auto_billing_status_idx');
            $table->dropIndex('subscriptions_bandwidth_plan_idx');
            $table->dropIndex('subscriptions_network_site_idx');
            $table->dropIndex('subscriptions_created_at_idx');
        });

        // Drop indexes for static_ip_services table
        Schema::table('static_ip_services', function (Blueprint $table) {
            $table->dropIndex('static_ip_customer_status_idx');
            $table->dropIndex('static_ip_device_status_idx');
            $table->dropIndex('static_ip_pool_status_idx');
            $table->dropIndex('static_ip_bandwidth_plan_idx');
            $table->dropIndex('static_ip_subscription_idx');
            $table->dropIndex('static_ip_address_idx');
            $table->dropIndex('static_ip_status_created_idx');
            $table->dropIndex('static_ip_mikrotik_id_idx');
        });

        // Drop indexes for pppoe_services table
        Schema::table('pppoe_services', function (Blueprint $table) {
            $table->dropIndex('pppoe_customer_status_idx');
            $table->dropIndex('pppoe_device_status_idx');
            $table->dropIndex('pppoe_bandwidth_plan_idx');
            $table->dropIndex('pppoe_subscription_idx');
            $table->dropIndex('pppoe_username_idx');
            $table->dropIndex('pppoe_status_created_idx');
            $table->dropIndex('pppoe_mikrotik_id_idx');
        });

        // Drop indexes for invoices table
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropIndex('invoices_customer_status_idx');
            $table->dropIndex('invoices_subscription_idx');
            $table->dropIndex('invoices_status_due_date_idx');
            $table->dropIndex('invoices_issue_date_idx');
            $table->dropIndex('invoices_due_date_idx');
            $table->dropIndex('invoices_auto_generated_idx');
            $table->dropIndex('invoices_billing_period_idx');
        });

        // Drop indexes for payments table (if exists)
        if (Schema::hasTable('payments')) {
            Schema::table('payments', function (Blueprint $table) {
                $table->dropIndex('payments_invoice_status_idx');
                $table->dropIndex('payments_method_status_idx');
                $table->dropIndex('payments_date_idx');
                $table->dropIndex('payments_status_created_idx');
            });
        }

        // Drop indexes for network_devices table
        Schema::table('network_devices', function (Blueprint $table) {
            $table->dropIndex('network_devices_status_idx');
            $table->dropIndex('network_devices_model_idx');
            $table->dropIndex('network_devices_ip_idx');
            $table->dropIndex('network_devices_site_idx');
            $table->dropIndex('network_devices_last_connected_idx');
        });

        // Drop indexes for bandwidth_plans table
        Schema::table('bandwidth_plans', function (Blueprint $table) {
            $table->dropIndex('bandwidth_plans_active_idx');
            $table->dropIndex('bandwidth_plans_download_speed_idx');
            $table->dropIndex('bandwidth_plans_upload_speed_idx');
            $table->dropIndex('bandwidth_plans_priority_idx');
        });

        // Drop indexes for ip_pools table
        Schema::table('ip_pools', function (Blueprint $table) {
            $table->dropIndex('ip_pools_device_status_idx');
            $table->dropIndex('ip_pools_status_idx');
            $table->dropIndex('ip_pools_network_address_idx');
        });

        // Drop indexes for bandwidth_usage table (if exists)
        if (Schema::hasTable('bandwidth_usage')) {
            Schema::table('bandwidth_usage', function (Blueprint $table) {
                $table->dropIndex('bandwidth_usage_usageable_idx');
                $table->dropIndex('bandwidth_usage_period_idx');
                $table->dropIndex('bandwidth_usage_created_at_idx');
            });
        }

        // Drop indexes for queue_usage table (if exists)
        if (Schema::hasTable('queue_usage')) {
            Schema::table('queue_usage', function (Blueprint $table) {
                $table->dropIndex('queue_usage_device_period_idx');
                $table->dropIndex('queue_usage_customer_period_idx');
                $table->dropIndex('queue_usage_target_ip_idx');
                $table->dropIndex('queue_usage_is_mapped_idx');
                $table->dropIndex('queue_usage_service_idx');
            });
        }
    }
};
