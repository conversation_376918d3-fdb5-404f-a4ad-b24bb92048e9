<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ip_pools', function (Blueprint $table) {
            $table->string('interface')->nullable()->after('device_id');
            $table->boolean('addresses_created')->default(false)->after('interface');
            $table->integer('total_addresses')->default(0)->after('addresses_created');
            $table->integer('available_addresses')->default(0)->after('total_addresses');
            $table->integer('assigned_addresses')->default(0)->after('available_addresses');
            $table->json('creation_log')->nullable()->after('assigned_addresses'); // Log of creation process
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ip_pools', function (Blueprint $table) {
            $table->dropColumn([
                'interface',
                'addresses_created',
                'total_addresses',
                'available_addresses',
                'assigned_addresses',
                'creation_log',
            ]);
        });
    }
};
