<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bandwidth_plans', function (Blueprint $table) {
            $table->decimal('price', 10, 2)->nullable()->after('priority')->comment('Monthly price in USD');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bandwidth_plans', function (Blueprint $table) {
            $table->dropColumn('price');
        });
    }
};
