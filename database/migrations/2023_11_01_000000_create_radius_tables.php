<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // RADIUS users table
        Schema::create('radius_users', function (Blueprint $table) {
            $table->id();
            $table->string('username')->unique();
            $table->string('password');
            $table->string('groupname')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
        });

        // RADIUS groups table
        Schema::create('radius_groups', function (Blueprint $table) {
            $table->id();
            $table->string('groupname')->unique();
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // RADIUS attributes table
        Schema::create('radius_attributes', function (Blueprint $table) {
            $table->id();
            $table->morphs('attributable'); // Polymorphic relation to users or groups
            $table->string('attribute');
            $table->string('op')->default('=');
            $table->string('value');
            $table->timestamps();
        });

        // RADIUS accounting table
        Schema::create('radius_accounting', function (Blueprint $table) {
            $table->id();
            $table->string('acct_session_id')->unique();
            $table->string('username');
            $table->string('nas_ip_address');
            $table->string('acct_status_type');
            $table->bigInteger('acct_input_octets')->nullable();
            $table->bigInteger('acct_output_octets')->nullable();
            $table->integer('acct_session_time')->nullable();
            $table->timestamp('acct_start_time')->nullable();
            $table->timestamp('acct_stop_time')->nullable();
            $table->timestamps();
        });

        // RADIUS NAS (Network Access Server) table
        Schema::create('radius_nas', function (Blueprint $table) {
            $table->id();
            $table->string('nasname')->unique();
            $table->string('shortname');
            $table->string('type')->default('other');
            $table->integer('ports')->nullable();
            $table->string('secret');
            $table->string('server')->nullable();
            $table->string('community')->nullable();
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('radius_nas');
        Schema::dropIfExists('radius_accounting');
        Schema::dropIfExists('radius_attributes');
        Schema::dropIfExists('radius_groups');
        Schema::dropIfExists('radius_users');
    }
};
