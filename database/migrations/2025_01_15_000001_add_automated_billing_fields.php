<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add pro-rated billing support to subscriptions
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->date('last_billing_date')->nullable()->after('next_billing_date');
            $table->date('suspension_date')->nullable()->after('last_billing_date');
            $table->date('reactivation_date')->nullable()->after('suspension_date');
            $table->integer('suspension_days')->default(0)->after('reactivation_date');
            $table->boolean('auto_billing_enabled')->default(true)->after('suspension_days');
            $table->integer('grace_period_days')->default(7)->after('auto_billing_enabled');
        });

        // Add billing period tracking to invoices
        Schema::table('invoices', function (Blueprint $table) {
            $table->date('billing_period_start')->nullable()->after('subscription_id');
            $table->date('billing_period_end')->nullable()->after('billing_period_start');
            $table->boolean('is_prorated')->default(false)->after('billing_period_end');
            $table->integer('active_days')->nullable()->after('is_prorated');
            $table->integer('total_days')->nullable()->after('active_days');
            $table->boolean('auto_generated')->default(false)->after('total_days');
            $table->json('billing_details')->nullable()->after('auto_generated');
        });

        // Create billing batch tracking table
        Schema::create('billing_batches', function (Blueprint $table) {
            $table->id();
            $table->string('batch_id')->unique();
            $table->string('operation_type'); // 'monthly_billing', 'bulk_suspension', 'bulk_reactivation'
            $table->date('billing_date');
            $table->integer('total_items')->default(0);
            $table->integer('processed_items')->default(0);
            $table->integer('successful_items')->default(0);
            $table->integer('failed_items')->default(0);
            $table->string('status')->default('pending'); // pending, processing, completed, failed
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->json('summary')->nullable();
            $table->text('error_log')->nullable();
            $table->timestamps();

            $table->index(['operation_type', 'billing_date']);
            $table->index(['status', 'created_at']);
        });

        // Create subscription billing history table
        Schema::create('subscription_billing_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subscription_id')->constrained()->cascadeOnDelete();
            $table->foreignId('invoice_id')->nullable()->constrained()->nullOnDelete();
            $table->string('action'); // 'billed', 'suspended', 'reactivated', 'terminated'
            $table->date('action_date');
            $table->date('period_start')->nullable();
            $table->date('period_end')->nullable();
            $table->decimal('amount', 10, 2)->nullable();
            $table->integer('active_days')->nullable();
            $table->integer('total_days')->nullable();
            $table->json('metadata')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['subscription_id', 'action_date']);
            $table->index(['action', 'action_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_billing_history');
        Schema::dropIfExists('billing_batches');

        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn([
                'billing_period_start',
                'billing_period_end',
                'is_prorated',
                'active_days',
                'total_days',
                'auto_generated',
                'billing_details',
            ]);
        });

        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn([
                'last_billing_date',
                'suspension_date',
                'reactivation_date',
                'suspension_days',
                'auto_billing_enabled',
                'grace_period_days',
            ]);
        });
    }
};
