<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('static_ip_services', function (Blueprint $table) {
            $table->string('mikrotik_id')->nullable()->after('mikrotik_firewall_id')
                ->comment('MikroTik address list entry ID for suspension');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('static_ip_services', function (Blueprint $table) {
            $table->dropColumn('mikrotik_id');
        });
    }
};
