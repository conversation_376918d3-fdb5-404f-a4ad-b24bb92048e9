<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Subscription>
 */
class SubscriptionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Subscription::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = Carbon::now()->startOfMonth(); // Start of current month
        // Set next_billing_date to yesterday so subscriptions are eligible for billing
        $nextBillingDate = Carbon::now()->subDay(); // Yesterday for immediate billing eligibility

        return [
            'customer_id' => Customer::factory(),
            'bandwidth_plan_id' => 1, // Hardcoded to plan ID 1 as requested
            'network_site_id' => 1, // Hardcoded to device/router ID 1 as requested
            'name' => 'Standard Internet Plan',
            'description' => 'Monthly internet subscription',
            'customer_notes' => null,
            'allow_overrides' => false,
            'status' => 'active',
            'price' => 50.00, // Default price, will be overridden by bandwidth plan
            'billing_cycle' => 'monthly',
            'start_date' => $startDate->toDateString(),
            'end_date' => null,
            'next_billing_date' => $nextBillingDate->toDateString(),
            'last_billing_date' => null,
            'suspension_date' => null,
            'reactivation_date' => null,
            'suspension_days' => 0, // Default to 0 as per database schema
            'auto_billing_enabled' => true,
            'grace_period_days' => 7,
        ];
    }

    /**
     * Indicate that the subscription should be inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the subscription should be suspended.
     */
    public function suspended(): static
    {
        $suspensionDays = fake()->numberBetween(1, 30);

        return $this->state(fn (array $attributes) => [
            'status' => 'suspended',
            'suspension_date' => Carbon::now()->subDays($suspensionDays),
            'suspension_days' => $suspensionDays,
        ]);
    }

    /**
     * Indicate that the subscription should be expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'expired',
            'end_date' => Carbon::now()->subDays(fake()->numberBetween(1, 90)),
        ]);
    }

    /**
     * Set a specific billing cycle.
     */
    public function billingCycle(string $cycle): static
    {
        $startDate = Carbon::now()->startOfMonth();
        $nextBillingDate = match ($cycle) {
            'quarterly' => $startDate->copy()->addMonths(3),
            'yearly' => $startDate->copy()->addYear(),
            default => $startDate->copy()->addMonth(),
        };

        return $this->state(fn (array $attributes) => [
            'billing_cycle' => $cycle,
            'next_billing_date' => $nextBillingDate->toDateString(),
        ]);
    }

    /**
     * Set subscription to need billing (next billing date in the past).
     */
    public function needsBilling(): static
    {
        return $this->state(fn (array $attributes) => [
            'next_billing_date' => Carbon::now()->subDays(fake()->numberBetween(1, 5))->toDateString(),
        ]);
    }
}
