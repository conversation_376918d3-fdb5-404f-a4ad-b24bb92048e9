<?php

namespace Database\Seeders;

use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Network\NetworkDevice;
use App\Models\Network\NetworkSite;
use App\Models\Services\IpPool;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Models\Subscription;
use App\Services\IpPoolService;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SimplifiedRealMikroTikSeeder extends Seeder
{
    protected $ipPoolService;

    public function __construct()
    {
        $this->ipPoolService = new IpPoolService;
    }

    /**
     * Run the database seeds.
     * Creates simplified realistic ISP test data with REAL MikroTik integration.
     */
    public function run(): void
    {
        $this->command->info('🚀 Creating simplified realistic ISP test data with REAL MikroTik...');
        $this->command->newLine();

        // Clear existing data completely
        $this->clearAllData();

        // Use the real MikroTik device with ID 3
        $device = $this->getRealMikroTikDevice();

        // Step 1: Create bandwidth plans
        $this->createBandwidthPlans();

        // Step 2: Create network infrastructure
        $sites = $this->createNetworkSites();

        // Step 3: Create IP pools on REAL MikroTik using actual interfaces
        $ipPools = $this->createRealIpPools($device);

        // Step 4: Create customers and subscriptions (small test set)
        $customers = $this->createCustomers();
        $subscriptions = $this->createSubscriptions($customers, $sites);

        // Step 5: Create services (database only for now)
        $this->createServices($subscriptions, $device, $ipPools);

        $this->command->info('✅ Simplified realistic ISP test data created!');
        $this->showSummary();
    }

    /**
     * Clear ALL existing data.
     */
    protected function clearAllData(): void
    {
        $this->command->info('🧹 Clearing ALL existing data...');

        // Clear database
        $driver = DB::getDriverName();
        if ($driver === 'mysql') {
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        } elseif ($driver === 'sqlite') {
            DB::statement('PRAGMA foreign_keys=OFF;');
        }

        // Clear in reverse dependency order (but keep the real MikroTik device)
        DB::table('static_ip_services')->delete();
        DB::table('pppoe_services')->delete();
        DB::table('ip_addresses')->delete();
        DB::table('ip_pools')->delete();
        DB::table('subscriptions')->delete();
        DB::table('customers')->delete();
        DB::table('network_sites')->delete();
        DB::table('bandwidth_plans')->delete();

        // Re-enable foreign key checks
        if ($driver === 'mysql') {
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        } elseif ($driver === 'sqlite') {
            DB::statement('PRAGMA foreign_keys=ON;');
        }

        $this->command->info('✅ All data cleared');
    }

    /**
     * Get the existing real MikroTik device with ID 3.
     */
    protected function getRealMikroTikDevice(): NetworkDevice
    {
        $device = NetworkDevice::find(3);

        if (! $device) {
            $this->command->error('❌ No MikroTik device found with ID 3! Please create a real MikroTik device first.');
            throw new \Exception('No MikroTik device found with ID 3.');
        }

        $this->command->info("🖥️  Using real MikroTik device: {$device->name} (ID: {$device->id}) at {$device->ip_address}");

        return $device;
    }

    /**
     * Create realistic bandwidth plans.
     */
    protected function createBandwidthPlans(): void
    {
        $this->command->info('📊 Creating bandwidth plans...');

        $plans = [
            [
                'name' => 'Basic 10 Mbps',
                'description' => 'Entry-level internet plan for basic browsing',
                'download_speed' => 10,
                'upload_speed' => 5,
                'price' => 25.00,
                'active' => true,
            ],
            [
                'name' => 'Standard 25 Mbps',
                'description' => 'Standard plan for home users',
                'download_speed' => 25,
                'upload_speed' => 10,
                'price' => 45.00,
                'active' => true,
            ],
            [
                'name' => 'Premium 50 Mbps',
                'description' => 'High-speed plan for power users',
                'download_speed' => 50,
                'upload_speed' => 20,
                'price' => 75.00,
                'active' => true,
            ],
        ];

        foreach ($plans as $plan) {
            BandwidthPlan::create($plan);
        }

        $this->command->info('✅ Created '.count($plans).' bandwidth plans');
    }

    /**
     * Create realistic network sites.
     */
    protected function createNetworkSites(): array
    {
        $this->command->info('🏢 Creating network sites...');

        $sites = [
            [
                'name' => 'Main Site',
                'description' => 'Primary network site',
                'address' => 'Main Router Location',
                'status' => 'active',
            ],
        ];

        $createdSites = [];
        foreach ($sites as $siteData) {
            $site = NetworkSite::create($siteData);
            $createdSites[] = $site;
        }

        $this->command->info('✅ Created '.count($createdSites).' network sites');

        return $createdSites;
    }

    /**
     * Create IP pools on REAL MikroTik device using actual interfaces.
     */
    protected function createRealIpPools(NetworkDevice $device): array
    {
        $this->command->info('🌐 Creating IP pools on REAL MikroTik...');

        // Use actual interfaces from your MikroTik
        $poolConfigs = [
            [
                'name' => 'Test-Pool-1',
                'description' => 'Test IP pool for customers',
                'network_cidr' => '*************/24',
                'interface' => 'ether11', // Using available interface without spacing
            ],
        ];

        $createdPools = [];
        foreach ($poolConfigs as $poolConfig) {
            $this->command->info("Creating IP pool: {$poolConfig['name']} ({$poolConfig['network_cidr']})...");

            try {
                // Parse CIDR to get network address and subnet mask
                [$networkAddress, $cidrBits] = explode('/', $poolConfig['network_cidr']);
                $subnetMask = $this->cidrToSubnetMask($cidrBits);
                $gateway = $this->getGatewayFromNetwork($networkAddress);

                // Use the IpPoolService to create pool on real MikroTik
                $poolData = [
                    'name' => $poolConfig['name'],
                    'description' => $poolConfig['description'],
                    'network_cidr' => $poolConfig['network_cidr'],
                    'network_address' => $networkAddress,
                    'subnet_mask' => $subnetMask,
                    'gateway' => $gateway,
                    'interface' => $poolConfig['interface'],
                    'device_id' => $device->id,
                    'dns_servers' => ['*******', '*******'],
                    'excluded_ips' => [$gateway], // Exclude gateway
                ];

                $result = $this->ipPoolService->createIpPool($poolData);

                if ($result['success']) {
                    $createdPools[] = $result['pool'];
                    $this->command->info("✅ Created IP pool on MikroTik: {$result['pool']->name} ({$result['addresses_created']} addresses)");
                } else {
                    $this->command->error("❌ Failed to create IP pool {$poolConfig['name']}: ".$result['error']);
                }
            } catch (\Exception $e) {
                $this->command->error("❌ Failed to create IP pool {$poolConfig['name']}: ".$e->getMessage());
                Log::error('Failed to create IP pool', [
                    'pool' => $poolConfig['name'],
                    'error' => $e->getMessage(),
                ]);
            }
        }

        $this->command->info('✅ Created '.count($createdPools).' IP pools on real MikroTik');

        return $createdPools;
    }

    /**
     * Create customers with realistic names.
     */
    protected function createCustomers(): array
    {
        $this->command->info('👥 Creating customers...');

        // Create small test set (10 customers)
        $totalCustomers = 10;
        $this->command->info("Creating {$totalCustomers} customers for testing...");

        $firstNames = ['James', 'Mary', 'John', 'Patricia', 'Robert', 'Jennifer', 'Michael', 'Linda'];
        $lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis'];

        $customers = [];
        for ($i = 0; $i < $totalCustomers; $i++) {
            $firstName = $firstNames[array_rand($firstNames)];
            $lastName = $lastNames[array_rand($lastNames)];
            $name = $firstName.' '.$lastName;

            $customer = Customer::create([
                'name' => $name,
                'email' => strtolower($firstName.'.'.$lastName.($i + 1).'@email.com'),
                'phone' => '('.rand(200, 999).') '.rand(200, 999).'-'.rand(1000, 9999),
                'address' => rand(100, 9999).' Main St',
                'city' => 'Test City',
                'state' => 'CA',
                'postal_code' => rand(10000, 99999),
                'country' => 'United States',
                'status' => 'active',
                'user_id' => null,
            ]);

            $customers[] = $customer;
        }

        $this->command->info("✅ Created {$totalCustomers} customers with realistic names");

        return $customers;
    }

    /**
     * Create subscriptions for customers.
     */
    protected function createSubscriptions(array $customers, array $sites): array
    {
        $this->command->info('📋 Creating subscriptions...');

        $bandwidthPlans = BandwidthPlan::all();
        $subscriptions = [];

        foreach ($customers as $index => $customer) {
            $site = $sites[0]; // Use the main site

            // Randomly assign bandwidth plan
            $bandwidthPlan = $bandwidthPlans[array_rand($bandwidthPlans->toArray())];

            // Set billing date to yesterday so they're eligible for billing
            $nextBillingDate = Carbon::now()->subDay();

            $subscription = Subscription::create([
                'customer_id' => $customer->id,
                'bandwidth_plan_id' => $bandwidthPlan->id,
                'network_site_id' => $site->id,
                'name' => $bandwidthPlan->name.' - '.$customer->name,
                'description' => 'Monthly internet subscription',
                'customer_notes' => null,
                'allow_overrides' => false,
                'status' => 'active',
                'price' => $bandwidthPlan->price,
                'billing_cycle' => 'monthly',
                'start_date' => Carbon::now()->startOfMonth()->toDateString(),
                'end_date' => null,
                'next_billing_date' => $nextBillingDate->toDateString(),
                'last_billing_date' => null,
                'suspension_date' => null,
                'reactivation_date' => null,
                'suspension_days' => 0,
                'auto_billing_enabled' => true,
                'grace_period_days' => 7,
            ]);

            $subscriptions[] = $subscription;
        }

        $this->command->info('✅ Created '.count($subscriptions).' subscriptions');

        return $subscriptions;
    }

    /**
     * Create services (database only for now).
     */
    protected function createServices(array $subscriptions, NetworkDevice $device, array $ipPools): void
    {
        $this->command->info('🔧 Creating services (60% PPPoE, 40% Static IP)...');

        if (empty($ipPools)) {
            $this->command->warn('⚠️  No IP pools available, skipping service creation');

            return;
        }

        $totalServices = count($subscriptions);
        $staticIpCount = (int) ($totalServices * 0.4); // 40% Static IP
        $pppoeCount = $totalServices - $staticIpCount; // 60% PPPoE

        // Create Static IP services first
        $this->createStaticIpServices($subscriptions, $device, $ipPools[0], $staticIpCount);

        // Create PPPoE services for remaining subscriptions
        $this->createPppoeServices($subscriptions, $device, $staticIpCount, $pppoeCount);

        $this->command->info("✅ Created {$staticIpCount} Static IP services and {$pppoeCount} PPPoE services");
    }

    /**
     * Create Static IP services.
     */
    protected function createStaticIpServices(array $subscriptions, NetworkDevice $device, IpPool $ipPool, int $count): void
    {
        $this->command->info("Creating {$count} Static IP services...");

        for ($i = 0; $i < $count; $i++) {
            $subscription = $subscriptions[$i];

            try {
                // Generate IP address (simple sequential assignment)
                $baseIp = explode('.', $ipPool->network_address);
                $hostPart = ($i % 250) + 2; // Start from .2, skip gateway at .1
                $ipAddress = $baseIp[0].'.'.$baseIp[1].'.'.$baseIp[2].'.'.$hostPart;

                // Create static IP service in database
                $service = StaticIpService::create([
                    'subscription_id' => $subscription->id,
                    'customer_id' => $subscription->customer_id,
                    'device_id' => $device->id,
                    'ip_address' => $ipAddress,
                    'subnet_mask' => '*************',
                    'gateway' => $ipPool->gateway,
                    'dns_servers' => ['*******', '*******'],
                    'bandwidth_plan_id' => $subscription->bandwidth_plan_id,
                    'ip_pool_id' => $ipPool->id,
                    'status' => 'active',
                    'comment' => 'Created by seeder for '.$subscription->customer->name,
                ]);

                $this->command->info("✅ Created Static IP service: {$subscription->customer->name} - {$ipAddress}");

            } catch (\Exception $e) {
                $this->command->error("❌ Failed to create Static IP service for {$subscription->customer->name}: ".$e->getMessage());
            }
        }
    }

    /**
     * Create PPPoE services.
     */
    protected function createPppoeServices(array $subscriptions, NetworkDevice $device, int $startIndex, int $count): void
    {
        $this->command->info("Creating {$count} PPPoE services...");

        for ($i = $startIndex; $i < $startIndex + $count; $i++) {
            $subscription = $subscriptions[$i];

            try {
                // Generate username and password
                $username = $this->generatePppoeUsername($subscription->customer->name, $subscription->customer->id);
                $password = $this->generatePppoePassword();

                // Create PPPoE service in database
                $service = PppoeService::create([
                    'subscription_id' => $subscription->id,
                    'customer_id' => $subscription->customer_id,
                    'device_id' => $device->id,
                    'username' => $username,
                    'password' => $password,
                    'service_profile' => 'default',
                    'bandwidth_plan_id' => $subscription->bandwidth_plan_id,
                    'status' => 'active',
                    'comment' => 'Created by seeder for '.$subscription->customer->name,
                ]);

                $this->command->info("✅ Created PPPoE service: {$subscription->customer->name} - {$username}");

            } catch (\Exception $e) {
                $this->command->error("❌ Failed to create PPPoE service for {$subscription->customer->name}: ".$e->getMessage());
            }
        }
    }

    /**
     * Generate PPPoE username.
     */
    protected function generatePppoeUsername(string $customerName, int $customerId): string
    {
        // Remove spaces and special characters
        $name = preg_replace('/[^a-zA-Z0-9]/', '', $customerName);
        $username = strtolower(substr($name, 0, 8)).$customerId;

        return $username;
    }

    /**
     * Generate PPPoE password.
     */
    protected function generatePppoePassword(): string
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $password = '';

        for ($i = 0; $i < 8; $i++) {
            $password .= $chars[rand(0, strlen($chars) - 1)];
        }

        return $password;
    }

    /**
     * Convert CIDR bits to subnet mask.
     */
    protected function cidrToSubnetMask(int $cidrBits): string
    {
        $mask = -1 << (32 - $cidrBits);

        return long2ip($mask);
    }

    /**
     * Get gateway IP from network address (first usable IP).
     */
    protected function getGatewayFromNetwork(string $networkAddress): string
    {
        $networkLong = ip2long($networkAddress);

        return long2ip($networkLong + 1); // Gateway is typically .1
    }

    /**
     * Show summary of created data.
     */
    protected function showSummary(): void
    {
        $this->command->newLine();
        $this->command->info('📊 Simplified Real MikroTik ISP Test Data Summary:');
        $this->command->newLine();

        $bandwidthPlans = BandwidthPlan::count();
        $sites = NetworkSite::count();
        $devices = NetworkDevice::count();
        $ipPools = IpPool::count();
        $customers = Customer::count();
        $subscriptions = Subscription::count();
        $staticIpServices = StaticIpService::count();
        $pppoeServices = PppoeService::count();

        $this->command->table(['Component', 'Count'], [
            ['Bandwidth Plans', $bandwidthPlans],
            ['Network Sites', $sites],
            ['Network Devices', $devices],
            ['IP Pools (Real MikroTik)', $ipPools],
            ['Customers', $customers],
            ['Subscriptions', $subscriptions],
            ['Static IP Services', $staticIpServices],
            ['PPPoE Services', $pppoeServices],
        ]);

        $this->command->newLine();
        $this->command->info('🎯 Next Steps:');
        $this->command->info('1. Check MikroTik: IP pool should be visible in /ip/address');
        $this->command->info('2. Test automated billing: php artisan billing:generate-monthly-invoices');
        $this->command->info('3. Verify service management workflows');
        $this->command->newLine();
    }
}
