<?php

namespace Database\Seeders;

use App\Models\Bandwidth\BandwidthPlan;
use App\Models\Customer;
use App\Models\Network\NetworkDevice;
use App\Models\Network\NetworkSite;
use App\Models\Services\IpPool;
use App\Models\Services\PppoeService;
use App\Models\Services\StaticIpService;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RealisticIspDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * Creates realistic ISP test data simulating a fresh migration.
     */
    public function run(): void
    {
        $this->command->info('🚀 Creating realistic ISP test data...');
        $this->command->newLine();

        // Clear existing data (always clear for fresh start)
        $this->clearExistingData();

        // Step 1: Create bandwidth plans
        $this->createBandwidthPlans();

        // Step 2: Create network infrastructure
        $sites = $this->createNetworkSites();
        $device = $this->getExistingMikroTikDevice(); // Use existing real MikroTik device
        $ipPools = $this->createIpPools($device, $sites);

        // Step 3: Create customers and subscriptions
        $customers = $this->createCustomers();
        $subscriptions = $this->createSubscriptions($customers, $sites);

        // Step 4: Create services (60% PPPoE, 40% Static IP)
        $this->createServices($subscriptions, $device, $ipPools);

        $this->command->info('✅ Realistic ISP test data created successfully!');
        $this->showSummary();
    }

    /**
     * Clear existing test data.
     */
    protected function clearExistingData(): void
    {
        $this->command->info('🧹 Clearing existing data...');

        // Disable foreign key checks for MySQL, skip for SQLite
        $driver = DB::getDriverName();
        if ($driver === 'mysql') {
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        } elseif ($driver === 'sqlite') {
            DB::statement('PRAGMA foreign_keys=OFF;');
        }

        // Clear in reverse dependency order (but keep the real MikroTik device)
        DB::table('static_ip_services')->delete();
        DB::table('pppoe_services')->delete();
        DB::table('ip_addresses')->delete();
        DB::table('ip_pools')->delete();
        DB::table('subscriptions')->delete();
        DB::table('customers')->delete();
        // Don't delete network_devices - keep the real MikroTik device
        DB::table('network_sites')->delete();
        DB::table('bandwidth_plans')->delete();

        // Re-enable foreign key checks
        if ($driver === 'mysql') {
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        } elseif ($driver === 'sqlite') {
            DB::statement('PRAGMA foreign_keys=ON;');
        }

        $this->command->info('✅ Existing data cleared');
    }

    /**
     * Create realistic bandwidth plans.
     */
    protected function createBandwidthPlans(): void
    {
        $this->command->info('📊 Creating bandwidth plans...');

        $plans = [
            [
                'name' => 'Basic 10 Mbps',
                'description' => 'Entry-level internet plan for basic browsing',
                'download_speed' => 10,
                'upload_speed' => 5,
                'price' => 25.00,
                'active' => true,
            ],
            [
                'name' => 'Standard 25 Mbps',
                'description' => 'Standard plan for home users',
                'download_speed' => 25,
                'upload_speed' => 10,
                'price' => 45.00,
                'active' => true,
            ],
            [
                'name' => 'Premium 50 Mbps',
                'description' => 'High-speed plan for power users',
                'download_speed' => 50,
                'upload_speed' => 20,
                'price' => 75.00,
                'active' => true,
            ],
            [
                'name' => 'Business 100 Mbps',
                'description' => 'Business-grade internet with priority support',
                'download_speed' => 100,
                'upload_speed' => 50,
                'price' => 150.00,
                'active' => true,
            ],
        ];

        foreach ($plans as $plan) {
            BandwidthPlan::create($plan);
        }

        $this->command->info('✅ Created '.count($plans).' bandwidth plans');
    }

    /**
     * Create realistic network sites.
     */
    protected function createNetworkSites(): array
    {
        $this->command->info('🏢 Creating network sites...');

        $sites = [
            [
                'name' => 'Downtown Core',
                'description' => 'Main distribution point in downtown business district',
                'address' => '123 Main Street, Downtown',
                'status' => 'active',
            ],
            [
                'name' => 'Residential North',
                'description' => 'Residential area coverage - North sector',
                'address' => '456 Oak Avenue, North Hills',
                'status' => 'active',
            ],
            [
                'name' => 'Industrial South',
                'description' => 'Industrial and commercial area - South sector',
                'address' => '789 Industrial Blvd, South District',
                'status' => 'active',
            ],
        ];

        $createdSites = [];
        foreach ($sites as $siteData) {
            $site = NetworkSite::create($siteData);
            $createdSites[] = $site;
        }

        $this->command->info('✅ Created '.count($createdSites).' network sites');

        return $createdSites;
    }

    /**
     * Get the existing real MikroTik device.
     */
    protected function getExistingMikroTikDevice(): NetworkDevice
    {
        $this->command->info('🖥️  Using existing real MikroTik device...');

        $device = NetworkDevice::first();

        if (! $device) {
            $this->command->error('❌ No MikroTik device found! Please create a real MikroTik device first.');
            throw new \Exception('No MikroTik device found in the database.');
        }

        $this->command->info("✅ Using real MikroTik device: {$device->name} (ID: {$device->id}) at {$device->ip_address}");

        return $device;
    }

    /**
     * Create IP pools for each site.
     */
    protected function createIpPools(NetworkDevice $device, array $sites): array
    {
        $this->command->info('🌐 Creating IP pools...');

        $poolConfigs = [
            [
                'name' => 'Downtown-Pool',
                'description' => 'IP pool for downtown customers',
                'network_address' => '***********',
                'network_cidr' => '***********/24',
                'gateway' => '***********',
            ],
            [
                'name' => 'North-Pool',
                'description' => 'IP pool for north residential customers',
                'network_address' => '***********',
                'network_cidr' => '***********/24',
                'gateway' => '***********',
            ],
            [
                'name' => 'South-Pool',
                'description' => 'IP pool for south industrial customers',
                'network_address' => '***********',
                'network_cidr' => '***********/24',
                'gateway' => '***********',
            ],
        ];

        $createdPools = [];
        foreach ($poolConfigs as $index => $poolConfig) {
            $pool = IpPool::create([
                'name' => $poolConfig['name'],
                'description' => $poolConfig['description'],
                'network_address' => $poolConfig['network_address'],
                'network_cidr' => $poolConfig['network_cidr'],
                'subnet_mask' => '*************', // Always /24 for simplicity
                'gateway' => $poolConfig['gateway'],
                'dns_servers' => ['*******', '*******'],
                'device_id' => $device->id,
                'status' => 'active',
                'excluded_ips' => [$poolConfig['gateway']], // Exclude gateway
                'interface' => 'ether'.($index + 2), // ether2, ether3, ether4
                'total_addresses' => 254, // /24 network minus network and broadcast
                'available_addresses' => 254,
                'assigned_addresses' => 0,
                'addresses_created' => false,
            ]);
            $createdPools[] = $pool;
        }

        $this->command->info('✅ Created '.count($createdPools).' IP pools');

        return $createdPools;
    }

    /**
     * Create 1000 customers with realistic names.
     */
    protected function createCustomers(): array
    {
        $this->command->info('👥 Creating 1000 customers...');

        // Use realistic names instead of fake data
        $firstNames = [
            'James', 'Mary', 'John', 'Patricia', 'Robert', 'Jennifer', 'Michael', 'Linda',
            'William', 'Elizabeth', 'David', 'Barbara', 'Richard', 'Susan', 'Joseph', 'Jessica',
            'Thomas', 'Sarah', 'Christopher', 'Karen', 'Charles', 'Nancy', 'Daniel', 'Lisa',
            'Matthew', 'Betty', 'Anthony', 'Helen', 'Mark', 'Sandra', 'Donald', 'Donna',
            'Steven', 'Carol', 'Paul', 'Ruth', 'Andrew', 'Sharon', 'Joshua', 'Michelle',
            'Kenneth', 'Laura', 'Kevin', 'Sarah', 'Brian', 'Kimberly', 'George', 'Deborah',
            'Timothy', 'Dorothy', 'Ronald', 'Lisa', 'Jason', 'Nancy', 'Edward', 'Karen',
            'Jeffrey', 'Betty', 'Ryan', 'Helen', 'Jacob', 'Sandra', 'Gary', 'Donna',
        ];

        $lastNames = [
            'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
            'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas',
            'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White',
            'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young',
            'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores',
            'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell',
            'Carter', 'Roberts', 'Gomez', 'Phillips', 'Evans', 'Turner', 'Diaz', 'Parker',
        ];

        $customers = [];
        $batchSize = 100;
        $totalBatches = 10;

        for ($batch = 0; $batch < $totalBatches; $batch++) {
            $this->command->info('Creating customer batch '.($batch + 1)." of {$totalBatches}...");

            $batchCustomers = [];
            for ($i = 0; $i < $batchSize; $i++) {
                $firstName = $firstNames[array_rand($firstNames)];
                $lastName = $lastNames[array_rand($lastNames)];
                $name = $firstName.' '.$lastName;

                $batchCustomers[] = [
                    'name' => $name,
                    'email' => strtolower($firstName.'.'.$lastName.($batch * $batchSize + $i + 1).'@email.com'),
                    'phone' => '('.rand(200, 999).') '.rand(200, 999).'-'.rand(1000, 9999),
                    'address' => rand(100, 9999).' '.['Main St', 'Oak Ave', 'Pine Rd', 'Elm Dr', 'Cedar Ln'][array_rand(['Main St', 'Oak Ave', 'Pine Rd', 'Elm Dr', 'Cedar Ln'])],
                    'city' => ['Springfield', 'Franklin', 'Georgetown', 'Madison', 'Washington'][array_rand(['Springfield', 'Franklin', 'Georgetown', 'Madison', 'Washington'])],
                    'state' => ['CA', 'TX', 'FL', 'NY', 'PA'][array_rand(['CA', 'TX', 'FL', 'NY', 'PA'])],
                    'postal_code' => rand(10000, 99999),
                    'country' => 'United States',
                    'status' => 'active',
                    'user_id' => null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            // Insert batch
            DB::table('customers')->insert($batchCustomers);
            $customers = array_merge($customers, $batchCustomers);
        }

        $this->command->info('✅ Created 1000 customers with realistic names');

        return Customer::all()->toArray();
    }

    /**
     * Create subscriptions for customers distributed across sites.
     */
    protected function createSubscriptions(array $customers, array $sites): array
    {
        $this->command->info('📋 Creating subscriptions...');

        $bandwidthPlans = BandwidthPlan::all();
        $subscriptions = [];
        $batchSize = 100;
        $totalBatches = 10;

        for ($batch = 0; $batch < $totalBatches; $batch++) {
            $this->command->info('Creating subscription batch '.($batch + 1)." of {$totalBatches}...");

            $batchSubscriptions = [];
            $startIndex = $batch * $batchSize;

            for ($i = 0; $i < $batchSize; $i++) {
                $customerIndex = $startIndex + $i;
                $customer = $customers[$customerIndex];

                // Distribute customers across sites
                $siteIndex = $customerIndex % count($sites);
                $site = $sites[$siteIndex];

                // Randomly assign bandwidth plan (weighted towards lower plans)
                $planWeights = [0 => 40, 1 => 35, 2 => 20, 3 => 5]; // 40% basic, 35% standard, 20% premium, 5% business
                $rand = rand(1, 100);
                $planIndex = 0;
                $cumulative = 0;
                foreach ($planWeights as $index => $weight) {
                    $cumulative += $weight;
                    if ($rand <= $cumulative) {
                        $planIndex = $index;
                        break;
                    }
                }

                $bandwidthPlan = $bandwidthPlans[$planIndex];

                // Set billing date to yesterday so they're eligible for billing
                $nextBillingDate = Carbon::now()->subDay();

                $batchSubscriptions[] = [
                    'customer_id' => $customer['id'],
                    'bandwidth_plan_id' => $bandwidthPlan->id,
                    'network_site_id' => $site->id,
                    'name' => $bandwidthPlan->name.' - '.$site->name,
                    'description' => 'Monthly internet subscription',
                    'customer_notes' => null,
                    'allow_overrides' => false,
                    'status' => 'active',
                    'price' => $bandwidthPlan->price,
                    'billing_cycle' => 'monthly',
                    'start_date' => Carbon::now()->startOfMonth()->toDateString(),
                    'end_date' => null,
                    'next_billing_date' => $nextBillingDate->toDateString(),
                    'last_billing_date' => null,
                    'suspension_date' => null,
                    'reactivation_date' => null,
                    'suspension_days' => 0,
                    'auto_billing_enabled' => true,
                    'grace_period_days' => 7,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            // Insert batch
            DB::table('subscriptions')->insert($batchSubscriptions);
            $subscriptions = array_merge($subscriptions, $batchSubscriptions);
        }

        $this->command->info('✅ Created 1000 subscriptions distributed across sites');

        return Subscription::all()->toArray();
    }

    /**
     * Create services (60% PPPoE, 40% Static IP).
     */
    protected function createServices(array $subscriptions, NetworkDevice $device, array $ipPools): void
    {
        $this->command->info('🔧 Creating services (60% PPPoE, 40% Static IP)...');

        $totalServices = count($subscriptions);
        $staticIpCount = (int) ($totalServices * 0.4); // 40% Static IP
        $pppoeCount = $totalServices - $staticIpCount; // 60% PPPoE

        // Create Static IP services first
        $this->createStaticIpServices($subscriptions, $device, $ipPools, $staticIpCount);

        // Create PPPoE services for remaining subscriptions
        $this->createPppoeServices($subscriptions, $device, $staticIpCount, $pppoeCount);

        $this->command->info("✅ Created {$staticIpCount} Static IP services and {$pppoeCount} PPPoE services");
    }

    /**
     * Create Static IP services.
     */
    protected function createStaticIpServices(array $subscriptions, NetworkDevice $device, array $ipPools, int $count): void
    {
        $this->command->info("Creating {$count} Static IP services...");

        $batchSize = 100;
        $batches = ceil($count / $batchSize);

        for ($batch = 0; $batch < $batches; $batch++) {
            $startIndex = $batch * $batchSize;
            $endIndex = min($startIndex + $batchSize, $count);
            $batchCount = $endIndex - $startIndex;

            $this->command->info('Creating Static IP batch '.($batch + 1)." of {$batches} ({$batchCount} services)...");

            $batchServices = [];
            for ($i = $startIndex; $i < $endIndex; $i++) {
                $subscription = $subscriptions[$i];

                // Determine which IP pool to use based on site
                $poolIndex = ($subscription['network_site_id'] - 1) % count($ipPools); // Sites are 1-indexed, ensure we don't exceed pool count
                $ipPool = $ipPools[$poolIndex];

                // Generate IP address (simple sequential assignment)
                $baseIp = explode('.', $ipPool->network_address);
                $hostPart = ($i % 250) + 2; // Start from .2, skip gateway at .1
                $ipAddress = $baseIp[0].'.'.$baseIp[1].'.'.$baseIp[2].'.'.$hostPart;

                $batchServices[] = [
                    'subscription_id' => $subscription['id'],
                    'customer_id' => $subscription['customer_id'],
                    'device_id' => $device->id,
                    'ip_address' => $ipAddress,
                    'subnet_mask' => '*************',
                    'gateway' => $ipPool->gateway,
                    'dns_servers' => json_encode(['*******', '*******']),
                    'bandwidth_plan_id' => $subscription['bandwidth_plan_id'],
                    'ip_pool_id' => $ipPool->id,
                    'status' => 'active',
                    'firewall_rules' => null,
                    'nat_rules' => null,
                    'comment' => 'Migrated from legacy system',
                    'mikrotik_route_id' => null,
                    'mikrotik_firewall_id' => null,
                    'mikrotik_nat_id' => null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            // Insert batch
            DB::table('static_ip_services')->insert($batchServices);
        }
    }

    /**
     * Create PPPoE services.
     */
    protected function createPppoeServices(array $subscriptions, NetworkDevice $device, int $startIndex, int $count): void
    {
        $this->command->info("Creating {$count} PPPoE services...");

        $batchSize = 100;
        $batches = ceil($count / $batchSize);

        for ($batch = 0; $batch < $batches; $batch++) {
            $currentStartIndex = $startIndex + ($batch * $batchSize);
            $currentEndIndex = min($currentStartIndex + $batchSize, $startIndex + $count);
            $batchCount = $currentEndIndex - $currentStartIndex;

            $this->command->info('Creating PPPoE batch '.($batch + 1)." of {$batches} ({$batchCount} services)...");

            $batchServices = [];
            for ($i = $currentStartIndex; $i < $currentEndIndex; $i++) {
                $subscription = $subscriptions[$i];

                // Generate username and password
                $customerName = DB::table('customers')->where('id', $subscription['customer_id'])->value('name');
                $username = $this->generatePppoeUsername($customerName, $subscription['customer_id']);
                $password = $this->generatePppoePassword();

                $batchServices[] = [
                    'subscription_id' => $subscription['id'],
                    'customer_id' => $subscription['customer_id'],
                    'device_id' => $device->id,
                    'username' => $username,
                    'password' => $password,
                    'service_profile' => 'default',
                    'bandwidth_plan_id' => $subscription['bandwidth_plan_id'],
                    'ip_address' => null, // Will be assigned dynamically
                    'remote_address' => null,
                    'status' => 'active',
                    'last_connected' => null,
                    'last_disconnected' => null,
                    'comment' => 'Migrated from legacy system',
                    'mikrotik_id' => null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            // Insert batch
            DB::table('pppoe_services')->insert($batchServices);
        }
    }

    /**
     * Generate PPPoE username.
     */
    protected function generatePppoeUsername(string $customerName, int $customerId): string
    {
        // Remove spaces and special characters
        $name = preg_replace('/[^a-zA-Z0-9]/', '', $customerName);
        $username = strtolower(substr($name, 0, 8)).$customerId;

        return $username;
    }

    /**
     * Generate PPPoE password.
     */
    protected function generatePppoePassword(): string
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $password = '';

        for ($i = 0; $i < 8; $i++) {
            $password .= $chars[rand(0, strlen($chars) - 1)];
        }

        return $password;
    }

    /**
     * Show summary of created data.
     */
    protected function showSummary(): void
    {
        $this->command->newLine();
        $this->command->info('📊 ISP Test Data Summary:');
        $this->command->newLine();

        $bandwidthPlans = BandwidthPlan::count();
        $sites = NetworkSite::count();
        $devices = NetworkDevice::count();
        $ipPools = IpPool::count();
        $customers = Customer::count();
        $subscriptions = Subscription::count();
        $staticIpServices = StaticIpService::count();
        $pppoeServices = PppoeService::count();

        $this->command->table(['Component', 'Count'], [
            ['Bandwidth Plans', $bandwidthPlans],
            ['Network Sites', $sites],
            ['Network Devices', $devices],
            ['IP Pools', $ipPools],
            ['Customers', $customers],
            ['Subscriptions', $subscriptions],
            ['Static IP Services', $staticIpServices],
            ['PPPoE Services', $pppoeServices],
        ]);

        $this->command->newLine();
        $this->command->info('🎯 Next Steps:');
        $this->command->info('1. Test automated billing: php artisan billing:generate-monthly-invoices');
        $this->command->info('2. Verify service management workflows');
        $this->command->info('3. Test network operations features');
        $this->command->newLine();
    }
}
