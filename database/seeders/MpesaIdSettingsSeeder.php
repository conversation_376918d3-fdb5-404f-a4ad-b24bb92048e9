<?php

namespace Database\Seeders;

use App\Models\SystemSetting;
use Illuminate\Database\Seeder;

class MpesaIdSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // M-Pesa ID Generation Settings
            [
                'key' => 'mpesa_id_assignment_mode',
                'value' => 'hybrid',
                'type' => 'string',
                'description' => 'M-Pesa ID assignment mode: auto, manual, or hybrid',
                'group' => 'mpesa_id',
            ],
            [
                'key' => 'mpesa_id_generation_method',
                'value' => 'sequential',
                'type' => 'string',
                'description' => 'M-Pesa ID generation method: sequential, customer_id, phone_number, or custom_format',
                'group' => 'mpesa_id',
            ],
            [
                'key' => 'mpesa_id_prefix',
                'value' => 'MP',
                'type' => 'string',
                'description' => 'Prefix for M-Pesa IDs',
                'group' => 'mpesa_id',
            ],
            [
                'key' => 'mpesa_id_suffix',
                'value' => '',
                'type' => 'string',
                'description' => 'Suffix for M-Pesa IDs',
                'group' => 'mpesa_id',
            ],
            [
                'key' => 'mpesa_id_starting_number',
                'value' => '1000',
                'type' => 'integer',
                'description' => 'Starting number for sequential M-Pesa IDs',
                'group' => 'mpesa_id',
            ],
            [
                'key' => 'mpesa_id_padding',
                'value' => '4',
                'type' => 'integer',
                'description' => 'Number padding for M-Pesa IDs',
                'group' => 'mpesa_id',
            ],
            [
                'key' => 'mpesa_id_custom_format',
                'value' => '{prefix}{customer_id}{suffix}',
                'type' => 'string',
                'description' => 'Custom format for M-Pesa IDs. Available placeholders: {prefix}, {suffix}, {customer_id}, {phone_last_4}, {phone_last_6}, {sequential}',
                'group' => 'mpesa_id',
            ],
            [
                'key' => 'mpesa_id_min_length',
                'value' => '3',
                'type' => 'integer',
                'description' => 'Minimum length for M-Pesa IDs',
                'group' => 'mpesa_id',
            ],
            [
                'key' => 'mpesa_id_max_length',
                'value' => '20',
                'type' => 'integer',
                'description' => 'Maximum length for M-Pesa IDs',
                'group' => 'mpesa_id',
            ],
            [
                'key' => 'mpesa_id_allowed_pattern',
                'value' => '/^[A-Z0-9]+$/',
                'type' => 'string',
                'description' => 'Regex pattern for allowed characters in M-Pesa IDs',
                'group' => 'mpesa_id',
            ],
            [
                'key' => 'mpesa_id_auto_generate_on_create',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Automatically generate M-Pesa ID when creating new customers',
                'group' => 'mpesa_id',
            ],
            [
                'key' => 'mpesa_id_required_for_payments',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Require M-Pesa ID for M-Pesa payments (fallback to invoice number if disabled)',
                'group' => 'mpesa_id',
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
