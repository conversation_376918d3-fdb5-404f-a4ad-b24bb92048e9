<?php

namespace Database\Seeders;

use App\Models\SystemSetting;
use Illuminate\Database\Seeder;

class CompanySettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'company_address',
                'value' => '123 ISP Street, Tech City, TC 12345',
                'type' => 'string',
                'description' => 'Company address for invoices and documents',
                'group' => 'company',
            ],
            [
                'key' => 'company_phone',
                'value' => '+*********** 456',
                'type' => 'string',
                'description' => 'Company phone number for invoices and documents',
                'group' => 'company',
            ],
            [
                'key' => 'company_website',
                'value' => 'https://your-isp.com',
                'type' => 'string',
                'description' => 'Company website URL',
                'group' => 'company',
            ],
            [
                'key' => 'company_tax_number',
                'value' => 'TAX123456789',
                'type' => 'string',
                'description' => 'Company tax/VAT number for invoices',
                'group' => 'company',
            ],
            [
                'key' => 'company_registration_number',
                'value' => 'REG987654321',
                'type' => 'string',
                'description' => 'Company registration number',
                'group' => 'company',
            ],
            [
                'key' => 'invoice_terms',
                'value' => 'Payment is due within 30 days of invoice date. Late payments may incur additional charges.',
                'type' => 'text',
                'description' => 'Default terms and conditions for invoices',
                'group' => 'company',
            ],
            [
                'key' => 'invoice_footer_text',
                'value' => 'Thank you for choosing our services!',
                'type' => 'string',
                'description' => 'Footer text for invoices',
                'group' => 'company',
            ],
        ];

        foreach ($settings as $setting) {
            SystemSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('Company settings seeded successfully.');
    }
}
