<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

/**
 * Mass customer migration seeder
 * Uses the new import system for scalable data generation
 */
class MassCustomerMigrationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting Mass Customer Migration Seeder');
        $this->command->info('==========================================');
        $this->command->newLine();

        // Use the mass import command for consistency
        $this->command->info('📋 Executing mass import command...');

        // For seeder, we need to bypass confirmation prompts
        $exitCode = Artisan::call('import:mass-migration', [
            '--customers' => 2000,
            '--clear-data' => true,
            '--force' => true,
            '--chunk-size' => 50,
        ]);

        // Display the command output
        $this->command->info(Artisan::output());

        $this->command->info('✅ Mass import command executed');
        $this->command->info('📝 Jobs have been dispatched to the queue');
        $this->command->newLine();

        $this->command->info('🔧 Next Steps:');
        $this->command->info('  1. Run queue worker: php artisan queue:work');
        $this->command->info('  2. Monitor progress in logs');
        $this->command->info('  3. Check database for imported data');
        $this->command->newLine();

        $this->command->warn('⚠️  Note: This seeder dispatches queue jobs.');
        $this->command->warn('   Data will be created asynchronously.');
        $this->command->warn('   Run the queue worker to process the jobs.');
    }
}
