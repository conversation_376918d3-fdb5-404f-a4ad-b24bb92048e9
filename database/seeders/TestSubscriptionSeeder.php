<?php

namespace Database\Seeders;

use App\Models\Customer;
use App\Models\Subscription;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TestSubscriptionSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     *
     * This seeder creates 1000 customers with subscriptions for testing
     * the automated invoice generation command.
     */
    public function run(): void
    {
        $this->command->info('Creating test data for invoice generation testing...');

        // Check if we should clear existing data
        $shouldClear = $this->command->option('clear') ?? false;

        if ($shouldClear) {
            // Disable foreign key checks for better performance
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            // Clear existing test data
            $this->command->info('Clearing existing test data...');
            DB::table('subscriptions')->truncate();
            DB::table('customers')->truncate();

            // Re-enable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }

        $this->command->info('Generating 1000 customers with subscriptions...');

        // Create customers in batches for better performance
        $batchSize = 100;
        $totalCustomers = 1000;
        $batches = ceil($totalCustomers / $batchSize);

        for ($batch = 0; $batch < $batches; $batch++) {
            $customersInThisBatch = min($batchSize, $totalCustomers - ($batch * $batchSize));

            $this->command->info('Creating batch '.($batch + 1)." of {$batches} ({$customersInThisBatch} customers)...");

            // Create customers for this batch
            $customers = Customer::factory($customersInThisBatch)->create();

            // Create one subscription per customer
            foreach ($customers as $customer) {
                Subscription::factory()->create([
                    'customer_id' => $customer->id,
                    'bandwidth_plan_id' => 1, // Hardcoded as requested
                    'network_site_id' => 1,   // Hardcoded as requested (device/router)
                ]);
            }
        }

        $this->command->info('Test data generation completed!');
        $this->command->info('Created:');
        $this->command->info('- 1000 customers');
        $this->command->info('- 1000 active subscriptions');
        $this->command->info('- All subscriptions use bandwidth plan ID: 1');
        $this->command->info('- All subscriptions use network site ID: 1');
        $this->command->info('- No invoices were generated (as requested)');
        $this->command->info('');
        $this->command->info('You can now test the automated invoice generation command:');
        $this->command->info('php artisan invoices:generate-monthly');
    }
}
